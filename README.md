# 🎨 Neural Style Transfer Application

## 📸 Demo Images

<div align="center">
  <img src="../pictures/image1.jpg" width="250" alt="Content Image"/>
  <img src="../pictures/image2.png" width="250" alt="Style Image"/>
  <img src="../pictures/image3.jpg" width="250" alt="Result"/>
  <br>
  <em>Content Image → Style Image → Stylized Result</em>
</div>

<div align="center">
  <img src="../pictures/image4.png" width="250" alt="Demo 2"/>
  <img src="../pictures/image5.png" width="250" alt="Demo 3"/>
  <img src="../pictures/image6.png" width="250" alt="Demo 4"/>
  <br>
  <em>More examples of style transfer results</em>
</div>

---

## 🚀 Overview

A high-performance **Neural Style Transfer** application built with advanced deep learning techniques. This application combines the content of one image with the artistic style of another, creating stunning artistic renditions using state-of-the-art optimization methods.

### ✨ Key Features

- **Real-time Style Transfer**: Interactive web interface powered by Gradio
- **Advanced Optimization**: Implementation of cutting-edge training techniques
- **GPU Acceleration**: CUDA support for faster processing
- **Flexible Parameters**: Fine-tune style and content weights, learning rates, and epochs
- **Multiple Initialization**: Support for both content-based and noise-based initialization

---

## 🔬 Technical Implementation

### Core Architecture

- **Feature Extractor**: Pre-trained VGG19 model for deep feature extraction
- **Loss Functions**: Combined content loss (MSE) and style loss (Gram matrix)
- **Optimization**: Advanced gradient-based optimization with multiple techniques

### 🧠 Advanced Techniques Implemented

#### 1. **Adam Optimizer with Learning Rate Scheduling**
```python
optimizer = optim.Adam([generated_image], lr=lr)
scheduler = optim.lr_scheduler.StepLR(optimizer, step_size=100, gamma=0.7)
```
- Adaptive learning rate optimization
- Exponential decay scheduling for stable convergence

#### 2. **Gradient Clipping**
```python
torch.nn.utils.clip_grad_norm_([generated_image], max_norm=1.0)
```
- Prevents gradient explosion
- Ensures stable training dynamics
- Improves convergence reliability

#### 3. **Mixed Precision Training (AMP)**
```python
scaler = torch.amp.GradScaler(enabled=use_amp)
scaler.scale(total_loss).backward()
scaler.unscale_(optimizer)
scaler.step(optimizer)
```
- Automatic Mixed Precision for memory efficiency
- Faster training on modern GPUs
- Maintains numerical stability

#### 4. **Gradient Decay Strategy**
- Learning rate decay: `γ = 0.7` every 100 steps
- Adaptive step size reduction for fine-tuning
- Prevents overshooting in later epochs

#### 5. **Advanced Loss Formulation**
```python
# Normalized Gram Matrix for style representation
G = torch.mm(features, features.t()).div(c * c)

# Weighted loss combination
total_loss = content_weight * content_loss + style_weight * style_loss
```
- Mathematically stable Gram matrix normalization
- Balanced content-style trade-off

#### 6. **Tensor Clamping & Regularization**
```python
with torch.no_grad():
    generated_image.clamp_(0, 1)
```
- Pixel value constraints for valid image generation
- Prevents numerical overflow

---

## 🛠️ Technology Stack

| Component | Technology |
|-----------|------------|
| **Deep Learning Framework** | PyTorch |
| **Pre-trained Model** | VGG19 (ImageNet weights) |
| **Optimization** | Adam + StepLR Scheduler |
| **Web Interface** | Gradio |
| **Image Processing** | PIL, torchvision |
| **Acceleration** | CUDA, Mixed Precision |

---

## 📋 Requirements

```txt
torch>=1.9.0
torchvision>=0.10.0
gradio>=3.0.0
Pillow>=8.0.0
```

---

## 🚀 Quick Start

1. **Clone the repository**
```bash
git clone <repository-url>
cd Neural-style-transfer-app
```

2. **Install dependencies**
```bash
pip install -r requirements.txt
```

3. **Run the application**
```bash
python app.py
```

4. **Open your browser** and navigate to the provided local URL

---

## 🎛️ Parameters

| Parameter | Description | Range | Default |
|-----------|-------------|-------|---------|
| **Style Weight** | Controls style influence | 10-1000 | 50 |
| **Content Weight** | Controls content preservation | 1-100 | 1 |
| **Epochs** | Optimization iterations | 100-10000 | 500 |
| **Learning Rate** | Optimization step size | 0.01-0.5 | 0.1 |
| **Init Noise** | Random vs content initialization | Boolean | False |

---

## 🔧 Advanced Configuration

### Custom Layer Selection
```python
content_layer_n = 21          # VGG19 conv4_2
style_layers = [0, 5, 10, 19, 28]  # Multiple conv layers
```

### Memory Optimization
- Automatic GPU detection and utilization
- Mixed precision training for large images
- Gradient accumulation for memory-constrained environments

---

## 📊 Performance Metrics

- **Processing Time**: ~30-60 seconds (GPU) / ~2-5 minutes (CPU)
- **Memory Usage**: ~2-4GB VRAM for 512x512 images
- **Convergence**: Typically stable within 300-500 epochs

---

## 🎯 Use Cases

- **Digital Art Creation**: Transform photos into artistic masterpieces
- **Content Creation**: Generate unique visuals for social media
- **Research**: Experiment with neural style transfer techniques
- **Education**: Learn about deep learning and computer vision

---

## 🤝 Contributing

Contributions are welcome! Please feel free to submit pull requests or open issues for:
- Performance improvements
- New optimization techniques
- UI/UX enhancements
- Bug fixes

---

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

---

## 🙏 Acknowledgments

- **Gatys et al.** for the original Neural Style Transfer paper
- **PyTorch Team** for the excellent deep learning framework
- **VGG Team** for the pre-trained feature extraction model

---

<div align="center">
  <strong>Built with ❤️ and advanced deep learning techniques</strong>
</div>