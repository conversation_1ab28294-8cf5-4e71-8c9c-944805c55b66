{"version": 3, "file": "ImagePreview-CpxjYXeK.js", "sources": ["../../../../js/image/shared/ImagePreview.svelte"], "sourcesContent": ["<script lang=\"ts\">\n\timport { createEventDispatcher, onMount } from \"svelte\";\n\timport type { SelectData } from \"@gradio/utils\";\n\timport { uploadToHuggingFace } from \"@gradio/utils\";\n\timport {\n\t\tBlockLabel,\n\t\tEmpty,\n\t\tIconButton,\n\t\tShareButton,\n\t\tIconButtonWrapper,\n\t\tFullscreenButton\n\t} from \"@gradio/atoms\";\n\timport { Download, Image as ImageIcon } from \"@gradio/icons\";\n\timport { get_coordinates_of_clicked_image } from \"./utils\";\n\timport Image from \"./Image.svelte\";\n\timport { DownloadLink } from \"@gradio/wasm/svelte\";\n\n\timport type { I18nFormatter } from \"@gradio/utils\";\n\timport type { FileData } from \"@gradio/client\";\n\n\texport let value: null | FileData;\n\texport let label: string | undefined = undefined;\n\texport let show_label: boolean;\n\texport let show_download_button = true;\n\texport let selectable = false;\n\texport let show_share_button = false;\n\texport let i18n: I18nFormatter;\n\texport let show_fullscreen_button = true;\n\texport let display_icon_button_wrapper_top_corner = false;\n\texport let fullscreen = false;\n\n\tconst dispatch = createEventDispatcher<{\n\t\tchange: string;\n\t\tselect: SelectData;\n\t\tfullscreen: boolean;\n\t}>();\n\n\tconst handle_click = (evt: MouseEvent): void => {\n\t\tlet coordinates = get_coordinates_of_clicked_image(evt);\n\t\tif (coordinates) {\n\t\t\tdispatch(\"select\", { index: coordinates, value: null });\n\t\t}\n\t};\n\n\tlet image_container: HTMLElement;\n</script>\n\n<BlockLabel\n\t{show_label}\n\tIcon={ImageIcon}\n\tlabel={!show_label ? \"\" : label || i18n(\"image.image\")}\n/>\n{#if value === null || !value.url}\n\t<Empty unpadded_box={true} size=\"large\"><ImageIcon /></Empty>\n{:else}\n\t<div class=\"image-container\" bind:this={image_container}>\n\t\t<IconButtonWrapper\n\t\t\tdisplay_top_corner={display_icon_button_wrapper_top_corner}\n\t\t>\n\t\t\t{#if show_fullscreen_button}\n\t\t\t\t<FullscreenButton {fullscreen} on:fullscreen />\n\t\t\t{/if}\n\n\t\t\t{#if show_download_button}\n\t\t\t\t<DownloadLink href={value.url} download={value.orig_name || \"image\"}>\n\t\t\t\t\t<IconButton Icon={Download} label={i18n(\"common.download\")} />\n\t\t\t\t</DownloadLink>\n\t\t\t{/if}\n\t\t\t{#if show_share_button}\n\t\t\t\t<ShareButton\n\t\t\t\t\t{i18n}\n\t\t\t\t\ton:share\n\t\t\t\t\ton:error\n\t\t\t\t\tformatter={async (value) => {\n\t\t\t\t\t\tif (!value) return \"\";\n\t\t\t\t\t\tlet url = await uploadToHuggingFace(value, \"url\");\n\t\t\t\t\t\treturn `<img src=\"${url}\" />`;\n\t\t\t\t\t}}\n\t\t\t\t\t{value}\n\t\t\t\t/>\n\t\t\t{/if}\n\t\t</IconButtonWrapper>\n\t\t<button on:click={handle_click}>\n\t\t\t<div class:selectable class=\"image-frame\">\n\t\t\t\t<Image src={value.url} alt=\"\" loading=\"lazy\" on:load />\n\t\t\t</div>\n\t\t</button>\n\t</div>\n{/if}\n\n<style>\n\t.image-container {\n\t\theight: 100%;\n\t\tposition: relative;\n\t\tmin-width: var(--size-20);\n\t}\n\n\t.image-container button {\n\t\twidth: var(--size-full);\n\t\theight: var(--size-full);\n\t\tborder-radius: var(--radius-lg);\n\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t}\n\n\t.image-frame :global(img) {\n\t\twidth: var(--size-full);\n\t\theight: var(--size-full);\n\t\tobject-fit: scale-down;\n\t}\n\n\t.selectable {\n\t\tcursor: crosshair;\n\t}\n\n\t:global(.fullscreen-controls svg) {\n\t\tposition: relative;\n\t\ttop: 0px;\n\t}\n\n\t:global(.image-container:fullscreen) {\n\t\tbackground-color: black;\n\t\tdisplay: flex;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t}\n\n\t:global(.image-container:fullscreen img) {\n\t\tmax-width: 90vw;\n\t\tmax-height: 90vh;\n\t\tobject-fit: scale-down;\n\t}\n\n\t.image-frame {\n\t\twidth: auto;\n\t\theight: 100%;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t}\n</style>\n"], "names": ["createEventDispatcher", "onMount", "ctx", "insert", "target", "div1", "anchor", "append", "button", "div0", "dirty", "image_changes", "downloadlink_changes", "Download", "iconbutton_changes", "create_if_block_3", "create_if_block_2", "create_if_block_1", "ImageIcon", "value", "$$props", "label", "show_label", "show_download_button", "selectable", "show_share_button", "i18n", "show_fullscreen_button", "display_icon_button_wrapper_top_corner", "fullscreen", "dispatch", "handle_click", "evt", "coordinates", "get_coordinates_of_clicked_image", "image_container", "uploadToHuggingFace", "$$value"], "mappings": "8vCACU,CAAA,sBAAAA,GAAA,QAAAC,IAAsC,OAAA,0GAwDzBC,EAAsC,CAAA,6DA2B7C,IAAAA,KAAM,oRA7BrBC,EAgCKC,EAAAC,EAAAC,CAAA,qBALJC,EAIQF,EAAAG,CAAA,EAHPD,EAEKC,EAAAC,CAAA,+CAHYP,EAAY,EAAA,CAAA,wDAzBTA,EAAsC,CAAA,8DA2B7CQ,EAAA,IAAAC,EAAA,IAAAT,KAAM,mQA/BA,qjBAWE,KAAAA,KAAM,aAAeA,EAAK,CAAA,EAAC,WAAa,iHAAxCQ,EAAA,IAAAE,EAAA,KAAAV,KAAM,sBAAeA,EAAK,CAAA,EAAC,WAAa,4LACzCW,EAAiB,MAAAX,KAAK,iBAAiB,oEAAtBQ,EAAA,KAAAI,EAAA,MAAAZ,KAAK,iBAAiB,0bANtDA,EAAsB,CAAA,GAAAa,EAAAb,CAAA,IAItBA,EAAoB,CAAA,GAAAc,EAAAd,CAAA,IAKpBA,EAAiB,CAAA,GAAAe,EAAAf,CAAA,8IATjBA,EAAsB,CAAA,0GAItBA,EAAoB,CAAA,wGAKpBA,EAAiB,CAAA,scAnBlBgB,QACEhB,EAAU,CAAA,EAAQA,EAAK,CAAA,GAAIA,EAAI,CAAA,EAAC,aAAa,EAAhC,2CAEjB,OAAAA,EAAU,CAAA,IAAA,MAAS,CAAAA,KAAM,IAAG,0LAFxBA,EAAU,CAAA,EAAQA,EAAK,CAAA,GAAIA,EAAI,CAAA,EAAC,aAAa,EAAhC,+RA9BV,GAAA,CAAA,MAAAiB,CAAA,EAAAC,GACA,MAAAC,EAA4B,MAAA,EAAAD,EAC5B,CAAA,WAAAE,CAAA,EAAAF,GACA,qBAAAG,EAAuB,EAAA,EAAAH,GACvB,WAAAI,EAAa,EAAA,EAAAJ,GACb,kBAAAK,EAAoB,EAAA,EAAAL,EACpB,CAAA,KAAAM,CAAA,EAAAN,GACA,uBAAAO,EAAyB,EAAA,EAAAP,GACzB,uCAAAQ,EAAyC,EAAA,EAAAR,GACzC,WAAAS,EAAa,EAAA,EAAAT,QAElBU,EAAW9B,KAMX+B,EAAgBC,GAAA,CACjB,IAAAC,EAAcC,EAAiCF,CAAG,EAClDC,GACHH,EAAS,SAAA,CAAY,MAAOG,EAAa,MAAO,IAAA,CAAA,GAI9C,IAAAE,+CA6BkBhB,GACZA,eACW,MAAAiB,EAAoBjB,CAAY,CACzB,OAFJ,0IAnBgBgB,EAAeE"}