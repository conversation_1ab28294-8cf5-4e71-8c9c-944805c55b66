/* empty css                                                        */import"./MarkdownCode.svelte_svelte_type_style_lang-Bl8p72Ow.js";import{B as ht}from"./BlockLabel-3KxTaaiM.js";import{I as pt}from"./IconButton-C_HS7fTi.js";import{C as bt}from"./Clear-By3xiIwg.js";import{I as wt}from"./Image-Bsh8Umrh.js";import{n as vt}from"./index-DJ2rNx9E.js";import{W as kt,a as yt,S as It}from"./SelectSource-DG1n31De.js";import{I as zt}from"./IconButtonWrapper--EIOWuEM.js";import{F as Ct}from"./FullscreenButton-BG4mOKmH.js";import{g as Dt}from"./utils-Gtzs_Zla.js";import{D as ot}from"./DropdownArrow-DYWFcSFn.js";import{S as St}from"./Square-oAGqOwsh.js";import"./StreamingBar.svelte_svelte_type_style_lang-DlRzv56K.js";import{f as qt}from"./index-BALGG9zl.js";import{S as Et}from"./StreamingBar-BpqwJkLy.js";import{a as Wt}from"./Upload-8igJ-HYX.js";import"./DownloadLink.svelte_svelte_type_style_lang-C_5UIfol.js";import{I as Bt}from"./Image-CnqB5dbD.js";const{SvelteComponent:Mt,append:Je,attr:L,detach:Tt,init:Rt,insert:jt,noop:Ne,safe_not_equal:Nt,svg_element:Ue}=window.__gradio__svelte__internal;function Ut(i){let e,t,n;return{c(){e=Ue("svg"),t=Ue("path"),n=Ue("circle"),L(t,"d","M23 19a2 2 0 0 1-2 2H3a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h4l2-3h6l2 3h4a2 2 0 0 1 2 2z"),L(n,"cx","12"),L(n,"cy","13"),L(n,"r","4"),L(e,"xmlns","http://www.w3.org/2000/svg"),L(e,"width","100%"),L(e,"height","100%"),L(e,"viewBox","0 0 24 24"),L(e,"fill","none"),L(e,"stroke","currentColor"),L(e,"stroke-width","1.5"),L(e,"stroke-linecap","round"),L(e,"stroke-linejoin","round"),L(e,"class","feather feather-camera")},m(l,s){jt(l,e,s),Je(e,t),Je(e,n)},p:Ne,i:Ne,o:Ne,d(l){l&&Tt(e)}}}class Lt extends Mt{constructor(e){super(),Rt(this,e,null,Ut,Nt,{})}}const{SvelteComponent:Pt,append:Ft,attr:$,detach:At,init:Ht,insert:Ot,noop:Le,safe_not_equal:$t,svg_element:Ke}=window.__gradio__svelte__internal;function Vt(i){let e,t;return{c(){e=Ke("svg"),t=Ke("circle"),$(t,"cx","12"),$(t,"cy","12"),$(t,"r","10"),$(e,"xmlns","http://www.w3.org/2000/svg"),$(e,"width","100%"),$(e,"height","100%"),$(e,"viewBox","0 0 24 24"),$(e,"stroke-width","1.5"),$(e,"stroke-linecap","round"),$(e,"stroke-linejoin","round"),$(e,"class","feather feather-circle")},m(n,l){Ot(n,e,l),Ft(e,t)},p:Le,i:Le,o:Le,d(n){n&&At(e)}}}class Gt extends Pt{constructor(e){super(),Ht(this,e,null,Vt,$t,{})}}const{SvelteComponent:Jt,append:ye,attr:Pe,create_component:Kt,destroy_component:Qt,detach:Xt,element:Fe,init:Yt,insert:Zt,listen:xt,mount_component:en,noop:tn,safe_not_equal:nn,set_style:ln,space:rn,text:sn,transition_in:on,transition_out:an}=window.__gradio__svelte__internal,{createEventDispatcher:cn}=window.__gradio__svelte__internal;function un(i){let e,t,n,l,s,r="Click to Access Webcam",c,o,u,m;return l=new kt({}),{c(){e=Fe("button"),t=Fe("div"),n=Fe("span"),Kt(l.$$.fragment),s=rn(),c=sn(r),Pe(n,"class","icon-wrap svelte-qbrfs"),Pe(t,"class","wrap svelte-qbrfs"),Pe(e,"class","svelte-qbrfs"),ln(e,"height","100%")},m(_,w){Zt(_,e,w),ye(e,t),ye(t,n),en(l,n,null),ye(t,s),ye(t,c),o=!0,u||(m=xt(e,"click",i[1]),u=!0)},p:tn,i(_){o||(on(l.$$.fragment,_),o=!0)},o(_){an(l.$$.fragment,_),o=!1},d(_){_&&Xt(e),Qt(l),u=!1,m()}}}function _n(i){const e=cn();return[e,()=>e("click")]}class fn extends Jt{constructor(e){super(),Yt(this,e,_n,un,nn,{})}}function mn(){return navigator.mediaDevices.enumerateDevices()}function dn(i,e){e.srcObject=i,e.muted=!0,e.play()}async function Qe(i,e,t,n){const l={video:n?{deviceId:{exact:n},...t?.video}:t?.video||{width:{ideal:1920},height:{ideal:1440}},audio:i&&(t?.audio??!0)};return navigator.mediaDevices.getUserMedia(l).then(s=>(dn(s,e),s))}function gn(i){return i.filter(t=>t.kind==="videoinput")}const{SvelteComponent:hn,action_destroyer:pn,add_render_callback:bn,append:S,attr:v,binding_callbacks:wn,check_outros:de,create_component:ee,create_in_transition:vn,destroy_component:te,destroy_each:kn,detach:N,element:R,empty:$e,ensure_array_like:Xe,flush:V,group_outros:ge,init:yn,insert:U,listen:De,mount_component:ne,noop:Ve,run_all:In,safe_not_equal:zn,set_data:ve,set_input_value:Ae,set_style:at,space:J,src_url_equal:Ye,stop_propagation:Cn,text:ke,toggle_class:re,transition_in:E,transition_out:T}=window.__gradio__svelte__internal,{createEventDispatcher:Dn,onDestroy:Sn,onMount:qn}=window.__gradio__svelte__internal;function Ze(i,e,t){const n=i.slice();return n[39]=e[t],n}function En(i){let e,t,n,l,s,r,c,o,u,m,_;const w=[Mn,Bn],k=[];function j(b,y){return b[2]==="video"||b[1]?0:1}n=j(i),l=k[n]=w[n](i);let g=!i[11]&&xe(i),p=i[13]&&i[8]&&et(i);return{c(){e=R("div"),t=R("button"),l.c(),r=J(),g&&g.c(),c=J(),p&&p.c(),o=$e(),v(t,"aria-label",s=i[2]==="image"?"capture photo":"start recording"),v(t,"class","svelte-10cpz3p"),v(e,"class","button-wrap svelte-10cpz3p")},m(b,y){U(b,e,y),S(e,t),k[n].m(t,null),S(e,r),g&&g.m(e,null),U(b,c,y),p&&p.m(b,y),U(b,o,y),u=!0,m||(_=De(t,"click",i[28]),m=!0)},p(b,y){let P=n;n=j(b),n===P?k[n].p(b,y):(ge(),T(k[P],1,1,()=>{k[P]=null}),de(),l=k[n],l?l.p(b,y):(l=k[n]=w[n](b),l.c()),E(l,1),l.m(t,null)),(!u||y[0]&4&&s!==(s=b[2]==="image"?"capture photo":"start recording"))&&v(t,"aria-label",s),b[11]?g&&(ge(),T(g,1,1,()=>{g=null}),de()):g?(g.p(b,y),y[0]&2048&&E(g,1)):(g=xe(b),g.c(),E(g,1),g.m(e,null)),b[13]&&b[8]?p?(p.p(b,y),y[0]&8448&&E(p,1)):(p=et(b),p.c(),E(p,1),p.m(o.parentNode,o)):p&&(ge(),T(p,1,1,()=>{p=null}),de())},i(b){u||(E(l),E(g),E(p),u=!0)},o(b){T(l),T(g),T(p),u=!1},d(b){b&&(N(e),N(c),N(o)),k[n].d(),g&&g.d(),p&&p.d(b),m=!1,_()}}}function Wn(i){let e,t,n,l;return t=new fn({}),t.$on("click",i[27]),{c(){e=R("div"),ee(t.$$.fragment),v(e,"title","grant webcam access"),at(e,"height","100%")},m(s,r){U(s,e,r),ne(t,e,null),l=!0},p:Ve,i(s){l||(E(t.$$.fragment,s),s&&(n||bn(()=>{n=vn(e,qt,{delay:100,duration:200}),n.start()})),l=!0)},o(s){T(t.$$.fragment,s),l=!1},d(s){s&&N(e),te(t)}}}function Bn(i){let e,t,n;return t=new Lt({}),{c(){e=R("div"),ee(t.$$.fragment),v(e,"class","icon svelte-10cpz3p"),v(e,"title","capture photo")},m(l,s){U(l,e,s),ne(t,e,null),n=!0},p:Ve,i(l){n||(E(t.$$.fragment,l),n=!0)},o(l){T(t.$$.fragment,l),n=!1},d(l){l&&N(e),te(t)}}}function Mn(i){let e,t,n,l;const s=[jn,Rn,Tn],r=[];function c(o,u){return o[1]&&o[10]==="waiting"?0:o[1]&&o[10]==="open"||!o[1]&&o[11]?1:2}return e=c(i),t=r[e]=s[e](i),{c(){t.c(),n=$e()},m(o,u){r[e].m(o,u),U(o,n,u),l=!0},p(o,u){let m=e;e=c(o),e===m?r[e].p(o,u):(ge(),T(r[m],1,1,()=>{r[m]=null}),de(),t=r[e],t?t.p(o,u):(t=r[e]=s[e](o),t.c()),E(t,1),t.m(n.parentNode,n))},i(o){l||(E(t),l=!0)},o(o){T(t),l=!1},d(o){o&&N(n),r[e].d(o)}}}function Tn(i){let e,t,n,l,s=i[4]("audio.record")+"",r,c;return n=new Gt({}),{c(){e=R("div"),t=R("div"),ee(n.$$.fragment),l=J(),r=ke(s),v(t,"class","icon color-primary svelte-10cpz3p"),v(t,"title","start recording"),v(e,"class","icon-with-text svelte-10cpz3p")},m(o,u){U(o,e,u),S(e,t),ne(n,t,null),S(e,l),S(e,r),c=!0},p(o,u){(!c||u[0]&16)&&s!==(s=o[4]("audio.record")+"")&&ve(r,s)},i(o){c||(E(n.$$.fragment,o),c=!0)},o(o){T(n.$$.fragment,o),c=!1},d(o){o&&N(e),te(n)}}}function Rn(i){let e,t,n,l,s=i[4]("audio.stop")+"",r,c;return n=new St({}),{c(){e=R("div"),t=R("div"),ee(n.$$.fragment),l=J(),r=ke(s),v(t,"class","icon color-primary svelte-10cpz3p"),v(t,"title","stop recording"),v(e,"class","icon-with-text svelte-10cpz3p")},m(o,u){U(o,e,u),S(e,t),ne(n,t,null),S(e,l),S(e,r),c=!0},p(o,u){(!c||u[0]&16)&&s!==(s=o[4]("audio.stop")+"")&&ve(r,s)},i(o){c||(E(n.$$.fragment,o),c=!0)},o(o){T(n.$$.fragment,o),c=!1},d(o){o&&N(e),te(n)}}}function jn(i){let e,t,n,l,s=i[4]("audio.waiting")+"",r,c;return n=new yt({}),{c(){e=R("div"),t=R("div"),ee(n.$$.fragment),l=J(),r=ke(s),v(t,"class","icon color-primary svelte-10cpz3p"),v(t,"title","spinner"),v(e,"class","icon-with-text svelte-10cpz3p"),at(e,"width","var(--size-24)")},m(o,u){U(o,e,u),S(e,t),ne(n,t,null),S(e,l),S(e,r),c=!0},p(o,u){(!c||u[0]&16)&&s!==(s=o[4]("audio.waiting")+"")&&ve(r,s)},i(o){c||(E(n.$$.fragment,o),c=!0)},o(o){T(n.$$.fragment,o),c=!1},d(o){o&&N(e),te(n)}}}function xe(i){let e,t,n,l,s;return t=new ot({}),{c(){e=R("button"),ee(t.$$.fragment),v(e,"class","icon svelte-10cpz3p"),v(e,"aria-label","select input source")},m(r,c){U(r,e,c),ne(t,e,null),n=!0,l||(s=De(e,"click",i[29]),l=!0)},p:Ve,i(r){n||(E(t.$$.fragment,r),n=!0)},o(r){T(t.$$.fragment,r),n=!1},d(r){r&&N(e),te(t),l=!1,s()}}}function et(i){let e,t,n,l,s,r,c;n=new ot({});function o(_,w){return _[7].length===0?Un:Nn}let u=o(i),m=u(i);return{c(){e=R("select"),t=R("button"),ee(n.$$.fragment),l=J(),m.c(),v(t,"class","inset-icon svelte-10cpz3p"),v(e,"class","select-wrap svelte-10cpz3p"),v(e,"aria-label","select source")},m(_,w){U(_,e,w),S(e,t),ne(n,t,null),S(t,l),m.m(e,null),s=!0,r||(c=[De(t,"click",Cn(i[30])),pn(Ge.call(null,e,i[17])),De(e,"change",i[14])],r=!0)},p(_,w){u===(u=o(_))&&m?m.p(_,w):(m.d(1),m=u(_),m&&(m.c(),m.m(e,null)))},i(_){s||(E(n.$$.fragment,_),s=!0)},o(_){T(n.$$.fragment,_),s=!1},d(_){_&&N(e),te(n),m.d(),r=!1,In(c)}}}function Nn(i){let e,t=Xe(i[7]),n=[];for(let l=0;l<t.length;l+=1)n[l]=tt(Ze(i,t,l));return{c(){for(let l=0;l<n.length;l+=1)n[l].c();e=$e()},m(l,s){for(let r=0;r<n.length;r+=1)n[r]&&n[r].m(l,s);U(l,e,s)},p(l,s){if(s[0]&384){t=Xe(l[7]);let r;for(r=0;r<t.length;r+=1){const c=Ze(l,t,r);n[r]?n[r].p(c,s):(n[r]=tt(c),n[r].c(),n[r].m(e.parentNode,e))}for(;r<n.length;r+=1)n[r].d(1);n.length=t.length}},d(l){l&&N(e),kn(n,l)}}}function Un(i){let e,t=i[4]("common.no_devices")+"",n;return{c(){e=R("option"),n=ke(t),e.__value="",Ae(e,e.__value),v(e,"class","svelte-10cpz3p")},m(l,s){U(l,e,s),S(e,n)},p(l,s){s[0]&16&&t!==(t=l[4]("common.no_devices")+"")&&ve(n,t)},d(l){l&&N(e)}}}function tt(i){let e,t=i[39].label+"",n,l,s,r;return{c(){e=R("option"),n=ke(t),l=J(),e.__value=s=i[39].deviceId,Ae(e,e.__value),e.selected=r=i[8].deviceId===i[39].deviceId,v(e,"class","svelte-10cpz3p")},m(c,o){U(c,e,o),S(e,n),S(e,l)},p(c,o){o[0]&128&&t!==(t=c[39].label+"")&&ve(n,t),o[0]&128&&s!==(s=c[39].deviceId)&&(e.__value=s,Ae(e,e.__value)),o[0]&384&&r!==(r=c[8].deviceId===c[39].deviceId)&&(e.selected=r)},d(c){c&&N(e)}}}function Ln(i){let e,t,n,l,s,r,c,o,u,m,_;t=new Et({props:{time_limit:i[9]}});const w=[Wn,En],k=[];function j(g,p){return g[12]?1:0}return u=j(i),m=k[u]=w[u](i),{c(){e=R("div"),ee(t.$$.fragment),n=J(),l=R("video"),s=J(),r=R("img"),o=J(),m.c(),v(l,"class","svelte-10cpz3p"),re(l,"flip",i[3]),re(l,"hide",!i[12]||i[12]&&!!i[0]),Ye(r.src,c=i[0]?.url)||v(r,"src",c),v(r,"class","svelte-10cpz3p"),re(r,"hide",!i[12]||i[12]&&!i[0]),v(e,"class","wrap svelte-10cpz3p")},m(g,p){U(g,e,p),ne(t,e,null),S(e,n),S(e,l),i[26](l),S(e,s),S(e,r),S(e,o),k[u].m(e,null),_=!0},p(g,p){const b={};p[0]&512&&(b.time_limit=g[9]),t.$set(b),(!_||p[0]&8)&&re(l,"flip",g[3]),(!_||p[0]&4097)&&re(l,"hide",!g[12]||g[12]&&!!g[0]),(!_||p[0]&1&&!Ye(r.src,c=g[0]?.url))&&v(r,"src",c),(!_||p[0]&4097)&&re(r,"hide",!g[12]||g[12]&&!g[0]);let y=u;u=j(g),u===y?k[u].p(g,p):(ge(),T(k[y],1,1,()=>{k[y]=null}),de(),m=k[u],m?m.p(g,p):(m=k[u]=w[u](g),m.c()),E(m,1),m.m(e,null))},i(g){_||(E(t.$$.fragment,g),E(m),_=!0)},o(g){T(t.$$.fragment,g),T(m),_=!1},d(g){g&&N(e),te(t),i[26](null),k[u].d()}}}function Ge(i,e){const t=n=>{i&&!i.contains(n.target)&&!n.defaultPrevented&&e(n)};return document.addEventListener("click",t,!0),{destroy(){document.removeEventListener("click",t,!0)}}}function Pn(i,e,t){let n,l=[],s=null,r=null,c="closed";const o=d=>{d==="closed"?(t(9,r=null),t(10,c="closed"),t(0,F=null)):d==="waiting"?t(10,c="waiting"):t(10,c="open")},u=d=>{h&&t(9,r=d)};let m,{streaming:_=!1}=e,{pending:w=!1}=e,{root:k=""}=e,{stream_every:j=1}=e,{mode:g="image"}=e,{mirror_webcam:p}=e,{include_audio:b}=e,{webcam_constraints:y=null}=e,{i18n:P}=e,{upload:H}=e,{value:F=null}=e;const q=Dn();qn(()=>{m=document.createElement("canvas"),_&&g==="image"&&window.setInterval(()=>{n&&!w&&f()},j*1e3)});const O=async d=>{const G=d.target.value;await Qe(b,n,y,G).then(async ue=>{A=ue,t(8,s=l.find(_e=>_e.deviceId===G)||null),t(13,X=!1)})};async function I(){try{Qe(b,n,y).then(async d=>{t(12,ae=!0),t(7,l=await mn()),A=d}).then(()=>gn(l)).then(d=>{t(7,l=d);const W=A.getTracks().map(G=>G.getSettings()?.deviceId)[0];t(8,s=W&&d.find(G=>G.deviceId===W)||l[0])}),(!navigator.mediaDevices||!navigator.mediaDevices.getUserMedia)&&q("error",P("image.no_webcam_support"))}catch(d){if(d instanceof DOMException&&d.name=="NotAllowedError")q("error",P("image.allow_webcam_access"));else throw d}}function f(){var d=m.getContext("2d");if((!_||_&&h)&&n.videoWidth&&n.videoHeight){if(m.width=n.videoWidth,m.height=n.videoHeight,d.drawImage(n,0,0,n.videoWidth,n.videoHeight),p&&(d.scale(-1,1),d.drawImage(n,-n.videoWidth,0)),_&&(!h||c==="waiting"))return;if(_){const W=m.toDataURL("image/jpeg");q("stream",W);return}m.toBlob(W=>{q(_?"stream":"capture",W)},`image/${_?"jpeg":"png"}`,.8)}}let h=!1,K=[],A,z,M;function Q(){if(h){M.stop();let d=new Blob(K,{type:z}),W=new FileReader;W.onload=async function(G){if(G.target){let ue=new File([d],"sample."+z.substring(6));const _e=await vt([ue]);let Re=(await H(_e,k))?.filter(Boolean)[0];q("capture",Re),q("stop_recording")}},W.readAsDataURL(d)}else if(typeof MediaRecorder<"u"){q("start_recording"),K=[];let d=["video/webm","video/mp4"];for(let W of d)if(MediaRecorder.isTypeSupported(W)){z=W;break}if(z===null){console.error("No supported MediaRecorder mimeType");return}M=new MediaRecorder(A,{mimeType:z}),M.addEventListener("dataavailable",function(W){K.push(W.data)}),M.start(200)}t(11,h=!h)}let ae=!1;function ce({destroy:d}={}){g==="image"&&_&&t(11,h=!h),d||(g==="image"?f():Q()),!h&&A&&(q("close_stream"),A.getTracks().forEach(W=>W.stop()),t(6,n.srcObject=null,n),t(12,ae=!1),window.setTimeout(()=>{t(0,F=null)},500),t(0,F=null))}let X=!1;function qe(d){d.preventDefault(),d.stopPropagation(),t(13,X=!1)}Sn(()=>{typeof window>"u"||(ce({destroy:!0}),A?.getTracks().forEach(d=>d.stop()))});function Ee(d){wn[d?"unshift":"push"](()=>{n=d,t(6,n)})}const We=async()=>I(),Be=()=>ce(),Me=()=>t(13,X=!0),Te=()=>t(13,X=!1);return i.$$set=d=>{"streaming"in d&&t(1,_=d.streaming),"pending"in d&&t(20,w=d.pending),"root"in d&&t(21,k=d.root),"stream_every"in d&&t(22,j=d.stream_every),"mode"in d&&t(2,g=d.mode),"mirror_webcam"in d&&t(3,p=d.mirror_webcam),"include_audio"in d&&t(23,b=d.include_audio),"webcam_constraints"in d&&t(24,y=d.webcam_constraints),"i18n"in d&&t(4,P=d.i18n),"upload"in d&&t(25,H=d.upload),"value"in d&&t(0,F=d.value)},[F,_,g,p,P,Ge,n,l,s,r,c,h,ae,X,O,I,ce,qe,o,u,w,k,j,b,y,H,Ee,We,Be,Me,Te]}class Fn extends hn{constructor(e){super(),yn(this,e,Pn,Ln,zn,{modify_stream:18,set_time_limit:19,streaming:1,pending:20,root:21,stream_every:22,mode:2,mirror_webcam:3,include_audio:23,webcam_constraints:24,i18n:4,upload:25,value:0,click_outside:5},null,[-1,-1])}get modify_stream(){return this.$$.ctx[18]}get set_time_limit(){return this.$$.ctx[19]}get streaming(){return this.$$.ctx[1]}set streaming(e){this.$$set({streaming:e}),V()}get pending(){return this.$$.ctx[20]}set pending(e){this.$$set({pending:e}),V()}get root(){return this.$$.ctx[21]}set root(e){this.$$set({root:e}),V()}get stream_every(){return this.$$.ctx[22]}set stream_every(e){this.$$set({stream_every:e}),V()}get mode(){return this.$$.ctx[2]}set mode(e){this.$$set({mode:e}),V()}get mirror_webcam(){return this.$$.ctx[3]}set mirror_webcam(e){this.$$set({mirror_webcam:e}),V()}get include_audio(){return this.$$.ctx[23]}set include_audio(e){this.$$set({include_audio:e}),V()}get webcam_constraints(){return this.$$.ctx[24]}set webcam_constraints(e){this.$$set({webcam_constraints:e}),V()}get i18n(){return this.$$.ctx[4]}set i18n(e){this.$$set({i18n:e}),V()}get upload(){return this.$$.ctx[25]}set upload(e){this.$$set({upload:e}),V()}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),V()}get click_outside(){return Ge}}const An=Fn,{SvelteComponent:Hn,add_flush_callback:he,append:Ie,attr:Ce,bind:pe,binding_callbacks:ie,bubble:fe,check_outros:be,create_component:Y,create_slot:On,destroy_component:Z,detach:se,element:He,empty:ct,flush:D,get_all_dirty_from_scope:$n,get_slot_changes:Vn,group_outros:we,init:Gn,insert:oe,listen:Oe,mount_component:x,run_all:Jn,safe_not_equal:Kn,set_style:nt,space:me,toggle_class:Se,transition_in:C,transition_out:B,update_slot_base:Qn}=window.__gradio__svelte__internal,{createEventDispatcher:Xn,tick:ze}=window.__gradio__svelte__internal;function lt(i){let e,t,n,l=i[18]&&it(i);return t=new pt({props:{Icon:bt,label:"Remove Image"}}),t.$on("click",i[34]),{c(){l&&l.c(),e=me(),Y(t.$$.fragment)},m(s,r){l&&l.m(s,r),oe(s,e,r),x(t,s,r),n=!0},p(s,r){s[18]?l?(l.p(s,r),r[0]&262144&&C(l,1)):(l=it(s),l.c(),C(l,1),l.m(e.parentNode,e)):l&&(we(),B(l,1,1,()=>{l=null}),be())},i(s){n||(C(l),C(t.$$.fragment,s),n=!0)},o(s){B(l),B(t.$$.fragment,s),n=!1},d(s){s&&se(e),l&&l.d(s),Z(t,s)}}}function it(i){let e,t;return e=new Ct({props:{fullscreen:i[19]}}),e.$on("fullscreen",i[33]),{c(){Y(e.$$.fragment)},m(n,l){x(e,n,l),t=!0},p(n,l){const s={};l[0]&524288&&(s.fullscreen=n[19]),e.$set(s)},i(n){t||(C(e.$$.fragment,n),t=!0)},o(n){B(e.$$.fragment,n),t=!1},d(n){Z(e,n)}}}function Yn(i){let e,t,n=i[3]?.url&&!i[20]&&lt(i);return{c(){n&&n.c(),e=ct()},m(l,s){n&&n.m(l,s),oe(l,e,s),t=!0},p(l,s){l[3]?.url&&!l[20]?n?(n.p(l,s),s[0]&1048584&&C(n,1)):(n=lt(l),n.c(),C(n,1),n.m(e.parentNode,e)):n&&(we(),B(n,1,1,()=>{n=null}),be())},i(l){t||(C(n),t=!0)},o(l){B(n),t=!1},d(l){l&&se(e),n&&n.d(l)}}}function rt(i){let e;const t=i[32].default,n=On(t,i,i[49],null);return{c(){n&&n.c()},m(l,s){n&&n.m(l,s),e=!0},p(l,s){n&&n.p&&(!e||s[1]&262144)&&Qn(n,t,l,l[49],e?Vn(t,l[49],s,null):$n(l[49]),null)},i(l){e||(C(n,l),e=!0)},o(l){B(n,l),e=!1},d(l){n&&n.d(l)}}}function Zn(i){let e,t,n=i[3]===null&&rt(i);return{c(){n&&n.c(),e=ct()},m(l,s){n&&n.m(l,s),oe(l,e,s),t=!0},p(l,s){l[3]===null?n?(n.p(l,s),s[0]&8&&C(n,1)):(n=rt(l),n.c(),C(n,1),n.m(e.parentNode,e)):n&&(we(),B(n,1,1,()=>{n=null}),be())},i(l){t||(C(n),t=!0)},o(l){B(n),t=!1},d(l){l&&se(e),n&&n.d(l)}}}function xn(i){let e,t,n,l,s;return t=new Bt({props:{src:i[3].url,alt:i[3].alt_text}}),{c(){e=He("div"),Y(t.$$.fragment),Ce(e,"class","image-frame svelte-1hdlew6"),Se(e,"selectable",i[11])},m(r,c){oe(r,e,c),x(t,e,null),n=!0,l||(s=Oe(e,"click",i[27]),l=!0)},p(r,c){const o={};c[0]&8&&(o.src=r[3].url),c[0]&8&&(o.alt=r[3].alt_text),t.$set(o),(!n||c[0]&2048)&&Se(e,"selectable",r[11])},i(r){n||(C(t.$$.fragment,r),n=!0)},o(r){B(t.$$.fragment,r),n=!1},d(r){r&&se(e),Z(t),l=!1,s()}}}function el(i){let e,t,n,l;function s(o){i[39](o)}function r(o){i[40](o)}let c={root:i[12],value:i[3],mirror_webcam:i[10].mirror,stream_every:i[17],streaming:i[9],mode:"image",include_audio:!1,i18n:i[13],upload:i[15],webcam_constraints:i[10].constraints};return i[4]!==void 0&&(c.modify_stream=i[4]),i[5]!==void 0&&(c.set_time_limit=i[5]),e=new An({props:c}),ie.push(()=>pe(e,"modify_stream",s)),ie.push(()=>pe(e,"set_time_limit",r)),e.$on("capture",i[41]),e.$on("stream",i[42]),e.$on("error",i[43]),e.$on("drag",i[44]),e.$on("upload",i[45]),e.$on("close_stream",i[46]),{c(){Y(e.$$.fragment)},m(o,u){x(e,o,u),l=!0},p(o,u){const m={};u[0]&4096&&(m.root=o[12]),u[0]&8&&(m.value=o[3]),u[0]&1024&&(m.mirror_webcam=o[10].mirror),u[0]&131072&&(m.stream_every=o[17]),u[0]&512&&(m.streaming=o[9]),u[0]&8192&&(m.i18n=o[13]),u[0]&32768&&(m.upload=o[15]),u[0]&1024&&(m.webcam_constraints=o[10].constraints),!t&&u[0]&16&&(t=!0,m.modify_stream=o[4],he(()=>t=!1)),!n&&u[0]&32&&(n=!0,m.set_time_limit=o[5],he(()=>n=!1)),e.$set(m)},i(o){l||(C(e.$$.fragment,o),l=!0)},o(o){B(e.$$.fragment,o),l=!1},d(o){Z(e,o)}}}function st(i){let e,t,n;function l(r){i[47](r)}let s={sources:i[8],handle_clear:i[24],handle_select:i[28]};return i[1]!==void 0&&(s.active_source=i[1]),e=new It({props:s}),ie.push(()=>pe(e,"active_source",l)),{c(){Y(e.$$.fragment)},m(r,c){x(e,r,c),n=!0},p(r,c){const o={};c[0]&256&&(o.sources=r[8]),!t&&c[0]&2&&(t=!0,o.active_source=r[1],he(()=>t=!1)),e.$set(o)},i(r){n||(C(e.$$.fragment,r),n=!0)},o(r){B(e.$$.fragment,r),n=!1},d(r){Z(e,r)}}}function tl(i){let e,t,n,l,s,r,c,o,u,m,_,w,k,j=i[8].length>1||i[8].includes("clipboard"),g,p,b;e=new ht({props:{show_label:i[7],Icon:wt,label:i[6]||"Image"}}),l=new zt({props:{$$slots:{default:[Yn]},$$scope:{ctx:i}}});function y(f){i[36](f)}function P(f){i[37](f)}let H={hidden:i[3]!==null||i[1]==="webcam",filetype:i[1]==="clipboard"?"clipboard":"image/*",root:i[12],max_file_size:i[14],disable_click:!i[8].includes("upload")||i[3]!==null,upload:i[15],stream_handler:i[16],aria_label:i[13]("image.drop_to_upload"),$$slots:{default:[Zn]},$$scope:{ctx:i}};i[0]!==void 0&&(H.uploading=i[0]),i[2]!==void 0&&(H.dragging=i[2]),c=new Wt({props:H}),i[35](c),ie.push(()=>pe(c,"uploading",y)),ie.push(()=>pe(c,"dragging",P)),c.$on("load",i[23]),c.$on("error",i[38]);const F=[el,xn],q=[];function O(f,h){return f[1]==="webcam"&&(f[9]||!f[9]&&!f[3])?0:f[3]!==null&&!f[9]?1:-1}~(_=O(i))&&(w=q[_]=F[_](i));let I=j&&st(i);return{c(){Y(e.$$.fragment),t=me(),n=He("div"),Y(l.$$.fragment),s=me(),r=He("div"),Y(c.$$.fragment),m=me(),w&&w.c(),k=me(),I&&I.c(),Ce(r,"class","upload-container svelte-1hdlew6"),Se(r,"reduced-height",i[8].length>1),nt(r,"width",i[3]?"auto":"100%"),Ce(n,"data-testid","image"),Ce(n,"class","image-container svelte-1hdlew6")},m(f,h){x(e,f,h),oe(f,t,h),oe(f,n,h),x(l,n,null),Ie(n,s),Ie(n,r),x(c,r,null),Ie(r,m),~_&&q[_].m(r,null),Ie(n,k),I&&I.m(n,null),i[48](n),g=!0,p||(b=[Oe(r,"dragover",i[29]),Oe(r,"drop",i[30])],p=!0)},p(f,h){const K={};h[0]&128&&(K.show_label=f[7]),h[0]&64&&(K.label=f[6]||"Image"),e.$set(K);const A={};h[0]&1835016|h[1]&262144&&(A.$$scope={dirty:h,ctx:f}),l.$set(A);const z={};h[0]&10&&(z.hidden=f[3]!==null||f[1]==="webcam"),h[0]&2&&(z.filetype=f[1]==="clipboard"?"clipboard":"image/*"),h[0]&4096&&(z.root=f[12]),h[0]&16384&&(z.max_file_size=f[14]),h[0]&264&&(z.disable_click=!f[8].includes("upload")||f[3]!==null),h[0]&32768&&(z.upload=f[15]),h[0]&65536&&(z.stream_handler=f[16]),h[0]&8192&&(z.aria_label=f[13]("image.drop_to_upload")),h[0]&8|h[1]&262144&&(z.$$scope={dirty:h,ctx:f}),!o&&h[0]&1&&(o=!0,z.uploading=f[0],he(()=>o=!1)),!u&&h[0]&4&&(u=!0,z.dragging=f[2],he(()=>u=!1)),c.$set(z);let M=_;_=O(f),_===M?~_&&q[_].p(f,h):(w&&(we(),B(q[M],1,1,()=>{q[M]=null}),be()),~_?(w=q[_],w?w.p(f,h):(w=q[_]=F[_](f),w.c()),C(w,1),w.m(r,null)):w=null),(!g||h[0]&256)&&Se(r,"reduced-height",f[8].length>1),h[0]&8&&nt(r,"width",f[3]?"auto":"100%"),h[0]&256&&(j=f[8].length>1||f[8].includes("clipboard")),j?I?(I.p(f,h),h[0]&256&&C(I,1)):(I=st(f),I.c(),C(I,1),I.m(n,null)):I&&(we(),B(I,1,1,()=>{I=null}),be())},i(f){g||(C(e.$$.fragment,f),C(l.$$.fragment,f),C(c.$$.fragment,f),C(w),C(I),g=!0)},o(f){B(e.$$.fragment,f),B(l.$$.fragment,f),B(c.$$.fragment,f),B(w),B(I),g=!1},d(f){f&&(se(t),se(n)),Z(e,f),Z(l),i[35](null),Z(c),~_&&q[_].d(),I&&I.d(),i[48](null),p=!1,Jn(b)}}}function nl(i,e,t){let n,{$$slots:l={},$$scope:s}=e,{value:r=null}=e,{label:c=void 0}=e,{show_label:o}=e,{sources:u=["upload","clipboard","webcam"]}=e,{streaming:m=!1}=e,{pending:_=!1}=e,{webcam_options:w}=e,{selectable:k=!1}=e,{root:j}=e,{i18n:g}=e,{max_file_size:p=null}=e,{upload:b}=e,{stream_handler:y}=e,{stream_every:P}=e,{modify_stream:H}=e,{set_time_limit:F}=e,{show_fullscreen_button:q=!0}=e,O,{uploading:I=!1}=e,{active_source:f=null}=e,{fullscreen:h=!1}=e;async function K({detail:a}){if(!m){if(a.path?.toLowerCase().endsWith(".svg")&&a.url){const je=await(await fetch(a.url)).text();t(3,r={...a,url:`data:image/svg+xml,${encodeURIComponent(je)}`})}else t(3,r=a);await ze(),M("upload")}}function A(){t(3,r=null),M("clear"),M("change",null)}async function z(a,le){if(le==="stream"){M("stream",{value:{url:a},is_value_data:!0});return}t(31,_=!0);const je=await O.load_files([new File([a],`image/${m?"jpeg":"png"}`)]);(le==="change"||le==="upload")&&(t(3,r=je?.[0]||null),await ze(),M("change")),t(31,_=!1)}const M=Xn();let{dragging:Q=!1}=e;function ae(a){let le=Dt(a);le&&M("select",{index:le,value:null})}async function ce(a){switch(a){case"clipboard":O.paste_clipboard();break}}let X;function qe(a){a.preventDefault(),a.stopPropagation(),a.dataTransfer&&(a.dataTransfer.dropEffect="copy"),t(2,Q=!0)}async function Ee(a){a.preventDefault(),a.stopPropagation(),t(2,Q=!1),r&&(A(),await ze()),t(1,f="upload"),await ze(),O.load_files_from_drop(a)}function We(a){fe.call(this,i,a)}const Be=a=>{t(3,r=null),M("clear"),a.stopPropagation()};function Me(a){ie[a?"unshift":"push"](()=>{O=a,t(21,O)})}function Te(a){I=a,t(0,I)}function d(a){Q=a,t(2,Q)}function W(a){fe.call(this,i,a)}function G(a){H=a,t(4,H)}function ue(a){F=a,t(5,F)}const _e=a=>z(a.detail,"change"),Re=a=>z(a.detail,"stream");function ut(a){fe.call(this,i,a)}function _t(a){fe.call(this,i,a)}const ft=a=>z(a.detail,"upload");function mt(a){fe.call(this,i,a)}function dt(a){f=a,t(1,f),t(8,u)}function gt(a){ie[a?"unshift":"push"](()=>{X=a,t(22,X)})}return i.$$set=a=>{"value"in a&&t(3,r=a.value),"label"in a&&t(6,c=a.label),"show_label"in a&&t(7,o=a.show_label),"sources"in a&&t(8,u=a.sources),"streaming"in a&&t(9,m=a.streaming),"pending"in a&&t(31,_=a.pending),"webcam_options"in a&&t(10,w=a.webcam_options),"selectable"in a&&t(11,k=a.selectable),"root"in a&&t(12,j=a.root),"i18n"in a&&t(13,g=a.i18n),"max_file_size"in a&&t(14,p=a.max_file_size),"upload"in a&&t(15,b=a.upload),"stream_handler"in a&&t(16,y=a.stream_handler),"stream_every"in a&&t(17,P=a.stream_every),"modify_stream"in a&&t(4,H=a.modify_stream),"set_time_limit"in a&&t(5,F=a.set_time_limit),"show_fullscreen_button"in a&&t(18,q=a.show_fullscreen_button),"uploading"in a&&t(0,I=a.uploading),"active_source"in a&&t(1,f=a.active_source),"fullscreen"in a&&t(19,h=a.fullscreen),"dragging"in a&&t(2,Q=a.dragging),"$$scope"in a&&t(49,s=a.$$scope)},i.$$.update=()=>{i.$$.dirty[0]&258&&!f&&u&&t(1,f=u[0]),i.$$.dirty[0]&514&&t(20,n=m&&f==="webcam"),i.$$.dirty[0]&1048577&&I&&!n&&t(3,r=null),i.$$.dirty[0]&4&&M("drag",Q)},[I,f,Q,r,H,F,c,o,u,m,w,k,j,g,p,b,y,P,q,h,n,O,X,K,A,z,M,ae,ce,qe,Ee,_,l,We,Be,Me,Te,d,W,G,ue,_e,Re,ut,_t,ft,mt,dt,gt,s]}class ll extends Hn{constructor(e){super(),Gn(this,e,nl,tl,Kn,{value:3,label:6,show_label:7,sources:8,streaming:9,pending:31,webcam_options:10,selectable:11,root:12,i18n:13,max_file_size:14,upload:15,stream_handler:16,stream_every:17,modify_stream:4,set_time_limit:5,show_fullscreen_button:18,uploading:0,active_source:1,fullscreen:19,dragging:2},null,[-1,-1])}get value(){return this.$$.ctx[3]}set value(e){this.$$set({value:e}),D()}get label(){return this.$$.ctx[6]}set label(e){this.$$set({label:e}),D()}get show_label(){return this.$$.ctx[7]}set show_label(e){this.$$set({show_label:e}),D()}get sources(){return this.$$.ctx[8]}set sources(e){this.$$set({sources:e}),D()}get streaming(){return this.$$.ctx[9]}set streaming(e){this.$$set({streaming:e}),D()}get pending(){return this.$$.ctx[31]}set pending(e){this.$$set({pending:e}),D()}get webcam_options(){return this.$$.ctx[10]}set webcam_options(e){this.$$set({webcam_options:e}),D()}get selectable(){return this.$$.ctx[11]}set selectable(e){this.$$set({selectable:e}),D()}get root(){return this.$$.ctx[12]}set root(e){this.$$set({root:e}),D()}get i18n(){return this.$$.ctx[13]}set i18n(e){this.$$set({i18n:e}),D()}get max_file_size(){return this.$$.ctx[14]}set max_file_size(e){this.$$set({max_file_size:e}),D()}get upload(){return this.$$.ctx[15]}set upload(e){this.$$set({upload:e}),D()}get stream_handler(){return this.$$.ctx[16]}set stream_handler(e){this.$$set({stream_handler:e}),D()}get stream_every(){return this.$$.ctx[17]}set stream_every(e){this.$$set({stream_every:e}),D()}get modify_stream(){return this.$$.ctx[4]}set modify_stream(e){this.$$set({modify_stream:e}),D()}get set_time_limit(){return this.$$.ctx[5]}set set_time_limit(e){this.$$set({set_time_limit:e}),D()}get show_fullscreen_button(){return this.$$.ctx[18]}set show_fullscreen_button(e){this.$$set({show_fullscreen_button:e}),D()}get uploading(){return this.$$.ctx[0]}set uploading(e){this.$$set({uploading:e}),D()}get active_source(){return this.$$.ctx[1]}set active_source(e){this.$$set({active_source:e}),D()}get fullscreen(){return this.$$.ctx[19]}set fullscreen(e){this.$$set({fullscreen:e}),D()}get dragging(){return this.$$.ctx[2]}set dragging(e){this.$$set({dragging:e}),D()}}const Il=ll;export{Gt as C,Il as I,An as W};
//# sourceMappingURL=ImageUploader-3ubqEwhl.js.map
