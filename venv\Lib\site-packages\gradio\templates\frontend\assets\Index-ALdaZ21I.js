import{B as io}from"./Block-CJdXVpa7.js";import"./MarkdownCode.svelte_svelte_type_style_lang-Bl8p72Ow.js";/* empty css                                                        */import{B as Le,w as so}from"./index-DJ2rNx9E.js";import{d as xl}from"./index-tFQomdd2.js";import{d as ro}from"./dsv-DB8NKgIY.js";import{d as nl}from"./index-CnqicUFC.js";import{a as oo}from"./Upload-8igJ-HYX.js";import"./DownloadLink.svelte_svelte_type_style_lang-C_5UIfol.js";import{M as ao}from"./MarkdownCode-Cdb8e5t4.js";import"./StreamingBar.svelte_svelte_type_style_lang-DlRzv56K.js";import{C as _o}from"./Checkbox-CjOIpf6b.js";import{C as qn}from"./Check-CEkiXcyC.js";import{D as uo}from"./DropdownArrow-DYWFcSFn.js";import{C as fo}from"./Copy-CxQ9EyK2.js";import{F as co}from"./FullscreenButton-BG4mOKmH.js";import{S as ho}from"./index-DYtg3pip.js";import mo from"./Index-B6-B2pPO.js";import{default as ch}from"./Example-CqPGqNav.js";import"./prism-python-CeMtt1IT.js";import"./svelte/svelte.js";/* empty css                                             */import"./IconButton-C_HS7fTi.js";import"./Clear-By3xiIwg.js";import"./ImagePreview-CpxjYXeK.js";import"./utils-BsGrhMNe.js";import"./BlockLabel-3KxTaaiM.js";import"./Empty-ZqppqzTN.js";import"./ShareButton-CuOwy-FH.js";import"./Community-Dw1micSV.js";import"./Download-DVtk-Jv3.js";import"./Image-Bsh8Umrh.js";import"./IconButtonWrapper--EIOWuEM.js";import"./utils-Gtzs_Zla.js";import"./Image-CnqB5dbD.js";import"./file-url-DoxvUUVV.js";import"./DownloadLink-QIttOhoR.js";/* empty css                                                   */import"./ImageUploader-3ubqEwhl.js";import"./SelectSource-DG1n31De.js";import"./Square-oAGqOwsh.js";import"./index-BALGG9zl.js";import"./StreamingBar-BpqwJkLy.js";import"./UploadText-Wd7ORU21.js";import"./Example-CC8yxxGn.js";/* empty css                                              */function dl(l,e,t){if(!e.length)return"none";const n=e.find(i=>{const r=i.col;return r<0||r>=t.length?!1:t[r]===l});return n?n.direction:"none"}function go(l,e){if(!l||!l.length||!l[0])return[];if(e.length>0){const t=[...Array(l.length)].map((n,i)=>i);return t.sort((n,i)=>{const r=l[n],s=l[i];for(const{col:o,direction:f}of e){if(!r||!s||o<0||o>=r.length||o>=s.length||!r[o]||!s[o])continue;const u=r[o].value,a=s[o].value,_=u<a?-1:u>a?1:0;if(_!==0)return f==="asc"?_:-_}return 0}),t}return[...Array(l.length)].map((t,n)=>n)}function bo(l,e,t,n,i,r){let s=null;i&&i[0]in l&&i[1]in l[i[0]]&&(s=l[i[0]][i[1]].id),Fs(l,e,t,n);let o=i;if(s){const[f,u]=r(s,l);o=[f,u]}return{data:l,selected:o}}function wo(l,e){if(!l||!l.length||!l[0])return[];let t=[...Array(l.length)].map((n,i)=>i);return e.length>0?(e.forEach(n=>{if(n.datatype==="string")switch(n.filter){case"Contains":t=t.filter(i=>l[i][n.col]?.value.toString().includes(n.value));break;case"Does not contain":t=t.filter(i=>!l[i][n.col]?.value.toString().includes(n.value));break;case"Starts with":t=t.filter(i=>l[i][n.col]?.value.toString().startsWith(n.value));break;case"Ends with":t=t.filter(i=>l[i][n.col]?.value.toString().endsWith(n.value));break;case"Is":t=t.filter(i=>l[i][n.col]?.value.toString()===n.value);break;case"Is not":t=t.filter(i=>l[i][n.col]?.value.toString()!==n.value);break;case"Is empty":t=t.filter(i=>l[i][n.col]?.value.toString()==="");break;case"Is not empty":t=t.filter(i=>l[i][n.col]?.value.toString()!=="");break}else if(n.datatype==="number")switch(n.filter){case"=":t=t.filter(i=>!isNaN(Number(l[i][n.col]?.value))&&!isNaN(Number(n.value))?Number(l[i][n.col]?.value)===Number(n.value):!1);break;case"≠":t=t.filter(i=>!isNaN(Number(l[i][n.col]?.value))&&!isNaN(Number(n.value))?Number(l[i][n.col]?.value)!==Number(n.value):!1);break;case">":t=t.filter(i=>!isNaN(Number(l[i][n.col]?.value))&&!isNaN(Number(n.value))?Number(l[i][n.col]?.value)>Number(n.value):!1);break;case"<":t=t.filter(i=>!isNaN(Number(l[i][n.col]?.value))&&!isNaN(Number(n.value))?Number(l[i][n.col]?.value)<Number(n.value):!1);break;case"≥":t=t.filter(i=>!isNaN(Number(l[i][n.col]?.value))&&!isNaN(Number(n.value))?Number(l[i][n.col]?.value)>=Number(n.value):!1);break;case"≤":t=t.filter(i=>!isNaN(Number(l[i][n.col]?.value))&&!isNaN(Number(n.value))?Number(l[i][n.col]?.value)<=Number(n.value):!1);break;case"Is empty":t=t.filter(i=>l[i][n.col]?.value.toString()==="");break;case"Is not empty":t=t.filter(i=>isNaN(Number(l[i][n.col]?.value))?!1:l[i][n.col]?.value.toString()!=="");break}}),t):[...Array(l.length)].map((n,i)=>i)}function ko(l,e,t,n,i,r,s,o,f){let u=null;i&&i[0]in l&&i[1]in l[i[0]]&&(u=l[i[0]][i[1]].id),vo(l,e,t,n,s,o,f);let a=i;if(u){const[_,c]=r(u,l);a=[_,c]}return{data:l,selected:a}}function po(l){if(!l||!l.length)return[];let e=l[0].slice();for(let t=0;t<l.length;t++)for(let n=0;n<l[t].length;n++)`${e[n].value}`.length<`${l[t][n].value}`.length&&(e[n]=l[t][n]);return e}function Fs(l,e,t,n){if(!n.length||!l||!l.length)return;const i=go(l,n),r=i.map(s=>l[s]);if(l.splice(0,l.length,...r),e){const s=i.map(o=>e[o]);e.splice(0,e.length,...s)}if(t){const s=i.map(o=>t[o]);t.splice(0,t.length,...s)}}function vo(l,e,t,n,i,r,s){const o=i??l,f=r??e,u=s??t;if(!n.length){l.splice(0,l.length,...o.map(c=>[...c])),e&&f&&e.splice(0,e.length,...f.map(c=>[...c])),t&&u&&t.splice(0,t.length,...u.map(c=>[...c]));return}if(!l||!l.length)return;const a=wo(o,n),_=a.map(c=>o[c]);if(l.splice(0,l.length,..._),e&&f){const c=a.map(w=>f[w]);e.splice(0,e.length,...c)}if(t&&u){const c=a.map(w=>u[w]);t.splice(0,t.length,...c)}}async function Ks(l,e){if(!l||!l.length)return;const n=(e||l.flatMap((o,f)=>o.map((u,a)=>[f,a]))).reduce((o,[f,u])=>{o[f]=o[f]||{};const a=String(l[f][u].value);return o[f][u]=a.includes(",")||a.includes('"')||a.includes(`
`)?`"${a.replace(/"/g,'""')}"`:a,o},{}),i=Object.keys(n).sort((o,f)=>+o-+f);if(!i.length)return;const r=Object.keys(n[i[0]]).sort((o,f)=>+o-+f),s=i.map(o=>r.map(f=>n[o][f]||"").join(",")).join(`
`);try{await navigator.clipboard.writeText(s)}catch(o){throw new Error("Failed to copy to clipboard: "+o.message)}}function yo(l,e){return e.filter(t);function t(n){var i=-1;return l.split(`
`).every(r);function r(s){if(!s)return!0;var o=s.split(n).length;return i<0&&(i=o),i===o&&o>1}}}function Co(l){const e=atob(l.split(",")[1]),t=l.split(",")[0].split(":")[1].split(";")[0],n=new ArrayBuffer(e.length),i=new Uint8Array(n);for(let r=0;r<e.length;r++)i[r]=e.charCodeAt(r);return new Blob([n],{type:t})}function So(l,e,t){const n=Co(l),i=new FileReader;i.addEventListener("loadend",r=>{if(!r?.target?.result||typeof r.target.result!="string")return;const[s]=yo(r.target.result,[",","	"]),[o,...f]=ro(s).parseRows(r.target.result);e(o),t(f)}),i.readAsText(n)}function No(l,e){const[t,n]=l;return e.some(([i,r])=>i===t&&r===n)}function qo(l,e){const[t,n]=l;if(!e.some(([f,u])=>f===t&&u===n))return"";const i=e.some(([f,u])=>f===t-1&&u===n),r=e.some(([f,u])=>f===t+1&&u===n),s=e.some(([f,u])=>f===t&&u===n-1),o=e.some(([f,u])=>f===t&&u===n+1);return`cell-selected${i?" no-top":""}${r?" no-bottom":""}${s?" no-left":""}${o?" no-right":""}`}function Mn(l,e){const[t,n]=l,[i,r]=e,s=Math.min(t,i),o=Math.max(t,i),f=Math.min(n,r),u=Math.max(n,r),a=[];a.push(l);for(let _=s;_<=o;_++)for(let c=f;c<=u;c++)_===t&&c===n||a.push([_,c]);return a}function Mo(l,e,t){if(t.shiftKey&&e.length>0)return Mn(e[e.length-1],l);if(t.metaKey||t.ctrlKey){const n=([r,s])=>r===l[0]&&s===l[1],i=e.findIndex(n);return i===-1?[...e,l]:e.filter((r,s)=>s!==i)}return[l]}function Ao(l,e){const t=l.map(n=>[...n]);return e.forEach(([n,i])=>{t[n]&&t[n][i]&&(t[n][i]={...t[n][i],value:""})}),t}function Eo(l,e,t){const[n,i]=l;return t&&e.length===1&&e[0][0]===n&&e[0][1]===i}function Lo(l,e,t){const[n,i]=l,r=t?-1:1;if(e[n]?.[i+r])return[n,i+r];const s=n+(r>0?1:0),o=n+(r<0?-1:0);return r>0&&e[s]?.[0]?[s,0]:r<0&&e[o]?.[e[0].length-1]?[o,e[0].length-1]:!1}function jo(l,e,t){const n=l.key,i={ArrowRight:[0,1],ArrowLeft:[0,-1],ArrowDown:[1,0],ArrowUp:[-1,0]}[n];let r,s;if(l.metaKey||l.ctrlKey)if(n==="ArrowRight")r=e[0],s=t[0].length-1;else if(n==="ArrowLeft")r=e[0],s=0;else if(n==="ArrowDown")r=t.length-1,s=e[1];else if(n==="ArrowUp")r=0,s=e[1];else return!1;else r=e[0]+i[0],s=e[1]+i[1];return r<0&&s<=0?!1:t[r]?.[s]?[r,s]:!1}function ti(l,e){return e.reduce((t,n,i)=>{const r=n.reduce((s,o,f)=>l===o.id?f:s,-1);return r===-1?t:[i,r]},[-1,-1])}function Bo(l,e){const[t]=l.composedPath();return!e.contains(t)}function zo(l,e,t,n,i){const[r,s]=l;if(!e[r]?.[s])return{col_pos:"0px",row_pos:void 0};const o=e[r][s].id,f=t[o]?.cell;if(!f)return{col_pos:"0px",row_pos:void 0};const u=f.getBoundingClientRect(),a=i.getBoundingClientRect(),_=`${u.left-a.left+u.width/2}px`,c=`${u.top-a.top+u.height/2}px`;return{col_pos:_,row_pos:c}}const{getContext:ah,setContext:li}=window.__gradio__svelte__internal,{tick:ni}=window.__gradio__svelte__internal,Do=Symbol("dataframe");function To(l,e){const t=s=>l.update(o=>({...o,...s(o)})),n=(s,o,f)=>{const u=s[0]?.length?Array(s[0].length).fill(null).map(()=>({value:"",id:o()})):[{value:"",id:o()}],a=[...s];return f!==void 0?a.splice(f,0,u):a.push(u),a},i=(s,o,f,u)=>{const a=e.headers?[...o.map(c=>e.headers[o.indexOf(c)].value)]:[...o,`Header ${o.length+1}`],_=s.map(c=>[...c,{value:"",id:f()}]);return u!==void 0&&(a.splice(u,0,a.pop()),_.forEach(c=>c.splice(u,0,c.pop()))),{data:_,headers:a}},r=(s,o)=>{s&&o&&o.splice(0,o.length,...JSON.parse(JSON.stringify(s)))};return{handle_search:s=>t(o=>({current_search_query:s})),handle_sort:(s,o)=>t(f=>{const u=f.sort_state.sort_columns.filter(_=>_.col!==s);f.sort_state.sort_columns.some(_=>_.col===s&&_.direction===o)||u.push({col:s,direction:o});const a=f.sort_state.initial_data||(e.data&&u.length>0?{data:JSON.parse(JSON.stringify(e.data)),display_value:e.display_value?JSON.parse(JSON.stringify(e.display_value)):null,styling:e.styling?JSON.parse(JSON.stringify(e.styling)):null}:null);return{sort_state:{...f.sort_state,sort_columns:u.slice(-3),initial_data:a}}}),handle_filter:(s,o,f,u)=>t(a=>{const _=a.filter_state.filter_columns.some(w=>w.col===s)?a.filter_state.filter_columns.filter(w=>w.col!==s):[...a.filter_state.filter_columns,{col:s,datatype:o,filter:f,value:u}],c=a.filter_state.initial_data||(e.data&&_.length>0?{data:JSON.parse(JSON.stringify(e.data)),display_value:e.display_value?JSON.parse(JSON.stringify(e.display_value)):null,styling:e.styling?JSON.parse(JSON.stringify(e.styling)):null}:null);return{filter_state:{...a.filter_state,filter_columns:_,initial_data:c}}}),get_sort_status:(s,o)=>{const u=Le(l).sort_state.sort_columns.find(a=>o[a.col]===s);return u?u.direction:"none"},sort_data:(s,o,f)=>{const{sort_state:{sort_columns:u}}=Le(l);u.length&&Fs(s,o,f,u)},update_row_order:s=>t(o=>({sort_state:{...o.sort_state,row_order:o.sort_state.sort_columns.length&&s[0]?[...Array(s.length)].map((f,u)=>u).sort((f,u)=>{for(const{col:a,direction:_}of o.sort_state.sort_columns){const c=(s[f]?.[a]?.value??"")<(s[u]?.[a]?.value??"")?-1:1;if(c)return _==="asc"?c:-c}return 0}):[...Array(s.length)].map((f,u)=>u)}})),filter_data:s=>{const o=Le(l).current_search_query?.toLowerCase();return o?s.filter(f=>f.some(u=>String(u?.value).toLowerCase().includes(o))):s},add_row:n,add_col:i,add_row_at:(s,o,f,u)=>n(s,u,f==="above"?o:o+1),add_col_at:(s,o,f,u,a)=>i(s,o,a,u==="left"?f:f+1),delete_row:(s,o)=>s.length>1?s.filter((f,u)=>u!==o):s,delete_col:(s,o,f)=>o.length>1?{data:s.map(u=>u.filter((a,_)=>_!==f)),headers:o.filter((u,a)=>a!==f)}:{data:s,headers:o},delete_row_at:(s,o)=>s.length>1?[...s.slice(0,o),...s.slice(o+1)]:s,delete_col_at:(s,o,f)=>o.length>1?{data:s.map(u=>[...u.slice(0,f),...u.slice(f+1)]),headers:[...o.slice(0,f),...o.slice(f+1)]}:{data:s,headers:o},trigger_change:async(s,o,f,u,a,_)=>{if(Le(l).current_search_query)return;const w=o.map(v=>v.value),L=s.map(v=>v.map(q=>String(q.value)));(!xl(L,f)||!xl(w,u))&&(xl(w,u)||t(v=>({sort_state:{sort_columns:[],row_order:[],initial_data:null},filter_state:{filter_columns:[],initial_data:null}})),_("change",{data:s.map(v=>v.map(q=>q.value)),headers:w,metadata:null}),a||_("input"))},reset_sort_state:()=>t(s=>{if(s.sort_state.initial_data&&e.data){const o=s.sort_state.initial_data;r(o.data,e.data),r(o.display_value,e.display_value),r(o.styling,e.styling)}return{sort_state:{sort_columns:[],row_order:[],initial_data:null}}}),reset_filter_state:()=>t(s=>{if(s.filter_state.initial_data&&e.data){const o=s.filter_state.initial_data;r(o.data,e.data),r(o.display_value,e.display_value),r(o.styling,e.styling)}return{filter_state:{filter_columns:[],initial_data:null}}}),set_active_cell_menu:s=>t(o=>({ui_state:{...o.ui_state,active_cell_menu:s}})),set_active_header_menu:s=>t(o=>({ui_state:{...o.ui_state,active_header_menu:s}})),set_selected_cells:s=>t(o=>({ui_state:{...o.ui_state,selected_cells:s}})),set_selected:s=>t(o=>({ui_state:{...o.ui_state,selected:s}})),set_editing:s=>t(o=>({ui_state:{...o.ui_state,editing:s}})),clear_ui_state:()=>t(s=>({ui_state:{active_cell_menu:null,active_header_menu:null,selected_cells:[],selected:!1,editing:!1,header_edit:!1,selected_header:!1,active_button:null,copy_flash:!1}})),set_header_edit:s=>t(o=>({ui_state:{...o.ui_state,selected_cells:[],selected_header:s,header_edit:s}})),set_selected_header:s=>t(o=>({ui_state:{...o.ui_state,selected_header:s,selected:!1,selected_cells:[]}})),handle_header_click:(s,o)=>t(f=>({ui_state:{...f.ui_state,active_cell_menu:null,active_header_menu:null,selected:!1,selected_cells:[],selected_header:s,header_edit:o?s:!1}})),end_header_edit:s=>{["Escape","Enter","Tab"].includes(s)&&t(o=>({ui_state:{...o.ui_state,selected:!1,header_edit:!1}}))},get_selected_cells:()=>Le(l).ui_state.selected_cells,get_active_cell_menu:()=>Le(l).ui_state.active_cell_menu,get_active_button:()=>Le(l).ui_state.active_button,set_active_button:s=>t(o=>({ui_state:{...o.ui_state,active_button:s}})),set_copy_flash:s=>t(o=>({ui_state:{...o.ui_state,copy_flash:s}})),handle_cell_click:(s,o,f)=>{s.preventDefault(),s.stopPropagation();const u=Le(l);if(u.config.show_row_numbers&&f===-1)return;let a=o;if(u.current_search_query&&e.data){const c=[];e.data.forEach((w,L)=>{w.some(v=>String(v?.value).toLowerCase().includes(u.current_search_query?.toLowerCase()||""))&&c.push(L)}),a=c[o]??o}const _=Mo([a,f],u.ui_state.selected_cells,s);t(c=>({ui_state:{...c.ui_state,active_cell_menu:null,active_header_menu:null,selected_header:!1,header_edit:!1,selected_cells:_,selected:_[0]}})),u.config.editable&&_.length===1?(t(c=>({ui_state:{...c.ui_state,editing:[a,f]}})),ni().then(()=>e.els[e.data[a][f].id]?.input?.focus())):ni().then(()=>{e.parent_element&&e.parent_element.focus()}),e.dispatch?.("select",{index:[a,f],col_value:e.get_column(f),row_value:e.get_row(a),value:e.get_data_at(a,f)})},toggle_cell_menu:(s,o,f)=>{s.stopPropagation();const u=Le(l).ui_state.active_cell_menu;if(u?.row===o&&u.col===f)t(a=>({ui_state:{...a.ui_state,active_cell_menu:null}}));else{const a=s.target.closest("td");if(a){const _=a.getBoundingClientRect();t(c=>({ui_state:{...c.ui_state,active_cell_menu:{row:o,col:f,x:_.right,y:_.bottom}}}))}}},toggle_cell_button:(s,o)=>{const f=Le(l).ui_state.active_button,u=f?.type==="cell"&&f.row===s&&f.col===o?null:{type:"cell",row:s,col:o};t(a=>({ui_state:{...a.ui_state,active_button:u}}))},handle_select_column:s=>{if(!e.data)return;const o=e.data.map((f,u)=>[u,s]);t(f=>({ui_state:{...f.ui_state,selected_cells:o,selected:o[0],editing:!1}})),setTimeout(()=>e.parent_element?.focus(),0)},handle_select_row:s=>{if(!e.data||!e.data[0])return;const o=e.data[0].map((f,u)=>[s,u]);t(f=>({ui_state:{...f.ui_state,selected_cells:o,selected:o[0],editing:!1}})),setTimeout(()=>e.parent_element?.focus(),0)},get_next_cell_coordinates:Lo,get_range_selection:Mn,move_cursor:jo}}function Ho(l){const e=so({config:l,current_search_query:null,sort_state:{sort_columns:[],row_order:[],initial_data:null},filter_state:{filter_columns:[],initial_data:null},ui_state:{active_cell_menu:null,active_header_menu:null,selected_cells:[],selected:!1,editing:!1,header_edit:!1,selected_header:!1,active_button:null,copy_flash:!1}}),t={state:e,actions:null};t.actions=To(e,t);const n=Symbol(`dataframe_${Math.random().toString(36).substring(2)}`);return li(n,t),li(Do,{instance_id:n,context:t}),t}const{SvelteComponent:Oo,append:en,attr:et,detach:Io,element:ii,flush:tn,init:Po,insert:Ro,listen:Jo,noop:si,null_to_empty:ri,safe_not_equal:Fo,stop_propagation:Ko,svg_element:oi}=window.__gradio__svelte__internal;function Wo(l){let e,t,n,i,r,s,o,f,u;return{c(){e=ii("button"),t=ii("span"),n=oi("svg"),i=oi("path"),et(i,"d","m16.707 13.293-4-4a1 1 0 0 0-1.414 0l-4 4A1 1 0 0 0 8 15h8a1 1 0 0 0 .707-1.707z"),et(i,"data-name",l[3]),et(n,"xmlns","http://www.w3.org/2000/svg"),et(n,"viewBox","0 0 24 24"),et(n,"class","svelte-1mp8yw1"),et(t,"class",r=ri(l[3])+" svelte-1mp8yw1"),et(e,"class",s="selection-button selection-button-"+l[0]+" "+(l[2]?`move-${l[3]}`:"")+" svelte-1mp8yw1"),et(e,"aria-label",o=`Select ${l[0]}`)},m(a,_){Ro(a,e,_),en(e,t),en(t,n),en(n,i),f||(u=Jo(e,"click",Ko(l[5])),f=!0)},p(a,[_]){_&8&&et(i,"data-name",a[3]),_&8&&r!==(r=ri(a[3])+" svelte-1mp8yw1")&&et(t,"class",r),_&13&&s!==(s="selection-button selection-button-"+a[0]+" "+(a[2]?`move-${a[3]}`:"")+" svelte-1mp8yw1")&&et(e,"class",s),_&1&&o!==(o=`Select ${a[0]}`)&&et(e,"aria-label",o)},i:si,o:si,d(a){a&&Io(e),f=!1,u()}}}function Uo(l,e,t){let n,i,{position:r}=e,{coords:s}=e,{on_click:o=null}=e;const f=()=>o&&o();return l.$$set=u=>{"position"in u&&t(0,r=u.position),"coords"in u&&t(4,s=u.coords),"on_click"in u&&t(1,o=u.on_click)},l.$$.update=()=>{l.$$.dirty&17&&t(2,n=r==="column"?s[0]===0:s[1]===0),l.$$.dirty&5&&t(3,i=r==="column"?n?"down":"up":n?"right":"left")},[r,o,n,i,s,f]}class ai extends Oo{constructor(e){super(),Po(this,e,Uo,Wo,Fo,{position:0,coords:4,on_click:1})}get position(){return this.$$.ctx[0]}set position(e){this.$$set({position:e}),tn()}get coords(){return this.$$.ctx[4]}set coords(e){this.$$set({coords:e}),tn()}get on_click(){return this.$$.ctx[1]}set on_click(e){this.$$set({on_click:e}),tn()}}const{SvelteComponent:Vo,add_flush_callback:Yo,attr:ln,bind:Xo,binding_callbacks:Go,create_component:Qo,destroy_component:Zo,detach:$o,element:xo,flush:nn,init:ea,insert:ta,listen:_i,mount_component:la,run_all:na,safe_not_equal:ia,transition_in:sa,transition_out:ra}=window.__gradio__svelte__internal;function oa(l){let e,t,n,i,r,s;function o(u){l[5](u)}let f={label:"",interactive:l[0]};return l[1]!==void 0&&(f.value=l[1]),t=new _o({props:f}),Go.push(()=>Xo(t,"value",o)),t.$on("change",l[2]),{c(){e=xo("div"),Qo(t.$$.fragment),ln(e,"class","bool-cell checkbox svelte-1s702wr"),ln(e,"role","button"),ln(e,"tabindex","-1")},m(u,a){ta(u,e,a),la(t,e,null),i=!0,r||(s=[_i(e,"click",aa),_i(e,"keydown",_a)],r=!0)},p(u,[a]){const _={};a&1&&(_.interactive=u[0]),!n&&a&2&&(n=!0,_.value=u[1],Yo(()=>n=!1)),t.$set(_)},i(u){i||(sa(t.$$.fragment,u),i=!0)},o(u){ra(t.$$.fragment,u),i=!1},d(u){u&&$o(e),Zo(t),r=!1,na(s)}}}function aa(l){l.stopPropagation()}function _a(l){(l.key==="Enter"||l.key===" ")&&l.stopPropagation()}function ua(l,e,t){let n,{value:i=!1}=e,{editable:r=!0}=e,{on_change:s}=e;function o(u){s(u.detail)}function f(u){n=u,t(1,n),t(3,i)}return l.$$set=u=>{"value"in u&&t(3,i=u.value),"editable"in u&&t(0,r=u.editable),"on_change"in u&&t(4,s=u.on_change)},l.$$.update=()=>{l.$$.dirty&8&&t(1,n=typeof i=="string"?i.toLowerCase()==="true":!!i)},[r,n,o,i,s,f]}class fa extends Vo{constructor(e){super(),ea(this,e,ua,oa,ia,{value:3,editable:0,on_change:4})}get value(){return this.$$.ctx[3]}set value(e){this.$$set({value:e}),nn()}get editable(){return this.$$.ctx[0]}set editable(e){this.$$set({editable:e}),nn()}get on_change(){return this.$$.ctx[4]}set on_change(e){this.$$set({on_change:e}),nn()}}const{HtmlTag:ca,SvelteComponent:ha,action_destroyer:da,attr:qe,binding_callbacks:ma,bubble:sn,check_outros:vl,construct_svelte_component:ui,create_component:Jt,destroy_component:Ft,detach:pt,element:Ws,empty:An,flush:ke,group_outros:yl,init:ga,insert:vt,listen:Lt,mount_component:Kt,noop:Cl,prevent_default:ba,run_all:Us,safe_not_equal:wa,set_data:ka,set_input_value:fi,space:fn,stop_propagation:ci,text:pa,toggle_class:Je,transition_in:Ye,transition_out:at}=window.__gradio__svelte__internal,{createEventDispatcher:va}=window.__gradio__svelte__internal;function hi(l){let e,t,n,i;return{c(){e=Ws("textarea"),e.readOnly=l[9],qe(e,"aria-readonly",l[9]),qe(e,"aria-label",t=l[9]?"Cell is read-only":"Edit cell"),qe(e,"tabindex","-1"),qe(e,"class","svelte-fvkwu"),Je(e,"header",l[4])},m(r,s){vt(r,e,s),l[29](e),fi(e,l[0]),n||(i=[Lt(e,"input",l[30]),Lt(e,"blur",l[21]),Lt(e,"mousedown",ci(l[27])),Lt(e,"click",ci(l[28])),da(La.call(null,e)),Lt(e,"keydown",l[22])],n=!0)},p(r,s){s[0]&512&&(e.readOnly=r[9]),s[0]&512&&qe(e,"aria-readonly",r[9]),s[0]&512&&t!==(t=r[9]?"Cell is read-only":"Edit cell")&&qe(e,"aria-label",t),s[0]&1&&fi(e,r[0]),s[0]&16&&Je(e,"header",r[4])},d(r){r&&pt(e),l[29](null),n=!1,Us(i)}}}function ya(l){let e,t,n,i,r,s;const o=[Ma,qa,Na,Sa],f=[];function u(a,_){return a[5]==="image"&&a[11].image?0:a[5]==="html"?1:a[5]==="markdown"?2:3}return t=u(l),n=f[t]=o[t](l),{c(){e=Ws("span"),n.c(),qe(e,"tabindex","0"),qe(e,"role","button"),qe(e,"style",l[3]),qe(e,"data-editable",l[8]),qe(e,"data-max-chars",l[10]),qe(e,"data-expanded",l[2]),qe(e,"placeholder"," "),qe(e,"class","svelte-fvkwu"),Je(e,"dragging",l[13]),Je(e,"edit",l[2]),Je(e,"expanded",l[2]),Je(e,"multiline",l[4]),Je(e,"text",l[5]==="str"),Je(e,"wrap",l[14])},m(a,_){vt(a,e,_),f[t].m(e,null),i=!0,r||(s=[Lt(e,"keydown",l[22]),Lt(e,"focus",ba(l[26]))],r=!0)},p(a,_){let c=t;t=u(a),t===c?f[t].p(a,_):(yl(),at(f[c],1,1,()=>{f[c]=null}),vl(),n=f[t],n?n.p(a,_):(n=f[t]=o[t](a),n.c()),Ye(n,1),n.m(e,null)),(!i||_[0]&8)&&qe(e,"style",a[3]),(!i||_[0]&256)&&qe(e,"data-editable",a[8]),(!i||_[0]&1024)&&qe(e,"data-max-chars",a[10]),(!i||_[0]&4)&&qe(e,"data-expanded",a[2]),(!i||_[0]&8192)&&Je(e,"dragging",a[13]),(!i||_[0]&4)&&Je(e,"edit",a[2]),(!i||_[0]&4)&&Je(e,"expanded",a[2]),(!i||_[0]&16)&&Je(e,"multiline",a[4]),(!i||_[0]&32)&&Je(e,"text",a[5]==="str"),(!i||_[0]&16384)&&Je(e,"wrap",a[14])},i(a){i||(Ye(n),i=!0)},o(a){at(n),i=!1},d(a){a&&pt(e),f[t].d(),r=!1,Us(s)}}}function Ca(l){let e,t;return e=new fa({props:{value:String(l[19]),editable:l[8],on_change:l[23]}}),{c(){Jt(e.$$.fragment)},m(n,i){Kt(e,n,i),t=!0},p(n,i){const r={};i[0]&524288&&(r.value=String(n[19])),i[0]&256&&(r.editable=n[8]),e.$set(r)},i(n){t||(Ye(e.$$.fragment,n),t=!0)},o(n){at(e.$$.fragment,n),t=!1},d(n){Ft(e,n)}}}function Sa(l){let e;return{c(){e=pa(l[20])},m(t,n){vt(t,e,n)},p(t,n){n[0]&1048576&&ka(e,t[20])},i:Cl,o:Cl,d(t){t&&pt(e)}}}function Na(l){let e,t;return e=new ao({props:{message:l[20].toLocaleString(),latex_delimiters:l[6],line_breaks:l[7],chatbot:!1}}),{c(){Jt(e.$$.fragment)},m(n,i){Kt(e,n,i),t=!0},p(n,i){const r={};i[0]&1048576&&(r.message=n[20].toLocaleString()),i[0]&64&&(r.latex_delimiters=n[6]),i[0]&128&&(r.line_breaks=n[7]),e.$set(r)},i(n){t||(Ye(e.$$.fragment,n),t=!0)},o(n){at(e.$$.fragment,n),t=!1},d(n){Ft(e,n)}}}function qa(l){let e,t;return{c(){e=new ca(!1),t=An(),e.a=t},m(n,i){e.m(l[20],n,i),vt(n,t,i)},p(n,i){i[0]&1048576&&e.p(n[20])},i:Cl,o:Cl,d(n){n&&(pt(t),e.d())}}}function Ma(l){let e,t,n;var i=l[11].image;function r(s,o){return{props:{value:{url:s[20]},show_label:!1,label:"cell-image",show_download_button:!1,i18n:s[12],gradio:{dispatch:ja}}}}return i&&(e=ui(i,r(l))),{c(){e&&Jt(e.$$.fragment),t=An()},m(s,o){e&&Kt(e,s,o),vt(s,t,o),n=!0},p(s,o){if(o[0]&2048&&i!==(i=s[11].image)){if(e){yl();const f=e;at(f.$$.fragment,1,0,()=>{Ft(f,1)}),vl()}i?(e=ui(i,r(s)),Jt(e.$$.fragment),Ye(e.$$.fragment,1),Kt(e,t.parentNode,t)):e=null}else if(i){const f={};o[0]&1048576&&(f.value={url:s[20]}),o[0]&4096&&(f.i18n=s[12]),e.$set(f)}},i(s){n||(e&&Ye(e.$$.fragment,s),n=!0)},o(s){e&&at(e.$$.fragment,s),n=!1},d(s){s&&pt(t),e&&Ft(e,s)}}}function di(l){let e,t,n,i;return e=new ai({props:{position:"column",coords:l[16],on_click:l[31]}}),n=new ai({props:{position:"row",coords:l[16],on_click:l[32]}}),{c(){Jt(e.$$.fragment),t=fn(),Jt(n.$$.fragment)},m(r,s){Kt(e,r,s),vt(r,t,s),Kt(n,r,s),i=!0},p(r,s){const o={};s[0]&65536&&(o.coords=r[16]),s[0]&196608&&(o.on_click=r[31]),e.$set(o);const f={};s[0]&65536&&(f.coords=r[16]),s[0]&327680&&(f.on_click=r[32]),n.$set(f)},i(r){i||(Ye(e.$$.fragment,r),Ye(n.$$.fragment,r),i=!0)},o(r){at(e.$$.fragment,r),at(n.$$.fragment,r),i=!1},d(r){r&&pt(t),Ft(e,r),Ft(n,r)}}}function Aa(l){let e,t,n,i,r,s,o=l[2]&&l[5]!=="bool"&&hi(l);const f=[Ca,ya],u=[];function a(c,w){return c[5]==="bool"?0:1}t=a(l),n=u[t]=f[t](l);let _=l[15]&&l[16]&&l[17]&&l[18]&&di(l);return{c(){o&&o.c(),e=fn(),n.c(),i=fn(),_&&_.c(),r=An()},m(c,w){o&&o.m(c,w),vt(c,e,w),u[t].m(c,w),vt(c,i,w),_&&_.m(c,w),vt(c,r,w),s=!0},p(c,w){c[2]&&c[5]!=="bool"?o?o.p(c,w):(o=hi(c),o.c(),o.m(e.parentNode,e)):o&&(o.d(1),o=null);let L=t;t=a(c),t===L?u[t].p(c,w):(yl(),at(u[L],1,1,()=>{u[L]=null}),vl(),n=u[t],n?n.p(c,w):(n=u[t]=f[t](c),n.c()),Ye(n,1),n.m(i.parentNode,i)),c[15]&&c[16]&&c[17]&&c[18]?_?(_.p(c,w),w[0]&491520&&Ye(_,1)):(_=di(c),_.c(),Ye(_,1),_.m(r.parentNode,r)):_&&(yl(),at(_,1,1,()=>{_=null}),vl())},i(c){s||(Ye(n),Ye(_),s=!0)},o(c){at(n),at(_),s=!1},d(c){c&&(pt(e),pt(i),pt(r)),o&&o.d(c),u[t].d(c),_&&_.d(c)}}}function Ea(l,e=null,t=!1){if(t)return String(l);const n=String(l);return!e||e<=0||n.length<=e?n:n.slice(0,e)+"..."}function La(l){return requestAnimationFrame(()=>{l.focus()}),{}}const ja=()=>{};function Ba(l,e,t){let n,i,r,{edit:s}=e,{value:o=""}=e,{display_value:f=null}=e,{styling:u=""}=e,{header:a=!1}=e,{datatype:_="str"}=e,{latex_delimiters:c}=e,{line_breaks:w=!0}=e,{editable:L=!0}=e,{is_static:v=!1}=e,{max_chars:q=null}=e,{components:b={}}=e,{i18n:p}=e,{is_dragging:k=!1}=e,{wrap_text:y=!1}=e,{show_selection_buttons:C=!1}=e,{coords:j}=e,{on_select_column:S=null}=e,{on_select_row:z=null}=e,{el:O}=e;const X=va();function I(N){X("blur",{blur_event:N,coords:j})}function D(N){X("keydown",N)}function T(N){t(0,o=N.toString()),X("blur",{blur_event:{target:{type:"checkbox",checked:N,value:N.toString()}},coords:j})}function Y(N){sn.call(this,l,N)}function ne(N){sn.call(this,l,N)}function K(N){sn.call(this,l,N)}function W(N){ma[N?"unshift":"push"](()=>{O=N,t(1,O)})}function ce(){o=this.value,t(0,o)}const B=()=>S(j[1]),Z=()=>z(j[0]);return l.$$set=N=>{"edit"in N&&t(2,s=N.edit),"value"in N&&t(0,o=N.value),"display_value"in N&&t(24,f=N.display_value),"styling"in N&&t(3,u=N.styling),"header"in N&&t(4,a=N.header),"datatype"in N&&t(5,_=N.datatype),"latex_delimiters"in N&&t(6,c=N.latex_delimiters),"line_breaks"in N&&t(7,w=N.line_breaks),"editable"in N&&t(8,L=N.editable),"is_static"in N&&t(9,v=N.is_static),"max_chars"in N&&t(10,q=N.max_chars),"components"in N&&t(11,b=N.components),"i18n"in N&&t(12,p=N.i18n),"is_dragging"in N&&t(13,k=N.is_dragging),"wrap_text"in N&&t(14,y=N.wrap_text),"show_selection_buttons"in N&&t(15,C=N.show_selection_buttons),"coords"in N&&t(16,j=N.coords),"on_select_column"in N&&t(17,S=N.on_select_column),"on_select_row"in N&&t(18,z=N.on_select_row),"el"in N&&t(1,O=N.el)},l.$$.update=()=>{l.$$.dirty[0]&1028&&t(25,n=!s&&q!==null&&q>0),l.$$.dirty[0]&16777473&&t(19,i=L?o:f!==null?f:o),l.$$.dirty[0]&34079776&&t(20,r=n?Ea(i,q,_==="image"):i)},[o,O,s,u,a,_,c,w,L,v,q,b,p,k,y,C,j,S,z,i,r,I,D,T,f,n,Y,ne,K,W,ce,B,Z]}class En extends ha{constructor(e){super(),ga(this,e,Ba,Aa,wa,{edit:2,value:0,display_value:24,styling:3,header:4,datatype:5,latex_delimiters:6,line_breaks:7,editable:8,is_static:9,max_chars:10,components:11,i18n:12,is_dragging:13,wrap_text:14,show_selection_buttons:15,coords:16,on_select_column:17,on_select_row:18,el:1},null,[-1,-1])}get edit(){return this.$$.ctx[2]}set edit(e){this.$$set({edit:e}),ke()}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),ke()}get display_value(){return this.$$.ctx[24]}set display_value(e){this.$$set({display_value:e}),ke()}get styling(){return this.$$.ctx[3]}set styling(e){this.$$set({styling:e}),ke()}get header(){return this.$$.ctx[4]}set header(e){this.$$set({header:e}),ke()}get datatype(){return this.$$.ctx[5]}set datatype(e){this.$$set({datatype:e}),ke()}get latex_delimiters(){return this.$$.ctx[6]}set latex_delimiters(e){this.$$set({latex_delimiters:e}),ke()}get line_breaks(){return this.$$.ctx[7]}set line_breaks(e){this.$$set({line_breaks:e}),ke()}get editable(){return this.$$.ctx[8]}set editable(e){this.$$set({editable:e}),ke()}get is_static(){return this.$$.ctx[9]}set is_static(e){this.$$set({is_static:e}),ke()}get max_chars(){return this.$$.ctx[10]}set max_chars(e){this.$$set({max_chars:e}),ke()}get components(){return this.$$.ctx[11]}set components(e){this.$$set({components:e}),ke()}get i18n(){return this.$$.ctx[12]}set i18n(e){this.$$set({i18n:e}),ke()}get is_dragging(){return this.$$.ctx[13]}set is_dragging(e){this.$$set({is_dragging:e}),ke()}get wrap_text(){return this.$$.ctx[14]}set wrap_text(e){this.$$set({wrap_text:e}),ke()}get show_selection_buttons(){return this.$$.ctx[15]}set show_selection_buttons(e){this.$$set({show_selection_buttons:e}),ke()}get coords(){return this.$$.ctx[16]}set coords(e){this.$$set({coords:e}),ke()}get on_select_column(){return this.$$.ctx[17]}set on_select_column(e){this.$$set({on_select_column:e}),ke()}get on_select_row(){return this.$$.ctx[18]}set on_select_row(e){this.$$set({on_select_row:e}),ke()}get el(){return this.$$.ctx[1]}set el(e){this.$$set({el:e}),ke()}}const{SvelteComponent:za,append:Da,attr:jt,detach:Ln,element:Vs,empty:Ta,flush:mi,init:Ha,insert:jn,noop:cn,safe_not_equal:Oa,set_data:Ia,text:Pa}=window.__gradio__svelte__internal;function Ra(l){let e,t=(l[0]!==null?l[0]+1:"")+"",n;return{c(){e=Vs("td"),n=Pa(t),jt(e,"class","row-number svelte-ux4in1"),jt(e,"tabindex","-1"),jt(e,"data-row",l[0]),jt(e,"data-col","row-number")},m(i,r){jn(i,e,r),Da(e,n)},p(i,r){r&1&&t!==(t=(i[0]!==null?i[0]+1:"")+"")&&Ia(n,t),r&1&&jt(e,"data-row",i[0])},d(i){i&&Ln(e)}}}function Ja(l){let e;return{c(){e=Vs("th"),e.innerHTML='<div class="cell-wrap"><div class="header-content"><div class="header-text"></div></div></div>',jt(e,"tabindex","-1"),jt(e,"class","row-number svelte-ux4in1")},m(t,n){jn(t,e,n)},p:cn,d(t){t&&Ln(e)}}}function Fa(l){let e;function t(r,s){return r[1]?Ja:Ra}let n=t(l),i=n(l);return{c(){i.c(),e=Ta()},m(r,s){i.m(r,s),jn(r,e,s)},p(r,[s]){n===(n=t(r))&&i?i.p(r,s):(i.d(1),i=n(r),i&&(i.c(),i.m(e.parentNode,e)))},i:cn,o:cn,d(r){r&&Ln(e),i.d(r)}}}function Ka(l,e,t){let{index:n=null}=e,{is_header:i=!1}=e;return l.$$set=r=>{"index"in r&&t(0,n=r.index),"is_header"in r&&t(1,i=r.is_header)},[n,i]}class Al extends za{constructor(e){super(),Ha(this,e,Ka,Fa,Oa,{index:0,is_header:1})}get index(){return this.$$.ctx[0]}set index(e){this.$$set({index:e}),mi()}get is_header(){return this.$$.ctx[1]}set is_header(e){this.$$set({is_header:e}),mi()}}const{SvelteComponent:Wa,attr:rn,detach:Ua,element:Va,flush:Ya,init:Xa,insert:Ga,is_function:Qa,listen:gi,noop:bi,run_all:Za,safe_not_equal:$a}=window.__gradio__svelte__internal;function xa(l){let e,t,n;return{c(){e=Va("button"),e.textContent="⋮",rn(e,"aria-label","Open cell menu"),rn(e,"class","cell-menu-button svelte-vt38nd"),rn(e,"aria-haspopup","menu")},m(i,r){Ga(i,e,r),t||(n=[gi(e,"click",function(){Qa(l[0])&&l[0].apply(this,arguments)}),gi(e,"touchstart",l[1])],t=!0)},p(i,[r]){l=i},i:bi,o:bi,d(i){i&&Ua(e),t=!1,Za(n)}}}function e_(l,e,t){let{on_click:n}=e;const i=r=>{r.preventDefault();const s=r.touches[0],o=new MouseEvent("click",{clientX:s.clientX,clientY:s.clientY,bubbles:!0,cancelable:!0,view:window});n(o)};return l.$$set=r=>{"on_click"in r&&t(0,n=r.on_click)},[n,i]}class Ys extends Wa{constructor(e){super(),Xa(this,e,e_,xa,$a,{on_click:0})}get on_click(){return this.$$.ctx[0]}set on_click(e){this.$$set({on_click:e}),Ya()}}const{SvelteComponent:t_,attr:wi,detach:l_,element:n_,init:i_,insert:s_,noop:on,safe_not_equal:r_}=window.__gradio__svelte__internal;function o_(l){let e;return{c(){e=n_("div"),e.innerHTML='<svg xmlns="http://www.w3.org/2000/svg" width="13" height="13" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect x="3" y="11" width="18" height="11" rx="2" ry="2"></rect><path d="M7 11V7a5 5 0 0 1 10 0v4"></path></svg>',wi(e,"class","wrapper svelte-1skchaw"),wi(e,"aria-label","Static column")},m(t,n){s_(t,e,n)},p:on,i:on,o:on,d(t){t&&l_(e)}}}class a_ extends t_{constructor(e){super(),i_(this,e,null,o_,r_,{})}}const{SvelteComponent:__,append:ki,attr:ze,detach:u_,flush:f_,init:c_,insert:h_,noop:pi,safe_not_equal:d_,svg_element:an}=window.__gradio__svelte__internal;function m_(l){let e,t,n;return{c(){e=an("svg"),t=an("path"),n=an("path"),ze(t,"d","M4 8L8 4L12 8"),ze(t,"stroke","currentColor"),ze(t,"stroke-width","1.5"),ze(t,"stroke-linecap","round"),ze(t,"stroke-linejoin","round"),ze(n,"d","M8 4V12"),ze(n,"stroke","currentColor"),ze(n,"stroke-width","1.5"),ze(n,"stroke-linecap","round"),ze(e,"width",l[0]),ze(e,"height",l[0]),ze(e,"viewBox","0 0 16 16"),ze(e,"fill","none"),ze(e,"xmlns","http://www.w3.org/2000/svg")},m(i,r){h_(i,e,r),ki(e,t),ki(e,n)},p(i,[r]){r&1&&ze(e,"width",i[0]),r&1&&ze(e,"height",i[0])},i:pi,o:pi,d(i){i&&u_(e)}}}function g_(l,e,t){let{size:n=16}=e;return l.$$set=i=>{"size"in i&&t(0,n=i.size)},[n]}class b_ extends __{constructor(e){super(),c_(this,e,g_,m_,d_,{size:0})}get size(){return this.$$.ctx[0]}set size(e){this.$$set({size:e}),f_()}}const{SvelteComponent:w_,append:vi,attr:De,detach:k_,flush:p_,init:v_,insert:y_,noop:yi,safe_not_equal:C_,svg_element:_n}=window.__gradio__svelte__internal;function S_(l){let e,t,n;return{c(){e=_n("svg"),t=_n("path"),n=_n("path"),De(t,"d","M4 8L8 12L12 8"),De(t,"stroke","currentColor"),De(t,"stroke-width","1.5"),De(t,"stroke-linecap","round"),De(t,"stroke-linejoin","round"),De(n,"d","M8 12V4"),De(n,"stroke","currentColor"),De(n,"stroke-width","1.5"),De(n,"stroke-linecap","round"),De(e,"width",l[0]),De(e,"height",l[0]),De(e,"viewBox","0 0 16 16"),De(e,"fill","none"),De(e,"xmlns","http://www.w3.org/2000/svg")},m(i,r){y_(i,e,r),vi(e,t),vi(e,n)},p(i,[r]){r&1&&De(e,"width",i[0]),r&1&&De(e,"height",i[0])},i:yi,o:yi,d(i){i&&k_(e)}}}function N_(l,e,t){let{size:n=16}=e;return l.$$set=i=>{"size"in i&&t(0,n=i.size)},[n]}class q_ extends w_{constructor(e){super(),v_(this,e,N_,S_,C_,{size:0})}get size(){return this.$$.ctx[0]}set size(e){this.$$set({size:e}),p_()}}const{SvelteComponent:M_,append:le,attr:d,detach:_t,empty:A_,flush:E_,init:L_,insert:ut,noop:Ci,safe_not_equal:j_,svg_element:U}=window.__gradio__svelte__internal;function B_(l){let e,t,n,i,r;return{c(){e=U("svg"),t=U("path"),n=U("path"),i=U("path"),r=U("path"),d(t,"d","M5 5H19"),d(t,"stroke","currentColor"),d(t,"stroke-width","2"),d(t,"stroke-linecap","round"),d(n,"d","M8 9H16"),d(n,"stroke","currentColor"),d(n,"stroke-width","2"),d(n,"stroke-linecap","round"),d(i,"d","M11 13H13"),d(i,"stroke","currentColor"),d(i,"stroke-width","2"),d(i,"stroke-linecap","round"),d(r,"d","M17 17L21 21M21 17L17 21"),d(r,"stroke","currentColor"),d(r,"stroke-width","2"),d(r,"stroke-linecap","round"),d(e,"viewBox","0 0 24 24"),d(e,"width","16"),d(e,"height","16")},m(s,o){ut(s,e,o),le(e,t),le(e,n),le(e,i),le(e,r)},d(s){s&&_t(e)}}}function z_(l){let e,t,n,i;return{c(){e=U("svg"),t=U("path"),n=U("path"),i=U("path"),d(t,"d","M5 5H19"),d(t,"stroke","currentColor"),d(t,"stroke-width","2"),d(t,"stroke-linecap","round"),d(n,"d","M8 9H16"),d(n,"stroke","currentColor"),d(n,"stroke-width","2"),d(n,"stroke-linecap","round"),d(i,"d","M11 13H13"),d(i,"stroke","currentColor"),d(i,"stroke-width","2"),d(i,"stroke-linecap","round"),d(e,"viewBox","0 0 24 24"),d(e,"width","16"),d(e,"height","16")},m(r,s){ut(r,e,s),le(e,t),le(e,n),le(e,i)},d(r){r&&_t(e)}}}function D_(l){let e,t,n,i,r,s;return{c(){e=U("svg"),t=U("path"),n=U("path"),i=U("path"),r=U("path"),s=U("path"),d(t,"d","M5 5H19"),d(t,"stroke","currentColor"),d(t,"stroke-width","2"),d(t,"stroke-linecap","round"),d(n,"d","M5 9H15"),d(n,"stroke","currentColor"),d(n,"stroke-width","2"),d(n,"stroke-linecap","round"),d(i,"d","M5 13H11"),d(i,"stroke","currentColor"),d(i,"stroke-width","2"),d(i,"stroke-linecap","round"),d(r,"d","M5 17H7"),d(r,"stroke","currentColor"),d(r,"stroke-width","2"),d(r,"stroke-linecap","round"),d(s,"d","M17 17L21 21M21 17L17 21"),d(s,"stroke","currentColor"),d(s,"stroke-width","2"),d(s,"stroke-linecap","round"),d(e,"viewBox","0 0 24 24"),d(e,"width","16"),d(e,"height","16")},m(o,f){ut(o,e,f),le(e,t),le(e,n),le(e,i),le(e,r),le(e,s)},d(o){o&&_t(e)}}}function T_(l){let e,t,n,i;return{c(){e=U("svg"),t=U("path"),n=U("path"),i=U("path"),d(t,"d","M8 12L12 16L16 12"),d(t,"stroke","currentColor"),d(t,"stroke-width","2"),d(t,"fill","none"),d(t,"stroke-linecap","round"),d(t,"stroke-linejoin","round"),d(n,"d","M12 16V9"),d(n,"stroke","currentColor"),d(n,"stroke-width","2"),d(n,"stroke-linecap","round"),d(i,"d","M5 5H19"),d(i,"stroke","currentColor"),d(i,"stroke-width","2"),d(i,"stroke-linecap","round"),d(e,"viewBox","0 0 24 24"),d(e,"width","16"),d(e,"height","16")},m(r,s){ut(r,e,s),le(e,t),le(e,n),le(e,i)},d(r){r&&_t(e)}}}function H_(l){let e,t,n,i;return{c(){e=U("svg"),t=U("path"),n=U("path"),i=U("path"),d(t,"d","M8 16L12 12L16 16"),d(t,"stroke","currentColor"),d(t,"stroke-width","2"),d(t,"fill","none"),d(t,"stroke-linecap","round"),d(t,"stroke-linejoin","round"),d(n,"d","M12 12V19"),d(n,"stroke","currentColor"),d(n,"stroke-width","2"),d(n,"stroke-linecap","round"),d(i,"d","M5 7H19"),d(i,"stroke","currentColor"),d(i,"stroke-width","2"),d(i,"stroke-linecap","round"),d(e,"viewBox","0 0 24 24"),d(e,"width","16"),d(e,"height","16")},m(r,s){ut(r,e,s),le(e,t),le(e,n),le(e,i)},d(r){r&&_t(e)}}}function O_(l){let e,t,n;return{c(){e=U("svg"),t=U("rect"),n=U("path"),d(t,"x","10"),d(t,"y","5"),d(t,"width","4"),d(t,"height","14"),d(t,"stroke","currentColor"),d(t,"stroke-width","2"),d(n,"d","M7 8L17 16M17 8L7 16"),d(n,"stroke","currentColor"),d(n,"stroke-width","2"),d(n,"stroke-linecap","round"),d(e,"viewBox","0 0 24 24"),d(e,"width","16"),d(e,"height","16")},m(i,r){ut(i,e,r),le(e,t),le(e,n)},d(i){i&&_t(e)}}}function I_(l){let e,t,n;return{c(){e=U("svg"),t=U("rect"),n=U("path"),d(t,"x","5"),d(t,"y","10"),d(t,"width","14"),d(t,"height","4"),d(t,"stroke","currentColor"),d(t,"stroke-width","2"),d(n,"d","M8 7L16 17M16 7L8 17"),d(n,"stroke","currentColor"),d(n,"stroke-width","2"),d(n,"stroke-linecap","round"),d(e,"viewBox","0 0 24 24"),d(e,"width","16"),d(e,"height","16")},m(i,r){ut(i,e,r),le(e,t),le(e,n)},d(i){i&&_t(e)}}}function P_(l){let e,t,n;return{c(){e=U("svg"),t=U("rect"),n=U("path"),d(t,"x","6"),d(t,"y","4"),d(t,"width","12"),d(t,"height","4"),d(t,"stroke","currentColor"),d(t,"stroke-width","2"),d(n,"d","M12 12V19M8 16L12 19L16 16"),d(n,"stroke","currentColor"),d(n,"stroke-width","2"),d(n,"fill","none"),d(n,"stroke-linecap","round"),d(e,"viewBox","0 0 24 24"),d(e,"width","16"),d(e,"height","16")},m(i,r){ut(i,e,r),le(e,t),le(e,n)},d(i){i&&_t(e)}}}function R_(l){let e,t,n;return{c(){e=U("svg"),t=U("rect"),n=U("path"),d(t,"x","6"),d(t,"y","16"),d(t,"width","12"),d(t,"height","4"),d(t,"stroke","currentColor"),d(t,"stroke-width","2"),d(n,"d","M12 12V5M8 8L12 5L16 8"),d(n,"stroke","currentColor"),d(n,"stroke-width","2"),d(n,"fill","none"),d(n,"stroke-linecap","round"),d(e,"viewBox","0 0 24 24"),d(e,"width","16"),d(e,"height","16")},m(i,r){ut(i,e,r),le(e,t),le(e,n)},d(i){i&&_t(e)}}}function J_(l){let e,t,n;return{c(){e=U("svg"),t=U("rect"),n=U("path"),d(t,"x","16"),d(t,"y","6"),d(t,"width","4"),d(t,"height","12"),d(t,"stroke","currentColor"),d(t,"stroke-width","2"),d(t,"fill","none"),d(n,"d","M12 12H5M8 8L5 12L8 16"),d(n,"stroke","currentColor"),d(n,"stroke-width","2"),d(n,"fill","none"),d(n,"stroke-linecap","round"),d(e,"viewBox","0 0 24 24"),d(e,"width","16"),d(e,"height","16")},m(i,r){ut(i,e,r),le(e,t),le(e,n)},d(i){i&&_t(e)}}}function F_(l){let e,t,n;return{c(){e=U("svg"),t=U("rect"),n=U("path"),d(t,"x","4"),d(t,"y","6"),d(t,"width","4"),d(t,"height","12"),d(t,"stroke","currentColor"),d(t,"stroke-width","2"),d(t,"fill","none"),d(n,"d","M12 12H19M16 8L19 12L16 16"),d(n,"stroke","currentColor"),d(n,"stroke-width","2"),d(n,"fill","none"),d(n,"stroke-linecap","round"),d(e,"viewBox","0 0 24 24"),d(e,"width","16"),d(e,"height","16")},m(i,r){ut(i,e,r),le(e,t),le(e,n)},d(i){i&&_t(e)}}}function K_(l){let e;function t(r,s){if(r[0]=="add-column-right")return F_;if(r[0]=="add-column-left")return J_;if(r[0]=="add-row-above")return R_;if(r[0]=="add-row-below")return P_;if(r[0]=="delete-row")return I_;if(r[0]=="delete-column")return O_;if(r[0]=="sort-asc")return H_;if(r[0]=="sort-desc")return T_;if(r[0]=="clear-sort")return D_;if(r[0]=="filter")return z_;if(r[0]=="clear-filter")return B_}let n=t(l),i=n&&n(l);return{c(){i&&i.c(),e=A_()},m(r,s){i&&i.m(r,s),ut(r,e,s)},p(r,[s]){n!==(n=t(r))&&(i&&i.d(1),i=n&&n(r),i&&(i.c(),i.m(e.parentNode,e)))},i:Ci,o:Ci,d(r){r&&_t(e),i&&i.d(r)}}}function W_(l,e,t){let{icon:n}=e;return l.$$set=i=>{"icon"in i&&t(0,n=i.icon)},[n]}class lt extends M_{constructor(e){super(),L_(this,e,W_,K_,j_,{icon:0})}get icon(){return this.$$.ctx[0]}set icon(e){this.$$set({icon:e}),E_()}}const{SvelteComponent:U_,add_flush_callback:Si,append:ft,attr:He,bind:Ni,binding_callbacks:qi,check_outros:xt,create_component:Ut,destroy_component:Vt,detach:El,element:kt,flush:pe,group_outros:el,init:V_,insert:Ll,listen:ml,mount_component:Yt,run_all:Y_,safe_not_equal:X_,set_data:G_,set_style:gl,space:tl,text:Q_,toggle_class:bt,transition_in:ye,transition_out:Te}=window.__gradio__svelte__internal;function Mi(l){let e,t,n,i,r,s;const o=[$_,Z_],f=[];function u(_,c){return _[19]==="asc"?0:1}n=u(l),i=f[n]=o[n](l);let a=l[11].length>1&&Ai(l);return{c(){e=kt("div"),t=kt("span"),i.c(),r=tl(),a&&a.c(),He(t,"class","sort-arrow svelte-41hbvn"),He(e,"class","sort-indicators svelte-41hbvn")},m(_,c){Ll(_,e,c),ft(e,t),f[n].m(t,null),ft(e,r),a&&a.m(e,null),s=!0},p(_,c){let w=n;n=u(_),n!==w&&(el(),Te(f[w],1,1,()=>{f[w]=null}),xt(),i=f[n],i||(i=f[n]=o[n](_),i.c()),ye(i,1),i.m(t,null)),_[11].length>1?a?a.p(_,c):(a=Ai(_),a.c(),a.m(e,null)):a&&(a.d(1),a=null)},i(_){s||(ye(i),s=!0)},o(_){Te(i),s=!1},d(_){_&&El(e),f[n].d(),a&&a.d()}}}function Z_(l){let e,t;return e=new q_({props:{size:12}}),{c(){Ut(e.$$.fragment)},m(n,i){Yt(e,n,i),t=!0},i(n){t||(ye(e.$$.fragment,n),t=!0)},o(n){Te(e.$$.fragment,n),t=!1},d(n){Vt(e,n)}}}function $_(l){let e,t;return e=new b_({props:{size:12}}),{c(){Ut(e.$$.fragment)},m(n,i){Yt(e,n,i),t=!0},i(n){t||(ye(e.$$.fragment,n),t=!0)},o(n){Te(e.$$.fragment,n),t=!1},d(n){Vt(e,n)}}}function Ai(l){let e,t;return{c(){e=kt("span"),t=Q_(l[20]),He(e,"class","sort-priority svelte-41hbvn")},m(n,i){Ll(n,e,i),ft(e,t)},p(n,i){i[0]&1048576&&G_(t,n[20])},d(n){n&&El(e)}}}function Ei(l){let e,t,n,i;return n=new lt({props:{icon:"filter"}}),{c(){e=kt("div"),t=kt("span"),Ut(n.$$.fragment),He(t,"class","filter-icon svelte-41hbvn"),He(e,"class","filter-indicators svelte-41hbvn")},m(r,s){Ll(r,e,s),ft(e,t),Yt(n,t,null),i=!0},i(r){i||(ye(n.$$.fragment,r),i=!0)},o(r){Te(n.$$.fragment,r),i=!1},d(r){r&&El(e),Vt(n)}}}function Li(l){let e,t;return e=new a_({}),{c(){Ut(e.$$.fragment)},m(n,i){Yt(e,n,i),t=!0},i(n){t||(ye(e.$$.fragment,n),t=!0)},o(n){Te(e.$$.fragment,n),t=!1},d(n){Vt(e,n)}}}function ji(l){let e,t;return e=new Ys({props:{on_click:l[30]}}),{c(){Ut(e.$$.fragment)},m(n,i){Yt(e,n,i),t=!0},p(n,i){const r={};i[0]&516&&(r.on_click=n[30]),e.$set(r)},i(n){t||(ye(e.$$.fragment,n),t=!0)},o(n){Te(e.$$.fragment,n),t=!1},d(n){Vt(e,n)}}}function x_(l){let e,t,n,i,r,s,o,f,u,a,_,c,w,L,v;function q(S){l[26](S)}function b(S){l[27](S)}let p={max_chars:l[14],latex_delimiters:l[12],line_breaks:l[13],edit:l[4]===l[2],header:!0,editable:l[15],is_static:l[17],i18n:l[16],coords:[l[2],0]};l[0]!==void 0&&(p.value=l[0]),l[1]!==void 0&&(p.el=l[1]),r=new En({props:p}),qi.push(()=>Ni(r,"value",q)),qi.push(()=>Ni(r,"el",b)),r.$on("keydown",l[28]);let k=l[18]!==-1&&Mi(l),y=l[21]!==-1&&Ei(),C=l[17]&&Li(),j=l[22]&&ji(l);return{c(){e=kt("th"),t=kt("div"),n=kt("div"),i=kt("button"),Ut(r.$$.fragment),f=tl(),k&&k.c(),u=tl(),y&&y.c(),a=tl(),C&&C.c(),_=tl(),j&&j.c(),He(i,"class","header-button svelte-41hbvn"),He(i,"title",l[0]),He(n,"class","header-content svelte-41hbvn"),He(t,"class","cell-wrap svelte-41hbvn"),He(e,"aria-sort",c=dl(l[0],l[11],l[6])==="none"?"none":dl(l[0],l[11],l[6])==="asc"?"ascending":"descending"),gl(e,"width",l[7](l[2])),gl(e,"left",l[23](l[2])),He(e,"title",l[0]),He(e,"class","svelte-41hbvn"),bt(e,"pinned-column",l[2]<l[3]),bt(e,"last-pinned",l[2]===l[3]-1),bt(e,"focus",l[4]===l[2]||l[5]===l[2]),bt(e,"sorted",l[18]!==-1),bt(e,"filtered",l[21]!==-1)},m(S,z){Ll(S,e,z),ft(e,t),ft(t,n),ft(n,i),Yt(r,i,null),ft(i,f),k&&k.m(i,null),ft(i,u),y&&y.m(i,null),ft(n,a),C&&C.m(n,null),ft(t,_),j&&j.m(t,null),w=!0,L||(v=[ml(i,"click",l[29]),ml(i,"mousedown",eu),ml(e,"click",l[31]),ml(e,"mousedown",tu)],L=!0)},p(S,z){const O={};z[0]&16384&&(O.max_chars=S[14]),z[0]&4096&&(O.latex_delimiters=S[12]),z[0]&8192&&(O.line_breaks=S[13]),z[0]&20&&(O.edit=S[4]===S[2]),z[0]&32768&&(O.editable=S[15]),z[0]&131072&&(O.is_static=S[17]),z[0]&65536&&(O.i18n=S[16]),z[0]&4&&(O.coords=[S[2],0]),!s&&z[0]&1&&(s=!0,O.value=S[0],Si(()=>s=!1)),!o&&z[0]&2&&(o=!0,O.el=S[1],Si(()=>o=!1)),r.$set(O),S[18]!==-1?k?(k.p(S,z),z[0]&262144&&ye(k,1)):(k=Mi(S),k.c(),ye(k,1),k.m(i,u)):k&&(el(),Te(k,1,1,()=>{k=null}),xt()),S[21]!==-1?y?z[0]&2097152&&ye(y,1):(y=Ei(),y.c(),ye(y,1),y.m(i,null)):y&&(el(),Te(y,1,1,()=>{y=null}),xt()),(!w||z[0]&1)&&He(i,"title",S[0]),S[17]?C?z[0]&131072&&ye(C,1):(C=Li(),C.c(),ye(C,1),C.m(n,null)):C&&(el(),Te(C,1,1,()=>{C=null}),xt()),S[22]?j?(j.p(S,z),z[0]&4194304&&ye(j,1)):(j=ji(S),j.c(),ye(j,1),j.m(t,null)):j&&(el(),Te(j,1,1,()=>{j=null}),xt()),(!w||z[0]&2113&&c!==(c=dl(S[0],S[11],S[6])==="none"?"none":dl(S[0],S[11],S[6])==="asc"?"ascending":"descending"))&&He(e,"aria-sort",c),(!w||z[0]&132)&&gl(e,"width",S[7](S[2])),(!w||z[0]&4)&&gl(e,"left",S[23](S[2])),(!w||z[0]&1)&&He(e,"title",S[0]),(!w||z[0]&12)&&bt(e,"pinned-column",S[2]<S[3]),(!w||z[0]&12)&&bt(e,"last-pinned",S[2]===S[3]-1),(!w||z[0]&52)&&bt(e,"focus",S[4]===S[2]||S[5]===S[2]),(!w||z[0]&262144)&&bt(e,"sorted",S[18]!==-1),(!w||z[0]&2097152)&&bt(e,"filtered",S[21]!==-1)},i(S){w||(ye(r.$$.fragment,S),ye(k),ye(y),ye(C),ye(j),w=!0)},o(S){Te(r.$$.fragment,S),Te(k),Te(y),Te(C),Te(j),w=!1},d(S){S&&El(e),Vt(r),k&&k.d(),y&&y.d(),C&&C.d(),j&&j.d(),L=!1,Y_(v)}}}const eu=l=>{l.preventDefault(),l.stopPropagation()},tu=l=>{l.preventDefault(),l.stopPropagation()};function lu(l,e,t){let n,i,r,s,o,{value:f}=e,{i:u}=e,{actual_pinned_columns:a}=e,{header_edit:_}=e,{selected_header:c}=e,{headers:w}=e,{get_cell_width:L}=e,{handle_header_click:v}=e,{toggle_header_menu:q}=e,{end_header_edit:b}=e,{sort_columns:p=[]}=e,{filter_columns:k=[]}=e,{latex_delimiters:y}=e,{line_breaks:C}=e,{max_chars:j}=e,{editable:S}=e,{i18n:z}=e,{el:O}=e,{is_static:X}=e,{col_count:I}=e;function D(B){return B>=a?"auto":B===0?"0":`calc(${Array(B).fill(0).map((N,J)=>L(J)).join(" + ")})`}function T(B){f=B,t(0,f)}function Y(B){O=B,t(1,O)}const ne=B=>{(B.detail.key==="Enter"||B.detail.key==="Escape"||B.detail.key==="Tab")&&b(B)},K=B=>v(B,u),W=B=>q(B,u),ce=B=>v(B,u);return l.$$set=B=>{"value"in B&&t(0,f=B.value),"i"in B&&t(2,u=B.i),"actual_pinned_columns"in B&&t(3,a=B.actual_pinned_columns),"header_edit"in B&&t(4,_=B.header_edit),"selected_header"in B&&t(5,c=B.selected_header),"headers"in B&&t(6,w=B.headers),"get_cell_width"in B&&t(7,L=B.get_cell_width),"handle_header_click"in B&&t(8,v=B.handle_header_click),"toggle_header_menu"in B&&t(9,q=B.toggle_header_menu),"end_header_edit"in B&&t(10,b=B.end_header_edit),"sort_columns"in B&&t(11,p=B.sort_columns),"filter_columns"in B&&t(24,k=B.filter_columns),"latex_delimiters"in B&&t(12,y=B.latex_delimiters),"line_breaks"in B&&t(13,C=B.line_breaks),"max_chars"in B&&t(14,j=B.max_chars),"editable"in B&&t(15,S=B.editable),"i18n"in B&&t(16,z=B.i18n),"el"in B&&t(1,O=B.el),"is_static"in B&&t(17,X=B.is_static),"col_count"in B&&t(25,I=B.col_count)},l.$$.update=()=>{l.$$.dirty[0]&33554432&&t(22,n=I&&I[1]==="dynamic"),l.$$.dirty[0]&2052&&t(18,i=p.findIndex(B=>B.col===u)),l.$$.dirty[0]&16777220&&t(21,r=k.findIndex(B=>B.col===u)),l.$$.dirty[0]&262144&&t(20,s=i!==-1?i+1:null),l.$$.dirty[0]&264192&&t(19,o=i!==-1?p[i].direction:null)},[f,O,u,a,_,c,w,L,v,q,b,p,y,C,j,S,z,X,i,o,s,r,n,D,k,I,T,Y,ne,K,W,ce]}class Xs extends U_{constructor(e){super(),V_(this,e,lu,x_,X_,{value:0,i:2,actual_pinned_columns:3,header_edit:4,selected_header:5,headers:6,get_cell_width:7,handle_header_click:8,toggle_header_menu:9,end_header_edit:10,sort_columns:11,filter_columns:24,latex_delimiters:12,line_breaks:13,max_chars:14,editable:15,i18n:16,el:1,is_static:17,col_count:25},null,[-1,-1])}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),pe()}get i(){return this.$$.ctx[2]}set i(e){this.$$set({i:e}),pe()}get actual_pinned_columns(){return this.$$.ctx[3]}set actual_pinned_columns(e){this.$$set({actual_pinned_columns:e}),pe()}get header_edit(){return this.$$.ctx[4]}set header_edit(e){this.$$set({header_edit:e}),pe()}get selected_header(){return this.$$.ctx[5]}set selected_header(e){this.$$set({selected_header:e}),pe()}get headers(){return this.$$.ctx[6]}set headers(e){this.$$set({headers:e}),pe()}get get_cell_width(){return this.$$.ctx[7]}set get_cell_width(e){this.$$set({get_cell_width:e}),pe()}get handle_header_click(){return this.$$.ctx[8]}set handle_header_click(e){this.$$set({handle_header_click:e}),pe()}get toggle_header_menu(){return this.$$.ctx[9]}set toggle_header_menu(e){this.$$set({toggle_header_menu:e}),pe()}get end_header_edit(){return this.$$.ctx[10]}set end_header_edit(e){this.$$set({end_header_edit:e}),pe()}get sort_columns(){return this.$$.ctx[11]}set sort_columns(e){this.$$set({sort_columns:e}),pe()}get filter_columns(){return this.$$.ctx[24]}set filter_columns(e){this.$$set({filter_columns:e}),pe()}get latex_delimiters(){return this.$$.ctx[12]}set latex_delimiters(e){this.$$set({latex_delimiters:e}),pe()}get line_breaks(){return this.$$.ctx[13]}set line_breaks(e){this.$$set({line_breaks:e}),pe()}get max_chars(){return this.$$.ctx[14]}set max_chars(e){this.$$set({max_chars:e}),pe()}get editable(){return this.$$.ctx[15]}set editable(e){this.$$set({editable:e}),pe()}get i18n(){return this.$$.ctx[16]}set i18n(e){this.$$set({i18n:e}),pe()}get el(){return this.$$.ctx[1]}set el(e){this.$$set({el:e}),pe()}get is_static(){return this.$$.ctx[17]}set is_static(e){this.$$set({is_static:e}),pe()}get col_count(){return this.$$.ctx[25]}set col_count(e){this.$$set({col_count:e}),pe()}}const{SvelteComponent:nu,add_flush_callback:Bi,append:zi,attr:tt,bind:Di,binding_callbacks:hn,check_outros:iu,create_component:Gs,destroy_component:Qs,detach:su,element:Ti,flush:ie,group_outros:ru,init:ou,insert:au,is_function:_u,listen:Hi,mount_component:Zs,prevent_default:uu,run_all:fu,safe_not_equal:cu,space:hu,toggle_class:ve,transition_in:ll,transition_out:kl}=window.__gradio__svelte__internal;function Oi(l){let e,t;return e=new Ys({props:{on_click:l[39]}}),{c(){Gs(e.$$.fragment)},m(n,i){Zs(e,n,i),t=!0},p(n,i){const r={};i[0]&536&&(r.on_click=n[39]),e.$set(r)},i(n){t||(ll(e.$$.fragment,n),t=!0)},o(n){kl(e.$$.fragment,n),t=!1},d(n){Qs(e,n)}}}function du(l){let e,t,n,i,r,s,o=l[19]&&l[10]([l[3],l[4]],l[0],l[19]),f,u,a,_,c,w;function L(p){l[36](p)}function v(p){l[37](p)}let q={display_value:l[26]!==void 0?l[26]:String(l[1]),latex_delimiters:l[14],line_breaks:l[15],editable:l[19],is_static:l[20],edit:l[17]&&l[17][0]===l[3]&&l[17][1]===l[4],datatype:l[16],max_chars:l[18],i18n:l[21],components:l[22],show_selection_buttons:l[0].length===1&&l[0][0][0]===l[3]&&l[0][0][1]===l[4],coords:[l[3],l[4]],on_select_column:l[23],on_select_row:l[24],is_dragging:l[25],wrap_text:l[27]};l[1]!==void 0&&(q.value=l[1]),l[2].input!==void 0&&(q.el=l[2].input),n=new En({props:q}),hn.push(()=>Di(n,"value",L)),hn.push(()=>Di(n,"el",v)),n.$on("focus",l[38]),n.$on("blur",function(){_u(l[8])&&l[8].apply(this,arguments)});let b=o&&Oi(l);return{c(){e=Ti("td"),t=Ti("div"),Gs(n.$$.fragment),s=hu(),b&&b.c(),tt(t,"class","cell-wrap svelte-v1pjjd"),tt(e,"tabindex",f=l[4]<l[5]?-1:0),tt(e,"data-row",l[3]),tt(e,"data-col",l[4]),tt(e,"data-testid",u=`cell-${l[3]}-${l[4]}`),tt(e,"style",a="width: "+l[6](l[4])+"; left: "+l[33](l[4])+"; "+(l[13]||"")),tt(e,"class","svelte-v1pjjd"),ve(e,"pinned-column",l[4]<l[5]),ve(e,"last-pinned",l[4]===l[5]-1),ve(e,"flash",l[11]&&l[32]),ve(e,"cell-selected",l[32]),ve(e,"no-top",l[31]),ve(e,"no-bottom",l[30]),ve(e,"no-left",l[29]),ve(e,"no-right",l[28]),ve(e,"menu-active",l[12]&&l[12].row===l[3]&&l[12].col===l[4]),ve(e,"dragging",l[25])},m(p,k){au(p,e,k),zi(e,t),Zs(n,t,null),zi(t,s),b&&b.m(t,null),l[40](e),_=!0,c||(w=[Hi(e,"mousedown",l[41]),Hi(e,"contextmenu",uu(l[42]))],c=!0)},p(p,k){l=p;const y={};k[0]&67108866&&(y.display_value=l[26]!==void 0?l[26]:String(l[1])),k[0]&16384&&(y.latex_delimiters=l[14]),k[0]&32768&&(y.line_breaks=l[15]),k[0]&524288&&(y.editable=l[19]),k[0]&1048576&&(y.is_static=l[20]),k[0]&131096&&(y.edit=l[17]&&l[17][0]===l[3]&&l[17][1]===l[4]),k[0]&65536&&(y.datatype=l[16]),k[0]&262144&&(y.max_chars=l[18]),k[0]&2097152&&(y.i18n=l[21]),k[0]&4194304&&(y.components=l[22]),k[0]&25&&(y.show_selection_buttons=l[0].length===1&&l[0][0][0]===l[3]&&l[0][0][1]===l[4]),k[0]&24&&(y.coords=[l[3],l[4]]),k[0]&8388608&&(y.on_select_column=l[23]),k[0]&16777216&&(y.on_select_row=l[24]),k[0]&33554432&&(y.is_dragging=l[25]),k[0]&134217728&&(y.wrap_text=l[27]),!i&&k[0]&2&&(i=!0,y.value=l[1],Bi(()=>i=!1)),!r&&k[0]&4&&(r=!0,y.el=l[2].input,Bi(()=>r=!1)),n.$set(y),k[0]&525337&&(o=l[19]&&l[10]([l[3],l[4]],l[0],l[19])),o?b?(b.p(l,k),k[0]&525337&&ll(b,1)):(b=Oi(l),b.c(),ll(b,1),b.m(t,null)):b&&(ru(),kl(b,1,1,()=>{b=null}),iu()),(!_||k[0]&48&&f!==(f=l[4]<l[5]?-1:0))&&tt(e,"tabindex",f),(!_||k[0]&8)&&tt(e,"data-row",l[3]),(!_||k[0]&16)&&tt(e,"data-col",l[4]),(!_||k[0]&24&&u!==(u=`cell-${l[3]}-${l[4]}`))&&tt(e,"data-testid",u),(!_||k[0]&8272&&a!==(a="width: "+l[6](l[4])+"; left: "+l[33](l[4])+"; "+(l[13]||"")))&&tt(e,"style",a),(!_||k[0]&48)&&ve(e,"pinned-column",l[4]<l[5]),(!_||k[0]&48)&&ve(e,"last-pinned",l[4]===l[5]-1),(!_||k[0]&2048|k[1]&2)&&ve(e,"flash",l[11]&&l[32]),(!_||k[1]&2)&&ve(e,"cell-selected",l[32]),(!_||k[1]&1)&&ve(e,"no-top",l[31]),(!_||k[0]&1073741824)&&ve(e,"no-bottom",l[30]),(!_||k[0]&536870912)&&ve(e,"no-left",l[29]),(!_||k[0]&268435456)&&ve(e,"no-right",l[28]),(!_||k[0]&4120)&&ve(e,"menu-active",l[12]&&l[12].row===l[3]&&l[12].col===l[4]),(!_||k[0]&33554432)&&ve(e,"dragging",l[25])},i(p){_||(ll(n.$$.fragment,p),ll(b),_=!0)},o(p){kl(n.$$.fragment,p),kl(b),_=!1},d(p){p&&su(e),Qs(n),b&&b.d(),l[40](null),c=!1,fu(w)}}}function mu(l,e,t){let n,i,r,s,o,f,{value:u}=e,{index:a}=e,{j:_}=e,{actual_pinned_columns:c}=e,{get_cell_width:w}=e,{handle_cell_click:L}=e,{handle_blur:v}=e,{toggle_cell_menu:q}=e,{is_cell_selected:b}=e,{should_show_cell_menu:p}=e,{selected_cells:k}=e,{copy_flash:y}=e,{active_cell_menu:C}=e,{styling:j}=e,{latex_delimiters:S}=e,{line_breaks:z}=e,{datatype:O}=e,{editing:X}=e,{max_chars:I}=e,{editable:D}=e,{is_static:T=!1}=e,{i18n:Y}=e,{components:ne={}}=e,{el:K}=e,{handle_select_column:W}=e,{handle_select_row:ce}=e,{is_dragging:B}=e,{display_value:Z}=e,{wrap:N=!1}=e;function J(A){return A>=c?"auto":A===0?"0":`calc(${Array(A).fill(0).map((Nt,x)=>w(x)).join(" + ")})`}function g(A){u=A,t(1,u)}function M(A){l.$$.not_equal(K.input,A)&&(K.input=A,t(2,K))}const m=()=>{const A=a,R=_;k.some(([Nt,x])=>Nt===A&&x===R)||t(0,k=[[A,R]])},F=A=>q(A,a,_);function Ee(A){hn[A?"unshift":"push"](()=>{K.cell=A,t(2,K)})}const mt=A=>L(A,a,_),$=A=>q(A,a,_);return l.$$set=A=>{"value"in A&&t(1,u=A.value),"index"in A&&t(3,a=A.index),"j"in A&&t(4,_=A.j),"actual_pinned_columns"in A&&t(5,c=A.actual_pinned_columns),"get_cell_width"in A&&t(6,w=A.get_cell_width),"handle_cell_click"in A&&t(7,L=A.handle_cell_click),"handle_blur"in A&&t(8,v=A.handle_blur),"toggle_cell_menu"in A&&t(9,q=A.toggle_cell_menu),"is_cell_selected"in A&&t(34,b=A.is_cell_selected),"should_show_cell_menu"in A&&t(10,p=A.should_show_cell_menu),"selected_cells"in A&&t(0,k=A.selected_cells),"copy_flash"in A&&t(11,y=A.copy_flash),"active_cell_menu"in A&&t(12,C=A.active_cell_menu),"styling"in A&&t(13,j=A.styling),"latex_delimiters"in A&&t(14,S=A.latex_delimiters),"line_breaks"in A&&t(15,z=A.line_breaks),"datatype"in A&&t(16,O=A.datatype),"editing"in A&&t(17,X=A.editing),"max_chars"in A&&t(18,I=A.max_chars),"editable"in A&&t(19,D=A.editable),"is_static"in A&&t(20,T=A.is_static),"i18n"in A&&t(21,Y=A.i18n),"components"in A&&t(22,ne=A.components),"el"in A&&t(2,K=A.el),"handle_select_column"in A&&t(23,W=A.handle_select_column),"handle_select_row"in A&&t(24,ce=A.handle_select_row),"is_dragging"in A&&t(25,B=A.is_dragging),"display_value"in A&&t(26,Z=A.display_value),"wrap"in A&&t(27,N=A.wrap)},l.$$.update=()=>{l.$$.dirty[0]&25|l.$$.dirty[1]&8&&t(35,n=b([a,_],k||[])),l.$$.dirty[0]&25&&t(32,i=No([a,_],k)),l.$$.dirty[1]&16&&t(31,r=n.includes("no-top")),l.$$.dirty[1]&16&&t(30,s=n.includes("no-bottom")),l.$$.dirty[1]&16&&t(29,o=n.includes("no-left")),l.$$.dirty[1]&16&&t(28,f=n.includes("no-right"))},[k,u,K,a,_,c,w,L,v,q,p,y,C,j,S,z,O,X,I,D,T,Y,ne,W,ce,B,Z,N,f,o,s,r,i,J,b,n,g,M,m,F,Ee,mt,$]}class gu extends nu{constructor(e){super(),ou(this,e,mu,du,cu,{value:1,index:3,j:4,actual_pinned_columns:5,get_cell_width:6,handle_cell_click:7,handle_blur:8,toggle_cell_menu:9,is_cell_selected:34,should_show_cell_menu:10,selected_cells:0,copy_flash:11,active_cell_menu:12,styling:13,latex_delimiters:14,line_breaks:15,datatype:16,editing:17,max_chars:18,editable:19,is_static:20,i18n:21,components:22,el:2,handle_select_column:23,handle_select_row:24,is_dragging:25,display_value:26,wrap:27},null,[-1,-1])}get value(){return this.$$.ctx[1]}set value(e){this.$$set({value:e}),ie()}get index(){return this.$$.ctx[3]}set index(e){this.$$set({index:e}),ie()}get j(){return this.$$.ctx[4]}set j(e){this.$$set({j:e}),ie()}get actual_pinned_columns(){return this.$$.ctx[5]}set actual_pinned_columns(e){this.$$set({actual_pinned_columns:e}),ie()}get get_cell_width(){return this.$$.ctx[6]}set get_cell_width(e){this.$$set({get_cell_width:e}),ie()}get handle_cell_click(){return this.$$.ctx[7]}set handle_cell_click(e){this.$$set({handle_cell_click:e}),ie()}get handle_blur(){return this.$$.ctx[8]}set handle_blur(e){this.$$set({handle_blur:e}),ie()}get toggle_cell_menu(){return this.$$.ctx[9]}set toggle_cell_menu(e){this.$$set({toggle_cell_menu:e}),ie()}get is_cell_selected(){return this.$$.ctx[34]}set is_cell_selected(e){this.$$set({is_cell_selected:e}),ie()}get should_show_cell_menu(){return this.$$.ctx[10]}set should_show_cell_menu(e){this.$$set({should_show_cell_menu:e}),ie()}get selected_cells(){return this.$$.ctx[0]}set selected_cells(e){this.$$set({selected_cells:e}),ie()}get copy_flash(){return this.$$.ctx[11]}set copy_flash(e){this.$$set({copy_flash:e}),ie()}get active_cell_menu(){return this.$$.ctx[12]}set active_cell_menu(e){this.$$set({active_cell_menu:e}),ie()}get styling(){return this.$$.ctx[13]}set styling(e){this.$$set({styling:e}),ie()}get latex_delimiters(){return this.$$.ctx[14]}set latex_delimiters(e){this.$$set({latex_delimiters:e}),ie()}get line_breaks(){return this.$$.ctx[15]}set line_breaks(e){this.$$set({line_breaks:e}),ie()}get datatype(){return this.$$.ctx[16]}set datatype(e){this.$$set({datatype:e}),ie()}get editing(){return this.$$.ctx[17]}set editing(e){this.$$set({editing:e}),ie()}get max_chars(){return this.$$.ctx[18]}set max_chars(e){this.$$set({max_chars:e}),ie()}get editable(){return this.$$.ctx[19]}set editable(e){this.$$set({editable:e}),ie()}get is_static(){return this.$$.ctx[20]}set is_static(e){this.$$set({is_static:e}),ie()}get i18n(){return this.$$.ctx[21]}set i18n(e){this.$$set({i18n:e}),ie()}get components(){return this.$$.ctx[22]}set components(e){this.$$set({components:e}),ie()}get el(){return this.$$.ctx[2]}set el(e){this.$$set({el:e}),ie()}get handle_select_column(){return this.$$.ctx[23]}set handle_select_column(e){this.$$set({handle_select_column:e}),ie()}get handle_select_row(){return this.$$.ctx[24]}set handle_select_row(e){this.$$set({handle_select_row:e}),ie()}get is_dragging(){return this.$$.ctx[25]}set is_dragging(e){this.$$set({is_dragging:e}),ie()}get display_value(){return this.$$.ctx[26]}set display_value(e){this.$$set({display_value:e}),ie()}get wrap(){return this.$$.ctx[27]}set wrap(e){this.$$set({wrap:e}),ie()}}const{SvelteComponent:bu,attr:Ii,detach:wu,element:ku,flush:pu,init:vu,insert:yu,is_function:Cu,listen:Su,noop:Pi,safe_not_equal:Nu}=window.__gradio__svelte__internal;function qu(l){let e,t,n;return{c(){e=ku("button"),e.textContent="+",Ii(e,"class","add-row-button svelte-jkwuz7"),Ii(e,"aria-label","Add row")},m(i,r){yu(i,e,r),t||(n=Su(e,"click",function(){Cu(l[0])&&l[0].apply(this,arguments)}),t=!0)},p(i,[r]){l=i},i:Pi,o:Pi,d(i){i&&wu(e),t=!1,n()}}}function Mu(l,e,t){let{on_click:n}=e;return l.$$set=i=>{"on_click"in i&&t(0,n=i.on_click)},[n]}class Au extends bu{constructor(e){super(),vu(this,e,Mu,qu,Nu,{on_click:0})}get on_click(){return this.$$.ctx[0]}set on_click(e){this.$$set({on_click:e}),pu()}}const{ResizeObserverSingleton:Eu,SvelteComponent:Lu,add_iframe_resize_listener:Ri,add_render_callback:Ji,append:Mt,attr:bl,binding_callbacks:Fi,check_outros:$s,create_slot:dn,detach:jl,element:Dt,empty:xs,ensure_array_like:Ki,flush:wt,get_all_dirty_from_scope:mn,get_slot_changes:gn,group_outros:er,init:ju,insert:Bl,listen:Bu,outro_and_destroy_block:zu,resize_observer_content_box:Du,safe_not_equal:Tu,set_style:We,space:Wi,text:Hu,toggle_class:Ui,transition_in:Bt,transition_out:It,update_keyed_each:Ou,update_slot_base:bn}=window.__gradio__svelte__internal,{onMount:Iu,tick:Pu,createEventDispatcher:Ru}=window.__gradio__svelte__internal,Ju=l=>({}),Vi=l=>({});function Yi(l,e,t){const n=l.slice();return n[37]=e[t],n}const Fu=l=>({item:l[0]&1024,index:l[0]&1024}),Xi=l=>({item:l[37].data,index:l[37].index}),Ku=l=>({}),Gi=l=>({});function Qi(l){let e=[],t=new Map,n,i,r=Ki(l[10]);const s=o=>o[37].data[0].id;for(let o=0;o<r.length;o+=1){let f=Yi(l,r,o),u=s(f);t.set(u,e[o]=Zi(u,f))}return{c(){for(let o=0;o<e.length;o+=1)e[o].c();n=xs()},m(o,f){for(let u=0;u<e.length;u+=1)e[u]&&e[u].m(o,f);Bl(o,n,f),i=!0},p(o,f){f[0]&8389632&&(r=Ki(o[10]),er(),e=Ou(e,f,s,1,o,r,t,n.parentNode,zu,Zi,n,Yi),$s())},i(o){if(!i){for(let f=0;f<r.length;f+=1)Bt(e[f]);i=!0}},o(o){for(let f=0;f<e.length;f+=1)It(e[f]);i=!1},d(o){o&&jl(n);for(let f=0;f<e.length;f+=1)e[f].d(o)}}}function Wu(l){let e;return{c(){e=Hu(`Missing Table Row
						`)},m(t,n){Bl(t,e,n)},d(t){t&&jl(e)}}}function Zi(l,e){let t,n;const i=e[24].tbody,r=dn(i,e,e[23],Xi),s=r||Wu();return{key:l,first:null,c(){t=xs(),s&&s.c(),this.first=t},m(o,f){Bl(o,t,f),s&&s.m(o,f),n=!0},p(o,f){e=o,r&&r.p&&(!n||f[0]&8389632)&&bn(r,i,e,e[23],n?gn(i,e[23],f,Fu):mn(e[23]),Xi)},i(o){n||(Bt(s,o),n=!0)},o(o){It(s,o),n=!1},d(o){o&&jl(t),s&&s.d(o)}}}function Uu(l){let e,t,n,i,r,s,o,f,u,a,_,c,w,L;const v=l[24].thead,q=dn(v,l,l[23],Gi);let b=l[10].length&&l[10][0].data.length&&Qi(l);const p=l[24].tfoot,k=dn(p,l,l[23],Vi);return{c(){e=Dt("svelte-virtual-table-viewport"),t=Dt("div"),n=Dt("table"),i=Dt("thead"),q&&q.c(),s=Wi(),o=Dt("tbody"),b&&b.c(),f=Wi(),u=Dt("tfoot"),k&&k.c(),bl(i,"class","thead svelte-zsmsrz"),Ji(()=>l[25].call(i)),bl(o,"class","tbody svelte-zsmsrz"),bl(u,"class","tfoot svelte-zsmsrz"),Ji(()=>l[27].call(u)),bl(n,"class","table svelte-zsmsrz"),We(n,"height",Vu),We(n,"--bw-svt-p-top",l[9]+"px"),We(n,"--bw-svt-p-bottom",l[5]+"px"),We(n,"--bw-svt-head-height",l[7]+"px"),We(n,"--bw-svt-foot-height",l[8]+"px"),We(n,"--bw-svt-avg-row-height",l[3]+"px"),We(n,"--max-height",l[1]+"px"),Ui(n,"disable-scroll",l[2])},m(y,C){Bl(y,e,C),Mt(e,t),Mt(t,n),Mt(n,i),q&&q.m(i,null),r=Ri(i,l[25].bind(i)),Mt(n,s),Mt(n,o),b&&b.m(o,null),l[26](o),Mt(n,f),Mt(n,u),k&&k.m(u,null),a=Ri(u,l[27].bind(u)),l[28](n),_=Du.observe(n,l[29].bind(n)),c=!0,w||(L=Bu(n,"scroll",l[11]),w=!0)},p(y,C){q&&q.p&&(!c||C[0]&8388608)&&bn(q,v,y,y[23],c?gn(v,y[23],C,Ku):mn(y[23]),Gi),y[10].length&&y[10][0].data.length?b?(b.p(y,C),C[0]&1024&&Bt(b,1)):(b=Qi(y),b.c(),Bt(b,1),b.m(o,null)):b&&(er(),It(b,1,1,()=>{b=null}),$s()),k&&k.p&&(!c||C[0]&8388608)&&bn(k,p,y,y[23],c?gn(p,y[23],C,Ju):mn(y[23]),Vi),(!c||C[0]&512)&&We(n,"--bw-svt-p-top",y[9]+"px"),(!c||C[0]&32)&&We(n,"--bw-svt-p-bottom",y[5]+"px"),(!c||C[0]&128)&&We(n,"--bw-svt-head-height",y[7]+"px"),(!c||C[0]&256)&&We(n,"--bw-svt-foot-height",y[8]+"px"),(!c||C[0]&8)&&We(n,"--bw-svt-avg-row-height",y[3]+"px"),(!c||C[0]&2)&&We(n,"--max-height",y[1]+"px"),(!c||C[0]&4)&&Ui(n,"disable-scroll",y[2])},i(y){c||(Bt(q,y),Bt(b),Bt(k,y),c=!0)},o(y){It(q,y),It(b),It(k,y),c=!1},d(y){y&&jl(e),q&&q.d(y),r(),b&&b.d(),l[26](null),k&&k.d(y),a(),l[28](null),_(),w=!1,L()}}}let Vu="100%";function Yu(l,e,t){let n,{$$slots:i={},$$scope:r}=e,{items:s=[]}=e,{max_height:o}=e,{actual_height:f}=e,{table_scrollbar_width:u}=e,{start:a=0}=e,{end:_=20}=e,{selected:c}=e,{disable_scroll:w=!1}=e,{show_scroll_button:L=!1}=e,{viewport:v}=e;Ru();let q=30,b=0,p,k=0,y=0,C=[],j,S,z=0,O=200,X=[],I;const D=typeof window<"u",T=D?window.requestAnimationFrame:g=>g();async function Y(){n.length<a&&await W(n.length-1,{behavior:"auto"});const g=Math.max(0,v.scrollTop);t(16,L=g>100),t(15,u=v.offsetWidth-v.clientWidth);for(let A=0;A<S.length;A+=1)C[a+A]=S[A].getBoundingClientRect().height;let M=0,m=k;for(;M<n.length;){const A=C[M]||q;if(m+A>g-o){t(12,a=M),t(9,z=m-k);break}m+=A,M+=1}let F=k;for(;M<n.length;){const A=C[M]||q;if(F+=A,M+=1,F-k>3*o)break}t(13,_=M);const Ee=n.length-_,mt=v.offsetHeight-v.clientHeight;mt>0&&(F+=mt);let $=C.filter(A=>typeof A=="number");for(t(3,q=$.reduce((A,R)=>A+R,0)/$.length||30),t(5,b=Ee*q),isFinite(b)||t(5,b=2e5),C.length=n.length;M<n.length;)M+=1,C[M]=q;o&&F>o?t(14,f=o):t(14,f=F)}async function ne(g){T(async()=>{if(typeof g!="number")return;const M=typeof g!="number"?!1:K(g);M!==!0&&(M==="back"&&await W(g,{behavior:"instant"}),M==="forwards"&&await W(g,{behavior:"instant"},!0))})}function K(g){const M=S&&S[g-a];if(!M&&g<a)return"back";if(!M&&g>=_-1)return"forwards";const{top:m}=v.getBoundingClientRect(),{top:F,bottom:Ee}=M.getBoundingClientRect();return F-m<37?"back":Ee-m>O?"forwards":!0}async function W(g,M,m=!1){await Pu();const F=q;let Ee=g*F;m&&(Ee=Ee-O+F+k);const mt=v.offsetHeight-v.clientHeight;mt>0&&(Ee+=mt);const $={top:Ee,behavior:"smooth",...M};v.scrollTo($)}Iu(()=>{S=p.children,t(20,j=!0)});function ce(){k=this.offsetHeight,t(7,k)}function B(g){Fi[g?"unshift":"push"](()=>{p=g,t(6,p)})}function Z(){y=this.offsetHeight,t(8,y)}function N(g){Fi[g?"unshift":"push"](()=>{v=g,t(0,v)})}function J(){I=Eu.entries.get(this)?.contentRect,t(4,I)}return l.$$set=g=>{"items"in g&&t(17,s=g.items),"max_height"in g&&t(1,o=g.max_height),"actual_height"in g&&t(14,f=g.actual_height),"table_scrollbar_width"in g&&t(15,u=g.table_scrollbar_width),"start"in g&&t(12,a=g.start),"end"in g&&t(13,_=g.end),"selected"in g&&t(18,c=g.selected),"disable_scroll"in g&&t(2,w=g.disable_scroll),"show_scroll_button"in g&&t(16,L=g.show_scroll_button),"viewport"in g&&t(0,v=g.viewport),"$$scope"in g&&t(23,r=g.$$scope)},l.$$.update=()=>{l.$$.dirty[0]&16&&t(21,O=I?.height||200),l.$$.dirty[0]&131072&&t(22,n=s),l.$$.dirty[0]&7340033&&j&&O&&v.offsetParent&&T(Y),l.$$.dirty[0]&262144&&ne(c),l.$$.dirty[0]&4206602&&t(10,X=D?n.slice(a,_).map((g,M)=>({index:M+a,data:g})):n.slice(0,o/n.length*q+1).map((g,M)=>({index:M+a,data:g})))},[v,o,w,q,I,b,p,k,y,z,X,Y,a,_,f,u,L,s,c,W,j,O,n,r,i,ce,B,Z,N,J]}class Xu extends Lu{constructor(e){super(),ju(this,e,Yu,Uu,Tu,{items:17,max_height:1,actual_height:14,table_scrollbar_width:15,start:12,end:13,selected:18,disable_scroll:2,show_scroll_button:16,viewport:0,scroll_to_index:19},null,[-1,-1])}get items(){return this.$$.ctx[17]}set items(e){this.$$set({items:e}),wt()}get max_height(){return this.$$.ctx[1]}set max_height(e){this.$$set({max_height:e}),wt()}get actual_height(){return this.$$.ctx[14]}set actual_height(e){this.$$set({actual_height:e}),wt()}get table_scrollbar_width(){return this.$$.ctx[15]}set table_scrollbar_width(e){this.$$set({table_scrollbar_width:e}),wt()}get start(){return this.$$.ctx[12]}set start(e){this.$$set({start:e}),wt()}get end(){return this.$$.ctx[13]}set end(e){this.$$set({end:e}),wt()}get selected(){return this.$$.ctx[18]}set selected(e){this.$$set({selected:e}),wt()}get disable_scroll(){return this.$$.ctx[2]}set disable_scroll(e){this.$$set({disable_scroll:e}),wt()}get show_scroll_button(){return this.$$.ctx[16]}set show_scroll_button(e){this.$$set({show_scroll_button:e}),wt()}get viewport(){return this.$$.ctx[0]}set viewport(e){this.$$set({viewport:e}),wt()}get scroll_to_index(){return this.$$.ctx[19]}}const{SvelteComponent:Gu,append:we,attr:Ne,binding_callbacks:Qu,bubble:Zu,create_component:$i,destroy_component:xi,destroy_each:$u,detach:Bn,element:Ue,ensure_array_like:es,flush:xu,init:ef,insert:zn,listen:Ht,mount_component:ts,run_all:tf,safe_not_equal:lf,set_data:wn,space:yt,stop_propagation:pl,text:kn,transition_in:ls,transition_out:ns}=window.__gradio__svelte__internal,{onMount:nf}=window.__gradio__svelte__internal;function is(l,e,t){const n=l.slice();return n[15]=e[t],n}function ss(l){let e,t=es(l[6][l[2]]),n=[];for(let i=0;i<t.length;i+=1)n[i]=rs(is(l,t,i));return{c(){e=Ue("div");for(let i=0;i<n.length;i+=1)n[i].c();Ne(e,"class","dropdown-filter-options svelte-1nf5kyf")},m(i,r){zn(i,e,r);for(let s=0;s<n.length;s+=1)n[s]&&n[s].m(e,null)},p(i,r){if(r&92){t=es(i[6][i[2]]);let s;for(s=0;s<t.length;s+=1){const o=is(i,t,s);n[s]?n[s].p(o,r):(n[s]=rs(o),n[s].c(),n[s].m(e,null))}for(;s<n.length;s+=1)n[s].d(1);n.length=t.length}},d(i){i&&Bn(e),$u(n,i)}}}function rs(l){let e,t=l[15]+"",n,i,r,s;function o(){return l[11](l[15])}return{c(){e=Ue("button"),n=kn(t),i=yt(),Ne(e,"class","filter-option svelte-1nf5kyf")},m(f,u){zn(f,e,u),we(e,n),we(e,i),r||(s=Ht(e,"click",pl(o)),r=!0)},p(f,u){l=f,u&4&&t!==(t=l[15]+"")&&wn(n,t)},d(f){f&&Bn(e),r=!1,s()}}}function sf(l){let e,t,n,i,r,s,o,f,u,a,_,c,w,L,v,q,b,p,k,y,C,j,S,z,O,X,I;b=new uo({});let D=l[4]&&ss(l);return z=new qn({}),{c(){e=Ue("div"),t=Ue("div"),n=yt(),i=Ue("div"),r=Ue("div"),s=Ue("span"),s.textContent="Filter as",o=yt(),f=Ue("button"),u=kn(l[2]),_=yt(),c=Ue("div"),w=Ue("div"),L=Ue("button"),v=kn(l[3]),q=yt(),$i(b.$$.fragment),k=yt(),D&&D.c(),y=yt(),C=Ue("input"),j=yt(),S=Ue("button"),$i(z.$$.fragment),Ne(t,"class","background svelte-1nf5kyf"),Ne(s,"class","svelte-1nf5kyf"),Ne(f,"aria-label",a=`Change filter type. Filtering ${l[2]}s`),Ne(f,"class","svelte-1nf5kyf"),Ne(r,"class","filter-datatype-container svelte-1nf5kyf"),Ne(L,"aria-label",p=`Change filter. Using '${l[3]}'`),Ne(L,"class","svelte-1nf5kyf"),Ne(w,"class","filter-dropdown"),Ne(C,"type","text"),C.value=l[5],Ne(C,"placeholder","Type a value"),Ne(C,"class","filter-input svelte-1nf5kyf"),Ne(c,"class","input-container svelte-1nf5kyf"),Ne(S,"class","check-button svelte-1nf5kyf"),Ne(i,"class","filter-menu svelte-1nf5kyf")},m(T,Y){zn(T,e,Y),we(e,t),we(e,n),we(e,i),we(i,r),we(r,s),we(r,o),we(r,f),we(f,u),we(i,_),we(i,c),we(c,w),we(w,L),we(L,v),we(L,q),ts(b,L,null),we(w,k),D&&D.m(w,null),we(c,y),we(c,C),we(i,j),we(i,S),ts(z,S,null),l[13](i),O=!0,X||(I=[Ht(f,"click",pl(l[9])),Ht(L,"click",pl(l[10])),Ht(C,"click",pl(l[8])),Ht(C,"input",l[7]),Ht(S,"click",l[12])],X=!0)},p(T,[Y]){(!O||Y&4)&&wn(u,T[2]),(!O||Y&4&&a!==(a=`Change filter type. Filtering ${T[2]}s`))&&Ne(f,"aria-label",a),(!O||Y&8)&&wn(v,T[3]),(!O||Y&8&&p!==(p=`Change filter. Using '${T[3]}'`))&&Ne(L,"aria-label",p),T[4]?D?D.p(T,Y):(D=ss(T),D.c(),D.m(w,null)):D&&(D.d(1),D=null),(!O||Y&32&&C.value!==T[5])&&(C.value=T[5])},i(T){O||(ls(b.$$.fragment,T),ls(z.$$.fragment,T),O=!0)},o(T){ns(b.$$.fragment,T),ns(z.$$.fragment,T),O=!1},d(T){T&&Bn(e),xi(b),D&&D.d(),xi(z),l[13](null),X=!1,tf(I)}}}function rf(l,e,t){let{on_filter:n=()=>{}}=e,i,r="string",s="Contains",o=!1,f="";const u={string:["Contains","Does not contain","Starts with","Ends with","Is","Is not","Is empty","Is not empty"],number:["=","≠",">","<","≥","≤","Is empty","Is not empty"]};nf(()=>{a()});function a(){if(!i)return;const p=window.innerWidth,k=window.innerHeight,y=i.getBoundingClientRect(),C=(p-y.width)/2,j=(k-y.height)/2;t(1,i.style.left=`${C}px`,i),t(1,i.style.top=`${j}px`,i)}function _(p){const k=p.target;t(5,f=k.value)}function c(p){Zu.call(this,l,p)}const w=()=>{t(2,r=r==="string"?"number":"string"),t(3,s=u[r][0])},L=()=>t(4,o=!o),v=p=>{t(3,s=p),t(4,o=!o)},q=()=>n(r,s,f);function b(p){Qu[p?"unshift":"push"](()=>{i=p,t(1,i)})}return l.$$set=p=>{"on_filter"in p&&t(0,n=p.on_filter)},[n,i,r,s,o,f,u,_,c,w,L,v,q,b]}class of extends Gu{constructor(e){super(),ef(this,e,rf,sf,lf,{on_filter:0})}get on_filter(){return this.$$.ctx[0]}set on_filter(e){this.$$set({on_filter:e}),xu()}}const{SvelteComponent:af,append:oe,attr:Q,binding_callbacks:_f,check_outros:Pt,create_component:nt,destroy_component:it,detach:ae,element:Oe,empty:Dn,flush:ge,group_outros:Rt,init:uf,insert:_e,is_function:Sl,listen:dt,mount_component:st,run_all:Tn,safe_not_equal:ff,set_data:Xe,space:he,stop_propagation:cf,text:Ge,toggle_class:Tt,transition_in:te,transition_out:me}=window.__gradio__svelte__internal,{onMount:hf}=window.__gradio__svelte__internal;function os(l){let e,t,n,i=l[15]("dataframe.sort_ascending")+"",r,s,o,f,u,a,_=l[15]("dataframe.sort_descending")+"",c,w,L,v,q,b,p=l[15]("dataframe.clear_sort")+"",k,y,C,j,S,z=l[15]("dataframe.filter")+"",O,X,I,D,T,Y,ne=l[15]("dataframe.clear_filter")+"",K,W,ce,B;t=new lt({props:{icon:"sort-asc"}});let Z=l[10]==="asc"&&l[11]!==null&&as(l);u=new lt({props:{icon:"sort-desc"}});let N=l[10]==="desc"&&l[11]!==null&&_s(l);q=new lt({props:{icon:"clear-sort"}}),j=new lt({props:{icon:"filter"}});let J=l[14]&&us();return T=new lt({props:{icon:"clear-filter"}}),{c(){e=Oe("button"),nt(t.$$.fragment),n=he(),r=Ge(i),s=he(),Z&&Z.c(),o=he(),f=Oe("button"),nt(u.$$.fragment),a=he(),c=Ge(_),w=he(),N&&N.c(),L=he(),v=Oe("button"),nt(q.$$.fragment),b=he(),k=Ge(p),y=he(),C=Oe("button"),nt(j.$$.fragment),S=he(),O=Ge(z),X=he(),J&&J.c(),I=he(),D=Oe("button"),nt(T.$$.fragment),Y=he(),K=Ge(ne),Q(e,"role","menuitem"),Q(e,"class","svelte-42thj4"),Tt(e,"active",l[10]==="asc"),Q(f,"role","menuitem"),Q(f,"class","svelte-42thj4"),Tt(f,"active",l[10]==="desc"),Q(v,"role","menuitem"),Q(v,"class","svelte-42thj4"),Q(C,"role","menuitem"),Q(C,"class","svelte-42thj4"),Tt(C,"active",l[14]||l[17]),Q(D,"role","menuitem"),Q(D,"class","svelte-42thj4")},m(g,M){_e(g,e,M),st(t,e,null),oe(e,n),oe(e,r),oe(e,s),Z&&Z.m(e,null),_e(g,o,M),_e(g,f,M),st(u,f,null),oe(f,a),oe(f,c),oe(f,w),N&&N.m(f,null),_e(g,L,M),_e(g,v,M),st(q,v,null),oe(v,b),oe(v,k),_e(g,y,M),_e(g,C,M),st(j,C,null),oe(C,S),oe(C,O),oe(C,X),J&&J.m(C,null),_e(g,I,M),_e(g,D,M),st(T,D,null),oe(D,Y),oe(D,K),W=!0,ce||(B=[dt(e,"click",l[28]),dt(f,"click",l[29]),dt(v,"click",function(){Sl(l[9])&&l[9].apply(this,arguments)}),dt(C,"click",cf(l[21])),dt(D,"click",function(){Sl(l[13])&&l[13].apply(this,arguments)})],ce=!0)},p(g,M){l=g,(!W||M[0]&32768)&&i!==(i=l[15]("dataframe.sort_ascending")+"")&&Xe(r,i),l[10]==="asc"&&l[11]!==null?Z?Z.p(l,M):(Z=as(l),Z.c(),Z.m(e,null)):Z&&(Z.d(1),Z=null),(!W||M[0]&1024)&&Tt(e,"active",l[10]==="asc"),(!W||M[0]&32768)&&_!==(_=l[15]("dataframe.sort_descending")+"")&&Xe(c,_),l[10]==="desc"&&l[11]!==null?N?N.p(l,M):(N=_s(l),N.c(),N.m(f,null)):N&&(N.d(1),N=null),(!W||M[0]&1024)&&Tt(f,"active",l[10]==="desc"),(!W||M[0]&32768)&&p!==(p=l[15]("dataframe.clear_sort")+"")&&Xe(k,p),(!W||M[0]&32768)&&z!==(z=l[15]("dataframe.filter")+"")&&Xe(O,z),l[14]?J||(J=us(),J.c(),J.m(C,null)):J&&(J.d(1),J=null),(!W||M[0]&147456)&&Tt(C,"active",l[14]||l[17]),(!W||M[0]&32768)&&ne!==(ne=l[15]("dataframe.clear_filter")+"")&&Xe(K,ne)},i(g){W||(te(t.$$.fragment,g),te(u.$$.fragment,g),te(q.$$.fragment,g),te(j.$$.fragment,g),te(T.$$.fragment,g),W=!0)},o(g){me(t.$$.fragment,g),me(u.$$.fragment,g),me(q.$$.fragment,g),me(j.$$.fragment,g),me(T.$$.fragment,g),W=!1},d(g){g&&(ae(e),ae(o),ae(f),ae(L),ae(v),ae(y),ae(C),ae(I),ae(D)),it(t),Z&&Z.d(),it(u),N&&N.d(),it(q),it(j),J&&J.d(),it(T),ce=!1,Tn(B)}}}function as(l){let e,t;return{c(){e=Oe("span"),t=Ge(l[11]),Q(e,"class","priority svelte-42thj4")},m(n,i){_e(n,e,i),oe(e,t)},p(n,i){i[0]&2048&&Xe(t,n[11])},d(n){n&&ae(e)}}}function _s(l){let e,t;return{c(){e=Oe("span"),t=Ge(l[11]),Q(e,"class","priority svelte-42thj4")},m(n,i){_e(n,e,i),oe(e,t)},p(n,i){i[0]&2048&&Xe(t,n[11])},d(n){n&&ae(e)}}}function us(l){let e;return{c(){e=Oe("span"),e.textContent="1",Q(e,"class","priority svelte-42thj4")},m(t,n){_e(t,e,n)},d(t){t&&ae(e)}}}function fs(l){let e,t,n,i=l[15]("dataframe.add_row_above")+"",r,s,o,f,u,a=l[15]("dataframe.add_row_below")+"",_,c,w,L,v,q;t=new lt({props:{icon:"add-row-above"}}),f=new lt({props:{icon:"add-row-below"}});let b=l[6]&&cs(l);return{c(){e=Oe("button"),nt(t.$$.fragment),n=he(),r=Ge(i),s=he(),o=Oe("button"),nt(f.$$.fragment),u=he(),_=Ge(a),c=he(),b&&b.c(),w=Dn(),Q(e,"role","menuitem"),Q(e,"aria-label","Add row above"),Q(e,"class","svelte-42thj4"),Q(o,"role","menuitem"),Q(o,"aria-label","Add row below"),Q(o,"class","svelte-42thj4")},m(p,k){_e(p,e,k),st(t,e,null),oe(e,n),oe(e,r),_e(p,s,k),_e(p,o,k),st(f,o,null),oe(o,u),oe(o,_),_e(p,c,k),b&&b.m(p,k),_e(p,w,k),L=!0,v||(q=[dt(e,"click",l[30]),dt(o,"click",l[31])],v=!0)},p(p,k){(!L||k[0]&32768)&&i!==(i=p[15]("dataframe.add_row_above")+"")&&Xe(r,i),(!L||k[0]&32768)&&a!==(a=p[15]("dataframe.add_row_below")+"")&&Xe(_,a),p[6]?b?(b.p(p,k),k[0]&64&&te(b,1)):(b=cs(p),b.c(),te(b,1),b.m(w.parentNode,w)):b&&(Rt(),me(b,1,1,()=>{b=null}),Pt())},i(p){L||(te(t.$$.fragment,p),te(f.$$.fragment,p),te(b),L=!0)},o(p){me(t.$$.fragment,p),me(f.$$.fragment,p),me(b),L=!1},d(p){p&&(ae(e),ae(s),ae(o),ae(c),ae(w)),it(t),it(f),b&&b.d(p),v=!1,Tn(q)}}}function cs(l){let e,t,n,i=l[15]("dataframe.delete_row")+"",r,s,o,f;return t=new lt({props:{icon:"delete-row"}}),{c(){e=Oe("button"),nt(t.$$.fragment),n=he(),r=Ge(i),Q(e,"role","menuitem"),Q(e,"class","delete svelte-42thj4"),Q(e,"aria-label","Delete row")},m(u,a){_e(u,e,a),st(t,e,null),oe(e,n),oe(e,r),s=!0,o||(f=dt(e,"click",function(){Sl(l[4])&&l[4].apply(this,arguments)}),o=!0)},p(u,a){l=u,(!s||a[0]&32768)&&i!==(i=l[15]("dataframe.delete_row")+"")&&Xe(r,i)},i(u){s||(te(t.$$.fragment,u),s=!0)},o(u){me(t.$$.fragment,u),s=!1},d(u){u&&ae(e),it(t),o=!1,f()}}}function hs(l){let e,t,n,i=l[15]("dataframe.add_column_left")+"",r,s,o,f,u,a=l[15]("dataframe.add_column_right")+"",_,c,w,L,v,q;t=new lt({props:{icon:"add-column-left"}}),f=new lt({props:{icon:"add-column-right"}});let b=l[7]&&ds(l);return{c(){e=Oe("button"),nt(t.$$.fragment),n=he(),r=Ge(i),s=he(),o=Oe("button"),nt(f.$$.fragment),u=he(),_=Ge(a),c=he(),b&&b.c(),w=Dn(),Q(e,"role","menuitem"),Q(e,"aria-label","Add column to the left"),Q(e,"class","svelte-42thj4"),Q(o,"role","menuitem"),Q(o,"aria-label","Add column to the right"),Q(o,"class","svelte-42thj4")},m(p,k){_e(p,e,k),st(t,e,null),oe(e,n),oe(e,r),_e(p,s,k),_e(p,o,k),st(f,o,null),oe(o,u),oe(o,_),_e(p,c,k),b&&b.m(p,k),_e(p,w,k),L=!0,v||(q=[dt(e,"click",l[32]),dt(o,"click",l[33])],v=!0)},p(p,k){(!L||k[0]&32768)&&i!==(i=p[15]("dataframe.add_column_left")+"")&&Xe(r,i),(!L||k[0]&32768)&&a!==(a=p[15]("dataframe.add_column_right")+"")&&Xe(_,a),p[7]?b?(b.p(p,k),k[0]&128&&te(b,1)):(b=ds(p),b.c(),te(b,1),b.m(w.parentNode,w)):b&&(Rt(),me(b,1,1,()=>{b=null}),Pt())},i(p){L||(te(t.$$.fragment,p),te(f.$$.fragment,p),te(b),L=!0)},o(p){me(t.$$.fragment,p),me(f.$$.fragment,p),me(b),L=!1},d(p){p&&(ae(e),ae(s),ae(o),ae(c),ae(w)),it(t),it(f),b&&b.d(p),v=!1,Tn(q)}}}function ds(l){let e,t,n,i=l[15]("dataframe.delete_column")+"",r,s,o,f;return t=new lt({props:{icon:"delete-column"}}),{c(){e=Oe("button"),nt(t.$$.fragment),n=he(),r=Ge(i),Q(e,"role","menuitem"),Q(e,"class","delete svelte-42thj4"),Q(e,"aria-label","Delete column")},m(u,a){_e(u,e,a),st(t,e,null),oe(e,n),oe(e,r),s=!0,o||(f=dt(e,"click",function(){Sl(l[5])&&l[5].apply(this,arguments)}),o=!0)},p(u,a){l=u,(!s||a[0]&32768)&&i!==(i=l[15]("dataframe.delete_column")+"")&&Xe(r,i)},i(u){s||(te(t.$$.fragment,u),s=!0)},o(u){me(t.$$.fragment,u),s=!1},d(u){u&&ae(e),it(t),o=!1,f()}}}function ms(l){let e,t;return e=new of({props:{on_filter:l[12]}}),{c(){nt(e.$$.fragment)},m(n,i){st(e,n,i),t=!0},p(n,i){const r={};i[0]&4096&&(r.on_filter=n[12]),e.$set(r)},i(n){t||(te(e.$$.fragment,n),t=!0)},o(n){me(e.$$.fragment,n),t=!1},d(n){it(e,n)}}}function df(l){let e,t,n,i,r,s,o=l[20]&&os(l),f=!l[20]&&l[19]&&fs(l),u=l[18]&&hs(l),a=l[17]&&ms(l);return{c(){e=Oe("div"),o&&o.c(),t=he(),f&&f.c(),n=he(),u&&u.c(),i=he(),a&&a.c(),r=Dn(),Q(e,"class","cell-menu svelte-42thj4"),Q(e,"role","menu")},m(_,c){_e(_,e,c),o&&o.m(e,null),oe(e,t),f&&f.m(e,null),oe(e,n),u&&u.m(e,null),l[34](e),_e(_,i,c),a&&a.m(_,c),_e(_,r,c),s=!0},p(_,c){_[20]?o?(o.p(_,c),c[0]&1048576&&te(o,1)):(o=os(_),o.c(),te(o,1),o.m(e,t)):o&&(Rt(),me(o,1,1,()=>{o=null}),Pt()),!_[20]&&_[19]?f?(f.p(_,c),c[0]&1572864&&te(f,1)):(f=fs(_),f.c(),te(f,1),f.m(e,n)):f&&(Rt(),me(f,1,1,()=>{f=null}),Pt()),_[18]?u?(u.p(_,c),c[0]&262144&&te(u,1)):(u=hs(_),u.c(),te(u,1),u.m(e,null)):u&&(Rt(),me(u,1,1,()=>{u=null}),Pt()),_[17]?a?(a.p(_,c),c[0]&131072&&te(a,1)):(a=ms(_),a.c(),te(a,1),a.m(r.parentNode,r)):a&&(Rt(),me(a,1,1,()=>{a=null}),Pt())},i(_){s||(te(o),te(f),te(u),te(a),s=!0)},o(_){me(o),me(f),me(u),me(a),s=!1},d(_){_&&(ae(e),ae(i),ae(r)),o&&o.d(),f&&f.d(),u&&u.d(),l[34](null),a&&a.d(_)}}}function mf(l,e,t){let n,i,r,{x:s}=e,{y:o}=e,{on_add_row_above:f}=e,{on_add_row_below:u}=e,{on_add_column_left:a}=e,{on_add_column_right:_}=e,{row:c}=e,{col_count:w}=e,{row_count:L}=e,{on_delete_row:v}=e,{on_delete_col:q}=e,{can_delete_rows:b}=e,{can_delete_cols:p}=e,{on_sort:k=()=>{}}=e,{on_clear_sort:y=()=>{}}=e,{sort_direction:C=null}=e,{sort_priority:j=null}=e,{on_filter:S=()=>{}}=e,{on_clear_filter:z=()=>{}}=e,{filter_active:O=null}=e,{editable:X=!0}=e,{i18n:I}=e,D,T=null;hf(()=>{Y()});function Y(){if(!D)return;const g=window.innerWidth,M=window.innerHeight,m=D.getBoundingClientRect();let F=s-30,Ee=o-20;F+m.width>g&&(F=s-m.width+10),Ee+m.height>M&&(Ee=o-m.height+10),t(16,D.style.left=`${F}px`,D),t(16,D.style.top=`${Ee}px`,D)}function ne(){if(O){S("string","","");return}const g=D.getBoundingClientRect();t(17,T={x:g.right,y:g.top+g.height/2})}const K=()=>k("asc"),W=()=>k("desc"),ce=()=>f(),B=()=>u(),Z=()=>a(),N=()=>_();function J(g){_f[g?"unshift":"push"](()=>{D=g,t(16,D)})}return l.$$set=g=>{"x"in g&&t(22,s=g.x),"y"in g&&t(23,o=g.y),"on_add_row_above"in g&&t(0,f=g.on_add_row_above),"on_add_row_below"in g&&t(1,u=g.on_add_row_below),"on_add_column_left"in g&&t(2,a=g.on_add_column_left),"on_add_column_right"in g&&t(3,_=g.on_add_column_right),"row"in g&&t(24,c=g.row),"col_count"in g&&t(25,w=g.col_count),"row_count"in g&&t(26,L=g.row_count),"on_delete_row"in g&&t(4,v=g.on_delete_row),"on_delete_col"in g&&t(5,q=g.on_delete_col),"can_delete_rows"in g&&t(6,b=g.can_delete_rows),"can_delete_cols"in g&&t(7,p=g.can_delete_cols),"on_sort"in g&&t(8,k=g.on_sort),"on_clear_sort"in g&&t(9,y=g.on_clear_sort),"sort_direction"in g&&t(10,C=g.sort_direction),"sort_priority"in g&&t(11,j=g.sort_priority),"on_filter"in g&&t(12,S=g.on_filter),"on_clear_filter"in g&&t(13,z=g.on_clear_filter),"filter_active"in g&&t(14,O=g.filter_active),"editable"in g&&t(27,X=g.editable),"i18n"in g&&t(15,I=g.i18n)},l.$$.update=()=>{l.$$.dirty[0]&16777216&&t(20,n=c===-1),l.$$.dirty[0]&201326592&&t(19,i=X&&L[1]==="dynamic"),l.$$.dirty[0]&167772160&&t(18,r=X&&w[1]==="dynamic")},[f,u,a,_,v,q,b,p,k,y,C,j,S,z,O,I,D,T,r,i,n,ne,s,o,c,w,L,X,K,W,ce,B,Z,N,J]}class gf extends af{constructor(e){super(),uf(this,e,mf,df,ff,{x:22,y:23,on_add_row_above:0,on_add_row_below:1,on_add_column_left:2,on_add_column_right:3,row:24,col_count:25,row_count:26,on_delete_row:4,on_delete_col:5,can_delete_rows:6,can_delete_cols:7,on_sort:8,on_clear_sort:9,sort_direction:10,sort_priority:11,on_filter:12,on_clear_filter:13,filter_active:14,editable:27,i18n:15},null,[-1,-1])}get x(){return this.$$.ctx[22]}set x(e){this.$$set({x:e}),ge()}get y(){return this.$$.ctx[23]}set y(e){this.$$set({y:e}),ge()}get on_add_row_above(){return this.$$.ctx[0]}set on_add_row_above(e){this.$$set({on_add_row_above:e}),ge()}get on_add_row_below(){return this.$$.ctx[1]}set on_add_row_below(e){this.$$set({on_add_row_below:e}),ge()}get on_add_column_left(){return this.$$.ctx[2]}set on_add_column_left(e){this.$$set({on_add_column_left:e}),ge()}get on_add_column_right(){return this.$$.ctx[3]}set on_add_column_right(e){this.$$set({on_add_column_right:e}),ge()}get row(){return this.$$.ctx[24]}set row(e){this.$$set({row:e}),ge()}get col_count(){return this.$$.ctx[25]}set col_count(e){this.$$set({col_count:e}),ge()}get row_count(){return this.$$.ctx[26]}set row_count(e){this.$$set({row_count:e}),ge()}get on_delete_row(){return this.$$.ctx[4]}set on_delete_row(e){this.$$set({on_delete_row:e}),ge()}get on_delete_col(){return this.$$.ctx[5]}set on_delete_col(e){this.$$set({on_delete_col:e}),ge()}get can_delete_rows(){return this.$$.ctx[6]}set can_delete_rows(e){this.$$set({can_delete_rows:e}),ge()}get can_delete_cols(){return this.$$.ctx[7]}set can_delete_cols(e){this.$$set({can_delete_cols:e}),ge()}get on_sort(){return this.$$.ctx[8]}set on_sort(e){this.$$set({on_sort:e}),ge()}get on_clear_sort(){return this.$$.ctx[9]}set on_clear_sort(e){this.$$set({on_clear_sort:e}),ge()}get sort_direction(){return this.$$.ctx[10]}set sort_direction(e){this.$$set({sort_direction:e}),ge()}get sort_priority(){return this.$$.ctx[11]}set sort_priority(e){this.$$set({sort_priority:e}),ge()}get on_filter(){return this.$$.ctx[12]}set on_filter(e){this.$$set({on_filter:e}),ge()}get on_clear_filter(){return this.$$.ctx[13]}set on_clear_filter(e){this.$$set({on_clear_filter:e}),ge()}get filter_active(){return this.$$.ctx[14]}set filter_active(e){this.$$set({filter_active:e}),ge()}get editable(){return this.$$.ctx[27]}set editable(e){this.$$set({editable:e}),ge()}get i18n(){return this.$$.ctx[15]}set i18n(e){this.$$set({i18n:e}),ge()}}const{SvelteComponent:bf,append:il,attr:Ce,bubble:wf,check_outros:sl,create_component:zl,destroy_component:Dl,detach:Tl,element:Wt,flush:At,group_outros:rl,init:kf,insert:Hl,is_function:pf,listen:Hn,mount_component:Ol,safe_not_equal:vf,space:pn,toggle_class:gs,transition_in:Me,transition_out:Fe}=window.__gradio__svelte__internal,{onDestroy:yf}=window.__gradio__svelte__internal,{createEventDispatcher:Cf}=window.__gradio__svelte__internal;function bs(l){let e,t,n,i,r,s,o,f,u,a=l[0]&&l[3]==="filter"&&ws(l);return{c(){e=Wt("div"),t=Wt("input"),s=pn(),a&&a.c(),Ce(t,"type","text"),t.value=n=l[0]||"",Ce(t,"placeholder",i=l[3]==="filter"?"Filter...":"Search..."),Ce(t,"class","search-input svelte-b1nr0g"),Ce(t,"title",r=`Enter text to ${l[3]} the table`),gs(t,"filter-mode",l[3]==="filter"),Ce(e,"class","search-container svelte-b1nr0g")},m(_,c){Hl(_,e,c),il(e,t),il(e,s),a&&a.m(e,null),o=!0,f||(u=Hn(t,"input",l[7]),f=!0)},p(_,c){(!o||c&1&&n!==(n=_[0]||"")&&t.value!==n)&&(t.value=n),(!o||c&8&&i!==(i=_[3]==="filter"?"Filter...":"Search..."))&&Ce(t,"placeholder",i),(!o||c&8&&r!==(r=`Enter text to ${_[3]} the table`))&&Ce(t,"title",r),(!o||c&8)&&gs(t,"filter-mode",_[3]==="filter"),_[0]&&_[3]==="filter"?a?(a.p(_,c),c&9&&Me(a,1)):(a=ws(_),a.c(),Me(a,1),a.m(e,null)):a&&(rl(),Fe(a,1,1,()=>{a=null}),sl())},i(_){o||(Me(a),o=!0)},o(_){Fe(a),o=!1},d(_){_&&Tl(e),a&&a.d(),f=!1,u()}}}function ws(l){let e,t,n,i,r;return t=new qn({}),{c(){e=Wt("button"),zl(t.$$.fragment),Ce(e,"class","toolbar-button check-button svelte-b1nr0g"),Ce(e,"aria-label","Apply filter and update dataframe values"),Ce(e,"title","Apply filter and update dataframe values")},m(s,o){Hl(s,e,o),Ol(t,e,null),n=!0,i||(r=Hn(e,"click",function(){pf(l[5])&&l[5].apply(this,arguments)}),i=!0)},p(s,o){l=s},i(s){n||(Me(t.$$.fragment,s),n=!0)},o(s){Fe(t.$$.fragment,s),n=!1},d(s){s&&Tl(e),Dl(t),i=!1,r()}}}function ks(l){let e,t,n,i,r,s,o,f;const u=[Nf,Sf],a=[];function _(c,w){return c[6]?0:1}return t=_(l),n=a[t]=u[t](l),{c(){e=Wt("button"),n.c(),Ce(e,"class","toolbar-button svelte-b1nr0g"),Ce(e,"aria-label",i=l[6]?"Copied to clipboard":"Copy table data"),Ce(e,"title",r=l[6]?"Copied to clipboard":"Copy table data")},m(c,w){Hl(c,e,w),a[t].m(e,null),s=!0,o||(f=Hn(e,"click",l[8]),o=!0)},p(c,w){let L=t;t=_(c),t!==L&&(rl(),Fe(a[L],1,1,()=>{a[L]=null}),sl(),n=a[t],n||(n=a[t]=u[t](c),n.c()),Me(n,1),n.m(e,null)),(!s||w&64&&i!==(i=c[6]?"Copied to clipboard":"Copy table data"))&&Ce(e,"aria-label",i),(!s||w&64&&r!==(r=c[6]?"Copied to clipboard":"Copy table data"))&&Ce(e,"title",r)},i(c){s||(Me(n),s=!0)},o(c){Fe(n),s=!1},d(c){c&&Tl(e),a[t].d(),o=!1,f()}}}function Sf(l){let e,t;return e=new fo({}),{c(){zl(e.$$.fragment)},m(n,i){Ol(e,n,i),t=!0},i(n){t||(Me(e.$$.fragment,n),t=!0)},o(n){Fe(e.$$.fragment,n),t=!1},d(n){Dl(e,n)}}}function Nf(l){let e,t;return e=new qn({}),{c(){zl(e.$$.fragment)},m(n,i){Ol(e,n,i),t=!0},i(n){t||(Me(e.$$.fragment,n),t=!0)},o(n){Fe(e.$$.fragment,n),t=!1},d(n){Dl(e,n)}}}function ps(l){let e,t;return e=new co({props:{fullscreen:l[4]}}),e.$on("fullscreen",l[10]),{c(){zl(e.$$.fragment)},m(n,i){Ol(e,n,i),t=!0},p(n,i){const r={};i&16&&(r.fullscreen=n[4]),e.$set(r)},i(n){t||(Me(e.$$.fragment,n),t=!0)},o(n){Fe(e.$$.fragment,n),t=!1},d(n){Dl(e,n)}}}function qf(l){let e,t,n,i,r,s=l[3]!=="none"&&bs(l),o=l[2]&&ks(l),f=l[1]&&ps(l);return{c(){e=Wt("div"),t=Wt("div"),s&&s.c(),n=pn(),o&&o.c(),i=pn(),f&&f.c(),Ce(t,"class","toolbar-buttons svelte-b1nr0g"),Ce(e,"class","toolbar svelte-b1nr0g"),Ce(e,"role","toolbar"),Ce(e,"aria-label","Table actions")},m(u,a){Hl(u,e,a),il(e,t),s&&s.m(t,null),il(t,n),o&&o.m(t,null),il(t,i),f&&f.m(t,null),r=!0},p(u,[a]){u[3]!=="none"?s?(s.p(u,a),a&8&&Me(s,1)):(s=bs(u),s.c(),Me(s,1),s.m(t,n)):s&&(rl(),Fe(s,1,1,()=>{s=null}),sl()),u[2]?o?(o.p(u,a),a&4&&Me(o,1)):(o=ks(u),o.c(),Me(o,1),o.m(t,i)):o&&(rl(),Fe(o,1,1,()=>{o=null}),sl()),u[1]?f?(f.p(u,a),a&2&&Me(f,1)):(f=ps(u),f.c(),Me(f,1),f.m(t,null)):f&&(rl(),Fe(f,1,1,()=>{f=null}),sl())},i(u){r||(Me(s),Me(o),Me(f),r=!0)},o(u){Fe(s),Fe(o),Fe(f),r=!1},d(u){u&&Tl(e),s&&s.d(),o&&o.d(),f&&f.d()}}}function Mf(l,e,t){let{show_fullscreen_button:n=!1}=e,{show_copy_button:i=!1}=e,{show_search:r="none"}=e,{fullscreen:s=!1}=e,{on_copy:o}=e,{on_commit_filter:f}=e;const u=Cf();let a=!1,_,{current_search_query:c=null}=e,w="";function L(p){w=p.target.value;const y=w||null;c!==y&&(t(0,c=y),u("search",c))}function v(){t(6,a=!0),_&&clearTimeout(_),_=setTimeout(()=>{t(6,a=!1)},2e3)}async function q(){await o(),v()}yf(()=>{_&&clearTimeout(_)});function b(p){wf.call(this,l,p)}return l.$$set=p=>{"show_fullscreen_button"in p&&t(1,n=p.show_fullscreen_button),"show_copy_button"in p&&t(2,i=p.show_copy_button),"show_search"in p&&t(3,r=p.show_search),"fullscreen"in p&&t(4,s=p.fullscreen),"on_copy"in p&&t(9,o=p.on_copy),"on_commit_filter"in p&&t(5,f=p.on_commit_filter),"current_search_query"in p&&t(0,c=p.current_search_query)},[c,n,i,r,s,f,a,L,q,o,b]}class Af extends bf{constructor(e){super(),kf(this,e,Mf,qf,vf,{show_fullscreen_button:1,show_copy_button:2,show_search:3,fullscreen:4,on_copy:9,on_commit_filter:5,current_search_query:0})}get show_fullscreen_button(){return this.$$.ctx[1]}set show_fullscreen_button(e){this.$$set({show_fullscreen_button:e}),At()}get show_copy_button(){return this.$$.ctx[2]}set show_copy_button(e){this.$$set({show_copy_button:e}),At()}get show_search(){return this.$$.ctx[3]}set show_search(e){this.$$set({show_search:e}),At()}get fullscreen(){return this.$$.ctx[4]}set fullscreen(e){this.$$set({fullscreen:e}),At()}get on_copy(){return this.$$.ctx[9]}set on_copy(e){this.$$set({on_copy:e}),At()}get on_commit_filter(){return this.$$.ctx[5]}set on_commit_filter(e){this.$$set({on_commit_filter:e}),At()}get current_search_query(){return this.$$.ctx[0]}set current_search_query(e){this.$$set({current_search_query:e}),At()}}function wl(l,e,t,n){let i=l||[];if(e[1]==="fixed"&&i.length<e[0]){const r=Array(e[0]-i.length).fill("").map((s,o)=>`${o+i.length}`);i=i.concat(r)}return!i||i.length===0?Array(e[0]).fill(0).map((r,s)=>{const o=n();return t[o]={cell:null,input:null},{id:o,value:JSON.stringify(s+1)}}):i.map((r,s)=>{const o=n();return t[o]={cell:null,input:null},{id:o,value:r??""}})}function Ef(l,e,t,n,i=null){return!l||l.length===0?[]:l.map((s,o)=>s.map((f,u)=>{const a=n();e[a]={cell:null,input:null},t[a]=f;let _=i?.[o]?.[u];return _===void 0&&(_=String(f)),{id:a,value:f,display_value:_}}))}const{tick:tr}=window.__gradio__svelte__internal;async function lr(l,e,t,n){if(!e.data||!e.data[t]||!e.data[t][n])return;const i=e.data[t][n].value;e.data[t][n].value=l,i!==l&&e.dispatch&&e.dispatch("change",{data:e.data.map(r=>r.map(s=>s.value)),headers:e.headers?.map(r=>r.value)||[],metadata:null}),e.actions.set_selected([t,n])}async function Lf(l,e,t){if(!e.data||!e.headers||!e.els)return;const n=l.target;!n||n.value===void 0||await lr(n.type==="checkbox"?String(n.checked):n.value,e,t[0],t[1])}function jf(l,e){const t=Le(e.state),n=t.ui_state.selected_header,i=t.ui_state.header_edit,r=e.headers||[];if(n===!1||i!==!1)return!1;switch(l.key){case"ArrowDown":return e.actions.set_selected_header(!1),e.actions.set_selected([0,n]),e.actions.set_selected_cells([[0,n]]),!0;case"ArrowLeft":return e.actions.set_selected_header(n>0?n-1:n),!0;case"ArrowRight":return e.actions.set_selected_header(n<r.length-1?n+1:n),!0;case"Escape":return l.preventDefault(),e.actions.set_selected_header(!1),!0;case"Enter":return l.preventDefault(),t.config.editable&&e.actions.set_header_edit(n),!0}return!1}function nr(l,e){if(!e.data||!e.headers||!e.els||!e.dispatch)return!1;const t=Le(e.state);if(!t.config.editable||l.key!=="Delete"&&l.key!=="Backspace")return!1;const n=t.ui_state.editing,i=t.ui_state.selected_cells;if(n){const[r,s]=n,o=e.els[e.data[r][s].id]?.input;if(o&&o.selectionStart!==o.selectionEnd||l.key==="Delete"&&o?.selectionStart!==o?.value.length||l.key==="Backspace"&&o?.selectionStart!==0)return!1}if(l.preventDefault(),i.length>0){const r=Ao(e.data,i);e.dispatch("change",{data:r.map(s=>s.map(o=>o.value)),headers:e.headers.map(s=>s.value),metadata:null})}return!0}function Bf(l,e,t,n){const i=Le(e.state),r=i.ui_state.editing,s=i.ui_state.selected_cells;if(r||!e.data)return!1;l.preventDefault();const o=e.actions.move_cursor(l,[t,n],e.data);return o?(l.shiftKey?(e.actions.set_selected_cells(e.actions.get_range_selection(s.length>0?s[0]:[t,n],o)),e.actions.set_editing(!1)):(e.actions.set_selected_cells([o]),e.actions.set_editing(!1)),e.actions.set_selected(o)):o===!1&&l.key==="ArrowUp"&&t===0&&(e.actions.set_selected_header(n),e.actions.set_selected(!1),e.actions.set_selected_cells([]),e.actions.set_editing(!1)),!0}async function zf(l,e,t,n){if(!e.data||!e.els)return!1;const i=Le(e.state);if(!i.config.editable)return!1;const r=i.ui_state.editing;if(r&&l.shiftKey)return!1;if(l.preventDefault(),r&&nl(r,[t,n])){const s=e.data[t][n].id,o=e.els[s]?.input;o&&await lr(o.value,e,t,n),e.actions.set_editing(!1),await tr(),e.parent_element?.focus()}else e.actions.set_editing([t,n]);return!0}function Df(l,e,t,n){if(!e.data)return!1;l.preventDefault(),e.actions.set_editing(!1);const i=e.actions.get_next_cell_coordinates([t,n],e.data,l.shiftKey);return i&&(e.actions.set_selected_cells([i]),e.actions.set_selected(i),Le(e.state).config.editable&&e.actions.set_editing(i)),!0}function Tf(l,e,t,n){const i=Le(e.state);if(!i.config.editable)return!1;const r=i.ui_state.editing;return(!r||r&&nl(r,[t,n]))&&l.key.length===1?(e.actions.set_editing([t,n]),!0):!1}async function Hf(l,e){if(!e.data)return!1;const t=Le(e.state),n=t.ui_state.selected,i=t.ui_state.selected_cells;if(!n)return!1;if(l.key==="c"&&(l.metaKey||l.ctrlKey))return l.preventDefault(),i.length>0&&await Ks(e.data,i),e.actions.set_copy_flash(!0),!0;const[r,s]=n;switch(l.key){case"ArrowRight":case"ArrowLeft":case"ArrowDown":case"ArrowUp":return Bf(l,e,r,s);case"Escape":return t.config.editable?(l.preventDefault(),e.actions.set_editing(!1),tr().then(()=>{e.parent_element&&e.parent_element.focus()}),!0):!1;case"Enter":return await zf(l,e,r,s);case"Tab":return Df(l,e,r,s);case"Delete":case"Backspace":return nr(l,e);default:return Tf(l,e,r,s)}}async function Of(l,e){jf(l,e)||nr(l,e)||await Hf(l,e)}function If(l,e,t,n,i,r,s){const o=(a,_,c)=>{a.target instanceof HTMLAnchorElement||r&&c===-1||(a.preventDefault(),a.stopPropagation(),l.mouse_down_pos={x:a.clientX,y:a.clientY},l.drag_start=[_,c],!a.shiftKey&&!a.metaKey&&!a.ctrlKey&&(t([[_,c]]),n([_,c]),i(a,_,c)))},f=a=>{const _=a.target.closest("td");if(!_)return;const c=parseInt(_.getAttribute("data-row")||"0"),w=parseInt(_.getAttribute("data-col")||"0");if(isNaN(c)||isNaN(w))return;const L=Mn(l.drag_start,[c,w]);t(L),n([c,w])},u=a=>{!l.is_dragging&&l.drag_start?i(a,l.drag_start[0],l.drag_start[1]):l.is_dragging&&s&&s.focus(),l.is_dragging=!1,e(!1),l.drag_start=null,l.mouse_down_pos=null};return{handle_mouse_down:o,handle_mouse_move(a){if(!l.drag_start||!l.mouse_down_pos)return;if(!(a.buttons&1)){u(a);return}const _=Math.abs(a.clientX-l.mouse_down_pos.x),c=Math.abs(a.clientY-l.mouse_down_pos.y);!l.is_dragging&&(_>3||c>3)&&(l.is_dragging=!0,e(!0)),l.is_dragging&&f(a)},handle_mouse_up:u}}const{SvelteComponent:Pf,add_flush_callback:rt,append:be,attr:de,bind:ot,binding_callbacks:Ie,bubble:Rf,check_outros:ct,component_subscribe:Jf,create_component:Qe,destroy_component:Ze,detach:je,element:Ae,empty:ol,ensure_array_like:St,flush:se,globals:Ff,group_outros:ht,init:Kf,insert:Be,is_function:un,listen:Ot,mount_component:$e,noop:ir,outro_and_destroy_block:Nl,run_all:Wf,safe_not_equal:Uf,set_data:On,set_style:vs,space:Ve,text:In,toggle_class:Ct,transition_in:V,transition_out:ee,update_keyed_each:ql}=window.__gradio__svelte__internal,{Map:Ml,window:Vf}=Ff,{afterUpdate:Yf,createEventDispatcher:Xf,tick:Gf,onMount:ys}=window.__gradio__svelte__internal;function Cs(l,e,t){const n=l.slice();return n[146]=e[t].value,n[147]=e[t].id,n[150]=e,n[151]=t,n}function Ss(l,e,t){const n=l.slice();return n[146]=e[t].value,n[147]=e[t].id,n[148]=e,n[149]=t,n}function Ns(l,e,t){const n=l.slice();return n[146]=e[t].value,n[147]=e[t].id,n[152]=e,n[149]=t,n}function qs(l,e,t){const n=l.slice();return n[146]=e[t].value,n[147]=e[t].id,n[153]=e,n[151]=t,n}function Ms(l){let e,t,n,i,r=l[3]&&l[3].length!==0&&l[4]&&As(l);return n=new Af({props:{show_fullscreen_button:l[18],fullscreen:l[23],on_copy:l[89],show_copy_button:l[19],show_search:l[21],on_commit_filter:l[68],current_search_query:l[33].current_search_query}}),n.$on("search",l[90]),n.$on("fullscreen",l[91]),{c(){e=Ae("div"),r&&r.c(),t=Ve(),Qe(n.$$.fragment),de(e,"class","header-row svelte-1vwr9xf")},m(s,o){Be(s,e,o),r&&r.m(e,null),be(e,t),$e(n,e,null),i=!0},p(s,o){s[3]&&s[3].length!==0&&s[4]?r?r.p(s,o):(r=As(s),r.c(),r.m(e,t)):r&&(r.d(1),r=null);const f={};o[0]&262144&&(f.show_fullscreen_button=s[18]),o[0]&8388608&&(f.fullscreen=s[23]),o[0]&67108864&&(f.on_copy=s[89]),o[0]&524288&&(f.show_copy_button=s[19]),o[0]&2097152&&(f.show_search=s[21]),o[1]&4&&(f.current_search_query=s[33].current_search_query),n.$set(f)},i(s){i||(V(n.$$.fragment,s),i=!0)},o(s){ee(n.$$.fragment,s),i=!1},d(s){s&&je(e),r&&r.d(),Ze(n)}}}function As(l){let e,t,n;return{c(){e=Ae("div"),t=Ae("p"),n=In(l[3]),de(t,"class","svelte-1vwr9xf"),de(e,"class","label svelte-1vwr9xf")},m(i,r){Be(i,e,r),be(e,t),be(t,n)},p(i,r){r[0]&8&&On(n,i[3])},d(i){i&&je(e)}}}function Es(l){let e,t;return{c(){e=Ae("caption"),t=In(l[3]),de(e,"class","sr-only svelte-1vwr9xf")},m(n,i){Be(n,e,i),be(e,t)},p(n,i){i[0]&8&&On(t,n[3])},d(n){n&&je(e)}}}function Ls(l){let e,t;return e=new Al({props:{is_header:!0}}),{c(){Qe(e.$$.fragment)},m(n,i){$e(e,n,i),t=!0},i(n){t||(V(e.$$.fragment,n),t=!0)},o(n){ee(e.$$.fragment,n),t=!1},d(n){Ze(e,n)}}}function js(l,e){let t,n,i,r,s;function o(a){e[92](a,e[151])}function f(a){e[93](a,e[147])}let u={i:e[151],actual_pinned_columns:e[53],header_edit:e[52],selected_header:e[51],headers:e[0],get_cell_width:Pn,handle_header_click:e[60],toggle_header_menu:e[65],end_header_edit:e[61],sort_columns:e[33].sort_state.sort_columns,filter_columns:e[33].filter_state.filter_columns,latex_delimiters:e[7],line_breaks:e[14],max_chars:e[20],editable:e[9],is_static:e[22].includes(e[151]),i18n:e[12],col_count:e[5]};return e[25][e[151]].value!==void 0&&(u.value=e[25][e[151]].value),e[24][e[147]].input!==void 0&&(u.el=e[24][e[147]].input),n=new Xs({props:u}),Ie.push(()=>ot(n,"value",o)),Ie.push(()=>ot(n,"el",f)),{key:l,first:null,c(){t=ol(),Qe(n.$$.fragment),this.first=t},m(a,_){Be(a,t,_),$e(n,a,_),s=!0},p(a,_){e=a;const c={};_[0]&33554432&&(c.i=e[151]),_[1]&4194304&&(c.actual_pinned_columns=e[53]),_[1]&2097152&&(c.header_edit=e[52]),_[1]&1048576&&(c.selected_header=e[51]),_[0]&1&&(c.headers=e[0]),_[1]&4&&(c.sort_columns=e[33].sort_state.sort_columns),_[1]&4&&(c.filter_columns=e[33].filter_state.filter_columns),_[0]&128&&(c.latex_delimiters=e[7]),_[0]&16384&&(c.line_breaks=e[14]),_[0]&1048576&&(c.max_chars=e[20]),_[0]&512&&(c.editable=e[9]),_[0]&37748736&&(c.is_static=e[22].includes(e[151])),_[0]&4096&&(c.i18n=e[12]),_[0]&32&&(c.col_count=e[5]),!i&&_[0]&33554432&&(i=!0,c.value=e[25][e[151]].value,rt(()=>i=!1)),!r&&_[0]&50331648&&(r=!0,c.el=e[24][e[147]].input,rt(()=>r=!1)),n.$set(c)},i(a){s||(V(n.$$.fragment,a),s=!0)},o(a){ee(n.$$.fragment,a),s=!1},d(a){a&&je(t),Ze(n,a)}}}function Bs(l){let e,t;return e=new Al({props:{index:0}}),{c(){Qe(e.$$.fragment)},m(n,i){$e(e,n,i),t=!0},i(n){t||(V(e.$$.fragment,n),t=!0)},o(n){ee(e.$$.fragment,n),t=!1},d(n){Ze(e,n)}}}function zs(l,e){let t,n,i,r,s=e[149],o;i=new En({props:{value:e[146],latex_delimiters:e[7],line_breaks:e[14],datatype:Array.isArray(e[2])?e[2][e[149]]:e[2],edit:!1,el:null,editable:e[9],i18n:e[12],show_selection_buttons:e[31].length===1&&e[31][0][0]===0&&e[31][0][1]===e[149],coords:e[39],on_select_column:e[55].handle_select_column,on_select_row:e[55].handle_select_row,is_dragging:e[42]}}),i.$on("blur",e[64]);const f=()=>e[94](t,s),u=()=>e[94](null,s);return{key:l,first:null,c(){t=Ae("td"),n=Ae("div"),Qe(i.$$.fragment),r=Ve(),de(n,"class","cell-wrap svelte-1vwr9xf"),de(t,"tabindex","-1"),de(t,"class","svelte-1vwr9xf"),this.first=t},m(a,_){Be(a,t,_),be(t,n),$e(i,n,null),be(t,r),f(),o=!0},p(a,_){e=a;const c={};_[1]&524288&&(c.value=e[146]),_[0]&128&&(c.latex_delimiters=e[7]),_[0]&16384&&(c.line_breaks=e[14]),_[0]&4|_[1]&524288&&(c.datatype=Array.isArray(e[2])?e[2][e[149]]:e[2]),_[0]&512&&(c.editable=e[9]),_[0]&4096&&(c.i18n=e[12]),_[1]&524289&&(c.show_selection_buttons=e[31].length===1&&e[31][0][0]===0&&e[31][0][1]===e[149]),_[1]&256&&(c.coords=e[39]),_[1]&2048&&(c.is_dragging=e[42]),i.$set(c),s!==e[149]&&(u(),s=e[149],f())},i(a){o||(V(i.$$.fragment,a),o=!0)},o(a){ee(i.$$.fragment,a),o=!1},d(a){a&&je(t),Ze(i),u()}}}function Ds(l){let e,t;return{c(){e=Ae("caption"),t=In(l[3]),de(e,"class","sr-only svelte-1vwr9xf")},m(n,i){Be(n,e,i),be(e,t)},p(n,i){i[0]&8&&On(t,n[3])},d(n){n&&je(e)}}}function Qf(l){let e,t=l[3]&&l[3].length!==0&&Ds(l);return{c(){t&&t.c(),e=ol()},m(n,i){t&&t.m(n,i),Be(n,e,i)},p(n,i){n[3]&&n[3].length!==0?t?t.p(n,i):(t=Ds(n),t.c(),t.m(e.parentNode,e)):t&&(t.d(1),t=null)},d(n){n&&je(e),t&&t.d(n)}}}function Ts(l){let e,t;return e=new Al({props:{is_header:!0}}),{c(){Qe(e.$$.fragment)},m(n,i){$e(e,n,i),t=!0},i(n){t||(V(e.$$.fragment,n),t=!0)},o(n){ee(e.$$.fragment,n),t=!1},d(n){Ze(e,n)}}}function Hs(l,e){let t,n,i,r,s;function o(a){e[99](a,e[151])}function f(a){e[100](a,e[147])}let u={i:e[151],actual_pinned_columns:e[53],header_edit:e[52],selected_header:e[51],headers:e[0],get_cell_width:Pn,handle_header_click:e[60],toggle_header_menu:e[65],end_header_edit:e[61],sort_columns:e[33].sort_state.sort_columns,filter_columns:e[33].filter_state.filter_columns,latex_delimiters:e[7],line_breaks:e[14],max_chars:e[20],editable:e[9],is_static:e[22].includes(e[151]),i18n:e[12],col_count:e[5]};return e[25][e[151]].value!==void 0&&(u.value=e[25][e[151]].value),e[24][e[147]].input!==void 0&&(u.el=e[24][e[147]].input),n=new Xs({props:u}),Ie.push(()=>ot(n,"value",o)),Ie.push(()=>ot(n,"el",f)),{key:l,first:null,c(){t=ol(),Qe(n.$$.fragment),this.first=t},m(a,_){Be(a,t,_),$e(n,a,_),s=!0},p(a,_){e=a;const c={};_[0]&33554432&&(c.i=e[151]),_[1]&4194304&&(c.actual_pinned_columns=e[53]),_[1]&2097152&&(c.header_edit=e[52]),_[1]&1048576&&(c.selected_header=e[51]),_[0]&1&&(c.headers=e[0]),_[1]&4&&(c.sort_columns=e[33].sort_state.sort_columns),_[1]&4&&(c.filter_columns=e[33].filter_state.filter_columns),_[0]&128&&(c.latex_delimiters=e[7]),_[0]&16384&&(c.line_breaks=e[14]),_[0]&1048576&&(c.max_chars=e[20]),_[0]&512&&(c.editable=e[9]),_[0]&37748736&&(c.is_static=e[22].includes(e[151])),_[0]&4096&&(c.i18n=e[12]),_[0]&32&&(c.col_count=e[5]),!i&&_[0]&33554432&&(i=!0,c.value=e[25][e[151]].value,rt(()=>i=!1)),!r&&_[0]&50331648&&(r=!0,c.el=e[24][e[147]].input,rt(()=>r=!1)),n.$set(c)},i(a){s||(V(n.$$.fragment,a),s=!0)},o(a){ee(n.$$.fragment,a),s=!1},d(a){a&&je(t),Ze(n,a)}}}function Zf(l){let e,t,n=[],i=new Ml,r,s=l[15]&&Ts(),o=St(l[25]);const f=u=>u[147];for(let u=0;u<o.length;u+=1){let a=Cs(l,o,u),_=f(a);i.set(_,n[u]=Hs(_,a))}return{c(){e=Ae("tr"),s&&s.c(),t=Ve();for(let u=0;u<n.length;u+=1)n[u].c();de(e,"slot","thead"),de(e,"class","svelte-1vwr9xf")},m(u,a){Be(u,e,a),s&&s.m(e,null),be(e,t);for(let _=0;_<n.length;_+=1)n[_]&&n[_].m(e,null);r=!0},p(u,a){u[15]?s?a[0]&32768&&V(s,1):(s=Ts(),s.c(),V(s,1),s.m(e,t)):s&&(ht(),ee(s,1,1,()=>{s=null}),ct()),a[0]&55595681|a[1]&1617952772|a[2]&8&&(o=St(u[25]),ht(),n=ql(n,a,f,1,u,o,i,e,Nl,Hs,null,Cs),ct())},i(u){if(!r){V(s);for(let a=0;a<o.length;a+=1)V(n[a]);r=!0}},o(u){ee(s);for(let a=0;a<n.length;a+=1)ee(n[a]);r=!1},d(u){u&&je(e),s&&s.d();for(let a=0;a<n.length;a+=1)n[a].d()}}}function Os(l){let e,t;return e=new Al({props:{index:l[144]}}),{c(){Qe(e.$$.fragment)},m(n,i){$e(e,n,i),t=!0},p(n,i){const r={};i[4]&1048576&&(r.index=n[144]),e.$set(r)},i(n){t||(V(e.$$.fragment,n),t=!0)},o(n){ee(e.$$.fragment,n),t=!1},d(n){Ze(e,n)}}}function Is(l,e){let t,n,i,r,s;function o(a){e[97](a,e[144],e[149])}function f(a){e[98](a,e[147])}let u={display_value:e[72](e[144],e[149]),index:e[33].current_search_query!==void 0&&e[27][e[144]]!==void 0?e[27][e[144]]:e[144],j:e[149],actual_pinned_columns:e[53],get_cell_width:Pn,handle_cell_click:e[96],handle_blur:e[64],toggle_cell_menu:e[55].toggle_cell_menu,is_cell_selected:qo,should_show_cell_menu:Eo,selected_cells:e[31],copy_flash:e[32],active_cell_menu:e[47],styling:e[35][e[144]][e[149]].styling,latex_delimiters:e[7],line_breaks:e[14],datatype:Array.isArray(e[2])?e[2][e[149]]:e[2],editing:e[48],max_chars:e[20],editable:e[9],is_static:e[22].includes(e[149]),i18n:e[12],components:e[8],handle_select_column:e[55].handle_select_column,handle_select_row:e[55].handle_select_row,is_dragging:e[42],wrap:e[10]};return e[35][e[144]][e[149]].value!==void 0&&(u.value=e[35][e[144]][e[149]].value),e[24][e[147]]!==void 0&&(u.el=e[24][e[147]]),n=new gu({props:u}),Ie.push(()=>ot(n,"value",o)),Ie.push(()=>ot(n,"el",f)),{key:l,first:null,c(){t=ol(),Qe(n.$$.fragment),this.first=t},m(a,_){Be(a,t,_),$e(n,a,_),s=!0},p(a,_){e=a;const c={};_[4]&3145728&&(c.display_value=e[72](e[144],e[149])),_[0]&134217728|_[1]&4|_[4]&1048576&&(c.index=e[33].current_search_query!==void 0&&e[27][e[144]]!==void 0?e[27][e[144]]:e[144]),_[4]&2097152&&(c.j=e[149]),_[1]&4194304&&(c.actual_pinned_columns=e[53]),_[1]&16384&&(c.handle_cell_click=e[96]),_[1]&1&&(c.selected_cells=e[31]),_[1]&2&&(c.copy_flash=e[32]),_[1]&65536&&(c.active_cell_menu=e[47]),_[1]&16|_[4]&3145728&&(c.styling=e[35][e[144]][e[149]].styling),_[0]&128&&(c.latex_delimiters=e[7]),_[0]&16384&&(c.line_breaks=e[14]),_[0]&4|_[4]&2097152&&(c.datatype=Array.isArray(e[2])?e[2][e[149]]:e[2]),_[1]&131072&&(c.editing=e[48]),_[0]&1048576&&(c.max_chars=e[20]),_[0]&512&&(c.editable=e[9]),_[0]&4194304|_[4]&2097152&&(c.is_static=e[22].includes(e[149])),_[0]&4096&&(c.i18n=e[12]),_[0]&256&&(c.components=e[8]),_[1]&2048&&(c.is_dragging=e[42]),_[0]&1024&&(c.wrap=e[10]),!i&&_[1]&16|_[4]&3145728&&(i=!0,c.value=e[35][e[144]][e[149]].value,rt(()=>i=!1)),!r&&_[0]&16777216|_[4]&2097152&&(r=!0,c.el=e[24][e[147]],rt(()=>r=!1)),n.$set(c)},i(a){s||(V(n.$$.fragment,a),s=!0)},o(a){ee(n.$$.fragment,a),s=!1},d(a){a&&je(t),Ze(n,a)}}}function $f(l){let e,t,n=[],i=new Ml,r,s=l[15]&&Os(l),o=St(l[145]);const f=u=>u[147];for(let u=0;u<o.length;u+=1){let a=Ss(l,o,u),_=f(a);i.set(_,n[u]=Is(_,a))}return{c(){e=Ae("tr"),s&&s.c(),t=Ve();for(let u=0;u<n.length;u+=1)n[u].c();de(e,"slot","tbody"),de(e,"class","svelte-1vwr9xf"),Ct(e,"row-odd",l[144]%2===0)},m(u,a){Be(u,e,a),s&&s.m(e,null),be(e,t);for(let _=0;_<n.length;_+=1)n[_]&&n[_].m(e,null);r=!0},p(u,a){u[15]?s?(s.p(u,a),a[0]&32768&&V(s,1)):(s=Os(u),s.c(),V(s,1),s.m(e,t)):s&&(ht(),ee(s,1,1,()=>{s=null}),ct()),a[0]&156260228|a[1]&21186583|a[2]&1028|a[4]&3145728&&(o=St(u[145]),ht(),n=ql(n,a,f,1,u,o,i,e,Nl,Is,null,Ss),ct()),(!r||a[4]&1048576)&&Ct(e,"row-odd",u[144]%2===0)},i(u){if(!r){V(s);for(let a=0;a<o.length;a+=1)V(n[a]);r=!0}},o(u){ee(s);for(let a=0;a<n.length;a+=1)ee(n[a]);r=!1},d(u){u&&je(e),s&&s.d();for(let a=0;a<n.length;a+=1)n[a].d()}}}function xf(l){let e,t,n,i,r,s,o,f;function u(v){l[101](v)}function a(v){l[102](v)}function _(v){l[103](v)}function c(v){l[104](v)}function w(v){l[105](v)}let L={max_height:l[13],selected:l[49],disable_scroll:l[47]!==null||l[46]!==null,$$slots:{tbody:[$f,({index:v,item:q})=>({144:v,145:q}),({index:v,item:q})=>[0,0,0,0,(v?1048576:0)|(q?2097152:0)]],thead:[Zf],default:[Qf]},$$scope:{ctx:l}};return l[35]!==void 0&&(L.items=l[35]),l[37]!==void 0&&(L.actual_height=l[37]),l[38]!==void 0&&(L.table_scrollbar_width=l[38]),l[40]!==void 0&&(L.viewport=l[40]),l[41]!==void 0&&(L.show_scroll_button=l[41]),t=new Xu({props:L}),Ie.push(()=>ot(t,"items",u)),Ie.push(()=>ot(t,"actual_height",a)),Ie.push(()=>ot(t,"table_scrollbar_width",_)),Ie.push(()=>ot(t,"viewport",c)),Ie.push(()=>ot(t,"show_scroll_button",w)),t.$on("scroll_top",tc),{c(){e=Ae("div"),Qe(t.$$.fragment),de(e,"class","table-wrap svelte-1vwr9xf")},m(v,q){Be(v,e,q),$e(t,e,null),f=!0},p(v,q){const b={};q[0]&8192&&(b.max_height=v[13]),q[1]&262144&&(b.selected=v[49]),q[1]&98304&&(b.disable_scroll=v[47]!==null||v[46]!==null),q[0]&189847469|q[1]&7555095|q[4]&1076887552&&(b.$$scope={dirty:q,ctx:v}),!n&&q[1]&16&&(n=!0,b.items=v[35],rt(()=>n=!1)),!i&&q[1]&64&&(i=!0,b.actual_height=v[37],rt(()=>i=!1)),!r&&q[1]&128&&(r=!0,b.table_scrollbar_width=v[38],rt(()=>r=!1)),!s&&q[1]&512&&(s=!0,b.viewport=v[40],rt(()=>s=!1)),!o&&q[1]&1024&&(o=!0,b.show_scroll_button=v[41],rt(()=>o=!1)),t.$set(b)},i(v){f||(V(t.$$.fragment,v),f=!0)},o(v){ee(t.$$.fragment,v),f=!1},d(v){v&&je(e),Ze(t)}}}function Ps(l){let e,t,n;return{c(){e=Ae("button"),e.textContent="↑",de(e,"class","scroll-top-button svelte-1vwr9xf")},m(i,r){Be(i,e,r),t||(n=Ot(e,"click",l[69]),t=!0)},p:ir,d(i){i&&je(e),t=!1,n()}}}function Rs(l){let e,t;return e=new Au({props:{on_click:l[110]}}),{c(){Qe(e.$$.fragment)},m(n,i){$e(e,n,i),t=!0},p:ir,i(n){t||(V(e.$$.fragment,n),t=!0)},o(n){ee(e.$$.fragment,n),t=!1},d(n){Ze(e,n)}}}function Js(l){let e,t;return e=new gf({props:{x:l[47]?.x??l[46]?.x??0,y:l[47]?.y??l[46]?.y??0,row:l[46]?-1:l[47]?.row??0,col_count:l[5],row_count:l[6],on_add_row_above:l[111],on_add_row_below:l[112],on_add_column_left:l[113],on_add_column_right:l[114],on_delete_row:l[115],on_delete_col:l[116],editable:l[9],can_delete_rows:!l[46]&&l[26].length>1&&l[9],can_delete_cols:l[26].length>0&&l[26][0]?.length>1&&l[9],i18n:l[12],on_sort:l[46]?l[117]:void 0,on_clear_sort:l[46]?l[118]:void 0,sort_direction:l[46]?l[33].sort_state.sort_columns.find(l[119])?.direction??null:null,sort_priority:l[46]&&l[33].sort_state.sort_columns.findIndex(l[120])+1||null,on_filter:l[46]?l[121]:void 0,on_clear_filter:l[46]?l[122]:void 0,filter_active:l[46]?l[33].filter_state.filter_columns.some(l[123]):null}}),{c(){Qe(e.$$.fragment)},m(n,i){$e(e,n,i),t=!0},p(n,i){const r={};i[1]&98304&&(r.x=n[47]?.x??n[46]?.x??0),i[1]&98304&&(r.y=n[47]?.y??n[46]?.y??0),i[1]&98304&&(r.row=n[46]?-1:n[47]?.row??0),i[0]&32&&(r.col_count=n[5]),i[0]&64&&(r.row_count=n[6]),i[1]&65536&&(r.on_add_row_above=n[111]),i[1]&65536&&(r.on_add_row_below=n[112]),i[1]&98304&&(r.on_add_column_left=n[113]),i[1]&98304&&(r.on_add_column_right=n[114]),i[1]&65536&&(r.on_delete_row=n[115]),i[1]&98304&&(r.on_delete_col=n[116]),i[0]&512&&(r.editable=n[9]),i[0]&67109376|i[1]&32768&&(r.can_delete_rows=!n[46]&&n[26].length>1&&n[9]),i[0]&67109376&&(r.can_delete_cols=n[26].length>0&&n[26][0]?.length>1&&n[9]),i[0]&4096&&(r.i18n=n[12]),i[1]&32768&&(r.on_sort=n[46]?n[117]:void 0),i[1]&32768&&(r.on_clear_sort=n[46]?n[118]:void 0),i[1]&32772&&(r.sort_direction=n[46]?n[33].sort_state.sort_columns.find(n[119])?.direction??null:null),i[1]&32772&&(r.sort_priority=n[46]&&n[33].sort_state.sort_columns.findIndex(n[120])+1||null),i[1]&32768&&(r.on_filter=n[46]?n[121]:void 0),i[1]&32768&&(r.on_clear_filter=n[46]?n[122]:void 0),i[1]&32772&&(r.filter_active=n[46]?n[33].filter_state.filter_columns.some(n[123]):null),e.$set(r)},i(n){t||(V(e.$$.fragment,n),t=!0)},o(n){ee(e.$$.fragment,n),t=!1},d(n){Ze(e,n)}}}function ec(l){let e,t,n,i,r,s,o,f,u=[],a=new Ml,_,c,w,L,v=[],q=new Ml,b,p,k,y,C,j,S,z,O,X,I=(l[3]&&l[3].length!==0&&l[4]||l[18]||l[19]||l[21]!=="none")&&Ms(l),D=l[3]&&l[3].length!==0&&Es(l),T=l[15]&&Ls(),Y=St(l[25]);const ne=M=>M[147];for(let M=0;M<Y.length;M+=1){let m=qs(l,Y,M),F=ne(m);a.set(F,u[M]=js(F,m))}let K=l[15]&&Bs(),W=St(l[50]);const ce=M=>M[147];for(let M=0;M<W.length;M+=1){let m=Ns(l,W,M),F=ce(m);q.set(F,v[M]=zs(F,m))}function B(M){l[106](M)}let Z={upload:l[16],stream_handler:l[17],flex:!1,center:!1,boundedheight:!1,disable_click:!0,root:l[11],aria_label:l[12]("dataframe.drop_to_upload"),$$slots:{default:[xf]},$$scope:{ctx:l}};l[36]!==void 0&&(Z.dragging=l[36]),p=new oo({props:Z}),Ie.push(()=>ot(p,"dragging",B)),p.$on("load",l[107]);let N=l[41]&&Ps(l),J=l[26].length===0&&l[9]&&l[6][1]==="dynamic"&&Rs(l),g=(l[47]||l[46])&&Js(l);return{c(){e=Ae("div"),I&&I.c(),t=Ve(),n=Ae("div"),i=Ae("table"),D&&D.c(),r=Ve(),s=Ae("thead"),o=Ae("tr"),T&&T.c(),f=Ve();for(let M=0;M<u.length;M+=1)u[M].c();_=Ve(),c=Ae("tbody"),w=Ae("tr"),K&&K.c(),L=Ve();for(let M=0;M<v.length;M+=1)v[M].c();b=Ve(),Qe(p.$$.fragment),y=Ve(),N&&N.c(),C=Ve(),J&&J.c(),j=Ve(),g&&g.c(),S=ol(),de(o,"class","svelte-1vwr9xf"),de(s,"class","svelte-1vwr9xf"),de(w,"class","svelte-1vwr9xf"),de(c,"class","svelte-1vwr9xf"),de(i,"aria-hidden","true"),de(i,"class","svelte-1vwr9xf"),de(n,"class","table-wrap svelte-1vwr9xf"),vs(n,"height",l[37]+"px"),de(n,"role","grid"),de(n,"tabindex","0"),Ct(n,"dragging",l[42]),Ct(n,"no-wrap",!l[10]),Ct(n,"menu-open",l[47]||l[46]),de(e,"class","table-container svelte-1vwr9xf")},m(M,m){Be(M,e,m),I&&I.m(e,null),be(e,t),be(e,n),be(n,i),D&&D.m(i,null),be(i,r),be(i,s),be(s,o),T&&T.m(o,null),be(o,f);for(let F=0;F<u.length;F+=1)u[F]&&u[F].m(o,null);be(i,_),be(i,c),be(c,w),K&&K.m(w,null),be(w,L);for(let F=0;F<v.length;F+=1)v[F]&&v[F].m(w,null);l[95](i),be(n,b),$e(p,n,null),be(n,y),N&&N.m(n,null),l[108](n),Be(M,C,m),J&&J.m(M,m),Be(M,j,m),g&&g.m(M,m),Be(M,S,m),z=!0,O||(X=[Ot(Vf,"resize",l[88]),Ot(n,"keydown",l[109]),Ot(n,"mousemove",function(){un(l[44])&&l[44].apply(this,arguments)}),Ot(n,"mouseup",function(){un(l[43])&&l[43].apply(this,arguments)}),Ot(n,"mouseleave",function(){un(l[43])&&l[43].apply(this,arguments)})],O=!0)},p(M,m){l=M,l[3]&&l[3].length!==0&&l[4]||l[18]||l[19]||l[21]!=="none"?I?(I.p(l,m),m[0]&2883608&&V(I,1)):(I=Ms(l),I.c(),V(I,1),I.m(e,t)):I&&(ht(),ee(I,1,1,()=>{I=null}),ct()),l[3]&&l[3].length!==0?D?D.p(l,m):(D=Es(l),D.c(),D.m(i,r)):D&&(D.d(1),D=null),l[15]?T?m[0]&32768&&V(T,1):(T=Ls(),T.c(),V(T,1),T.m(o,f)):T&&(ht(),ee(T,1,1,()=>{T=null}),ct()),m[0]&55595681|m[1]&1617952772|m[2]&8&&(Y=St(l[25]),ht(),u=ql(u,m,ne,1,l,Y,a,o,Nl,js,null,qs),ct()),l[15]?K?m[0]&32768&&V(K,1):(K=Bs(),K.c(),V(K,1),K.m(w,L)):K&&(ht(),ee(K,1,1,()=>{K=null}),ct()),m[0]&268456580|m[1]&17303809|m[2]&4&&(W=St(l[50]),ht(),v=ql(v,m,ce,1,l,W,q,w,Nl,zs,null,Ns),ct());const F={};m[0]&65536&&(F.upload=l[16]),m[0]&131072&&(F.stream_handler=l[17]),m[0]&2048&&(F.root=l[11]),m[0]&4096&&(F.aria_label=l[12]("dataframe.drop_to_upload")),m[0]&189855661|m[1]&7851735|m[4]&1073741824&&(F.$$scope={dirty:m,ctx:l}),!k&&m[1]&32&&(k=!0,F.dragging=l[36],rt(()=>k=!1)),p.$set(F),l[41]?N?N.p(l,m):(N=Ps(l),N.c(),N.m(n,null)):N&&(N.d(1),N=null),(!z||m[1]&64)&&vs(n,"height",l[37]+"px"),(!z||m[1]&2048)&&Ct(n,"dragging",l[42]),(!z||m[0]&1024)&&Ct(n,"no-wrap",!l[10]),(!z||m[1]&98304)&&Ct(n,"menu-open",l[47]||l[46]),l[26].length===0&&l[9]&&l[6][1]==="dynamic"?J?(J.p(l,m),m[0]&67109440&&V(J,1)):(J=Rs(l),J.c(),V(J,1),J.m(j.parentNode,j)):J&&(ht(),ee(J,1,1,()=>{J=null}),ct()),l[47]||l[46]?g?(g.p(l,m),m[1]&98304&&V(g,1)):(g=Js(l),g.c(),V(g,1),g.m(S.parentNode,S)):g&&(ht(),ee(g,1,1,()=>{g=null}),ct())},i(M){if(!z){V(I),V(T);for(let m=0;m<Y.length;m+=1)V(u[m]);V(K);for(let m=0;m<W.length;m+=1)V(v[m]);V(p.$$.fragment,M),V(J),V(g),z=!0}},o(M){ee(I),ee(T);for(let m=0;m<u.length;m+=1)ee(u[m]);ee(K);for(let m=0;m<v.length;m+=1)ee(v[m]);ee(p.$$.fragment,M),ee(J),ee(g),z=!1},d(M){M&&(je(e),je(C),je(j),je(S)),I&&I.d(),D&&D.d(),T&&T.d();for(let m=0;m<u.length;m+=1)u[m].d();K&&K.d();for(let m=0;m<v.length;m+=1)v[m].d();l[95](null),Ze(p),N&&N.d(),l[108](null),J&&J.d(M),g&&g.d(M),O=!1,Wf(X)}}}function Et(){return Math.random().toString(36).substring(2,15)}function Pn(l){return`var(--cell-width-${l})`}const tc=l=>{};function lc(l,e,t){let n,i,r,s,o,f,u,a,_,c,w,L,v,q,b,{datatype:p}=e,{label:k=null}=e,{show_label:y=!0}=e,{headers:C=[]}=e,{values:j=[]}=e,{col_count:S}=e,{row_count:z}=e,{latex_delimiters:O}=e,{components:X={}}=e,{editable:I=!0}=e,{wrap:D=!1}=e,{root:T}=e,{i18n:Y}=e,{max_height:ne=500}=e,{line_breaks:K=!0}=e,{column_widths:W=[]}=e,{show_row_numbers:ce=!1}=e,{upload:B}=e,{stream_handler:Z}=e,{show_fullscreen_button:N=!1}=e,{show_copy_button:J=!1}=e,{value_is_output:g=!1}=e,{max_chars:M=void 0}=e,{show_search:m="none"}=e,{pinned_columns:F=0}=e,{static_columns:Ee=[]}=e,{fullscreen:mt=!1}=e;const $=Ho({show_fullscreen_button:N,show_copy_button:J,show_search:m,show_row_numbers:ce,editable:I,pinned_columns:F,show_label:y,line_breaks:K,wrap:D,max_height:ne,column_widths:W,max_chars:M}),{state:A,actions:R}=$;Jf(l,A,h=>t(33,b=h)),ys(()=>{t(34,$.parent_element=ue,$),t(34,$.get_data_at=ar,$),t(34,$.get_column=_r,$),t(34,$.get_row=ur,$),t(34,$.dispatch=Nt,$),$n();const h=new IntersectionObserver(H=>{H.forEach(G=>{G.isIntersecting&&!Vn&&t(84,qt=!1),Vn=G.isIntersecting})});h.observe(ue),document.addEventListener("click",Un),window.addEventListener("resize",Qn);const E=H=>{(Qt||$l)&&q(H)};return document.addEventListener("mouseup",E),()=>{h.disconnect(),document.removeEventListener("click",Un),window.removeEventListener("resize",Qn),document.removeEventListener("mouseup",E)}});const Nt=Xf();let x={},Il={},Se=wl(C,S,x,Et),Xt=C,P=[[]],Ke,xe=[[]],Pl=!1,Rn,zt=[];ys(()=>{Rn=getComputedStyle(document.documentElement).getPropertyValue("--color-accent").trim()+"40",document.documentElement.style.setProperty("--color-accent-copied",Rn)});const ar=(h,E)=>P?.[h]?.[E]?.value,_r=h=>P?.map(E=>E[h]?.value)??[],ur=h=>P?.[h]?.map(E=>E.value)??[];let{display_value:Pe=null}=e,{styling:Re=null}=e,Rl=Se.map(h=>h.value),Jl=P.map(h=>h.map(E=>String(E.value)));function Jn(h,E){R.handle_sort(h,E),_l(P,Pe,Re)}function Fn(){R.reset_sort_state(),_l(P,Pe,Re)}function Kn(h,E,H,G){R.handle_filter(h,E,H,G),ul(P,Pe,Re)}function Wn(){R.reset_filter_state(),ul(P,Pe,Re)}async function fr(h,E=!1){!I||s===h||S[1]!=="dynamic"||R.set_header_edit(h)}function cr(h,E){h.target instanceof HTMLAnchorElement||(h.preventDefault(),h.stopPropagation(),I&&(R.set_editing(!1),R.handle_header_click(E,I),ue.focus()))}function hr(h){I&&(R.end_header_edit(h.detail.key),ue.focus())}async function Fl(h){if(ue.focus(),z[1]!=="dynamic")return;const E=Array(P[0]?.length||C.length).fill(0).map((H,G)=>{const fe=Et();return t(24,x[fe]={cell:null,input:null},x),{id:fe,value:""}});P.length===0?t(26,P=[E]):h!==void 0&&h>=0&&h<=P.length?P.splice(h,0,E):P.push(E),t(87,i=[h!==void 0?h:P.length-1,0])}async function dr(h){if(ue.focus(),S[1]!=="dynamic")return;const E=R.add_col(P,C,Et,h);E.data.forEach(H=>{H.forEach(G=>{x[G.id]||t(24,x[G.id]={cell:null,input:null},x)})}),t(26,P=E.data),t(0,C=E.headers),await Gf(),requestAnimationFrame(()=>{fr(h!==void 0?h:P[0].length-1,!0);const H=ue.querySelectorAll("tbody")[1].offsetWidth;ue.querySelectorAll("table")[1].scrollTo({left:H})})}function Un(h){Bo(h,ue)&&(R.clear_ui_state(),t(52,s=!1),t(51,o=!1))}let Kl,qt=!1,gt=[],ue,al,Wl=0,Ul=0;function Gt(){const h=P[0]?.length||0;if(b.filter_state.filter_columns.length>0||Wl===P.length&&Ul===h&&b.sort_state.sort_columns.length>0)return;Wl=P.length,Ul=h;const E=gt.map(H=>H?.clientWidth||0);if(E.length!==0){ce&&ue.style.setProperty("--cell-width-row-number",`${E[0]}px`);for(let H=0;H<50;H++)if(!W[H])ue.style.removeProperty(`--cell-width-${H}`);else if(W[H].endsWith("%")){const G=parseFloat(W[H]),fe=Math.floor(G/100*ue.clientWidth);ue.style.setProperty(`--cell-width-${H}`,`${fe}px`)}else ue.style.setProperty(`--cell-width-${H}`,W[H]);E.forEach((H,G)=>{if(!W[G]){const fe=`${Math.max(H,45)}px`;ue.style.setProperty(`--cell-width-${G}`,fe)}})}}let Vl=j.slice(0,ne/j.length*37).length*37+37,Yl=0;function _l(h,E,H){const G=bo(h,E,H,b.sort_state.sort_columns,i,ti);t(26,P=G.data),t(87,i=G.selected)}function ul(h,E,H){const G=ko(h,E,H,b.filter_state.filter_columns,i,ti,b.filter_state.initial_data?.data,b.filter_state.initial_data?.display_value,b.filter_state.initial_data?.styling);t(26,P=G.data),t(87,i=G.selected)}let Vn=!1;const mr=h=>{R.set_copy_flash(h)};let Xl=[];function gr(h){const{blur_event:E,coords:H}=h.detail;Lf(E,$,H)}function br(h,E){if(h.stopPropagation(),u&&u.col===E)R.set_active_header_menu(null);else{const H=h.target.closest("th");if(H){const G=H.getBoundingClientRect();R.set_active_header_menu({col:E,x:G.right,y:G.bottom})}}}Yf(()=>{t(73,g=!1)});function Yn(h){if(S[1]!=="dynamic"||P[0].length<=1)return;const E=R.delete_col_at(P,C,h);t(26,P=E.data),t(0,C=E.headers),t(25,Se=wl(C,S,x,Et)),R.set_active_cell_menu(null),R.set_active_header_menu(null),R.set_selected(!1),R.set_selected_cells([]),R.set_editing(!1)}function Xn(h){t(26,P=R.delete_row_at(P,h)),R.set_active_cell_menu(null),R.set_active_header_menu(null)}let Gn;function wr(){if(b.current_search_query&&m==="filter"){const h=[],E=[],H=[];xe.forEach(fe=>{const hl=[],xn=[],ei=[];fe.forEach($t=>{hl.push($t.value),xn.push($t.display_value!==void 0?$t.display_value:String($t.value)),ei.push($t.styling||"")}),h.push(hl),E.push(xn),H.push(ei)});const G={data:h,headers:Se.map(fe=>fe.value),metadata:{display_value:E,styling:H}};Nt("change",G),g||Nt("input"),R.handle_search(null)}}let fl,Gl=!1;function kr(){fl.scrollTo({top:0})}function Qn(){R.set_active_cell_menu(null),R.set_active_header_menu(null),t(31,n=[]),t(87,i=!1),t(48,r=!1),t(84,qt=!1),Gt()}function Ql(h,E){const H=E==="above"?h:h+1;Fl(H),t(47,f=null),t(46,u=null)}function Zl(h,E){const H=E==="left"?h:h+1;dr(H),t(47,f=null),t(46,u=null)}function pr(){R.reset_sort_state()}let Qt=!1,$l=null,Zn=null;const cl={is_dragging:Qt,drag_start:$l,mouse_down_pos:Zn};let Zt;function $n(){t(86,Zt=If(cl,h=>t(42,Qt=h),h=>R.set_selected_cells(h),h=>R.set_selected(h),(h,E,H)=>R.handle_cell_click(h,E,H),ce,ue))}function vr(h,E){return b.current_search_query!==void 0&&xe?.[h]?.[E]?xe[h][E].display_value!==void 0?xe[h][E].display_value:String(xe[h][E].value):P?.[h]?.[E]?P[h][E].display_value!==void 0?P[h][E].display_value:String(P[h][E].value):""}const yr=()=>Gt(),Cr=async()=>await Ks(P,null),Sr=h=>R.handle_search(h.detail);function Nr(h){Rf.call(this,l,h)}function qr(h,E){l.$$.not_equal(Se[E].value,h)&&(Se[E].value=h,t(25,Se),t(0,C),t(79,Xt),t(5,S),t(24,x))}function Mr(h,E){l.$$.not_equal(x[E].input,h)&&(x[E].input=h,t(24,x))}function Ar(h,E){Ie[h?"unshift":"push"](()=>{gt[E]=h,t(28,gt)})}function Er(h){Ie[h?"unshift":"push"](()=>{al=h,t(30,al)})}const Lr=(h,E,H)=>L(h,E,H);function jr(h,E,H){l.$$.not_equal(xe[E][H].value,h)&&(xe[E][H].value=h,t(35,xe),t(33,b),t(26,P),t(27,zt),t(77,Re),t(1,j),t(80,Ke),t(29,ue),t(24,x),t(131,Il),t(76,Pe),t(28,gt))}function Br(h,E){l.$$.not_equal(x[E],h)&&(x[E]=h,t(24,x))}function zr(h,E){l.$$.not_equal(Se[E].value,h)&&(Se[E].value=h,t(25,Se),t(0,C),t(79,Xt),t(5,S),t(24,x))}function Dr(h,E){l.$$.not_equal(x[E].input,h)&&(x[E].input=h,t(24,x))}function Tr(h){xe=h,t(35,xe),t(33,b),t(26,P),t(27,zt),t(77,Re),t(1,j),t(80,Ke),t(29,ue),t(24,x),t(131,Il),t(76,Pe),t(28,gt)}function Hr(h){Vl=h,t(37,Vl)}function Or(h){Yl=h,t(38,Yl)}function Ir(h){fl=h,t(40,fl)}function Pr(h){Gl=h,t(41,Gl)}function Rr(h){Pl=h,t(36,Pl)}const Jr=({detail:h})=>So(h.data,E=>(t(25,Se=wl(E.map(H=>H??""),S,x,Et)),Se),E=>{t(1,j=E)});function Fr(h){Ie[h?"unshift":"push"](()=>{ue=h,t(29,ue)})}const Kr=h=>Of(h,$),Wr=()=>Fl(),Ur=()=>Ql(f?.row??-1,"above"),Vr=()=>Ql(f?.row??-1,"below"),Yr=()=>Zl(f?.col??u?.col??-1,"left"),Xr=()=>Zl(f?.col??u?.col??-1,"right"),Gr=()=>Xn(f?.row??-1),Qr=()=>Yn(f?.col??u?.col??-1),Zr=h=>{u&&(Jn(u.col,h),R.set_active_header_menu(null))},$r=()=>{Fn(),R.set_active_header_menu(null)},xr=h=>h.col===(u?.col??-1),eo=h=>h.col===(u?.col??-1),to=(h,E,H)=>{u&&(Kn(u.col,h,E,H),R.set_active_header_menu(null))},lo=()=>{Wn(),R.set_active_header_menu(null)},no=h=>h.col===(u?.col??-1);return l.$$set=h=>{"datatype"in h&&t(2,p=h.datatype),"label"in h&&t(3,k=h.label),"show_label"in h&&t(4,y=h.show_label),"headers"in h&&t(0,C=h.headers),"values"in h&&t(1,j=h.values),"col_count"in h&&t(5,S=h.col_count),"row_count"in h&&t(6,z=h.row_count),"latex_delimiters"in h&&t(7,O=h.latex_delimiters),"components"in h&&t(8,X=h.components),"editable"in h&&t(9,I=h.editable),"wrap"in h&&t(10,D=h.wrap),"root"in h&&t(11,T=h.root),"i18n"in h&&t(12,Y=h.i18n),"max_height"in h&&t(13,ne=h.max_height),"line_breaks"in h&&t(14,K=h.line_breaks),"column_widths"in h&&t(74,W=h.column_widths),"show_row_numbers"in h&&t(15,ce=h.show_row_numbers),"upload"in h&&t(16,B=h.upload),"stream_handler"in h&&t(17,Z=h.stream_handler),"show_fullscreen_button"in h&&t(18,N=h.show_fullscreen_button),"show_copy_button"in h&&t(19,J=h.show_copy_button),"value_is_output"in h&&t(73,g=h.value_is_output),"max_chars"in h&&t(20,M=h.max_chars),"show_search"in h&&t(21,m=h.show_search),"pinned_columns"in h&&t(75,F=h.pinned_columns),"static_columns"in h&&t(22,Ee=h.static_columns),"fullscreen"in h&&t(23,mt=h.fullscreen),"display_value"in h&&t(76,Pe=h.display_value),"styling"in h&&t(77,Re=h.styling)},l.$$.update=()=>{if(l.$$.dirty[1]&4&&t(31,n=b.ui_state.selected_cells),l.$$.dirty[1]&4&&t(87,i=b.ui_state.selected),l.$$.dirty[1]&4&&t(48,r=b.ui_state.editing),l.$$.dirty[1]&4&&t(52,s=b.ui_state.header_edit),l.$$.dirty[1]&4&&t(51,o=b.ui_state.selected_header),l.$$.dirty[1]&4&&t(47,f=b.ui_state.active_cell_menu),l.$$.dirty[1]&4&&t(46,u=b.ui_state.active_header_menu),l.$$.dirty[1]&4&&t(32,a=b.ui_state.copy_flash),l.$$.dirty[0]&889192450|l.$$.dirty[1]&4|l.$$.dirty[2]&311296&&!nl(j,Ke)){if(ue){const H=j.length===0||j.length===1&&j[0].length===0,G=Ke!==void 0&&(j.length!==Ke.length||j[0]&&Ke[0]&&j[0].length!==Ke[0].length);if(H||G){for(let fe=0;fe<50;fe++)ue.style.removeProperty(`--cell-width-${fe}`);Wl=0,Ul=0,t(84,qt=!1)}}const h=j.length===0||j.length===1&&j[0].length===0,E=Ke!==void 0&&(j.length!==Ke.length||j[0]&&Ke[0]&&j[0].length!==Ke[0].length);t(26,P=Ef(j,x,Il,Et,Pe)),t(80,Ke=JSON.parse(JSON.stringify(j))),h||E?R.reset_sort_state():b.sort_state.sort_columns.length>0?_l(P,Pe,Re):(R.handle_sort(-1,"asc"),R.reset_sort_state()),b.filter_state.filter_columns.length>0?ul(P,Pe,Re):R.reset_filter_state(),b.current_search_query&&R.handle_search(null),ue&&gt.length>0&&(h||E)&&t(84,qt=!1)}if(l.$$.dirty[0]&67108864|l.$$.dirty[2]&8192&&t(53,_=F&&P?.[0]?.length?Math.min(F,P[0].length):0),l.$$.dirty[0]&16777249|l.$$.dirty[2]&131072&&(nl(C,Xt)||(t(25,Se=wl(C,S,x,Et)),t(79,Xt=JSON.parse(JSON.stringify(C))))),l.$$.dirty[0]&117440512|l.$$.dirty[2]&49152&&(P||Se||x)&&(t(34,$.data=P,$),t(34,$.headers=Se,$),t(34,$.els=x,$),t(34,$.display_value=Pe,$),t(34,$.styling=Re,$)),l.$$.dirty[0]&201326592|l.$$.dirty[1]&4|l.$$.dirty[2]&32768)if(b.current_search_query!==void 0){const h=new Map;t(27,zt=[]),P.forEach((H,G)=>{H.some(fe=>String(fe?.value).toLowerCase().includes(b.current_search_query?.toLowerCase()||""))&&zt.push(G),H.forEach((fe,hl)=>{h.set(fe.id,{value:fe.value,display_value:fe.display_value!==void 0?fe.display_value:String(fe.value),styling:Re?.[G]?.[hl]||""})})});const E=R.filter_data(P);t(35,xe=E.map(H=>H.map(G=>{const fe=h.get(G.id);return{...G,display_value:fe?.display_value!==void 0?fe.display_value:String(G.value),styling:fe?.styling||""}})))}else t(27,zt=[]);if(l.$$.dirty[0]&100663296|l.$$.dirty[2]&1574912&&(P||Se)&&(R.trigger_change(P,Se,Jl,Rl,g,Nt),t(82,Jl=P.map(h=>h.map(E=>String(E.value)))),t(81,Rl=Se.map(h=>h.value))),l.$$.dirty[0]&67108864|l.$$.dirty[1]&4|l.$$.dirty[2]&49152&&(b.filter_state.filter_columns.length>0&&ul(P,Pe,Re),b.sort_state.sort_columns.length>0&&(_l(P,Pe,Re),R.update_row_order(P))),l.$$.dirty[0]&67108864&&t(50,c=po(P)),l.$$.dirty[0]&268435456|l.$$.dirty[2]&2097152&&gt[0]&&gt[0]?.clientWidth&&(clearTimeout(Kl),t(83,Kl=setTimeout(()=>Gt(),100))),l.$$.dirty[0]&268435456|l.$$.dirty[2]&4194304&&gt[0]&&!qt&&(Gt(),t(84,qt=!0)),l.$$.dirty[2]&33554432&&t(49,w=!!i&&i[0]),l.$$.dirty[1]&3|l.$$.dirty[2]&8388608&&(a&&!nl(n,Xl)&&mr(!1),t(85,Xl=n)),l.$$.dirty[2]&33554432&&i!==!1&&t(39,Gn=i),l.$$.dirty[0]&1694498816|l.$$.dirty[2]&33554432&&i!==!1){const h=zo(i,P,x,ue,al);document.documentElement.style.setProperty("--selected-col-pos",h.col_pos),document.documentElement.style.setProperty("--selected-row-pos",h.row_pos||"0px")}l.$$.dirty[0]&536870912&&ue&&$n(),l.$$.dirty[2]&16777216&&t(45,L=Zt?.handle_mouse_down||(()=>{})),l.$$.dirty[2]&16777216&&t(44,v=Zt?.handle_mouse_move||(()=>{})),l.$$.dirty[2]&16777216&&t(43,q=Zt?.handle_mouse_up||(()=>{}))},t(42,Qt=cl.is_dragging),$l=cl.drag_start,Zn=cl.mouse_down_pos,[C,j,p,k,y,S,z,O,X,I,D,T,Y,ne,K,ce,B,Z,N,J,M,m,Ee,mt,x,Se,P,zt,gt,ue,al,n,a,b,$,xe,Pl,Vl,Yl,Gn,fl,Gl,Qt,q,v,L,u,f,r,w,c,o,s,_,A,R,Jn,Fn,Kn,Wn,cr,hr,Fl,Gt,gr,br,Yn,Xn,wr,kr,Ql,Zl,vr,g,W,F,Pe,Re,pr,Xt,Ke,Rl,Jl,Kl,qt,Xl,Zt,i,yr,Cr,Sr,Nr,qr,Mr,Ar,Er,Lr,jr,Br,zr,Dr,Tr,Hr,Or,Ir,Pr,Rr,Jr,Fr,Kr,Wr,Ur,Vr,Yr,Xr,Gr,Qr,Zr,$r,xr,eo,to,lo,no]}class nc extends Pf{constructor(e){super(),Kf(this,e,lc,ec,Uf,{datatype:2,label:3,show_label:4,headers:0,values:1,col_count:5,row_count:6,latex_delimiters:7,components:8,editable:9,wrap:10,root:11,i18n:12,max_height:13,line_breaks:14,column_widths:74,show_row_numbers:15,upload:16,stream_handler:17,show_fullscreen_button:18,show_copy_button:19,value_is_output:73,max_chars:20,show_search:21,pinned_columns:75,static_columns:22,fullscreen:23,display_value:76,styling:77,reset_sort_state:78},null,[-1,-1,-1,-1,-1])}get datatype(){return this.$$.ctx[2]}set datatype(e){this.$$set({datatype:e}),se()}get label(){return this.$$.ctx[3]}set label(e){this.$$set({label:e}),se()}get show_label(){return this.$$.ctx[4]}set show_label(e){this.$$set({show_label:e}),se()}get headers(){return this.$$.ctx[0]}set headers(e){this.$$set({headers:e}),se()}get values(){return this.$$.ctx[1]}set values(e){this.$$set({values:e}),se()}get col_count(){return this.$$.ctx[5]}set col_count(e){this.$$set({col_count:e}),se()}get row_count(){return this.$$.ctx[6]}set row_count(e){this.$$set({row_count:e}),se()}get latex_delimiters(){return this.$$.ctx[7]}set latex_delimiters(e){this.$$set({latex_delimiters:e}),se()}get components(){return this.$$.ctx[8]}set components(e){this.$$set({components:e}),se()}get editable(){return this.$$.ctx[9]}set editable(e){this.$$set({editable:e}),se()}get wrap(){return this.$$.ctx[10]}set wrap(e){this.$$set({wrap:e}),se()}get root(){return this.$$.ctx[11]}set root(e){this.$$set({root:e}),se()}get i18n(){return this.$$.ctx[12]}set i18n(e){this.$$set({i18n:e}),se()}get max_height(){return this.$$.ctx[13]}set max_height(e){this.$$set({max_height:e}),se()}get line_breaks(){return this.$$.ctx[14]}set line_breaks(e){this.$$set({line_breaks:e}),se()}get column_widths(){return this.$$.ctx[74]}set column_widths(e){this.$$set({column_widths:e}),se()}get show_row_numbers(){return this.$$.ctx[15]}set show_row_numbers(e){this.$$set({show_row_numbers:e}),se()}get upload(){return this.$$.ctx[16]}set upload(e){this.$$set({upload:e}),se()}get stream_handler(){return this.$$.ctx[17]}set stream_handler(e){this.$$set({stream_handler:e}),se()}get show_fullscreen_button(){return this.$$.ctx[18]}set show_fullscreen_button(e){this.$$set({show_fullscreen_button:e}),se()}get show_copy_button(){return this.$$.ctx[19]}set show_copy_button(e){this.$$set({show_copy_button:e}),se()}get value_is_output(){return this.$$.ctx[73]}set value_is_output(e){this.$$set({value_is_output:e}),se()}get max_chars(){return this.$$.ctx[20]}set max_chars(e){this.$$set({max_chars:e}),se()}get show_search(){return this.$$.ctx[21]}set show_search(e){this.$$set({show_search:e}),se()}get pinned_columns(){return this.$$.ctx[75]}set pinned_columns(e){this.$$set({pinned_columns:e}),se()}get static_columns(){return this.$$.ctx[22]}set static_columns(e){this.$$set({static_columns:e}),se()}get fullscreen(){return this.$$.ctx[23]}set fullscreen(e){this.$$set({fullscreen:e}),se()}get display_value(){return this.$$.ctx[76]}set display_value(e){this.$$set({display_value:e}),se()}get styling(){return this.$$.ctx[77]}set styling(e){this.$$set({styling:e}),se()}get reset_sort_state(){return this.$$.ctx[78]}}const ic=nc,{SvelteComponent:sc,add_flush_callback:sr,assign:rc,bind:rr,binding_callbacks:or,create_component:vn,destroy_component:yn,detach:oc,flush:re,get_spread_object:ac,get_spread_update:_c,init:uc,insert:fc,mount_component:Cn,safe_not_equal:cc,space:hc,transition_in:Sn,transition_out:Nn}=window.__gradio__svelte__internal;function dc(l){let e,t,n,i,r;const s=[{autoscroll:l[17].autoscroll},{i18n:l[17].i18n},l[20]];let o={};for(let a=0;a<s.length;a+=1)o=rc(o,s[a]);e=new ho({props:o}),e.$on("clear_status",l[29]);function f(a){l[32](a)}let u={root:l[14],label:l[8],show_label:l[9],row_count:l[7],col_count:l[6],values:l[0].data,display_value:l[0].metadata?.display_value,styling:l[0].metadata?.styling,headers:l[0].headers,fullscreen:l[2],wrap:l[10],datatype:l[11],latex_delimiters:l[18],editable:l[21],max_height:l[19],i18n:l[17].i18n,line_breaks:l[15],column_widths:l[16],upload:l[30],stream_handler:l[31],show_fullscreen_button:l[22],max_chars:l[23],show_copy_button:l[24],show_row_numbers:l[25],show_search:l[26],pinned_columns:l[27],components:{image:mo},static_columns:l[28]};return l[1]!==void 0&&(u.value_is_output=l[1]),n=new ic({props:u}),or.push(()=>rr(n,"value_is_output",f)),n.$on("change",l[33]),n.$on("input",l[34]),n.$on("select",l[35]),n.$on("fullscreen",l[36]),{c(){vn(e.$$.fragment),t=hc(),vn(n.$$.fragment)},m(a,_){Cn(e,a,_),fc(a,t,_),Cn(n,a,_),r=!0},p(a,_){const c=_[0]&1179648?_c(s,[_[0]&131072&&{autoscroll:a[17].autoscroll},_[0]&131072&&{i18n:a[17].i18n},_[0]&1048576&&ac(a[20])]):{};e.$set(c);const w={};_[0]&16384&&(w.root=a[14]),_[0]&256&&(w.label=a[8]),_[0]&512&&(w.show_label=a[9]),_[0]&128&&(w.row_count=a[7]),_[0]&64&&(w.col_count=a[6]),_[0]&1&&(w.values=a[0].data),_[0]&1&&(w.display_value=a[0].metadata?.display_value),_[0]&1&&(w.styling=a[0].metadata?.styling),_[0]&1&&(w.headers=a[0].headers),_[0]&4&&(w.fullscreen=a[2]),_[0]&1024&&(w.wrap=a[10]),_[0]&2048&&(w.datatype=a[11]),_[0]&262144&&(w.latex_delimiters=a[18]),_[0]&2097152&&(w.editable=a[21]),_[0]&524288&&(w.max_height=a[19]),_[0]&131072&&(w.i18n=a[17].i18n),_[0]&32768&&(w.line_breaks=a[15]),_[0]&65536&&(w.column_widths=a[16]),_[0]&131072&&(w.upload=a[30]),_[0]&131072&&(w.stream_handler=a[31]),_[0]&4194304&&(w.show_fullscreen_button=a[22]),_[0]&8388608&&(w.max_chars=a[23]),_[0]&16777216&&(w.show_copy_button=a[24]),_[0]&33554432&&(w.show_row_numbers=a[25]),_[0]&67108864&&(w.show_search=a[26]),_[0]&134217728&&(w.pinned_columns=a[27]),_[0]&268435456&&(w.static_columns=a[28]),!i&&_[0]&2&&(i=!0,w.value_is_output=a[1],sr(()=>i=!1)),n.$set(w)},i(a){r||(Sn(e.$$.fragment,a),Sn(n.$$.fragment,a),r=!0)},o(a){Nn(e.$$.fragment,a),Nn(n.$$.fragment,a),r=!1},d(a){a&&oc(t),yn(e,a),yn(n,a)}}}function mc(l){let e,t,n;function i(s){l[37](s)}let r={visible:l[5],padding:!1,elem_id:l[3],elem_classes:l[4],container:!1,scale:l[12],min_width:l[13],overflow_behavior:"visible",$$slots:{default:[dc]},$$scope:{ctx:l}};return l[2]!==void 0&&(r.fullscreen=l[2]),e=new io({props:r}),or.push(()=>rr(e,"fullscreen",i)),{c(){vn(e.$$.fragment)},m(s,o){Cn(e,s,o),n=!0},p(s,o){const f={};o[0]&32&&(f.visible=s[5]),o[0]&8&&(f.elem_id=s[3]),o[0]&16&&(f.elem_classes=s[4]),o[0]&4096&&(f.scale=s[12]),o[0]&8192&&(f.min_width=s[13]),o[0]&536858567|o[1]&128&&(f.$$scope={dirty:o,ctx:s}),!t&&o[0]&4&&(t=!0,f.fullscreen=s[2],sr(()=>t=!1)),e.$set(f)},i(s){n||(Sn(e.$$.fragment,s),n=!0)},o(s){Nn(e.$$.fragment,s),n=!1},d(s){yn(e,s)}}}function gc(l,e,t){let{elem_id:n=""}=e,{elem_classes:i=[]}=e,{visible:r=!0}=e,{value:s={data:[["","",""]],headers:["1","2","3"],metadata:null}}=e,{value_is_output:o=!1}=e,{col_count:f}=e,{row_count:u}=e,{label:a=null}=e,{show_label:_=!0}=e,{wrap:c}=e,{datatype:w}=e,{scale:L=null}=e,{min_width:v=void 0}=e,{root:q}=e,{line_breaks:b=!0}=e,{column_widths:p=[]}=e,{gradio:k}=e,{latex_delimiters:y}=e,{max_height:C=void 0}=e,{loading_status:j}=e,{interactive:S}=e,{show_fullscreen_button:z=!1}=e,{max_chars:O=void 0}=e,{show_copy_button:X=!1}=e,{show_row_numbers:I=!1}=e,{show_search:D="none"}=e,{pinned_columns:T=0}=e,{static_columns:Y=[]}=e,{fullscreen:ne=!1}=e;const K=()=>k.dispatch("clear_status",j),W=(...m)=>k.client.upload(...m),ce=(...m)=>k.client.stream(...m);function B(m){o=m,t(1,o)}const Z=m=>{t(0,s.data=m.detail.data,s),t(0,s.headers=m.detail.headers,s),k.dispatch("change")},N=m=>k.dispatch("input"),J=m=>k.dispatch("select",m.detail),g=({detail:m})=>{t(2,ne=m)};function M(m){ne=m,t(2,ne)}return l.$$set=m=>{"elem_id"in m&&t(3,n=m.elem_id),"elem_classes"in m&&t(4,i=m.elem_classes),"visible"in m&&t(5,r=m.visible),"value"in m&&t(0,s=m.value),"value_is_output"in m&&t(1,o=m.value_is_output),"col_count"in m&&t(6,f=m.col_count),"row_count"in m&&t(7,u=m.row_count),"label"in m&&t(8,a=m.label),"show_label"in m&&t(9,_=m.show_label),"wrap"in m&&t(10,c=m.wrap),"datatype"in m&&t(11,w=m.datatype),"scale"in m&&t(12,L=m.scale),"min_width"in m&&t(13,v=m.min_width),"root"in m&&t(14,q=m.root),"line_breaks"in m&&t(15,b=m.line_breaks),"column_widths"in m&&t(16,p=m.column_widths),"gradio"in m&&t(17,k=m.gradio),"latex_delimiters"in m&&t(18,y=m.latex_delimiters),"max_height"in m&&t(19,C=m.max_height),"loading_status"in m&&t(20,j=m.loading_status),"interactive"in m&&t(21,S=m.interactive),"show_fullscreen_button"in m&&t(22,z=m.show_fullscreen_button),"max_chars"in m&&t(23,O=m.max_chars),"show_copy_button"in m&&t(24,X=m.show_copy_button),"show_row_numbers"in m&&t(25,I=m.show_row_numbers),"show_search"in m&&t(26,D=m.show_search),"pinned_columns"in m&&t(27,T=m.pinned_columns),"static_columns"in m&&t(28,Y=m.static_columns),"fullscreen"in m&&t(2,ne=m.fullscreen)},[s,o,ne,n,i,r,f,u,a,_,c,w,L,v,q,b,p,k,y,C,j,S,z,O,X,I,D,T,Y,K,W,ce,B,Z,N,J,g,M]}class _h extends sc{constructor(e){super(),uc(this,e,gc,mc,cc,{elem_id:3,elem_classes:4,visible:5,value:0,value_is_output:1,col_count:6,row_count:7,label:8,show_label:9,wrap:10,datatype:11,scale:12,min_width:13,root:14,line_breaks:15,column_widths:16,gradio:17,latex_delimiters:18,max_height:19,loading_status:20,interactive:21,show_fullscreen_button:22,max_chars:23,show_copy_button:24,show_row_numbers:25,show_search:26,pinned_columns:27,static_columns:28,fullscreen:2},null,[-1,-1])}get elem_id(){return this.$$.ctx[3]}set elem_id(e){this.$$set({elem_id:e}),re()}get elem_classes(){return this.$$.ctx[4]}set elem_classes(e){this.$$set({elem_classes:e}),re()}get visible(){return this.$$.ctx[5]}set visible(e){this.$$set({visible:e}),re()}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),re()}get value_is_output(){return this.$$.ctx[1]}set value_is_output(e){this.$$set({value_is_output:e}),re()}get col_count(){return this.$$.ctx[6]}set col_count(e){this.$$set({col_count:e}),re()}get row_count(){return this.$$.ctx[7]}set row_count(e){this.$$set({row_count:e}),re()}get label(){return this.$$.ctx[8]}set label(e){this.$$set({label:e}),re()}get show_label(){return this.$$.ctx[9]}set show_label(e){this.$$set({show_label:e}),re()}get wrap(){return this.$$.ctx[10]}set wrap(e){this.$$set({wrap:e}),re()}get datatype(){return this.$$.ctx[11]}set datatype(e){this.$$set({datatype:e}),re()}get scale(){return this.$$.ctx[12]}set scale(e){this.$$set({scale:e}),re()}get min_width(){return this.$$.ctx[13]}set min_width(e){this.$$set({min_width:e}),re()}get root(){return this.$$.ctx[14]}set root(e){this.$$set({root:e}),re()}get line_breaks(){return this.$$.ctx[15]}set line_breaks(e){this.$$set({line_breaks:e}),re()}get column_widths(){return this.$$.ctx[16]}set column_widths(e){this.$$set({column_widths:e}),re()}get gradio(){return this.$$.ctx[17]}set gradio(e){this.$$set({gradio:e}),re()}get latex_delimiters(){return this.$$.ctx[18]}set latex_delimiters(e){this.$$set({latex_delimiters:e}),re()}get max_height(){return this.$$.ctx[19]}set max_height(e){this.$$set({max_height:e}),re()}get loading_status(){return this.$$.ctx[20]}set loading_status(e){this.$$set({loading_status:e}),re()}get interactive(){return this.$$.ctx[21]}set interactive(e){this.$$set({interactive:e}),re()}get show_fullscreen_button(){return this.$$.ctx[22]}set show_fullscreen_button(e){this.$$set({show_fullscreen_button:e}),re()}get max_chars(){return this.$$.ctx[23]}set max_chars(e){this.$$set({max_chars:e}),re()}get show_copy_button(){return this.$$.ctx[24]}set show_copy_button(e){this.$$set({show_copy_button:e}),re()}get show_row_numbers(){return this.$$.ctx[25]}set show_row_numbers(e){this.$$set({show_row_numbers:e}),re()}get show_search(){return this.$$.ctx[26]}set show_search(e){this.$$set({show_search:e}),re()}get pinned_columns(){return this.$$.ctx[27]}set pinned_columns(e){this.$$set({pinned_columns:e}),re()}get static_columns(){return this.$$.ctx[28]}set static_columns(e){this.$$set({static_columns:e}),re()}get fullscreen(){return this.$$.ctx[2]}set fullscreen(e){this.$$set({fullscreen:e}),re()}}export{ic as BaseDataFrame,ch as BaseExample,_h as default};
//# sourceMappingURL=Index-ALdaZ21I.js.map
