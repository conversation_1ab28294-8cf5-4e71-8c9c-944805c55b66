import{g as yl}from"./color-DTCUJ6g1.js";import{v as qe}from"./index-DJ2rNx9E.js";import{B as jl}from"./Block-CJdXVpa7.js";import"./MarkdownCode.svelte_svelte_type_style_lang-Bl8p72Ow.js";/* empty css                                                        */import{B as Sl}from"./BlockLabel-3KxTaaiM.js";import{E as zl}from"./Empty-ZqppqzTN.js";import{S as El}from"./index-DYtg3pip.js";import"./StreamingBar.svelte_svelte_type_style_lang-DlRzv56K.js";import"./svelte/svelte.js";import"./prism-python-CeMtt1IT.js";import"./IconButton-C_HS7fTi.js";import"./Clear-By3xiIwg.js";const{SvelteComponent:ut,append:Re,attr:A,detach:ht,init:dt,insert:gt,noop:Oe,safe_not_equal:mt,svg_element:Le}=window.__gradio__svelte__internal;function bt(l){let e,t,n;return{c(){e=Le("svg"),t=Le("path"),n=Le("path"),A(t,"fill","currentColor"),A(t,"d","M12 15H5a3 3 0 0 1-3-3v-2a3 3 0 0 1 3-3h5V5a1 1 0 0 0-1-1H3V2h6a3 3 0 0 1 3 3zM5 9a1 1 0 0 0-1 1v2a1 1 0 0 0 1 1h5V9zm15 14v2a1 1 0 0 0 1 1h5v-4h-5a1 1 0 0 0-1 1z"),A(n,"fill","currentColor"),A(n,"d","M2 30h28V2Zm26-2h-7a3 3 0 0 1-3-3v-2a3 3 0 0 1 3-3h5v-2a1 1 0 0 0-1-1h-6v-2h6a3 3 0 0 1 3 3Z"),A(e,"xmlns","http://www.w3.org/2000/svg"),A(e,"xmlns:xlink","http://www.w3.org/1999/xlink"),A(e,"aria-hidden","true"),A(e,"role","img"),A(e,"class","iconify iconify--carbon"),A(e,"width","100%"),A(e,"height","100%"),A(e,"preserveAspectRatio","xMidYMid meet"),A(e,"viewBox","0 0 32 32")},m(o,s){gt(o,e,s),Re(e,t),Re(e,n)},p:Oe,i:Oe,o:Oe,d(o){o&&ht(e)}}}class Se extends ut{constructor(e){super(),dt(this,e,null,bt,mt,{})}}function De(l,e,t){if(!t){var n=document.createElement("canvas");t=n.getContext("2d")}t.fillStyle=l,t.fillRect(0,0,1,1);const[o,s,i]=t.getImageData(0,0,1,1).data;return t.clearRect(0,0,1,1),`rgba(${o}, ${s}, ${i}, ${255/e})`}function $l(l,e,t,n){for(const o in l){const s=l[o].trim();s in qe?e[o]=qe[s]:e[o]={primary:t?De(l[o],1,n):l[o],secondary:t?De(l[o],.5,n):l[o]}}}function Cl(l,e){let t=[],n=null,o=null;for(const s of l)o===s.class_or_confidence?n=n?n+s.token:s.token:(n!==null&&t.push({token:n,class_or_confidence:o}),n=s.token,o=s.class_or_confidence);return n!==null&&t.push({token:n,class_or_confidence:o}),t}const{SvelteComponent:kt,append:Q,attr:H,destroy_each:ze,detach:I,element:Z,empty:Tl,ensure_array_like:x,flush:ce,init:pt,insert:q,listen:_e,noop:Ae,run_all:vt,safe_not_equal:wt,set_data:Me,set_style:pe,space:re,text:ue,toggle_class:X}=window.__gradio__svelte__internal,{createEventDispatcher:yt}=window.__gradio__svelte__internal;function Fe(l,e,t){const n=l.slice();n[18]=e[t];const o=typeof n[18].class_or_confidence=="string"?parseInt(n[18].class_or_confidence):n[18].class_or_confidence;return n[27]=o,n}function Ze(l,e,t){const n=l.slice();return n[18]=e[t],n[20]=t,n}function Ke(l,e,t){const n=l.slice();return n[21]=e[t],n[23]=t,n}function Pe(l,e,t){const n=l.slice();return n[24]=e[t][0],n[25]=e[t][1],n[20]=t,n}function jt(l){let e,t,n=l[1]&&Ue(),o=x(l[0]),s=[];for(let i=0;i<o.length;i+=1)s[i]=Ye(Fe(l,o,i));return{c(){n&&n.c(),e=re(),t=Z("div");for(let i=0;i<s.length;i+=1)s[i].c();H(t,"class","textfield svelte-ju12zg"),H(t,"data-testid","highlighted-text:textfield")},m(i,a){n&&n.m(i,a),q(i,e,a),q(i,t,a);for(let r=0;r<s.length;r+=1)s[r]&&s[r].m(t,null)},p(i,a){if(i[1]?n||(n=Ue(),n.c(),n.m(e.parentNode,e)):n&&(n.d(1),n=null),a&1){o=x(i[0]);let r;for(r=0;r<o.length;r+=1){const c=Fe(i,o,r);s[r]?s[r].p(c,a):(s[r]=Ye(c),s[r].c(),s[r].m(t,null))}for(;r<s.length;r+=1)s[r].d(1);s.length=o.length}},d(i){i&&(I(e),I(t)),n&&n.d(i),ze(s,i)}}}function St(l){let e,t,n=l[1]&&Ge(l),o=x(l[0]),s=[];for(let i=0;i<o.length;i+=1)s[i]=el(Ze(l,o,i));return{c(){n&&n.c(),e=re(),t=Z("div");for(let i=0;i<s.length;i+=1)s[i].c();H(t,"class","textfield svelte-ju12zg")},m(i,a){n&&n.m(i,a),q(i,e,a),q(i,t,a);for(let r=0;r<s.length;r+=1)s[r]&&s[r].m(t,null)},p(i,a){if(i[1]?n?n.p(i,a):(n=Ge(i),n.c(),n.m(e.parentNode,e)):n&&(n.d(1),n=null),a&223){o=x(i[0]);let r;for(r=0;r<o.length;r+=1){const c=Ze(i,o,r);s[r]?s[r].p(c,a):(s[r]=el(c),s[r].c(),s[r].m(t,null))}for(;r<s.length;r+=1)s[r].d(1);s.length=o.length}},d(i){i&&(I(e),I(t)),n&&n.d(i),ze(s,i)}}}function Ue(l){let e;return{c(){e=Z("div"),e.innerHTML="<span>-1</span> <span>0</span> <span>+1</span>",H(e,"class","color-legend svelte-ju12zg"),H(e,"data-testid","highlighted-text:color-legend")},m(t,n){q(t,e,n)},d(t){t&&I(e)}}}function Ye(l){let e,t,n=l[18].token+"",o,s,i;return{c(){e=Z("span"),t=Z("span"),o=ue(n),s=re(),H(t,"class","text svelte-ju12zg"),H(e,"class","textspan score-text svelte-ju12zg"),H(e,"style",i="background-color: rgba("+(l[27]&&l[27]<0?"128, 90, 213,"+-l[27]:"239, 68, 60,"+l[27])+")")},m(a,r){q(a,e,r),Q(e,t),Q(t,o),Q(e,s)},p(a,r){r&1&&n!==(n=a[18].token+"")&&Me(o,n),r&1&&i!==(i="background-color: rgba("+(a[27]&&a[27]<0?"128, 90, 213,"+-a[27]:"239, 68, 60,"+a[27])+")")&&H(e,"style",i)},d(a){a&&I(e)}}}function Ge(l){let e,t=x(Object.entries(l[6])),n=[];for(let o=0;o<t.length;o+=1)n[o]=Je(Pe(l,t,o));return{c(){e=Z("div");for(let o=0;o<n.length;o+=1)n[o].c();H(e,"class","category-legend svelte-ju12zg"),H(e,"data-testid","highlighted-text:category-legend")},m(o,s){q(o,e,s);for(let i=0;i<n.length;i+=1)n[i]&&n[i].m(e,null)},p(o,s){if(s&832){t=x(Object.entries(o[6]));let i;for(i=0;i<t.length;i+=1){const a=Pe(o,t,i);n[i]?n[i].p(a,s):(n[i]=Je(a),n[i].c(),n[i].m(e,null))}for(;i<n.length;i+=1)n[i].d(1);n.length=t.length}},d(o){o&&I(e),ze(n,o)}}}function Je(l){let e,t=l[24]+"",n,o,s,i;function a(){return l[11](l[24])}function r(){return l[12](l[24])}return{c(){e=Z("div"),n=ue(t),o=re(),H(e,"class","category-label svelte-ju12zg"),H(e,"style","background-color:"+l[25].secondary)},m(c,f){q(c,e,f),Q(e,n),Q(e,o),s||(i=[_e(e,"mouseover",a),_e(e,"focus",r),_e(e,"mouseout",l[13]),_e(e,"blur",l[14])],s=!0)},p(c,f){l=c},d(c){c&&I(e),s=!1,vt(i)}}}function Qe(l){let e,t,n=l[21]+"",o,s,i,a,r=!l[1]&&l[2]&&l[18].class_or_confidence!==null&&We(l);function c(){return l[15](l[20],l[18])}return{c(){e=Z("span"),t=Z("span"),o=ue(n),s=re(),r&&r.c(),H(t,"class","text svelte-ju12zg"),X(t,"no-label",l[18].class_or_confidence===null||!l[6][l[18].class_or_confidence]),H(e,"class","textspan svelte-ju12zg"),X(e,"no-cat",l[18].class_or_confidence===null||l[4]&&l[4]!==l[18].class_or_confidence),X(e,"hl",l[18].class_or_confidence!==null),X(e,"selectable",l[3]),pe(e,"background-color",l[18].class_or_confidence===null||l[4]&&l[4]!==l[18].class_or_confidence?"":l[6][l[18].class_or_confidence].secondary)},m(f,g){q(f,e,g),Q(e,t),Q(t,o),Q(e,s),r&&r.m(e,null),i||(a=_e(e,"click",c),i=!0)},p(f,g){l=f,g&1&&n!==(n=l[21]+"")&&Me(o,n),g&65&&X(t,"no-label",l[18].class_or_confidence===null||!l[6][l[18].class_or_confidence]),!l[1]&&l[2]&&l[18].class_or_confidence!==null?r?r.p(l,g):(r=We(l),r.c(),r.m(e,null)):r&&(r.d(1),r=null),g&17&&X(e,"no-cat",l[18].class_or_confidence===null||l[4]&&l[4]!==l[18].class_or_confidence),g&1&&X(e,"hl",l[18].class_or_confidence!==null),g&8&&X(e,"selectable",l[3]),g&17&&pe(e,"background-color",l[18].class_or_confidence===null||l[4]&&l[4]!==l[18].class_or_confidence?"":l[6][l[18].class_or_confidence].secondary)},d(f){f&&I(e),r&&r.d(),i=!1,a()}}}function We(l){let e,t,n=l[18].class_or_confidence+"",o;return{c(){e=ue(` 
								`),t=Z("span"),o=ue(n),H(t,"class","label svelte-ju12zg"),pe(t,"background-color",l[18].class_or_confidence===null||l[4]&&l[4]!==l[18].class_or_confidence?"":l[6][l[18].class_or_confidence].primary)},m(s,i){q(s,e,i),q(s,t,i),Q(t,o)},p(s,i){i&1&&n!==(n=s[18].class_or_confidence+"")&&Me(o,n),i&17&&pe(t,"background-color",s[18].class_or_confidence===null||s[4]&&s[4]!==s[18].class_or_confidence?"":s[6][s[18].class_or_confidence].primary)},d(s){s&&(I(e),I(t))}}}function Xe(l){let e;return{c(){e=Z("br")},m(t,n){q(t,e,n)},d(t){t&&I(e)}}}function xe(l){let e=l[21].trim()!=="",t,n=l[23]<ve(l[18].token).length-1,o,s=e&&Qe(l),i=n&&Xe();return{c(){s&&s.c(),t=re(),i&&i.c(),o=Tl()},m(a,r){s&&s.m(a,r),q(a,t,r),i&&i.m(a,r),q(a,o,r)},p(a,r){r&1&&(e=a[21].trim()!==""),e?s?s.p(a,r):(s=Qe(a),s.c(),s.m(t.parentNode,t)):s&&(s.d(1),s=null),r&1&&(n=a[23]<ve(a[18].token).length-1),n?i||(i=Xe(),i.c(),i.m(o.parentNode,o)):i&&(i.d(1),i=null)},d(a){a&&(I(t),I(o)),s&&s.d(a),i&&i.d(a)}}}function el(l){let e,t=x(ve(l[18].token)),n=[];for(let o=0;o<t.length;o+=1)n[o]=xe(Ke(l,t,o));return{c(){for(let o=0;o<n.length;o+=1)n[o].c();e=Tl()},m(o,s){for(let i=0;i<n.length;i+=1)n[i]&&n[i].m(o,s);q(o,e,s)},p(o,s){if(s&223){t=x(ve(o[18].token));let i;for(i=0;i<t.length;i+=1){const a=Ke(o,t,i);n[i]?n[i].p(a,s):(n[i]=xe(a),n[i].c(),n[i].m(e.parentNode,e))}for(;i<n.length;i+=1)n[i].d(1);n.length=t.length}},d(o){o&&I(e),ze(n,o)}}}function zt(l){let e;function t(s,i){return s[5]==="categories"?St:jt}let n=t(l),o=n(l);return{c(){e=Z("div"),o.c(),H(e,"class","container svelte-ju12zg")},m(s,i){q(s,e,i),o.m(e,null)},p(s,[i]){n===(n=t(s))&&o?o.p(s,i):(o.d(1),o=n(s),o&&(o.c(),o.m(e,null)))},i:Ae,o:Ae,d(s){s&&I(e),o.d()}}}function ve(l){return l.split(`
`)}function Et(l,e,t){const n=typeof document<"u";let{value:o=[]}=e,{show_legend:s=!1}=e,{show_inline_category:i=!0}=e,{color_map:a={}}=e,{selectable:r=!1}=e,c,f={},g="";const p=yt();let b;function _(m){t(4,g=m)}function h(){t(4,g="")}const w=m=>_(m),S=m=>_(m),y=()=>h(),d=()=>h(),j=(m,E)=>{p("select",{index:m,value:[E.token,E.class_or_confidence]})};return l.$$set=m=>{"value"in m&&t(0,o=m.value),"show_legend"in m&&t(1,s=m.show_legend),"show_inline_category"in m&&t(2,i=m.show_inline_category),"color_map"in m&&t(10,a=m.color_map),"selectable"in m&&t(3,r=m.selectable)},l.$$.update=()=>{if(l.$$.dirty&1025){if(a||t(10,a={}),o.length>0){for(let m of o)if(m.class_or_confidence!==null)if(typeof m.class_or_confidence=="string"){if(t(5,b="categories"),!(m.class_or_confidence in a)){let E=yl(Object.keys(a).length);t(10,a[m.class_or_confidence]=E,a)}}else t(5,b="scores")}$l(a,f,n,c)}},[o,s,i,r,g,b,f,p,_,h,a,w,S,y,d,j]}class $t extends kt{constructor(e){super(),pt(this,e,Et,zt,wt,{value:0,show_legend:1,show_inline_category:2,color_map:10,selectable:3})}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),ce()}get show_legend(){return this.$$.ctx[1]}set show_legend(e){this.$$set({show_legend:e}),ce()}get show_inline_category(){return this.$$.ctx[2]}set show_inline_category(e){this.$$set({show_inline_category:e}),ce()}get color_map(){return this.$$.ctx[10]}set color_map(e){this.$$set({color_map:e}),ce()}get selectable(){return this.$$.ctx[3]}set selectable(e){this.$$set({selectable:e}),ce()}}const Ct=$t,{SvelteComponent:Tt,attr:Y,detach:Ve,element:Nl,empty:Nt,flush:J,init:Ot,insert:He,listen:le,noop:ll,run_all:Ol,safe_not_equal:Lt,set_style:se}=window.__gradio__svelte__internal;function Mt(l){let e,t,n,o;return{c(){e=Nl("input"),Y(e,"class","label-input svelte-1cag2po"),e.autofocus=!0,Y(e,"type","number"),Y(e,"step","0.1"),Y(e,"style",t="background-color: rgba("+(typeof l[1]=="number"&&l[1]<0?"128, 90, 213,"+-l[1]:"239, 68, 60,"+l[1])+")"),e.value=l[1],se(e,"width","7ch")},m(s,i){He(s,e,i),e.focus(),n||(o=[le(e,"input",l[8]),le(e,"blur",l[14]),le(e,"keydown",l[15])],n=!0)},p(s,i){i&2&&t!==(t="background-color: rgba("+(typeof s[1]=="number"&&s[1]<0?"128, 90, 213,"+-s[1]:"239, 68, 60,"+s[1])+")")&&Y(e,"style",t),i&2&&e.value!==s[1]&&(e.value=s[1]);const a=i&2;(i&2||a)&&se(e,"width","7ch")},d(s){s&&Ve(e),n=!1,Ol(o)}}}function Vt(l){let e,t,n,o;return{c(){e=Nl("input"),Y(e,"class","label-input svelte-1cag2po"),e.autofocus=!0,Y(e,"id",t=`label-input-${l[3]}`),Y(e,"type","text"),Y(e,"placeholder","label"),e.value=l[1],se(e,"background-color",l[1]===null||l[2]&&l[2]!==l[1]?"":l[6][l[1]].primary),se(e,"width",l[7]?l[7].toString()?.length+4+"ch":"8ch")},m(s,i){He(s,e,i),e.focus(),n||(o=[le(e,"input",l[8]),le(e,"blur",l[12]),le(e,"keydown",l[13]),le(e,"focus",Bt)],n=!0)},p(s,i){i&8&&t!==(t=`label-input-${s[3]}`)&&Y(e,"id",t),i&2&&e.value!==s[1]&&(e.value=s[1]),i&70&&se(e,"background-color",s[1]===null||s[2]&&s[2]!==s[1]?"":s[6][s[1]].primary),i&128&&se(e,"width",s[7]?s[7].toString()?.length+4+"ch":"8ch")},d(s){s&&Ve(e),n=!1,Ol(o)}}}function Ht(l){let e;function t(s,i){return s[5]?Mt:Vt}let n=t(l),o=n(l);return{c(){o.c(),e=Nt()},m(s,i){o.m(s,i),He(s,e,i)},p(s,[i]){n===(n=t(s))&&o?o.p(s,i):(o.d(1),o=n(s),o&&(o.c(),o.m(e.parentNode,e)))},i:ll,o:ll,d(s){s&&Ve(e),o.d(s)}}}function Bt(l){let e=l.target;e&&e.placeholder&&(e.placeholder="")}function It(l,e,t){let{value:n}=e,{category:o}=e,{active:s}=e,{labelToEdit:i}=e,{indexOfLabel:a}=e,{text:r}=e,{handleValueChange:c}=e,{isScoresMode:f=!1}=e,{_color_map:g}=e,p=o;function b(d){let j=d.target;j&&t(7,p=j.value)}function _(d,j,m){let E=d.target;t(10,n=[...n.slice(0,j),{token:m,class_or_confidence:E.value===""?null:f?Number(E.value):E.value},...n.slice(j+1)]),c()}const h=d=>_(d,a,r),w=d=>{d.key==="Enter"&&(_(d,a,r),t(0,i=-1))},S=d=>_(d,a,r),y=d=>{d.key==="Enter"&&(_(d,a,r),t(0,i=-1))};return l.$$set=d=>{"value"in d&&t(10,n=d.value),"category"in d&&t(1,o=d.category),"active"in d&&t(2,s=d.active),"labelToEdit"in d&&t(0,i=d.labelToEdit),"indexOfLabel"in d&&t(3,a=d.indexOfLabel),"text"in d&&t(4,r=d.text),"handleValueChange"in d&&t(11,c=d.handleValueChange),"isScoresMode"in d&&t(5,f=d.isScoresMode),"_color_map"in d&&t(6,g=d._color_map)},[i,o,s,a,r,f,g,p,b,_,n,c,h,w,S,y]}class Ll extends Tt{constructor(e){super(),Ot(this,e,It,Ht,Lt,{value:10,category:1,active:2,labelToEdit:0,indexOfLabel:3,text:4,handleValueChange:11,isScoresMode:5,_color_map:6})}get value(){return this.$$.ctx[10]}set value(e){this.$$set({value:e}),J()}get category(){return this.$$.ctx[1]}set category(e){this.$$set({category:e}),J()}get active(){return this.$$.ctx[2]}set active(e){this.$$set({active:e}),J()}get labelToEdit(){return this.$$.ctx[0]}set labelToEdit(e){this.$$set({labelToEdit:e}),J()}get indexOfLabel(){return this.$$.ctx[3]}set indexOfLabel(e){this.$$set({indexOfLabel:e}),J()}get text(){return this.$$.ctx[4]}set text(e){this.$$set({text:e}),J()}get handleValueChange(){return this.$$.ctx[11]}set handleValueChange(e){this.$$set({handleValueChange:e}),J()}get isScoresMode(){return this.$$.ctx[5]}set isScoresMode(e){this.$$set({isScoresMode:e}),J()}get _color_map(){return this.$$.ctx[6]}set _color_map(e){this.$$set({_color_map:e}),J()}}const{SvelteComponent:qt,add_flush_callback:Ml,append:D,attr:v,bind:Vl,binding_callbacks:Hl,check_outros:oe,create_component:Bl,destroy_component:Il,destroy_each:Ee,detach:O,element:B,empty:Be,ensure_array_like:ee,flush:ke,group_outros:ie,init:Rt,insert:L,listen:$,mount_component:ql,run_all:ae,safe_not_equal:Dt,set_data:$e,set_style:we,space:G,text:ge,toggle_class:F,transition_in:T,transition_out:R}=window.__gradio__svelte__internal,{createEventDispatcher:At,onMount:Ft}=window.__gradio__svelte__internal;function tl(l,e,t){const n=l.slice();n[45]=e[t].token,n[46]=e[t].class_or_confidence,n[48]=t;const o=typeof n[46]=="string"?parseInt(n[46]):n[46];return n[54]=o,n}function nl(l,e,t){const n=l.slice();return n[45]=e[t].token,n[46]=e[t].class_or_confidence,n[48]=t,n}function ol(l,e,t){const n=l.slice();return n[49]=e[t],n[51]=t,n}function il(l,e,t){const n=l.slice();return n[46]=e[t][0],n[52]=e[t][1],n[48]=t,n}function Zt(l){let e,t,n,o=l[1]&&sl(),s=ee(l[0]),i=[];for(let r=0;r<s.length;r+=1)i[r]=fl(tl(l,s,r));const a=r=>R(i[r],1,1,()=>{i[r]=null});return{c(){o&&o.c(),e=G(),t=B("div");for(let r=0;r<i.length;r+=1)i[r].c();v(t,"class","textfield svelte-1ozsnjl"),v(t,"data-testid","highlighted-text:textfield")},m(r,c){o&&o.m(r,c),L(r,e,c),L(r,t,c);for(let f=0;f<i.length;f+=1)i[f]&&i[f].m(t,null);n=!0},p(r,c){if(r[1]?o||(o=sl(),o.c(),o.m(e.parentNode,e)):o&&(o.d(1),o=null),c[0]&889){s=ee(r[0]);let f;for(f=0;f<s.length;f+=1){const g=tl(r,s,f);i[f]?(i[f].p(g,c),T(i[f],1)):(i[f]=fl(g),i[f].c(),T(i[f],1),i[f].m(t,null))}for(ie(),f=s.length;f<i.length;f+=1)a(f);oe()}},i(r){if(!n){for(let c=0;c<s.length;c+=1)T(i[c]);n=!0}},o(r){i=i.filter(Boolean);for(let c=0;c<i.length;c+=1)R(i[c]);n=!1},d(r){r&&(O(e),O(t)),o&&o.d(r),Ee(i,r)}}}function Kt(l){let e,t,n,o=l[1]&&cl(l),s=ee(l[0]),i=[];for(let r=0;r<s.length;r+=1)i[r]=pl(nl(l,s,r));const a=r=>R(i[r],1,1,()=>{i[r]=null});return{c(){o&&o.c(),e=G(),t=B("div");for(let r=0;r<i.length;r+=1)i[r].c();v(t,"class","textfield svelte-1ozsnjl")},m(r,c){o&&o.m(r,c),L(r,e,c),L(r,t,c);for(let f=0;f<i.length;f+=1)i[f]&&i[f].m(t,null);n=!0},p(r,c){if(r[1]?o?o.p(r,c):(o=cl(r),o.c(),o.m(e.parentNode,e)):o&&(o.d(1),o=null),c[0]&13183){s=ee(r[0]);let f;for(f=0;f<s.length;f+=1){const g=nl(r,s,f);i[f]?(i[f].p(g,c),T(i[f],1)):(i[f]=pl(g),i[f].c(),T(i[f],1),i[f].m(t,null))}for(ie(),f=s.length;f<i.length;f+=1)a(f);oe()}},i(r){if(!n){for(let c=0;c<s.length;c+=1)T(i[c]);n=!0}},o(r){i=i.filter(Boolean);for(let c=0;c<i.length;c+=1)R(i[c]);n=!1},d(r){r&&(O(e),O(t)),o&&o.d(r),Ee(i,r)}}}function sl(l){let e;return{c(){e=B("div"),e.innerHTML="<span>-1</span> <span>0</span> <span>+1</span>",v(e,"class","color-legend svelte-1ozsnjl"),v(e,"data-testid","highlighted-text:color-legend")},m(t,n){L(t,e,n)},d(t){t&&O(e)}}}function rl(l){let e,t,n;function o(i){l[32](i)}let s={labelToEdit:l[6],_color_map:l[3],category:l[46],active:l[5],indexOfLabel:l[48],text:l[45],handleValueChange:l[9],isScoresMode:!0};return l[0]!==void 0&&(s.value=l[0]),e=new Ll({props:s}),Hl.push(()=>Vl(e,"value",o)),{c(){Bl(e.$$.fragment)},m(i,a){ql(e,i,a),n=!0},p(i,a){const r={};a[0]&64&&(r.labelToEdit=i[6]),a[0]&8&&(r._color_map=i[3]),a[0]&1&&(r.category=i[46]),a[0]&32&&(r.active=i[5]),a[0]&1&&(r.text=i[45]),!t&&a[0]&1&&(t=!0,r.value=i[0],Ml(()=>t=!1)),e.$set(r)},i(i){n||(T(e.$$.fragment,i),n=!0)},o(i){R(e.$$.fragment,i),n=!1},d(i){Il(e,i)}}}function al(l){let e,t,n;function o(){return l[37](l[48])}function s(...i){return l[38](l[48],...i)}return{c(){e=B("span"),e.textContent="×",v(e,"class","label-clear-button svelte-1ozsnjl"),v(e,"role","button"),v(e,"aria-roledescription","Remove label from text"),v(e,"tabindex","0")},m(i,a){L(i,e,a),t||(n=[$(e,"click",o),$(e,"keydown",s)],t=!0)},p(i,a){l=i},d(i){i&&O(e),t=!1,ae(n)}}}function fl(l){let e,t,n,o=l[45]+"",s,i,a,r,c,f,g,p,b=l[46]&&l[6]===l[48]&&rl(l);function _(){return l[33](l[48])}function h(){return l[34](l[48])}function w(){return l[35](l[48])}function S(...d){return l[36](l[48],...d)}let y=l[46]&&l[4]===l[48]&&al(l);return{c(){e=B("span"),t=B("span"),n=B("span"),s=ge(o),i=G(),b&&b.c(),r=G(),y&&y.c(),c=G(),v(n,"class","text svelte-1ozsnjl"),v(t,"class","textspan score-text svelte-1ozsnjl"),v(t,"role","button"),v(t,"tabindex","0"),v(t,"style",a="background-color: rgba("+(l[54]&&l[54]<0?"128, 90, 213,"+-l[54]:"239, 68, 60,"+l[54])+")"),F(t,"no-cat",l[46]===null||l[5]&&l[5]!==l[46]),F(t,"hl",l[46]!==null),v(e,"class","score-text-container svelte-1ozsnjl")},m(d,j){L(d,e,j),D(e,t),D(t,n),D(n,s),D(t,i),b&&b.m(t,null),D(e,r),y&&y.m(e,null),D(e,c),f=!0,g||(p=[$(t,"mouseover",_),$(t,"focus",h),$(t,"click",w),$(t,"keydown",S)],g=!0)},p(d,j){l=d,(!f||j[0]&1)&&o!==(o=l[45]+"")&&$e(s,o),l[46]&&l[6]===l[48]?b?(b.p(l,j),j[0]&65&&T(b,1)):(b=rl(l),b.c(),T(b,1),b.m(t,null)):b&&(ie(),R(b,1,1,()=>{b=null}),oe()),(!f||j[0]&1&&a!==(a="background-color: rgba("+(l[54]&&l[54]<0?"128, 90, 213,"+-l[54]:"239, 68, 60,"+l[54])+")"))&&v(t,"style",a),(!f||j[0]&33)&&F(t,"no-cat",l[46]===null||l[5]&&l[5]!==l[46]),(!f||j[0]&1)&&F(t,"hl",l[46]!==null),l[46]&&l[4]===l[48]?y?y.p(l,j):(y=al(l),y.c(),y.m(e,c)):y&&(y.d(1),y=null)},i(d){f||(T(b),f=!0)},o(d){R(b),f=!1},d(d){d&&O(e),b&&b.d(),y&&y.d(),g=!1,ae(p)}}}function cl(l){let e,t=l[3]&&_l(l);return{c(){e=B("div"),t&&t.c(),v(e,"class","class_or_confidence-legend svelte-1ozsnjl"),v(e,"data-testid","highlighted-text:class_or_confidence-legend")},m(n,o){L(n,e,o),t&&t.m(e,null)},p(n,o){n[3]?t?t.p(n,o):(t=_l(n),t.c(),t.m(e,null)):t&&(t.d(1),t=null)},d(n){n&&O(e),t&&t.d()}}}function _l(l){let e,t=ee(Object.entries(l[3])),n=[];for(let o=0;o<t.length;o+=1)n[o]=ul(il(l,t,o));return{c(){for(let o=0;o<n.length;o+=1)n[o].c();e=Be()},m(o,s){for(let i=0;i<n.length;i+=1)n[i]&&n[i].m(o,s);L(o,e,s)},p(o,s){if(s[0]&3080){t=ee(Object.entries(o[3]));let i;for(i=0;i<t.length;i+=1){const a=il(o,t,i);n[i]?n[i].p(a,s):(n[i]=ul(a),n[i].c(),n[i].m(e.parentNode,e))}for(;i<n.length;i+=1)n[i].d(1);n.length=t.length}},d(o){o&&O(e),Ee(n,o)}}}function ul(l){let e,t=l[46]+"",n,o,s,i,a;function r(){return l[15](l[46])}function c(){return l[16](l[46])}return{c(){e=B("div"),n=ge(t),o=G(),v(e,"role","button"),v(e,"aria-roledescription","Categories of highlighted text. Hover to see text with this class_or_confidence highlighted."),v(e,"tabindex","0"),v(e,"class","class_or_confidence-label svelte-1ozsnjl"),v(e,"style",s="background-color:"+l[52].secondary)},m(f,g){L(f,e,g),D(e,n),D(e,o),i||(a=[$(e,"mouseover",r),$(e,"focus",c),$(e,"mouseout",l[17]),$(e,"blur",l[18])],i=!0)},p(f,g){l=f,g[0]&8&&t!==(t=l[46]+"")&&$e(n,t),g[0]&8&&s!==(s="background-color:"+l[52].secondary)&&v(e,"style",s)},d(f){f&&O(e),i=!1,ae(a)}}}function hl(l){let e,t,n,o=l[49]+"",s,i,a,r,c,f,g;function p(){return l[20](l[48])}function b(){return l[21](l[48])}function _(){return l[22](l[48])}let h=!l[1]&&l[46]!==null&&l[6]!==l[48]&&dl(l),w=l[6]===l[48]&&l[46]!==null&&gl(l);function S(){return l[26](l[46],l[48],l[45])}function y(...E){return l[27](l[46],l[48],l[45],...E)}function d(){return l[28](l[48])}function j(){return l[29](l[48])}let m=l[46]!==null&&ml(l);return{c(){e=B("span"),t=B("span"),n=B("span"),s=ge(o),i=G(),h&&h.c(),a=G(),w&&w.c(),r=G(),m&&m.c(),v(n,"class","text svelte-1ozsnjl"),v(n,"role","button"),v(n,"tabindex","0"),F(n,"no-label",l[46]===null),v(t,"role","button"),v(t,"tabindex","0"),v(t,"class","textspan svelte-1ozsnjl"),F(t,"no-cat",l[46]===null||l[5]&&l[5]!==l[46]),F(t,"hl",l[46]!==null),F(t,"selectable",l[2]),we(t,"background-color",l[46]===null||l[5]&&l[5]!==l[46]?"":l[46]&&l[3][l[46]]?l[3][l[46]].secondary:""),v(e,"class","text-class_or_confidence-container svelte-1ozsnjl")},m(E,M){L(E,e,M),D(e,t),D(t,n),D(n,s),D(t,i),h&&h.m(t,null),D(t,a),w&&w.m(t,null),D(e,r),m&&m.m(e,null),c=!0,f||(g=[$(n,"keydown",l[19]),$(n,"focus",p),$(n,"mouseover",b),$(n,"click",_),$(t,"click",S),$(t,"keydown",y),$(t,"focus",d),$(t,"mouseover",j)],f=!0)},p(E,M){l=E,(!c||M[0]&1)&&o!==(o=l[49]+"")&&$e(s,o),(!c||M[0]&1)&&F(n,"no-label",l[46]===null),!l[1]&&l[46]!==null&&l[6]!==l[48]?h?h.p(l,M):(h=dl(l),h.c(),h.m(t,a)):h&&(h.d(1),h=null),l[6]===l[48]&&l[46]!==null?w?(w.p(l,M),M[0]&65&&T(w,1)):(w=gl(l),w.c(),T(w,1),w.m(t,null)):w&&(ie(),R(w,1,1,()=>{w=null}),oe()),(!c||M[0]&33)&&F(t,"no-cat",l[46]===null||l[5]&&l[5]!==l[46]),(!c||M[0]&1)&&F(t,"hl",l[46]!==null),(!c||M[0]&4)&&F(t,"selectable",l[2]),M[0]&41&&we(t,"background-color",l[46]===null||l[5]&&l[5]!==l[46]?"":l[46]&&l[3][l[46]]?l[3][l[46]].secondary:""),l[46]!==null?m?m.p(l,M):(m=ml(l),m.c(),m.m(e,null)):m&&(m.d(1),m=null)},i(E){c||(T(w),c=!0)},o(E){R(w),c=!1},d(E){E&&O(e),h&&h.d(),w&&w.d(),m&&m.d(),f=!1,ae(g)}}}function dl(l){let e,t=l[46]+"",n,o,s;function i(){return l[23](l[48])}function a(){return l[24](l[48])}return{c(){e=B("span"),n=ge(t),v(e,"id",`label-tag-${l[48]}`),v(e,"class","label svelte-1ozsnjl"),v(e,"role","button"),v(e,"tabindex","0"),we(e,"background-color",l[46]===null||l[5]&&l[5]!==l[46]?"":l[3][l[46]].primary)},m(r,c){L(r,e,c),D(e,n),o||(s=[$(e,"click",i),$(e,"keydown",a)],o=!0)},p(r,c){l=r,c[0]&1&&t!==(t=l[46]+"")&&$e(n,t),c[0]&41&&we(e,"background-color",l[46]===null||l[5]&&l[5]!==l[46]?"":l[3][l[46]].primary)},d(r){r&&O(e),o=!1,ae(s)}}}function gl(l){let e,t,n,o;function s(a){l[25](a)}let i={labelToEdit:l[6],category:l[46],active:l[5],_color_map:l[3],indexOfLabel:l[48],text:l[45],handleValueChange:l[9]};return l[0]!==void 0&&(i.value=l[0]),t=new Ll({props:i}),Hl.push(()=>Vl(t,"value",s)),{c(){e=ge(` 
									`),Bl(t.$$.fragment)},m(a,r){L(a,e,r),ql(t,a,r),o=!0},p(a,r){const c={};r[0]&64&&(c.labelToEdit=a[6]),r[0]&1&&(c.category=a[46]),r[0]&32&&(c.active=a[5]),r[0]&8&&(c._color_map=a[3]),r[0]&1&&(c.text=a[45]),!n&&r[0]&1&&(n=!0,c.value=a[0],Ml(()=>n=!1)),t.$set(c)},i(a){o||(T(t.$$.fragment,a),o=!0)},o(a){R(t.$$.fragment,a),o=!1},d(a){a&&O(e),Il(t,a)}}}function ml(l){let e,t,n;function o(){return l[30](l[48])}function s(...i){return l[31](l[48],...i)}return{c(){e=B("span"),e.textContent="×",v(e,"class","label-clear-button svelte-1ozsnjl"),v(e,"role","button"),v(e,"aria-roledescription","Remove label from text"),v(e,"tabindex","0")},m(i,a){L(i,e,a),t||(n=[$(e,"click",o),$(e,"keydown",s)],t=!0)},p(i,a){l=i},d(i){i&&O(e),t=!1,ae(n)}}}function bl(l){let e;return{c(){e=B("br")},m(t,n){L(t,e,n)},d(t){t&&O(e)}}}function kl(l){let e=l[49].trim()!=="",t,n=l[51]<ye(l[45]).length-1,o,s,i=e&&hl(l),a=n&&bl();return{c(){i&&i.c(),t=G(),a&&a.c(),o=Be()},m(r,c){i&&i.m(r,c),L(r,t,c),a&&a.m(r,c),L(r,o,c),s=!0},p(r,c){c[0]&1&&(e=r[49].trim()!==""),e?i?(i.p(r,c),c[0]&1&&T(i,1)):(i=hl(r),i.c(),T(i,1),i.m(t.parentNode,t)):i&&(ie(),R(i,1,1,()=>{i=null}),oe()),c[0]&1&&(n=r[51]<ye(r[45]).length-1),n?a||(a=bl(),a.c(),a.m(o.parentNode,o)):a&&(a.d(1),a=null)},i(r){s||(T(i),s=!0)},o(r){R(i),s=!1},d(r){r&&(O(t),O(o)),i&&i.d(r),a&&a.d(r)}}}function pl(l){let e,t,n=ee(ye(l[45])),o=[];for(let i=0;i<n.length;i+=1)o[i]=kl(ol(l,n,i));const s=i=>R(o[i],1,1,()=>{o[i]=null});return{c(){for(let i=0;i<o.length;i+=1)o[i].c();e=Be()},m(i,a){for(let r=0;r<o.length;r+=1)o[r]&&o[r].m(i,a);L(i,e,a),t=!0},p(i,a){if(a[0]&13183){n=ee(ye(i[45]));let r;for(r=0;r<n.length;r+=1){const c=ol(i,n,r);o[r]?(o[r].p(c,a),T(o[r],1)):(o[r]=kl(c),o[r].c(),T(o[r],1),o[r].m(e.parentNode,e))}for(ie(),r=n.length;r<o.length;r+=1)s(r);oe()}},i(i){if(!t){for(let a=0;a<n.length;a+=1)T(o[a]);t=!0}},o(i){o=o.filter(Boolean);for(let a=0;a<o.length;a+=1)R(o[a]);t=!1},d(i){i&&O(e),Ee(o,i)}}}function Pt(l){let e,t,n,o;const s=[Kt,Zt],i=[];function a(r,c){return r[7]==="categories"?0:1}return t=a(l),n=i[t]=s[t](l),{c(){e=B("div"),n.c(),v(e,"class","container svelte-1ozsnjl")},m(r,c){L(r,e,c),i[t].m(e,null),o=!0},p(r,c){let f=t;t=a(r),t===f?i[t].p(r,c):(ie(),R(i[f],1,1,()=>{i[f]=null}),oe(),n=i[t],n?n.p(r,c):(n=i[t]=s[t](r),n.c()),T(n,1),n.m(e,null))},i(r){o||(T(n),o=!0)},o(r){R(n),o=!1},d(r){r&&O(e),i[t].d()}}}function ye(l){return l.split(`
`)}function Ut(l,e,t){const n=typeof document<"u";let{value:o=[]}=e,{show_legend:s=!1}=e,{color_map:i={}}=e,{selectable:a=!1}=e,r=-1,c,f={},g="",p,b=-1;Ft(()=>{const u=()=>{p=window.getSelection(),E(),window.removeEventListener("mouseup",u)};window.addEventListener("mousedown",()=>{window.addEventListener("mouseup",u)})});async function _(u,N){if(p?.toString()&&r!==-1&&o[r].token.toString().includes(p.toString())){const W=Symbol(),fe=o[r].token,[at,ft,ct]=[fe.substring(0,u),fe.substring(u,N),fe.substring(N)];let me=[...o.slice(0,r),{token:at,class_or_confidence:null},{token:ft,class_or_confidence:y==="scores"?1:"label",flag:W},{token:ct,class_or_confidence:null},...o.slice(r+1)];t(6,b=me.findIndex(({flag:be})=>be===W)),me=me.filter(be=>be.token.trim()!==""),t(0,o=me.map(({flag:be,..._t})=>_t)),S(),document.getElementById(`label-input-${b}`)?.focus()}}const h=At();function w(u){!o||u<0||u>=o.length||(t(0,o[u].class_or_confidence=null,o),t(0,o=Cl(o)),S(),window.getSelection()?.empty())}function S(){h("change",o),t(6,b=-1),s&&(t(14,i={}),t(3,f={}))}let y;function d(u){t(5,g=u)}function j(){t(5,g="")}async function m(u){p=window.getSelection(),u.key==="Enter"&&E()}function E(){if(p&&p?.toString().trim()!==""){const u=p.getRangeAt(0).startOffset,N=p.getRangeAt(0).endOffset;_(u,N)}}function M(u,N,W){h("select",{index:u,value:[N,W]})}const Ce=u=>d(u),Te=u=>d(u),Ne=()=>j(),k=()=>j(),Fl=u=>m(u),Zl=u=>t(4,r=u),Kl=u=>t(4,r=u),Pl=u=>t(6,b=u),Ul=u=>t(6,b=u),Yl=u=>t(6,b=u);function Gl(u){o=u,t(0,o)}const Jl=(u,N,W)=>{u!==null&&M(N,W,u)},Ql=(u,N,W,fe)=>{u!==null?(t(6,b=N),M(N,W,u)):m(fe)},Wl=u=>t(4,r=u),Xl=u=>t(4,r=u),xl=u=>w(u),et=(u,N)=>{N.key==="Enter"&&w(u)};function lt(u){o=u,t(0,o)}const tt=u=>t(4,r=u),nt=u=>t(4,r=u),ot=u=>t(6,b=u),it=(u,N)=>{N.key==="Enter"&&t(6,b=u)},st=u=>w(u),rt=(u,N)=>{N.key==="Enter"&&w(u)};return l.$$set=u=>{"value"in u&&t(0,o=u.value),"show_legend"in u&&t(1,s=u.show_legend),"color_map"in u&&t(14,i=u.color_map),"selectable"in u&&t(2,a=u.selectable)},l.$$.update=()=>{if(l.$$.dirty[0]&16393){if(i||t(14,i={}),o.length>0){for(let u of o)if(u.class_or_confidence!==null)if(typeof u.class_or_confidence=="string"){if(t(7,y="categories"),!(u.class_or_confidence in i)){let N=yl(Object.keys(i).length);t(14,i[u.class_or_confidence]=N,i)}}else t(7,y="scores")}$l(i,f,n,c)}},[o,s,a,f,r,g,b,y,w,S,d,j,m,M,i,Ce,Te,Ne,k,Fl,Zl,Kl,Pl,Ul,Yl,Gl,Jl,Ql,Wl,Xl,xl,et,lt,tt,nt,ot,it,st,rt]}class Yt extends qt{constructor(e){super(),Rt(this,e,Ut,Pt,Dt,{value:0,show_legend:1,color_map:14,selectable:2},null,[-1,-1])}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),ke()}get show_legend(){return this.$$.ctx[1]}set show_legend(e){this.$$set({show_legend:e}),ke()}get color_map(){return this.$$.ctx[14]}set color_map(e){this.$$set({color_map:e}),ke()}get selectable(){return this.$$.ctx[2]}set selectable(e){this.$$set({selectable:e}),ke()}}const Gt=Yt,{SvelteComponent:Jt,add_flush_callback:Qt,assign:Rl,bind:Wt,binding_callbacks:Xt,check_outros:he,create_component:K,destroy_component:P,detach:te,empty:Ie,flush:V,get_spread_object:Dl,get_spread_update:Al,group_outros:de,init:xt,insert:ne,mount_component:U,safe_not_equal:en,space:je,transition_in:z,transition_out:C}=window.__gradio__svelte__internal;function ln(l){let e,t;return e=new jl({props:{variant:l[13]?"dashed":"solid",test_id:"highlighted-text",visible:l[5],elem_id:l[3],elem_classes:l[4],padding:!1,container:l[9],scale:l[10],min_width:l[11],$$slots:{default:[rn]},$$scope:{ctx:l}}}),{c(){K(e.$$.fragment)},m(n,o){U(e,n,o),t=!0},p(n,o){const s={};o&8192&&(s.variant=n[13]?"dashed":"solid"),o&32&&(s.visible=n[5]),o&8&&(s.elem_id=n[3]),o&16&&(s.elem_classes=n[4]),o&512&&(s.container=n[9]),o&1024&&(s.scale=n[10]),o&2048&&(s.min_width=n[11]),o&16896839&&(s.$$scope={dirty:o,ctx:n}),e.$set(s)},i(n){t||(z(e.$$.fragment,n),t=!0)},o(n){C(e.$$.fragment,n),t=!1},d(n){P(e,n)}}}function tn(l){let e,t;return e=new jl({props:{variant:"solid",test_id:"highlighted-text",visible:l[5],elem_id:l[3],elem_classes:l[4],padding:!1,container:l[9],scale:l[10],min_width:l[11],rtl:l[15],$$slots:{default:[_n]},$$scope:{ctx:l}}}),{c(){K(e.$$.fragment)},m(n,o){U(e,n,o),t=!0},p(n,o){const s={};o&32&&(s.visible=n[5]),o&8&&(s.elem_id=n[3]),o&16&&(s.elem_classes=n[4]),o&512&&(s.container=n[9]),o&1024&&(s.scale=n[10]),o&2048&&(s.min_width=n[11]),o&32768&&(s.rtl=n[15]),o&16896967&&(s.$$scope={dirty:o,ctx:n}),e.$set(s)},i(n){t||(z(e.$$.fragment,n),t=!0)},o(n){C(e.$$.fragment,n),t=!1},d(n){P(e,n)}}}function vl(l){let e,t;return e=new Sl({props:{Icon:Se,label:l[8],float:!1,disable:l[9]===!1,show_label:l[14],rtl:l[15]}}),{c(){K(e.$$.fragment)},m(n,o){U(e,n,o),t=!0},p(n,o){const s={};o&256&&(s.label=n[8]),o&512&&(s.disable=n[9]===!1),o&16384&&(s.show_label=n[14]),o&32768&&(s.rtl=n[15]),e.$set(s)},i(n){t||(z(e.$$.fragment,n),t=!0)},o(n){C(e.$$.fragment,n),t=!1},d(n){P(e,n)}}}function nn(l){let e,t;return e=new zl({props:{size:"small",unpadded_box:!0,$$slots:{default:[sn]},$$scope:{ctx:l}}}),{c(){K(e.$$.fragment)},m(n,o){U(e,n,o),t=!0},p(n,o){const s={};o&16777216&&(s.$$scope={dirty:o,ctx:n}),e.$set(s)},i(n){t||(z(e.$$.fragment,n),t=!0)},o(n){C(e.$$.fragment,n),t=!1},d(n){P(e,n)}}}function on(l){let e,t,n;function o(i){l[22](i)}let s={selectable:l[12],show_legend:l[6],color_map:l[1]};return l[0]!==void 0&&(s.value=l[0]),e=new Gt({props:s}),Xt.push(()=>Wt(e,"value",o)),e.$on("change",l[23]),{c(){K(e.$$.fragment)},m(i,a){U(e,i,a),n=!0},p(i,a){const r={};a&4096&&(r.selectable=i[12]),a&64&&(r.show_legend=i[6]),a&2&&(r.color_map=i[1]),!t&&a&1&&(t=!0,r.value=i[0],Qt(()=>t=!1)),e.$set(r)},i(i){n||(z(e.$$.fragment,i),n=!0)},o(i){C(e.$$.fragment,i),n=!1},d(i){P(e,i)}}}function sn(l){let e,t;return e=new Se({}),{c(){K(e.$$.fragment)},m(n,o){U(e,n,o),t=!0},i(n){t||(z(e.$$.fragment,n),t=!0)},o(n){C(e.$$.fragment,n),t=!1},d(n){P(e,n)}}}function rn(l){let e,t,n,o,s,i,a;const r=[{autoscroll:l[2].autoscroll},l[16],{i18n:l[2].i18n}];let c={};for(let _=0;_<r.length;_+=1)c=Rl(c,r[_]);e=new El({props:c}),e.$on("clear_status",l[21]);let f=l[8]&&l[14]&&vl(l);const g=[on,nn],p=[];function b(_,h){return _[0]?0:1}return o=b(l),s=p[o]=g[o](l),{c(){K(e.$$.fragment),t=je(),f&&f.c(),n=je(),s.c(),i=Ie()},m(_,h){U(e,_,h),ne(_,t,h),f&&f.m(_,h),ne(_,n,h),p[o].m(_,h),ne(_,i,h),a=!0},p(_,h){const w=h&65540?Al(r,[h&4&&{autoscroll:_[2].autoscroll},h&65536&&Dl(_[16]),h&4&&{i18n:_[2].i18n}]):{};e.$set(w),_[8]&&_[14]?f?(f.p(_,h),h&16640&&z(f,1)):(f=vl(_),f.c(),z(f,1),f.m(n.parentNode,n)):f&&(de(),C(f,1,1,()=>{f=null}),he());let S=o;o=b(_),o===S?p[o].p(_,h):(de(),C(p[S],1,1,()=>{p[S]=null}),he(),s=p[o],s?s.p(_,h):(s=p[o]=g[o](_),s.c()),z(s,1),s.m(i.parentNode,i))},i(_){a||(z(e.$$.fragment,_),z(f),z(s),a=!0)},o(_){C(e.$$.fragment,_),C(f),C(s),a=!1},d(_){_&&(te(t),te(n),te(i)),P(e,_),f&&f.d(_),p[o].d(_)}}}function wl(l){let e,t;return e=new Sl({props:{Icon:Se,label:l[8],float:!1,disable:l[9]===!1,show_label:l[14],rtl:l[15]}}),{c(){K(e.$$.fragment)},m(n,o){U(e,n,o),t=!0},p(n,o){const s={};o&256&&(s.label=n[8]),o&512&&(s.disable=n[9]===!1),o&16384&&(s.show_label=n[14]),o&32768&&(s.rtl=n[15]),e.$set(s)},i(n){t||(z(e.$$.fragment,n),t=!0)},o(n){C(e.$$.fragment,n),t=!1},d(n){P(e,n)}}}function an(l){let e,t;return e=new zl({props:{$$slots:{default:[cn]},$$scope:{ctx:l}}}),{c(){K(e.$$.fragment)},m(n,o){U(e,n,o),t=!0},p(n,o){const s={};o&16777216&&(s.$$scope={dirty:o,ctx:n}),e.$set(s)},i(n){t||(z(e.$$.fragment,n),t=!0)},o(n){C(e.$$.fragment,n),t=!1},d(n){P(e,n)}}}function fn(l){let e,t;return e=new Ct({props:{selectable:l[12],value:l[0],show_legend:l[6],show_inline_category:l[7],color_map:l[1]}}),e.$on("select",l[20]),{c(){K(e.$$.fragment)},m(n,o){U(e,n,o),t=!0},p(n,o){const s={};o&4096&&(s.selectable=n[12]),o&1&&(s.value=n[0]),o&64&&(s.show_legend=n[6]),o&128&&(s.show_inline_category=n[7]),o&2&&(s.color_map=n[1]),e.$set(s)},i(n){t||(z(e.$$.fragment,n),t=!0)},o(n){C(e.$$.fragment,n),t=!1},d(n){P(e,n)}}}function cn(l){let e,t;return e=new Se({}),{c(){K(e.$$.fragment)},m(n,o){U(e,n,o),t=!0},i(n){t||(z(e.$$.fragment,n),t=!0)},o(n){C(e.$$.fragment,n),t=!1},d(n){P(e,n)}}}function _n(l){let e,t,n,o,s,i,a;const r=[{autoscroll:l[2].autoscroll},{i18n:l[2].i18n},l[16]];let c={};for(let _=0;_<r.length;_+=1)c=Rl(c,r[_]);e=new El({props:c}),e.$on("clear_status",l[19]);let f=l[8]&&l[14]&&wl(l);const g=[fn,an],p=[];function b(_,h){return _[0]?0:1}return o=b(l),s=p[o]=g[o](l),{c(){K(e.$$.fragment),t=je(),f&&f.c(),n=je(),s.c(),i=Ie()},m(_,h){U(e,_,h),ne(_,t,h),f&&f.m(_,h),ne(_,n,h),p[o].m(_,h),ne(_,i,h),a=!0},p(_,h){const w=h&65540?Al(r,[h&4&&{autoscroll:_[2].autoscroll},h&4&&{i18n:_[2].i18n},h&65536&&Dl(_[16])]):{};e.$set(w),_[8]&&_[14]?f?(f.p(_,h),h&16640&&z(f,1)):(f=wl(_),f.c(),z(f,1),f.m(n.parentNode,n)):f&&(de(),C(f,1,1,()=>{f=null}),he());let S=o;o=b(_),o===S?p[o].p(_,h):(de(),C(p[S],1,1,()=>{p[S]=null}),he(),s=p[o],s?s.p(_,h):(s=p[o]=g[o](_),s.c()),z(s,1),s.m(i.parentNode,i))},i(_){a||(z(e.$$.fragment,_),z(f),z(s),a=!0)},o(_){C(e.$$.fragment,_),C(f),C(s),a=!1},d(_){_&&(te(t),te(n),te(i)),P(e,_),f&&f.d(_),p[o].d(_)}}}function un(l){let e,t,n,o;const s=[tn,ln],i=[];function a(r,c){return r[13]?1:0}return e=a(l),t=i[e]=s[e](l),{c(){t.c(),n=Ie()},m(r,c){i[e].m(r,c),ne(r,n,c),o=!0},p(r,[c]){let f=e;e=a(r),e===f?i[e].p(r,c):(de(),C(i[f],1,1,()=>{i[f]=null}),he(),t=i[e],t?t.p(r,c):(t=i[e]=s[e](r),t.c()),z(t,1),t.m(n.parentNode,n))},i(r){o||(z(t),o=!0)},o(r){C(t),o=!1},d(r){r&&te(n),i[e].d(r)}}}function hn(l,e,t){let{gradio:n}=e,{elem_id:o=""}=e,{elem_classes:s=[]}=e,{visible:i=!0}=e,{value:a}=e,r,{show_legend:c}=e,{show_inline_category:f}=e,{color_map:g={}}=e,{label:p=n.i18n("highlighted_text.highlighted_text")}=e,{container:b=!0}=e,{scale:_=null}=e,{min_width:h=void 0}=e,{_selectable:w=!1}=e,{combine_adjacent:S=!1}=e,{interactive:y}=e,{show_label:d=!0}=e,{rtl:j=!1}=e,{loading_status:m}=e;const E=()=>n.dispatch("clear_status",m),M=({detail:k})=>n.dispatch("select",k),Ce=()=>n.dispatch("clear_status",m);function Te(k){a=k,t(0,a),t(17,S)}const Ne=()=>n.dispatch("change");return l.$$set=k=>{"gradio"in k&&t(2,n=k.gradio),"elem_id"in k&&t(3,o=k.elem_id),"elem_classes"in k&&t(4,s=k.elem_classes),"visible"in k&&t(5,i=k.visible),"value"in k&&t(0,a=k.value),"show_legend"in k&&t(6,c=k.show_legend),"show_inline_category"in k&&t(7,f=k.show_inline_category),"color_map"in k&&t(1,g=k.color_map),"label"in k&&t(8,p=k.label),"container"in k&&t(9,b=k.container),"scale"in k&&t(10,_=k.scale),"min_width"in k&&t(11,h=k.min_width),"_selectable"in k&&t(12,w=k._selectable),"combine_adjacent"in k&&t(17,S=k.combine_adjacent),"interactive"in k&&t(13,y=k.interactive),"show_label"in k&&t(14,d=k.show_label),"rtl"in k&&t(15,j=k.rtl),"loading_status"in k&&t(16,m=k.loading_status)},l.$$.update=()=>{l.$$.dirty&2&&!g&&Object.keys(g).length&&t(1,g),l.$$.dirty&131073&&a&&S&&t(0,a=Cl(a)),l.$$.dirty&262149&&a!==r&&(t(18,r=a),n.dispatch("change"))},[a,g,n,o,s,i,c,f,p,b,_,h,w,y,d,j,m,S,r,E,M,Ce,Te,Ne]}class $n extends Jt{constructor(e){super(),xt(this,e,hn,un,en,{gradio:2,elem_id:3,elem_classes:4,visible:5,value:0,show_legend:6,show_inline_category:7,color_map:1,label:8,container:9,scale:10,min_width:11,_selectable:12,combine_adjacent:17,interactive:13,show_label:14,rtl:15,loading_status:16})}get gradio(){return this.$$.ctx[2]}set gradio(e){this.$$set({gradio:e}),V()}get elem_id(){return this.$$.ctx[3]}set elem_id(e){this.$$set({elem_id:e}),V()}get elem_classes(){return this.$$.ctx[4]}set elem_classes(e){this.$$set({elem_classes:e}),V()}get visible(){return this.$$.ctx[5]}set visible(e){this.$$set({visible:e}),V()}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),V()}get show_legend(){return this.$$.ctx[6]}set show_legend(e){this.$$set({show_legend:e}),V()}get show_inline_category(){return this.$$.ctx[7]}set show_inline_category(e){this.$$set({show_inline_category:e}),V()}get color_map(){return this.$$.ctx[1]}set color_map(e){this.$$set({color_map:e}),V()}get label(){return this.$$.ctx[8]}set label(e){this.$$set({label:e}),V()}get container(){return this.$$.ctx[9]}set container(e){this.$$set({container:e}),V()}get scale(){return this.$$.ctx[10]}set scale(e){this.$$set({scale:e}),V()}get min_width(){return this.$$.ctx[11]}set min_width(e){this.$$set({min_width:e}),V()}get _selectable(){return this.$$.ctx[12]}set _selectable(e){this.$$set({_selectable:e}),V()}get combine_adjacent(){return this.$$.ctx[17]}set combine_adjacent(e){this.$$set({combine_adjacent:e}),V()}get interactive(){return this.$$.ctx[13]}set interactive(e){this.$$set({interactive:e}),V()}get show_label(){return this.$$.ctx[14]}set show_label(e){this.$$set({show_label:e}),V()}get rtl(){return this.$$.ctx[15]}set rtl(e){this.$$set({rtl:e}),V()}get loading_status(){return this.$$.ctx[16]}set loading_status(e){this.$$set({loading_status:e}),V()}}export{Gt as BaseInteractiveHighlightedText,Ct as BaseStaticHighlightedText,$n as default};
//# sourceMappingURL=Index-B14Vg3Db.js.map
