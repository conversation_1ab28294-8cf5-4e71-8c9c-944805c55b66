import Ce from"./ImagePreview-CpxjYXeK.js";import{I as Te}from"./ImageUploader-3ubqEwhl.js";import{W as Gt}from"./ImageUploader-3ubqEwhl.js";import{B as ee}from"./Block-CJdXVpa7.js";import"./MarkdownCode.svelte_svelte_type_style_lang-Bl8p72Ow.js";/* empty css                                                        */import{E as Ae}from"./Empty-ZqppqzTN.js";import{I as Ge}from"./Image-Bsh8Umrh.js";import"./index-DJ2rNx9E.js";import{U as te}from"./UploadText-Wd7ORU21.js";import{S as se}from"./index-DYtg3pip.js";import"./StreamingBar.svelte_svelte_type_style_lang-DlRzv56K.js";import{I as Kt}from"./Image-CnqB5dbD.js";import{default as Mt}from"./Example-CC8yxxGn.js";import"./utils-BsGrhMNe.js";import"./BlockLabel-3KxTaaiM.js";import"./IconButton-C_HS7fTi.js";import"./ShareButton-CuOwy-FH.js";import"./Community-Dw1micSV.js";import"./Download-DVtk-Jv3.js";import"./IconButtonWrapper--EIOWuEM.js";import"./FullscreenButton-BG4mOKmH.js";import"./utils-Gtzs_Zla.js";import"./DownloadLink-QIttOhoR.js";import"./DownloadLink.svelte_svelte_type_style_lang-C_5UIfol.js";import"./file-url-DoxvUUVV.js";/* empty css                                                   */import"./Clear-By3xiIwg.js";import"./SelectSource-DG1n31De.js";import"./Upload-8igJ-HYX.js";/* empty css                                             */import"./DropdownArrow-DYWFcSFn.js";import"./Square-oAGqOwsh.js";import"./index-BALGG9zl.js";import"./StreamingBar-BpqwJkLy.js";import"./prism-python-CeMtt1IT.js";import"./svelte/svelte.js";/* empty css                                              */const{SvelteComponent:He,add_flush_callback:U,assign:ne,bind:N,binding_callbacks:z,check_outros:ie,create_component:v,destroy_component:I,detach:H,empty:le,flush:m,get_spread_object:oe,get_spread_update:re,group_outros:ae,init:Ke,insert:K,mount_component:B,safe_not_equal:Le,space:_e,transition_in:b,transition_out:w}=window.__gradio__svelte__internal,{afterUpdate:Me}=window.__gradio__svelte__internal;function Qe(t){let e,s,i;function _(r){t[63](r)}let f={visible:t[5],variant:t[0]===null?"dashed":"solid",border_mode:t[29]?"focus":"base",padding:!1,elem_id:t[3],elem_classes:t[4],height:t[10]||void 0,width:t[11],allow_overflow:!1,container:t[14],scale:t[15],min_width:t[16],$$slots:{default:[$e]},$$scope:{ctx:t}};return t[28]!==void 0&&(f.fullscreen=t[28]),e=new ee({props:f}),z.push(()=>N(e,"fullscreen",_)),e.$on("dragenter",t[32]),e.$on("dragleave",t[32]),e.$on("dragover",t[32]),e.$on("drop",t[33]),{c(){v(e.$$.fragment)},m(r,o){B(e,r,o),i=!0},p(r,o){const l={};o[0]&32&&(l.visible=r[5]),o[0]&1&&(l.variant=r[0]===null?"dashed":"solid"),o[0]&536870912&&(l.border_mode=r[29]?"focus":"base"),o[0]&8&&(l.elem_id=r[3]),o[0]&16&&(l.elem_classes=r[4]),o[0]&1024&&(l.height=r[10]||void 0),o[0]&2048&&(l.width=r[11]),o[0]&16384&&(l.container=r[14]),o[0]&32768&&(l.scale=r[15]),o[0]&65536&&(l.min_width=r[16]),o[0]&2138321607|o[1]&1|o[2]&8&&(l.$$scope={dirty:o,ctx:r}),!s&&o[0]&268435456&&(s=!0,l.fullscreen=r[28],U(()=>s=!1)),e.$set(l)},i(r){i||(b(e.$$.fragment,r),i=!0)},o(r){w(e.$$.fragment,r),i=!1},d(r){I(e,r)}}}function Re(t){let e,s,i;function _(r){t[43](r)}let f={visible:t[5],variant:"solid",border_mode:t[29]?"focus":"base",padding:!1,elem_id:t[3],elem_classes:t[4],height:t[10]||void 0,width:t[11],allow_overflow:!1,container:t[14],scale:t[15],min_width:t[16],$$slots:{default:[xe]},$$scope:{ctx:t}};return t[28]!==void 0&&(f.fullscreen=t[28]),e=new ee({props:f}),z.push(()=>N(e,"fullscreen",_)),{c(){v(e.$$.fragment)},m(r,o){B(e,r,o),i=!0},p(r,o){const l={};o[0]&32&&(l.visible=r[5]),o[0]&536870912&&(l.border_mode=r[29]?"focus":"base"),o[0]&8&&(l.elem_id=r[3]),o[0]&16&&(l.elem_classes=r[4]),o[0]&1024&&(l.height=r[10]||void 0),o[0]&2048&&(l.width=r[11]),o[0]&16384&&(l.container=r[14]),o[0]&32768&&(l.scale=r[15]),o[0]&65536&&(l.min_width=r[16]),o[0]&310518213|o[2]&8&&(l.$$scope={dirty:o,ctx:r}),!s&&o[0]&268435456&&(s=!0,l.fullscreen=r[28],U(()=>s=!1)),e.$set(l)},i(r){i||(b(e.$$.fragment,r),i=!0)},o(r){w(e.$$.fragment,r),i=!1},d(r){I(e,r)}}}function Ve(t){let e,s;return e=new Ae({props:{unpadded_box:!0,size:"large",$$slots:{default:[Ze]},$$scope:{ctx:t}}}),{c(){v(e.$$.fragment)},m(i,_){B(e,i,_),s=!0},p(i,_){const f={};_[2]&8&&(f.$$scope={dirty:_,ctx:i}),e.$set(f)},i(i){s||(b(e.$$.fragment,i),s=!0)},o(i){w(e.$$.fragment,i),s=!1},d(i){I(e,i)}}}function Xe(t){let e,s;return e=new te({props:{i18n:t[25].i18n,type:"clipboard",mode:"short"}}),{c(){v(e.$$.fragment)},m(i,_){B(e,i,_),s=!0},p(i,_){const f={};_[0]&33554432&&(f.i18n=i[25].i18n),e.$set(f)},i(i){s||(b(e.$$.fragment,i),s=!0)},o(i){w(e.$$.fragment,i),s=!1},d(i){I(e,i)}}}function Ye(t){let e,s;return e=new te({props:{i18n:t[25].i18n,type:"image",placeholder:t[22]}}),{c(){v(e.$$.fragment)},m(i,_){B(e,i,_),s=!0},p(i,_){const f={};_[0]&33554432&&(f.i18n=i[25].i18n),_[0]&4194304&&(f.placeholder=i[22]),e.$set(f)},i(i){s||(b(e.$$.fragment,i),s=!0)},o(i){w(e.$$.fragment,i),s=!1},d(i){I(e,i)}}}function Ze(t){let e,s;return e=new Ge({}),{c(){v(e.$$.fragment)},m(i,_){B(e,i,_),s=!0},i(i){s||(b(e.$$.fragment,i),s=!0)},o(i){w(e.$$.fragment,i),s=!1},d(i){I(e,i)}}}function ye(t){let e,s,i,_;const f=[Ye,Xe,Ve],r=[];function o(l,g){return l[30]==="upload"||!l[30]?0:l[30]==="clipboard"?1:2}return e=o(t),s=r[e]=f[e](t),{c(){s.c(),i=le()},m(l,g){r[e].m(l,g),K(l,i,g),_=!0},p(l,g){let c=e;e=o(l),e===c?r[e].p(l,g):(ae(),w(r[c],1,1,()=>{r[c]=null}),ie(),s=r[e],s?s.p(l,g):(s=r[e]=f[e](l),s.c()),b(s,1),s.m(i.parentNode,i))},i(l){_||(b(s),_=!0)},o(l){w(s),_=!1},d(l){l&&H(i),r[e].d(l)}}}function $e(t){let e,s,i,_,f,r,o,l,g,c;const F=[{autoscroll:t[25].autoscroll},{i18n:t[25].i18n},t[2]];let p={};for(let a=0;a<F.length;a+=1)p=ne(p,F[a]);e=new se({props:p}),e.$on("clear_status",t[44]);function J(a){t[47](a)}function W(a){t[48](a)}function j(a){t[49](a)}function q(a){t[50](a)}function C(a){t[51](a)}function T(a){t[52](a)}let k={selectable:t[13],root:t[9],sources:t[18],fullscreen:t[28],label:t[6],show_label:t[7],pending:t[21],streaming:t[20],webcam_options:t[24],stream_every:t[12],max_file_size:t[25].max_file_size,i18n:t[25].i18n,upload:t[45],stream_handler:t[25].client?.stream,$$slots:{default:[ye]},$$scope:{ctx:t}};return t[26]!==void 0&&(k.uploading=t[26]),t[30]!==void 0&&(k.active_source=t[30]),t[0]!==void 0&&(k.value=t[0]),t[29]!==void 0&&(k.dragging=t[29]),t[27]!==void 0&&(k.modify_stream=t[27]),t[1]!==void 0&&(k.set_time_limit=t[1]),i=new Te({props:k}),t[46](i),z.push(()=>N(i,"uploading",J)),z.push(()=>N(i,"active_source",W)),z.push(()=>N(i,"value",j)),z.push(()=>N(i,"dragging",q)),z.push(()=>N(i,"modify_stream",C)),z.push(()=>N(i,"set_time_limit",T)),i.$on("edit",t[53]),i.$on("clear",t[54]),i.$on("stream",t[55]),i.$on("drag",t[56]),i.$on("upload",t[57]),i.$on("select",t[58]),i.$on("share",t[59]),i.$on("error",t[60]),i.$on("close_stream",t[61]),i.$on("fullscreen",t[62]),{c(){v(e.$$.fragment),s=_e(),v(i.$$.fragment)},m(a,u){B(e,a,u),K(a,s,u),B(i,a,u),c=!0},p(a,u){const A=u[0]&33554436?re(F,[u[0]&33554432&&{autoscroll:a[25].autoscroll},u[0]&33554432&&{i18n:a[25].i18n},u[0]&4&&oe(a[2])]):{};e.$set(A);const h={};u[0]&8192&&(h.selectable=a[13]),u[0]&512&&(h.root=a[9]),u[0]&262144&&(h.sources=a[18]),u[0]&268435456&&(h.fullscreen=a[28]),u[0]&64&&(h.label=a[6]),u[0]&128&&(h.show_label=a[7]),u[0]&2097152&&(h.pending=a[21]),u[0]&1048576&&(h.streaming=a[20]),u[0]&16777216&&(h.webcam_options=a[24]),u[0]&4096&&(h.stream_every=a[12]),u[0]&33554432&&(h.max_file_size=a[25].max_file_size),u[0]&33554432&&(h.i18n=a[25].i18n),u[0]&33554432&&(h.upload=a[45]),u[0]&33554432&&(h.stream_handler=a[25].client?.stream),u[0]&1111490560|u[2]&8&&(h.$$scope={dirty:u,ctx:a}),!_&&u[0]&67108864&&(_=!0,h.uploading=a[26],U(()=>_=!1)),!f&&u[0]&1073741824&&(f=!0,h.active_source=a[30],U(()=>f=!1)),!r&&u[0]&1&&(r=!0,h.value=a[0],U(()=>r=!1)),!o&&u[0]&536870912&&(o=!0,h.dragging=a[29],U(()=>o=!1)),!l&&u[0]&134217728&&(l=!0,h.modify_stream=a[27],U(()=>l=!1)),!g&&u[0]&2&&(g=!0,h.set_time_limit=a[1],U(()=>g=!1)),i.$set(h)},i(a){c||(b(e.$$.fragment,a),b(i.$$.fragment,a),c=!0)},o(a){w(e.$$.fragment,a),w(i.$$.fragment,a),c=!1},d(a){a&&H(s),I(e,a),t[46](null),I(i,a)}}}function xe(t){let e,s,i,_;const f=[{autoscroll:t[25].autoscroll},{i18n:t[25].i18n},t[2]];let r={};for(let o=0;o<f.length;o+=1)r=ne(r,f[o]);return e=new se({props:r}),i=new Ce({props:{fullscreen:t[28],value:t[0],label:t[6],show_label:t[7],show_download_button:t[8],selectable:t[13],show_share_button:t[17],i18n:t[25].i18n,show_fullscreen_button:t[23]}}),i.$on("select",t[39]),i.$on("share",t[40]),i.$on("error",t[41]),i.$on("fullscreen",t[42]),{c(){v(e.$$.fragment),s=_e(),v(i.$$.fragment)},m(o,l){B(e,o,l),K(o,s,l),B(i,o,l),_=!0},p(o,l){const g=l[0]&33554436?re(f,[l[0]&33554432&&{autoscroll:o[25].autoscroll},l[0]&33554432&&{i18n:o[25].i18n},l[0]&4&&oe(o[2])]):{};e.$set(g);const c={};l[0]&268435456&&(c.fullscreen=o[28]),l[0]&1&&(c.value=o[0]),l[0]&64&&(c.label=o[6]),l[0]&128&&(c.show_label=o[7]),l[0]&256&&(c.show_download_button=o[8]),l[0]&8192&&(c.selectable=o[13]),l[0]&131072&&(c.show_share_button=o[17]),l[0]&33554432&&(c.i18n=o[25].i18n),l[0]&8388608&&(c.show_fullscreen_button=o[23]),i.$set(c)},i(o){_||(b(e.$$.fragment,o),b(i.$$.fragment,o),_=!0)},o(o){w(e.$$.fragment,o),w(i.$$.fragment,o),_=!1},d(o){o&&H(s),I(e,o),I(i,o)}}}function et(t){let e,s,i,_;const f=[Re,Qe],r=[];function o(l,g){return l[19]?1:0}return e=o(t),s=r[e]=f[e](t),{c(){s.c(),i=le()},m(l,g){r[e].m(l,g),K(l,i,g),_=!0},p(l,g){let c=e;e=o(l),e===c?r[e].p(l,g):(ae(),w(r[c],1,1,()=>{r[c]=null}),ie(),s=r[e],s?s.p(l,g):(s=r[e]=f[e](l),s.c()),b(s,1),s.m(i.parentNode,i))},i(l){_||(b(s),_=!0)},o(l){w(s),_=!1},d(l){l&&H(i),r[e].d(l)}}}function tt(t,e,s){let i="closed",_=()=>{};function f(n){i=n,_(n)}const r=()=>i;let{set_time_limit:o}=e,{value_is_output:l=!1}=e,{elem_id:g=""}=e,{elem_classes:c=[]}=e,{visible:F=!0}=e,{value:p=null}=e,J=null,{label:W}=e,{show_label:j}=e,{show_download_button:q}=e,{root:C}=e,{height:T}=e,{width:k}=e,{stream_every:a}=e,{_selectable:u=!1}=e,{container:A=!0}=e,{scale:h=null}=e,{min_width:R=void 0}=e,{loading_status:D}=e,{show_share_button:V=!1}=e,{sources:X=["upload","clipboard","webcam"]}=e,{interactive:L}=e,{streaming:Y}=e,{pending:Z}=e,{placeholder:y=void 0}=e,{show_fullscreen_button:$}=e,{input_ready:M}=e,{webcam_options:x}=e,E=!1,G=!1,{gradio:d}=e;Me(()=>{s(34,l=!1)});let P,Q=null,O;const ue=n=>{const S=n;S.preventDefault(),S.stopPropagation(),S.type==="dragenter"||S.type==="dragover"?s(29,P=!0):S.type==="dragleave"&&s(29,P=!1)},fe=n=>{if(L){const S=n;S.preventDefault(),S.stopPropagation(),s(29,P=!1),O&&O.loadFilesFromDrop(S)}},me=({detail:n})=>d.dispatch("select",n),ce=({detail:n})=>d.dispatch("share",n),he=({detail:n})=>d.dispatch("error",n),ge=({detail:n})=>{s(28,E=n)};function de(n){E=n,s(28,E)}const be=()=>d.dispatch("clear_status",D),we=(...n)=>d.client.upload(...n);function pe(n){z[n?"unshift":"push"](()=>{O=n,s(31,O)})}function ke(n){G=n,s(26,G)}function ve(n){Q=n,s(30,Q)}function Ie(n){p=n,s(0,p)}function Be(n){P=n,s(29,P)}function Se(n){_=n,s(27,_)}function ze(n){o=n,s(1,o)}const Ue=()=>d.dispatch("edit"),Ne=()=>{d.dispatch("clear")},De=({detail:n})=>d.dispatch("stream",n),Ee=({detail:n})=>s(29,P=n),Pe=()=>d.dispatch("upload"),Fe=({detail:n})=>d.dispatch("select",n),Je=({detail:n})=>d.dispatch("share",n),Oe=({detail:n})=>{s(2,D=D||{}),s(2,D.status="error",D),d.dispatch("error",n)},We=()=>{d.dispatch("close_stream","stream")},je=({detail:n})=>{s(28,E=n)};function qe(n){E=n,s(28,E)}return t.$$set=n=>{"set_time_limit"in n&&s(1,o=n.set_time_limit),"value_is_output"in n&&s(34,l=n.value_is_output),"elem_id"in n&&s(3,g=n.elem_id),"elem_classes"in n&&s(4,c=n.elem_classes),"visible"in n&&s(5,F=n.visible),"value"in n&&s(0,p=n.value),"label"in n&&s(6,W=n.label),"show_label"in n&&s(7,j=n.show_label),"show_download_button"in n&&s(8,q=n.show_download_button),"root"in n&&s(9,C=n.root),"height"in n&&s(10,T=n.height),"width"in n&&s(11,k=n.width),"stream_every"in n&&s(12,a=n.stream_every),"_selectable"in n&&s(13,u=n._selectable),"container"in n&&s(14,A=n.container),"scale"in n&&s(15,h=n.scale),"min_width"in n&&s(16,R=n.min_width),"loading_status"in n&&s(2,D=n.loading_status),"show_share_button"in n&&s(17,V=n.show_share_button),"sources"in n&&s(18,X=n.sources),"interactive"in n&&s(19,L=n.interactive),"streaming"in n&&s(20,Y=n.streaming),"pending"in n&&s(21,Z=n.pending),"placeholder"in n&&s(22,y=n.placeholder),"show_fullscreen_button"in n&&s(23,$=n.show_fullscreen_button),"input_ready"in n&&s(35,M=n.input_ready),"webcam_options"in n&&s(24,x=n.webcam_options),"gradio"in n&&s(25,d=n.gradio)},t.$$.update=()=>{t.$$.dirty[0]&67108864&&s(35,M=!G),t.$$.dirty[0]&33554433|t.$$.dirty[1]&136&&JSON.stringify(p)!==JSON.stringify(J)&&(s(38,J=p),d.dispatch("change"),l||d.dispatch("input"))},[p,o,D,g,c,F,W,j,q,C,T,k,a,u,A,h,R,V,X,L,Y,Z,y,$,x,d,G,_,E,P,Q,O,ue,fe,l,M,f,r,J,me,ce,he,ge,de,be,we,pe,ke,ve,Ie,Be,Se,ze,Ue,Ne,De,Ee,Pe,Fe,Je,Oe,We,je,qe]}class Ct extends He{constructor(e){super(),Ke(this,e,tt,et,Le,{modify_stream_state:36,get_stream_state:37,set_time_limit:1,value_is_output:34,elem_id:3,elem_classes:4,visible:5,value:0,label:6,show_label:7,show_download_button:8,root:9,height:10,width:11,stream_every:12,_selectable:13,container:14,scale:15,min_width:16,loading_status:2,show_share_button:17,sources:18,interactive:19,streaming:20,pending:21,placeholder:22,show_fullscreen_button:23,input_ready:35,webcam_options:24,gradio:25},null,[-1,-1,-1])}get modify_stream_state(){return this.$$.ctx[36]}get get_stream_state(){return this.$$.ctx[37]}get set_time_limit(){return this.$$.ctx[1]}set set_time_limit(e){this.$$set({set_time_limit:e}),m()}get value_is_output(){return this.$$.ctx[34]}set value_is_output(e){this.$$set({value_is_output:e}),m()}get elem_id(){return this.$$.ctx[3]}set elem_id(e){this.$$set({elem_id:e}),m()}get elem_classes(){return this.$$.ctx[4]}set elem_classes(e){this.$$set({elem_classes:e}),m()}get visible(){return this.$$.ctx[5]}set visible(e){this.$$set({visible:e}),m()}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),m()}get label(){return this.$$.ctx[6]}set label(e){this.$$set({label:e}),m()}get show_label(){return this.$$.ctx[7]}set show_label(e){this.$$set({show_label:e}),m()}get show_download_button(){return this.$$.ctx[8]}set show_download_button(e){this.$$set({show_download_button:e}),m()}get root(){return this.$$.ctx[9]}set root(e){this.$$set({root:e}),m()}get height(){return this.$$.ctx[10]}set height(e){this.$$set({height:e}),m()}get width(){return this.$$.ctx[11]}set width(e){this.$$set({width:e}),m()}get stream_every(){return this.$$.ctx[12]}set stream_every(e){this.$$set({stream_every:e}),m()}get _selectable(){return this.$$.ctx[13]}set _selectable(e){this.$$set({_selectable:e}),m()}get container(){return this.$$.ctx[14]}set container(e){this.$$set({container:e}),m()}get scale(){return this.$$.ctx[15]}set scale(e){this.$$set({scale:e}),m()}get min_width(){return this.$$.ctx[16]}set min_width(e){this.$$set({min_width:e}),m()}get loading_status(){return this.$$.ctx[2]}set loading_status(e){this.$$set({loading_status:e}),m()}get show_share_button(){return this.$$.ctx[17]}set show_share_button(e){this.$$set({show_share_button:e}),m()}get sources(){return this.$$.ctx[18]}set sources(e){this.$$set({sources:e}),m()}get interactive(){return this.$$.ctx[19]}set interactive(e){this.$$set({interactive:e}),m()}get streaming(){return this.$$.ctx[20]}set streaming(e){this.$$set({streaming:e}),m()}get pending(){return this.$$.ctx[21]}set pending(e){this.$$set({pending:e}),m()}get placeholder(){return this.$$.ctx[22]}set placeholder(e){this.$$set({placeholder:e}),m()}get show_fullscreen_button(){return this.$$.ctx[23]}set show_fullscreen_button(e){this.$$set({show_fullscreen_button:e}),m()}get input_ready(){return this.$$.ctx[35]}set input_ready(e){this.$$set({input_ready:e}),m()}get webcam_options(){return this.$$.ctx[24]}set webcam_options(e){this.$$set({webcam_options:e}),m()}get gradio(){return this.$$.ctx[25]}set gradio(e){this.$$set({gradio:e}),m()}}export{Mt as BaseExample,Kt as BaseImage,Te as BaseImageUploader,Ce as BaseStaticImage,Gt as Webcam,Ct as default};
//# sourceMappingURL=Index-B6-B2pPO.js.map
