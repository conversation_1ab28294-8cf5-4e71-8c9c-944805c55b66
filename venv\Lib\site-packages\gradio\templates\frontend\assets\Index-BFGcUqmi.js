import{T as p}from"./Textbox-Co0OLweQ.js";import{B as $}from"./Block-CJdXVpa7.js";import"./MarkdownCode.svelte_svelte_type_style_lang-Bl8p72Ow.js";/* empty css                                                        */import"./index-DJ2rNx9E.js";import{S as tt}from"./index-DYtg3pip.js";import"./StreamingBar.svelte_svelte_type_style_lang-DlRzv56K.js";import{default as Lt}from"./Example-BdAjEacD.js";import"./BlockTitle-C6qeQAMx.js";import"./Info-D7HP20hi.js";import"./MarkdownCode-Cdb8e5t4.js";import"./Check-CEkiXcyC.js";import"./Copy-CxQ9EyK2.js";import"./Send-DyoOovnk.js";import"./Square-oAGqOwsh.js";import"./index-BALGG9zl.js";/* empty css                                              */import"./prism-python-CeMtt1IT.js";import"./svelte/svelte.js";import"./IconButton-C_HS7fTi.js";import"./Clear-By3xiIwg.js";/* empty css                                              */const{SvelteComponent:et,add_flush_callback:J,assign:it,bind:K,binding_callbacks:L,check_outros:st,create_component:v,destroy_component:k,detach:lt,flush:o,get_spread_object:nt,get_spread_update:ut,group_outros:at,init:_t,insert:ot,mount_component:x,safe_not_equal:ht,space:ft,transition_in:m,transition_out:b}=window.__gradio__svelte__internal;function M(s){let t,i;const n=[{autoscroll:s[2].autoscroll},{i18n:s[2].i18n},s[19]];let f={};for(let u=0;u<n.length;u+=1)f=it(f,n[u]);return t=new tt({props:f}),t.$on("clear_status",s[27]),{c(){v(t.$$.fragment)},m(u,_){x(t,u,_),i=!0},p(u,_){const c=_[0]&524292?ut(n,[_[0]&4&&{autoscroll:u[2].autoscroll},_[0]&4&&{i18n:u[2].i18n},_[0]&524288&&nt(u[19])]):{};t.$set(c)},i(u){i||(m(t.$$.fragment,u),i=!0)},o(u){b(t.$$.fragment,u),i=!1},d(u){k(t,u)}}}function rt(s){let t,i,n,f,u,_=s[19]&&M(s);function c(l){s[28](l)}function g(l){s[29](l)}let r={label:s[3],info:s[4],show_label:s[10],lines:s[8],type:s[12],rtl:s[20],text_align:s[21],max_lines:s[11],placeholder:s[9],submit_btn:s[16],stop_btn:s[17],show_copy_button:s[18],autofocus:s[22],container:s[13],autoscroll:s[23],max_length:s[25],html_attributes:s[26],disabled:!s[24]};return s[0]!==void 0&&(r.value=s[0]),s[1]!==void 0&&(r.value_is_output=s[1]),i=new p({props:r}),L.push(()=>K(i,"value",c)),L.push(()=>K(i,"value_is_output",g)),i.$on("change",s[30]),i.$on("input",s[31]),i.$on("submit",s[32]),i.$on("blur",s[33]),i.$on("select",s[34]),i.$on("focus",s[35]),i.$on("stop",s[36]),i.$on("copy",s[37]),{c(){_&&_.c(),t=ft(),v(i.$$.fragment)},m(l,a){_&&_.m(l,a),ot(l,t,a),x(i,l,a),u=!0},p(l,a){l[19]?_?(_.p(l,a),a[0]&524288&&m(_,1)):(_=M(l),_.c(),m(_,1),_.m(t.parentNode,t)):_&&(at(),b(_,1,1,()=>{_=null}),st());const h={};a[0]&8&&(h.label=l[3]),a[0]&16&&(h.info=l[4]),a[0]&1024&&(h.show_label=l[10]),a[0]&256&&(h.lines=l[8]),a[0]&4096&&(h.type=l[12]),a[0]&1048576&&(h.rtl=l[20]),a[0]&2097152&&(h.text_align=l[21]),a[0]&2048&&(h.max_lines=l[11]),a[0]&512&&(h.placeholder=l[9]),a[0]&65536&&(h.submit_btn=l[16]),a[0]&131072&&(h.stop_btn=l[17]),a[0]&262144&&(h.show_copy_button=l[18]),a[0]&4194304&&(h.autofocus=l[22]),a[0]&8192&&(h.container=l[13]),a[0]&8388608&&(h.autoscroll=l[23]),a[0]&33554432&&(h.max_length=l[25]),a[0]&67108864&&(h.html_attributes=l[26]),a[0]&16777216&&(h.disabled=!l[24]),!n&&a[0]&1&&(n=!0,h.value=l[0],J(()=>n=!1)),!f&&a[0]&2&&(f=!0,h.value_is_output=l[1],J(()=>f=!1)),i.$set(h)},i(l){u||(m(_),m(i.$$.fragment,l),u=!0)},o(l){b(_),b(i.$$.fragment,l),u=!1},d(l){l&&lt(t),_&&_.d(l),k(i,l)}}}function ct(s){let t,i;return t=new $({props:{visible:s[7],elem_id:s[5],elem_classes:s[6],scale:s[14],min_width:s[15],allow_overflow:!1,padding:s[13],$$slots:{default:[rt]},$$scope:{ctx:s}}}),{c(){v(t.$$.fragment)},m(n,f){x(t,n,f),i=!0},p(n,f){const u={};f[0]&128&&(u.visible=n[7]),f[0]&32&&(u.elem_id=n[5]),f[0]&64&&(u.elem_classes=n[6]),f[0]&16384&&(u.scale=n[14]),f[0]&32768&&(u.min_width=n[15]),f[0]&8192&&(u.padding=n[13]),f[0]&134168351|f[1]&128&&(u.$$scope={dirty:f,ctx:n}),t.$set(u)},i(n){i||(m(t.$$.fragment,n),i=!0)},o(n){b(t.$$.fragment,n),i=!1},d(n){k(t,n)}}}function mt(s,t,i){let{gradio:n}=t,{label:f="Textbox"}=t,{info:u=void 0}=t,{elem_id:_=""}=t,{elem_classes:c=[]}=t,{visible:g=!0}=t,{value:r=""}=t,{lines:l}=t,{placeholder:a=""}=t,{show_label:h}=t,{max_lines:B=void 0}=t,{type:T="text"}=t,{container:S=!0}=t,{scale:j=null}=t,{min_width:q=void 0}=t,{submit_btn:C=null}=t,{stop_btn:E=null}=t,{show_copy_button:I=!1}=t,{loading_status:w=void 0}=t,{value_is_output:d=!1}=t,{rtl:N=!1}=t,{text_align:z=void 0}=t,{autofocus:A=!1}=t,{autoscroll:D=!0}=t,{interactive:F}=t,{max_length:G=void 0}=t,{html_attributes:H=null}=t;const O=()=>n.dispatch("clear_status",w);function P(e){r=e,i(0,r)}function Q(e){d=e,i(1,d)}const R=()=>n.dispatch("change",r),U=()=>n.dispatch("input"),V=()=>n.dispatch("submit"),W=()=>n.dispatch("blur"),X=e=>n.dispatch("select",e.detail),Y=()=>n.dispatch("focus"),Z=()=>n.dispatch("stop"),y=e=>n.dispatch("copy",e.detail);return s.$$set=e=>{"gradio"in e&&i(2,n=e.gradio),"label"in e&&i(3,f=e.label),"info"in e&&i(4,u=e.info),"elem_id"in e&&i(5,_=e.elem_id),"elem_classes"in e&&i(6,c=e.elem_classes),"visible"in e&&i(7,g=e.visible),"value"in e&&i(0,r=e.value),"lines"in e&&i(8,l=e.lines),"placeholder"in e&&i(9,a=e.placeholder),"show_label"in e&&i(10,h=e.show_label),"max_lines"in e&&i(11,B=e.max_lines),"type"in e&&i(12,T=e.type),"container"in e&&i(13,S=e.container),"scale"in e&&i(14,j=e.scale),"min_width"in e&&i(15,q=e.min_width),"submit_btn"in e&&i(16,C=e.submit_btn),"stop_btn"in e&&i(17,E=e.stop_btn),"show_copy_button"in e&&i(18,I=e.show_copy_button),"loading_status"in e&&i(19,w=e.loading_status),"value_is_output"in e&&i(1,d=e.value_is_output),"rtl"in e&&i(20,N=e.rtl),"text_align"in e&&i(21,z=e.text_align),"autofocus"in e&&i(22,A=e.autofocus),"autoscroll"in e&&i(23,D=e.autoscroll),"interactive"in e&&i(24,F=e.interactive),"max_length"in e&&i(25,G=e.max_length),"html_attributes"in e&&i(26,H=e.html_attributes)},[r,d,n,f,u,_,c,g,l,a,h,B,T,S,j,q,C,E,I,w,N,z,A,D,F,G,H,O,P,Q,R,U,V,W,X,Y,Z,y]}class Ht extends et{constructor(t){super(),_t(this,t,mt,ct,ht,{gradio:2,label:3,info:4,elem_id:5,elem_classes:6,visible:7,value:0,lines:8,placeholder:9,show_label:10,max_lines:11,type:12,container:13,scale:14,min_width:15,submit_btn:16,stop_btn:17,show_copy_button:18,loading_status:19,value_is_output:1,rtl:20,text_align:21,autofocus:22,autoscroll:23,interactive:24,max_length:25,html_attributes:26},null,[-1,-1])}get gradio(){return this.$$.ctx[2]}set gradio(t){this.$$set({gradio:t}),o()}get label(){return this.$$.ctx[3]}set label(t){this.$$set({label:t}),o()}get info(){return this.$$.ctx[4]}set info(t){this.$$set({info:t}),o()}get elem_id(){return this.$$.ctx[5]}set elem_id(t){this.$$set({elem_id:t}),o()}get elem_classes(){return this.$$.ctx[6]}set elem_classes(t){this.$$set({elem_classes:t}),o()}get visible(){return this.$$.ctx[7]}set visible(t){this.$$set({visible:t}),o()}get value(){return this.$$.ctx[0]}set value(t){this.$$set({value:t}),o()}get lines(){return this.$$.ctx[8]}set lines(t){this.$$set({lines:t}),o()}get placeholder(){return this.$$.ctx[9]}set placeholder(t){this.$$set({placeholder:t}),o()}get show_label(){return this.$$.ctx[10]}set show_label(t){this.$$set({show_label:t}),o()}get max_lines(){return this.$$.ctx[11]}set max_lines(t){this.$$set({max_lines:t}),o()}get type(){return this.$$.ctx[12]}set type(t){this.$$set({type:t}),o()}get container(){return this.$$.ctx[13]}set container(t){this.$$set({container:t}),o()}get scale(){return this.$$.ctx[14]}set scale(t){this.$$set({scale:t}),o()}get min_width(){return this.$$.ctx[15]}set min_width(t){this.$$set({min_width:t}),o()}get submit_btn(){return this.$$.ctx[16]}set submit_btn(t){this.$$set({submit_btn:t}),o()}get stop_btn(){return this.$$.ctx[17]}set stop_btn(t){this.$$set({stop_btn:t}),o()}get show_copy_button(){return this.$$.ctx[18]}set show_copy_button(t){this.$$set({show_copy_button:t}),o()}get loading_status(){return this.$$.ctx[19]}set loading_status(t){this.$$set({loading_status:t}),o()}get value_is_output(){return this.$$.ctx[1]}set value_is_output(t){this.$$set({value_is_output:t}),o()}get rtl(){return this.$$.ctx[20]}set rtl(t){this.$$set({rtl:t}),o()}get text_align(){return this.$$.ctx[21]}set text_align(t){this.$$set({text_align:t}),o()}get autofocus(){return this.$$.ctx[22]}set autofocus(t){this.$$set({autofocus:t}),o()}get autoscroll(){return this.$$.ctx[23]}set autoscroll(t){this.$$set({autoscroll:t}),o()}get interactive(){return this.$$.ctx[24]}set interactive(t){this.$$set({interactive:t}),o()}get max_length(){return this.$$.ctx[25]}set max_length(t){this.$$set({max_length:t}),o()}get html_attributes(){return this.$$.ctx[26]}set html_attributes(t){this.$$set({html_attributes:t}),o()}}export{Lt as BaseExample,p as BaseTextbox,Ht as default};
//# sourceMappingURL=Index-BFGcUqmi.js.map
