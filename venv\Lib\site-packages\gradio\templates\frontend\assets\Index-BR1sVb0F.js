import{P as J,a as K}from"./Plot-DPlFUnzo.js";import{B as M}from"./Block-CJdXVpa7.js";import"./MarkdownCode.svelte_svelte_type_style_lang-Bl8p72Ow.js";/* empty css                                                        */import{B as O}from"./BlockLabel-3KxTaaiM.js";import"./index-DJ2rNx9E.js";import{I as Q}from"./IconButtonWrapper--EIOWuEM.js";import{F as R}from"./FullscreenButton-BG4mOKmH.js";import{S as T}from"./index-DYtg3pip.js";import"./StreamingBar.svelte_svelte_type_style_lang-DlRzv56K.js";import"./Empty-ZqppqzTN.js";import"./prism-python-CeMtt1IT.js";import"./svelte/svelte.js";import"./IconButton-C_HS7fTi.js";import"./Clear-By3xiIwg.js";const{SvelteComponent:U,add_flush_callback:V,assign:X,bind:Y,binding_callbacks:Z,check_outros:$,create_component:k,destroy_component:v,detach:F,flush:r,get_spread_object:x,get_spread_update:y,group_outros:p,init:ee,insert:j,mount_component:B,safe_not_equal:te,space:q,transition_in:h,transition_out:b}=window.__gradio__svelte__internal;function z(l){let e,n;return e=new Q({props:{$$slots:{default:[se]},$$scope:{ctx:l}}}),{c(){k(e.$$.fragment)},m(i,u){B(e,i,u),n=!0},p(i,u){const c={};u&17039360&&(c.$$scope={dirty:u,ctx:i}),e.$set(c)},i(i){n||(h(e.$$.fragment,i),n=!0)},o(i){b(e.$$.fragment,i),n=!1},d(i){v(e,i)}}}function se(l){let e,n;return e=new R({props:{fullscreen:l[18]}}),e.$on("fullscreen",l[19]),{c(){k(e.$$.fragment)},m(i,u){B(e,i,u),n=!0},p(i,u){const c={};u&262144&&(c.fullscreen=i[18]),e.$set(c)},i(i){n||(h(e.$$.fragment,i),n=!0)},o(i){b(e.$$.fragment,i),n=!1},d(i){v(e,i)}}}function le(l){let e,n,i,u,c,o,f;e=new O({props:{show_label:l[6],label:l[5]||l[13].i18n("plot.plot"),Icon:J}});let _=l[17]&&z(l);const g=[{autoscroll:l[13].autoscroll},{i18n:l[13].i18n},l[4]];let w={};for(let t=0;t<g.length;t+=1)w=X(w,g[t]);return u=new T({props:w}),u.$on("clear_status",l[20]),o=new K({props:{value:l[0],theme_mode:l[10],caption:l[11],bokeh_version:l[12],show_actions_button:l[14],gradio:l[13],show_label:l[6],_selectable:l[15],x_lim:l[16]}}),o.$on("change",l[21]),o.$on("select",l[22]),{c(){k(e.$$.fragment),n=q(),_&&_.c(),i=q(),k(u.$$.fragment),c=q(),k(o.$$.fragment)},m(t,a){B(e,t,a),j(t,n,a),_&&_.m(t,a),j(t,i,a),B(u,t,a),j(t,c,a),B(o,t,a),f=!0},p(t,a){const d={};a&64&&(d.show_label=t[6]),a&8224&&(d.label=t[5]||t[13].i18n("plot.plot")),e.$set(d),t[17]?_?(_.p(t,a),a&131072&&h(_,1)):(_=z(t),_.c(),h(_,1),_.m(i.parentNode,i)):_&&(p(),b(_,1,1,()=>{_=null}),$());const P=a&8208?y(g,[a&8192&&{autoscroll:t[13].autoscroll},a&8192&&{i18n:t[13].i18n},a&16&&x(t[4])]):{};u.$set(P);const m={};a&1&&(m.value=t[0]),a&1024&&(m.theme_mode=t[10]),a&2048&&(m.caption=t[11]),a&4096&&(m.bokeh_version=t[12]),a&16384&&(m.show_actions_button=t[14]),a&8192&&(m.gradio=t[13]),a&64&&(m.show_label=t[6]),a&32768&&(m._selectable=t[15]),a&65536&&(m.x_lim=t[16]),o.$set(m)},i(t){f||(h(e.$$.fragment,t),h(_),h(u.$$.fragment,t),h(o.$$.fragment,t),f=!0)},o(t){b(e.$$.fragment,t),b(_),b(u.$$.fragment,t),b(o.$$.fragment,t),f=!1},d(t){t&&(F(n),F(i),F(c)),v(e,t),_&&_.d(t),v(u,t),v(o,t)}}}function ne(l){let e,n,i;function u(o){l[23](o)}let c={padding:!1,elem_id:l[1],elem_classes:l[2],visible:l[3],container:l[7],scale:l[8],min_width:l[9],allow_overflow:!1,$$slots:{default:[le]},$$scope:{ctx:l}};return l[18]!==void 0&&(c.fullscreen=l[18]),e=new M({props:c}),Z.push(()=>Y(e,"fullscreen",u)),{c(){k(e.$$.fragment)},m(o,f){B(e,o,f),i=!0},p(o,[f]){const _={};f&2&&(_.elem_id=o[1]),f&4&&(_.elem_classes=o[2]),f&8&&(_.visible=o[3]),f&128&&(_.container=o[7]),f&256&&(_.scale=o[8]),f&512&&(_.min_width=o[9]),f&17300593&&(_.$$scope={dirty:f,ctx:o}),!n&&f&262144&&(n=!0,_.fullscreen=o[18],V(()=>n=!1)),e.$set(_)},i(o){i||(h(e.$$.fragment,o),i=!0)},o(o){b(e.$$.fragment,o),i=!1},d(o){v(e,o)}}}function ie(l,e,n){let{value:i=null}=e,{elem_id:u=""}=e,{elem_classes:c=[]}=e,{visible:o=!0}=e,{loading_status:f}=e,{label:_}=e,{show_label:g}=e,{container:w=!0}=e,{scale:t=null}=e,{min_width:a=void 0}=e,{theme_mode:d}=e,{caption:P}=e,{bokeh_version:m}=e,{gradio:I}=e,{show_actions_button:C=!1}=e,{_selectable:L=!1}=e,{x_lim:N=null}=e,{show_fullscreen_button:W=!1}=e,S=!1;const A=({detail:s})=>{n(18,S=s)},D=()=>I.dispatch("clear_status",f),E=()=>I.dispatch("change"),G=s=>I.dispatch("select",s.detail);function H(s){S=s,n(18,S)}return l.$$set=s=>{"value"in s&&n(0,i=s.value),"elem_id"in s&&n(1,u=s.elem_id),"elem_classes"in s&&n(2,c=s.elem_classes),"visible"in s&&n(3,o=s.visible),"loading_status"in s&&n(4,f=s.loading_status),"label"in s&&n(5,_=s.label),"show_label"in s&&n(6,g=s.show_label),"container"in s&&n(7,w=s.container),"scale"in s&&n(8,t=s.scale),"min_width"in s&&n(9,a=s.min_width),"theme_mode"in s&&n(10,d=s.theme_mode),"caption"in s&&n(11,P=s.caption),"bokeh_version"in s&&n(12,m=s.bokeh_version),"gradio"in s&&n(13,I=s.gradio),"show_actions_button"in s&&n(14,C=s.show_actions_button),"_selectable"in s&&n(15,L=s._selectable),"x_lim"in s&&n(16,N=s.x_lim),"show_fullscreen_button"in s&&n(17,W=s.show_fullscreen_button)},[i,u,c,o,f,_,g,w,t,a,d,P,m,I,C,L,N,W,S,A,D,E,G,H]}class Be extends U{constructor(e){super(),ee(this,e,ie,ne,te,{value:0,elem_id:1,elem_classes:2,visible:3,loading_status:4,label:5,show_label:6,container:7,scale:8,min_width:9,theme_mode:10,caption:11,bokeh_version:12,gradio:13,show_actions_button:14,_selectable:15,x_lim:16,show_fullscreen_button:17})}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),r()}get elem_id(){return this.$$.ctx[1]}set elem_id(e){this.$$set({elem_id:e}),r()}get elem_classes(){return this.$$.ctx[2]}set elem_classes(e){this.$$set({elem_classes:e}),r()}get visible(){return this.$$.ctx[3]}set visible(e){this.$$set({visible:e}),r()}get loading_status(){return this.$$.ctx[4]}set loading_status(e){this.$$set({loading_status:e}),r()}get label(){return this.$$.ctx[5]}set label(e){this.$$set({label:e}),r()}get show_label(){return this.$$.ctx[6]}set show_label(e){this.$$set({show_label:e}),r()}get container(){return this.$$.ctx[7]}set container(e){this.$$set({container:e}),r()}get scale(){return this.$$.ctx[8]}set scale(e){this.$$set({scale:e}),r()}get min_width(){return this.$$.ctx[9]}set min_width(e){this.$$set({min_width:e}),r()}get theme_mode(){return this.$$.ctx[10]}set theme_mode(e){this.$$set({theme_mode:e}),r()}get caption(){return this.$$.ctx[11]}set caption(e){this.$$set({caption:e}),r()}get bokeh_version(){return this.$$.ctx[12]}set bokeh_version(e){this.$$set({bokeh_version:e}),r()}get gradio(){return this.$$.ctx[13]}set gradio(e){this.$$set({gradio:e}),r()}get show_actions_button(){return this.$$.ctx[14]}set show_actions_button(e){this.$$set({show_actions_button:e}),r()}get _selectable(){return this.$$.ctx[15]}set _selectable(e){this.$$set({_selectable:e}),r()}get x_lim(){return this.$$.ctx[16]}set x_lim(e){this.$$set({x_lim:e}),r()}get show_fullscreen_button(){return this.$$.ctx[17]}set show_fullscreen_button(e){this.$$set({show_fullscreen_button:e}),r()}}export{K as BasePlot,Be as default};
//# sourceMappingURL=Index-BR1sVb0F.js.map
