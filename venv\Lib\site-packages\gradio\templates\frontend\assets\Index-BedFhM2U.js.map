{"version": 3, "file": "Index-BedFhM2U.js", "sources": ["../../../../js/box/Index.svelte"], "sourcesContent": ["<script lang=\"ts\">\n\timport { Block } from \"@gradio/atoms\";\n\texport let elem_id: string;\n\texport let elem_classes: string[];\n\texport let visible = true;\n</script>\n\n<Block {elem_id} {elem_classes} {visible} explicit_call>\n\t<slot />\n</Block>\n"], "names": ["elem_id", "$$props", "elem_classes", "visible"], "mappings": "ksCAEY,CAAA,QAAAA,CAAA,EAAAC,EACA,CAAA,aAAAC,CAAA,EAAAD,GACA,QAAAE,EAAU,EAAA,EAAAF"}