const __vite__fileDeps=["./Blocks-nqcZ2TmD.js","./index-DJ2rNx9E.js","./index-rsZ55Oi2.css","./StreamingBar.svelte_svelte_type_style_lang-DlRzv56K.js","./StreamingBar-Cgs5stVH.css","./MarkdownCode.svelte_svelte_type_style_lang-Bl8p72Ow.js","./prism-python-CeMtt1IT.js","./MarkdownCode-DVeJpfiO.css","./Toast-BDCWOwui.js","./index-BALGG9zl.js","./IconButtonWrapper-BqcF4N5S.css","./utils-BsGrhMNe.js","./Blocks-B6OQoqZ7.css","./Login-DN2LJua4.js","./Index-DE1Sah7F.js","./Index-12OnbRhk.css","./Textbox-Co0OLweQ.js","./BlockTitle-C6qeQAMx.js","./Info-D7HP20hi.js","./MarkdownCode-Cdb8e5t4.js","./Check-CEkiXcyC.js","./Copy-CxQ9EyK2.js","./Send-DyoOovnk.js","./Square-oAGqOwsh.js","./Textbox-jWD3sCxr.css","./Block-CJdXVpa7.js","./Button-BY4Yg2Ti.js","./Image-CnqB5dbD.js","./file-url-DoxvUUVV.js","./DownloadLink.svelte_svelte_type_style_lang-C_5UIfol.js","./DownloadLink-CqD3Uu0l.css","./Image-B8dFOee4.css","./Button-DTh9AgeE.css","./ImagePreview-C_qhEOxI.css","./Index-BrpXEgHX.js","./index-DYtg3pip.js","./IconButton-C_HS7fTi.js","./Clear-By3xiIwg.js","./Index-D-5U5m91.css","./Login-BCwzjozv.css","./Example-ClKJOMGh.css"],__vite__mapDeps=i=>i.map(i=>__vite__fileDeps[i]);
import{$ as Fe,s as He,w as Ve,m as Ge,p as me,_ as he}from"./index-DJ2rNx9E.js";import{E as We}from"./Embed-DP0vgn8y.js";import{S as Ze}from"./index-DYtg3pip.js";/* empty css                                                        */import"./StreamingBar.svelte_svelte_type_style_lang-DlRzv56K.js";import{s as xe}from"./DownloadLink.svelte_svelte_type_style_lang-C_5UIfol.js";import"./svelte/svelte.js";import"./MarkdownCode.svelte_svelte_type_style_lang-Bl8p72Ow.js";import"./prism-python-CeMtt1IT.js";import"./IconButton-C_HS7fTi.js";import"./Clear-By3xiIwg.js";var Ye=()=>{const t=document.createElement("link");t.href="https://fonts.googleapis.com/css2?family=Source+Sans+Pro:ital,wght@0,200;0,300;0,400;0,600;0,700;0,900;1,200;1,300;1,400;1,600;1,700;1,900&display=swap",t.rel="stylesheet";const e=document.createElement("link");e.href="https://fonts.googleapis.com/css2?family=IBM+Plex+Mono:wght@400;600;700&display=swap",e.rel="stylesheet",document.head.appendChild(t),document.head.appendChild(e)},Je=()=>{const t=document.createElement("div");return t.style.backgroundImage="linear-gradient(to top, #f9fafb, white)",t.style.border="1px solid #e5e7eb",t.style.borderRadius="0.75rem",t.style.boxShadow="0 0 10px rgba(0, 0, 0, 0.1)",t.style.color="#374151",t.style.display="flex",t.style.flexDirection="row",t.style.alignItems="center",t.style.height="40px",t.style.justifyContent="space-between",t.style.overflow="hidden",t.style.position="fixed",t.style.right=".75rem",t.style.top=".75rem",t.style.width="auto",t.style.zIndex="20",t.style.paddingLeft="1rem",t.setAttribute("id","huggingface-space-header"),window.matchMedia("(max-width: 768px)").addEventListener("change",e=>{e.matches?t.style.display="none":t.style.display="flex"}),t},Ke=()=>{const t=document.createElementNS("http://www.w3.org/2000/svg","svg");t.setAttribute("xmlns","http://www.w3.org/2000/svg"),t.setAttribute("xmlns:link","http://www.w3.org/1999/xlink"),t.setAttribute("aria-hidden","true"),t.setAttribute("focusable","false"),t.setAttribute("role","img"),t.setAttribute("width","1em"),t.setAttribute("height","1em"),t.setAttribute("preserveAspectRatio","xMidYMid meet"),t.setAttribute("viewBox","0 0 12 12"),t.setAttribute("fill","currentColor");const e=document.createElementNS("http://www.w3.org/2000/svg","path");return e.setAttribute("d","M0.375001 10.3828L0.375 1.61719C0.375 1.104 0.816001 0.687501 1.35938 0.687501L10.6406 0.6875C10.9017 0.6875 11.1521 0.785449 11.3367 0.959797C11.5213 1.13415 11.625 1.37062 11.625 1.61719V10.3828C11.625 10.6294 11.5213 10.8659 11.3367 11.0402C11.1521 11.2145 10.9017 11.3125 10.6406 11.3125H1.35938C0.816001 11.3125 0.375001 10.896 0.375001 10.3828ZM1.35938 10.5156H10.6406C10.7183 10.5156 10.7813 10.4561 10.7813 10.3828V4.40625H1.21875V10.3828C1.21875 10.418 1.23356 10.4518 1.25994 10.4767C1.28631 10.5017 1.32208 10.5156 1.35938 10.5156ZM4.61052 6.38251L5.9999 7.69472L7.38927 6.38251C7.44083 6.33007 7.50645 6.29173 7.57913 6.27153C7.6518 6.25134 7.72898 6.25003 7.8024 6.26776C7.87583 6.28549 7.9428 6.3216 7.99628 6.37227C8.04983 6.42295 8.08785 6.48631 8.10645 6.5557C8.12528 6.62497 8.12393 6.69773 8.10263 6.76635C8.0814 6.83497 8.0409 6.8969 7.98555 6.94564L6.29802 8.53936C6.21892 8.61399 6.11169 8.65592 5.9999 8.65592C5.8881 8.65592 5.78087 8.61399 5.70177 8.53936L4.01427 6.94564C3.95874 6.89694 3.91814 6.835 3.89676 6.76633C3.87538 6.69766 3.874 6.62483 3.89277 6.55549C3.91154 6.48615 3.94977 6.42287 4.00343 6.37233C4.05708 6.32179 4.12418 6.28585 4.19765 6.2683C4.27098 6.25054 4.34803 6.25178 4.42068 6.27188C4.49334 6.29198 4.55891 6.3302 4.61052 6.38251Z"),t.appendChild(e),t},Qe=(t,e)=>{const r=document.createElement("div");return r.setAttribute("id","space-header__collapse"),r.style.display="flex",r.style.flexDirection="row",r.style.alignItems="center",r.style.justifyContent="center",r.style.fontSize="16px",r.style.paddingLeft="10px",r.style.paddingRight="10px",r.style.height="40px",r.style.cursor="pointer",r.style.color="#40546e",r.style.transitionDuration="0.1s",r.style.transitionProperty="all",r.style.transitionTimingFunction="ease-in-out",r.appendChild(Ke()),r.addEventListener("click",n=>{n.preventDefault(),n.stopPropagation(),e()}),r.addEventListener("mouseenter",()=>{r.style.color="#213551"}),r.addEventListener("mouseleave",()=>{r.style.color="#40546e"}),r},Xe=t=>{const e=document.createElement("p");return e.style.margin="0",e.style.padding="0",e.style.color="#9ca3af",e.style.fontSize="14px",e.style.fontFamily="Source Sans Pro, sans-serif",e.style.padding="0px 6px",e.style.borderLeft="1px solid #e5e7eb",e.style.marginLeft="4px",e.textContent=(t??0).toString(),e},$e=()=>{const t=document.createElementNS("http://www.w3.org/2000/svg","svg");t.setAttribute("xmlns","http://www.w3.org/2000/svg"),t.setAttribute("xmlns:link","http://www.w3.org/1999/xlink"),t.setAttribute("aria-hidden","true"),t.setAttribute("focusable","false"),t.setAttribute("role","img"),t.setAttribute("width","1em"),t.setAttribute("height","1em"),t.setAttribute("preserveAspectRatio","xMidYMid meet"),t.setAttribute("viewBox","0 0 32 32"),t.setAttribute("fill","#6b7280");const e=document.createElementNS("http://www.w3.org/2000/svg","path");return e.setAttribute("d","M22.45,6a5.47,5.47,0,0,1,3.91,1.64,5.7,5.7,0,0,1,0,8L16,26.13,5.64,15.64a5.7,5.7,0,0,1,0-8,5.48,5.48,0,0,1,7.82,0L16,10.24l2.53-2.58A5.44,5.44,0,0,1,22.45,6m0-2a7.47,7.47,0,0,0-5.34,2.24L16,7.36,14.89,6.24a7.49,7.49,0,0,0-10.68,0,7.72,7.72,0,0,0,0,10.82L16,29,27.79,17.06a7.72,7.72,0,0,0,0-10.82A7.49,7.49,0,0,0,22.45,4Z"),t.appendChild(e),t},et=t=>{const e=document.createElement("a");return e.setAttribute("href",`https://huggingface.co/spaces/${t.id}`),e.setAttribute("rel","noopener noreferrer"),e.setAttribute("target","_blank"),e.style.border="1px solid #e5e7eb",e.style.borderRadius="6px",e.style.display="flex",e.style.flexDirection="row",e.style.alignItems="center",e.style.margin="0 0 0 12px",e.style.fontSize="14px",e.style.paddingLeft="4px",e.style.textDecoration="none",e.appendChild($e()),e.appendChild(Xe(t.likes)),e},tt=(t,e="user")=>{const r=e==="user"?"users":"organizations",n=document.createElement("img");return n.src=`https://huggingface.co/api/${r}/${t}/avatar`,n.style.width="0.875rem",n.style.height="0.875rem",n.style.borderRadius="50%",n.style.flex="none",n.style.marginRight="0.375rem",n},rt=t=>{const[e,r]=t.split("/"),n=document.createElement("a");return n.setAttribute("href",`https://huggingface.co/spaces/${t}`),n.setAttribute("rel","noopener noreferrer"),n.setAttribute("target","_blank"),n.style.color="#1f2937",n.style.textDecoration="none",n.style.fontWeight="600",n.style.fontSize="15px",n.style.lineHeight="24px",n.style.flex="none",n.style.fontFamily="IBM Plex Mono, sans-serif",n.addEventListener("mouseover",()=>{n.style.color="#2563eb"}),n.addEventListener("mouseout",()=>{n.style.color="#1f2937"}),n.textContent=r,n},nt=()=>{const t=document.createElement("div");return t.style.marginLeft=".125rem",t.style.marginRight=".125rem",t.style.color="#d1d5db",t.textContent="/",t},st=t=>{const e=document.createElement("a");return e.setAttribute("href",`https://huggingface.co/${t}`),e.setAttribute("rel","noopener noreferrer"),e.setAttribute("target","_blank"),e.style.color="rgb(107, 114, 128)",e.style.textDecoration="none",e.style.fontWeight="400",e.style.fontSize="16px",e.style.lineHeight="24px",e.style.flex="none",e.style.fontFamily="Source Sans Pro, sans-serif",e.addEventListener("mouseover",()=>{e.style.color="#2563eb"}),e.addEventListener("mouseout",()=>{e.style.color="rgb(107, 114, 128)"}),e.textContent=t,e},ot=t=>{const e=document.createElement("div");return e.style.display="flex",e.style.flexDirection="row",e.style.alignItems="center",e.style.justifyContent="center",e.style.borderRight="1px solid #e5e7eb",e.style.paddingRight="12px",e.style.height="40px",t.type!=="unknown"&&e.appendChild(tt(t.author,t.type)),e.appendChild(st(t.author)),e.appendChild(nt()),e.appendChild(rt(t.id)),e.appendChild(et(t)),e},it=t=>{const e=Je(),r=()=>e.style.display="none";return e.appendChild(ot(t)),e.appendChild(Qe(t,r)),e},ge=async(t,e="user")=>{const r=e==="user"?"users":"organizations";try{return(await fetch(`https://huggingface.co/api/${r}/${t}/avatar`)).ok}catch{return!1}},at=async t=>{try{return await(await fetch(`https://huggingface.co/api/spaces/${t}`)).json()}catch{return null}},lt=(t,e)=>{if(document.body===null)return console.error("document.body is null");document.body.appendChild(t)};async function ct(t,e){var r,n;if(window===void 0)return console.error("Please run this script in a browser environment");if(Object.values((n=(r=window.location)==null?void 0:r.ancestorOrigins)!=null?n:{0:window.document.referrer}).some(f=>{var l;return((l=new URL(f))==null?void 0:l.origin)==="https://huggingface.co"}))return;Ye();let a;if(typeof t=="string"){if(a=await at(t),a===null)return console.error("Space not found")}else a=t;const[o,u]=await Promise.all([ge(a.author,"user"),ge(a.author,"org")]);a.type=o?"user":u?"org":"unknown";const d=it(a);return lt(d),{element:d}}var ut=(t,e)=>ct(t);const{SvelteComponent:dt,add_flush_callback:H,append:k,assign:_t,attr:y,bind:V,binding_callbacks:G,check_outros:we,component_subscribe:be,create_component:W,destroy_component:Z,detach:L,element:E,empty:ft,flush:g,get_spread_object:pt,get_spread_update:mt,group_outros:ye,init:ht,insert:R,mount_component:x,noop:gt,safe_not_equal:wt,set_data:Ae,space:Le,text:O,transition_in:v,transition_out:C}=window.__gradio__svelte__internal,{onMount:ve,createEventDispatcher:bt,onDestroy:yt}=window.__gradio__svelte__internal;function ke(t){let e,r;return e=new Ze({props:{absolute:!t[4],status:t[19],timer:!1,queue_position:null,queue_size:null,translucent:!0,loading_text:t[20],i18n:t[25],autoscroll:t[0],$$slots:{error:[At],"additional-loading-text":[kt]},$$scope:{ctx:t}}}),{c(){W(e.$$.fragment)},m(n,i){x(e,n,i),r=!0},p(n,i){const a={};i[0]&16&&(a.absolute=!n[4]),i[0]&524288&&(a.status=n[19]),i[0]&1048576&&(a.loading_text=n[20]),i[0]&33554432&&(a.i18n=n[25]),i[0]&1&&(a.autoscroll=n[0]),i[0]&33562880|i[1]&67108864&&(a.$$scope={dirty:i,ctx:n}),e.$set(a)},i(n){r||(v(e.$$.fragment,n),r=!0)},o(n){C(e.$$.fragment,n),r=!1},d(n){Z(e,n)}}}function vt(t){let e;return{c(){e=E("p"),e.innerHTML='If your custom component never loads, consult the troubleshooting <a style="color: blue;" href="https://www.gradio.app/guides/frequently-asked-questions#the-development-server-didnt-work-for-me" class="svelte-y6l4b">guide</a>.'},m(r,n){R(r,e,n)},d(r){r&&L(e)}}}function kt(t){let e,r=t[27]==="dev"&&vt();return{c(){e=E("div"),r&&r.c(),y(e,"class","load-text"),y(e,"slot","additional-loading-text")},m(n,i){R(n,e,i),r&&r.m(e,null)},p:gt,d(n){n&&L(e),r&&r.d()}}}function Ct(t){let e,r=t[25]("errors.contact_page_author")+"",n;return{c(){e=E("p"),n=O(r),y(e,"class","svelte-y6l4b")},m(i,a){R(i,e,a),k(e,n)},p(i,a){a[0]&33554432&&r!==(r=i[25]("errors.contact_page_author")+"")&&Ae(n,r)},d(i){i&&L(e)}}}function Et(t){let e,r,n,i,a,o;return{c(){e=E("p"),r=O("Please "),n=E("a"),i=O("contact the author of the space"),o=O(" to let them know."),y(n,"href",a="https://huggingface.co/spaces/"+t[8]+"/discussions/new?title="+t[28].title(t[13]?.detail)+"&description="+t[28].description(t[13]?.detail,location.origin)),y(n,"class","svelte-y6l4b"),y(e,"class","svelte-y6l4b")},m(u,d){R(u,e,d),k(e,r),k(e,n),k(n,i),k(e,o)},p(u,d){d[0]&8448&&a!==(a="https://huggingface.co/spaces/"+u[8]+"/discussions/new?title="+u[28].title(u[13]?.detail)+"&description="+u[28].description(u[13]?.detail,location.origin))&&y(n,"href",a)},d(u){u&&L(e)}}}function At(t){let e,r,n,i=(t[13]?.message||"")+"",a,o;function u(l,c){return(l[13].status==="space_error"||l[13].status==="paused")&&l[13].discussions_enabled?Et:Ct}let d=u(t),f=d(t);return{c(){e=E("div"),r=E("p"),n=E("strong"),a=O(i),o=Le(),f.c(),y(r,"class","svelte-y6l4b"),y(e,"class","error svelte-y6l4b"),y(e,"slot","error")},m(l,c){R(l,e,c),k(e,r),k(r,n),k(n,a),k(e,o),f.m(e,null)},p(l,c){c[0]&8192&&i!==(i=(l[13]?.message||"")+"")&&Ae(a,i),d===(d=u(l))&&f?f.p(l,c):(f.d(1),f=d(l),f&&(f.c(),f.m(e,null)))},d(l){l&&L(e),f.d()}}}function Lt(t){let e,r,n,i,a;const o=[{app:t[14]},t[12],{fill_height:!t[4]&&t[12].fill_height},{theme_mode:t[21]},{control_page_title:t[5]},{target:t[9]},{autoscroll:t[0]},{show_footer:!t[4]},{app_mode:t[3]},{version:t[1]},{api_prefix:t[12].api_prefix||""},{max_file_size:t[12].max_file_size},{initial_layout:void 0},{search_params:new URLSearchParams(window.location.search)}];function u(c){t[37](c)}function d(c){t[38](c)}function f(c){t[39](c)}let l={};for(let c=0;c<o.length;c+=1)l=_t(l,o[c]);return t[10]!==void 0&&(l.ready=t[10]),t[11]!==void 0&&(l.render_complete=t[11]),t[15]!==void 0&&(l.add_new_message=t[15]),e=new t[23]({props:l}),G.push(()=>V(e,"ready",u)),G.push(()=>V(e,"render_complete",d)),G.push(()=>V(e,"add_new_message",f)),{c(){W(e.$$.fragment)},m(c,_){x(e,c,_),a=!0},p(c,_){const A=_[0]&2118203?mt(o,[_[0]&16384&&{app:c[14]},_[0]&4096&&pt(c[12]),_[0]&4112&&{fill_height:!c[4]&&c[12].fill_height},_[0]&2097152&&{theme_mode:c[21]},_[0]&32&&{control_page_title:c[5]},_[0]&512&&{target:c[9]},_[0]&1&&{autoscroll:c[0]},_[0]&16&&{show_footer:!c[4]},_[0]&8&&{app_mode:c[3]},_[0]&2&&{version:c[1]},_[0]&4096&&{api_prefix:c[12].api_prefix||""},_[0]&4096&&{max_file_size:c[12].max_file_size},_&0&&{initial_layout:void 0},_&0&&{search_params:new URLSearchParams(window.location.search)}]):{};!r&&_[0]&1024&&(r=!0,A.ready=c[10],H(()=>r=!1)),!n&&_[0]&2048&&(n=!0,A.render_complete=c[11],H(()=>n=!1)),!i&&_[0]&32768&&(i=!0,A.add_new_message=c[15],H(()=>i=!1)),e.$set(A)},i(c){a||(v(e.$$.fragment,c),a=!0)},o(c){C(e.$$.fragment,c),a=!1},d(c){Z(e,c)}}}function Rt(t){let e,r;return e=new t[24]({props:{auth_message:t[12].auth_message,root:t[12].root,space_id:t[8],app_mode:t[3]}}),{c(){W(e.$$.fragment)},m(n,i){x(e,n,i),r=!0},p(n,i){const a={};i[0]&4096&&(a.auth_message=n[12].auth_message),i[0]&4096&&(a.root=n[12].root),i[0]&256&&(a.space_id=n[8]),i[0]&8&&(a.app_mode=n[3]),e.$set(a)},i(n){r||(v(e.$$.fragment,n),r=!0)},o(n){C(e.$$.fragment,n),r=!1},d(n){Z(e,n)}}}function St(t){let e,r,n,i,a,o=(t[19]==="pending"||t[19]==="error")&&!(t[12]&&t[12]?.auth_required)&&ke(t);const u=[Rt,Lt],d=[];function f(l,c){return l[12]?.auth_required&&l[24]?0:l[12]&&l[23]&&l[22]?1:-1}return~(r=f(t))&&(n=d[r]=u[r](t)),{c(){o&&o.c(),e=Le(),n&&n.c(),i=ft()},m(l,c){o&&o.m(l,c),R(l,e,c),~r&&d[r].m(l,c),R(l,i,c),a=!0},p(l,c){(l[19]==="pending"||l[19]==="error")&&!(l[12]&&l[12]?.auth_required)?o?(o.p(l,c),c[0]&528384&&v(o,1)):(o=ke(l),o.c(),v(o,1),o.m(e.parentNode,e)):o&&(ye(),C(o,1,1,()=>{o=null}),we());let _=r;r=f(l),r===_?~r&&d[r].p(l,c):(n&&(ye(),C(d[_],1,1,()=>{d[_]=null}),we()),~r?(n=d[r],n?n.p(l,c):(n=d[r]=u[r](l),n.c()),v(n,1),n.m(i.parentNode,i)):n=null)},i(l){a||(v(o),v(n),a=!0)},o(l){C(o),C(n),a=!1},d(l){l&&(L(e),L(i)),o&&o.d(l),~r&&d[r].d(l)}}}function It(t){let e,r,n;function i(o){t[40](o)}let a={display:t[6]&&t[4],is_embed:t[4],info:!!t[8]&&t[7],version:t[1],initial_height:t[2],space:t[8],loaded:t[19]==="complete",fill_width:t[12]?.fill_width||!1,pages:t[16],current_page:t[17],root:t[18],is_lite:t[26],$$slots:{default:[St]},$$scope:{ctx:t}};return t[9]!==void 0&&(a.wrapper=t[9]),e=new We({props:a}),G.push(()=>V(e,"wrapper",i)),{c(){W(e.$$.fragment)},m(o,u){x(e,o,u),n=!0},p(o,u){const d={};u[0]&80&&(d.display=o[6]&&o[4]),u[0]&16&&(d.is_embed=o[4]),u[0]&384&&(d.info=!!o[8]&&o[7]),u[0]&2&&(d.version=o[1]),u[0]&4&&(d.initial_height=o[2]),u[0]&256&&(d.space=o[8]),u[0]&524288&&(d.loaded=o[19]==="complete"),u[0]&4096&&(d.fill_width=o[12]?.fill_width||!1),u[0]&65536&&(d.pages=o[16]),u[0]&131072&&(d.current_page=o[17]),u[0]&262144&&(d.root=o[18]),u[0]&66649915|u[1]&67108864&&(d.$$scope={dirty:u,ctx:o}),!r&&u[0]&512&&(r=!0,d.wrapper=o[9],H(()=>r=!1)),e.$set(d)},i(o){n||(v(e.$$.fragment,o),n=!0)},o(o){C(e.$$.fragment,o),n=!1},d(o){Z(e,o)}}}let Pt=-1;function Mt(){const t=Ve({}),e=new Map,r=new IntersectionObserver(i=>{i.forEach(a=>{if(a.isIntersecting){let o=e.get(a.target);o!==void 0&&t.update(u=>({...u,[o]:!0}))}})});function n(i,a){e.set(a,i),r.observe(a)}return{register:n,subscribe:t.subscribe}}const Ce=Mt();async function Ee(t){if(t){const e=new DOMParser,r=Array.from(e.parseFromString(t,"text/html").head.children);if(r)for(let n of r){let i=document.createElement(n.tagName);if(Array.from(n.attributes).forEach(a=>{i.setAttribute(a.name,a.value)}),i.textContent=n.textContent,i.tagName=="META"){const a=i.getAttribute("property"),o=i.getAttribute("name");if(a||o){const d=Array.from(document.head.getElementsByTagName("meta")??[]).find(f=>a&&f.getAttribute("property")===a||o&&f.getAttribute("name")===o?!f.isEqualNode(i):!1);if(d){document.head.replaceChild(i,d);continue}}}document.head.appendChild(i)}}}function Nt(t,e,r){let n,i;be(t,Fe,s=>r(25,n=s)),be(t,Ce,s=>r(36,i=s)),He();const a=bt();let{autoscroll:o}=e,{version:u}=e,{initial_height:d}=e,{app_mode:f}=e,{is_embed:l}=e,{theme_mode:c="system"}=e,{control_page_title:_}=e,{container:A}=e,{info:ee}=e,{eager:Y}=e,J,te=[],re,ne,{mount_css:U=Ge}=e,{Client:q}=e,{worker_proxy:S=void 0}=e;S&&(xe(S),S.addEventListener("progress-update",s=>{r(20,ie=s.detail+"...")}));let Re=S!==void 0,{space:K}=e,{src:Q}=e,se=Pt++,oe="pending",I,N=!1,T=!1,p,ie="Loading...",ae,B,D=null;async function le(s){s&&(D||(D=document.createElement("style"),document.head.appendChild(D)),D.textContent=me(s,u,D)),await U(p.root+"/theme.css?v="+p.theme_hash,document.head),p.stylesheets&&await Promise.all(p.stylesheets.map(h=>h.startsWith("http:")||h.startsWith("https:")?U(h,document.head):fetch(p.root+"/"+h).then(b=>b.text()).then(b=>{me(b,u)})))}function Se(s){const h=window.__gradio_mode__==="website";let m;if(h)m="light";else{const F=new URL(window.location.toString()).searchParams.get("__theme");m=c||F||"system"}return m==="dark"||m==="light"?ce(s,m):m=Ie(s),m}function Ie(s){const h=m();window?.matchMedia("(prefers-color-scheme: dark)")?.addEventListener("change",m);function m(){let b=window?.matchMedia?.("(prefers-color-scheme: dark)").matches?"dark":"light";return ce(s,b),b}return h}function ce(s,h){const m=l?s.parentElement:document.body,b=l?s:s.parentElement;b.style.background="var(--body-background-fill)",h==="dark"?m.classList.add("dark"):m.classList.remove("dark")}let P={message:"",load_status:"pending",status:"sleeping",detail:"SLEEPING"},w,X=!1;function ue(s){r(13,P=s)}const de=window.__GRADIO_DEV__;let j=!1,M;ve(async()=>{r(21,ae=Se(I));const s=window.__GRADIO__SERVER_PORT__;B=de==="dev"?`http://localhost:${typeof s=="number"?s:7860}`:K||Q||new URL(location.pathname,location.origin).href.replace(/\/$/,"");const h=new URLSearchParams(window.location.search).get("deep_link"),m={};if(h&&(m.deep_link=h),r(14,w=await q.connect(B,{status_callback:ue,with_null_state:!0,events:["data","log","status","render"],query_params:m})),window.addEventListener("beforeunload",()=>{w.close()}),!w.config)throw new Error("Could not resolve app config");r(12,p=w.get_url_config()),window.__gradio_space__=p.space_id,window.__gradio_session_hash__=w.session_hash,r(13,P={message:"",load_status:"complete",status:"running",detail:"RUNNING"}),await le(p.css),await Ee(p.head),r(22,X=!0),window.__is_colab__=p.is_colab;const b="supports-zerogpu-headers";window.addEventListener("message",$=>{$.data===b&&(window.supports_zerogpu_headers=!0)});const F=window.location.hostname,Be=F.includes(".dev.")?`https://moon-${F.split(".")[1]}.dev.spaces.huggingface.tech`:"https://huggingface.co";window.parent.postMessage(b,Be),a("loaded"),r(16,te=p.pages),r(17,re=p.current_page),r(18,ne=p.root),p.deep_link_state==="invalid"&&r(35,j=!0),p.dev_mode&&setTimeout(()=>{const{host:$}=new URL(B);let je=new URL(`${window.location.protocol}//${$}${w.api_prefix}/dev/reload`);J=new EventSource(je),J.addEventListener("error",async pe=>{M("Error","Error reloading app","error"),console.error(JSON.parse(pe.data))}),J.addEventListener("reload",async pe=>{if(w.close(),r(14,w=await q.connect(B,{status_callback:ue,with_null_state:!0,events:["data","log","status","render"],session_hash:w.session_hash})),!w.config)throw new Error("Could not resolve app config");r(12,p=w.get_url_config()),window.__gradio_space__=p.space_id,await le(p.css),await Ee(p.head),r(22,X=!0),window.__is_colab__=p.is_colab,a("loaded")})},200)});let _e,fe;async function Pe(){r(23,_e=(await he(()=>import("./Blocks-nqcZ2TmD.js").then(s=>s.B),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12]),import.meta.url)).default)}async function Me(){r(24,fe=(await he(()=>import("./Login-DN2LJua4.js"),__vite__mapDeps([13,14,15,16,17,18,19,1,2,5,6,7,10,20,21,22,23,9,24,25,3,4,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40]),import.meta.url)).default)}function Ne(){p.auth_required?Me():Pe()}const De={readable_error:{NO_APP_FILE:n("errors.no_app_file"),CONFIG_ERROR:n("errors.config_error"),BUILD_ERROR:n("errors.build_error"),RUNTIME_ERROR:n("errors.runtime_error"),PAUSED:n("errors.space_paused")},title(s){return encodeURIComponent(n("errors.space_not_working"))},description(s,h){return encodeURIComponent(`Hello,

Firstly, thanks for creating this space!

I noticed that the space isn't working correctly because there is ${this.readable_error[s]||"an error"}.

It would be great if you could take a look at this because this space is being embedded on ${h}.

Thanks!`)}};ve(async()=>{Ce.register(se,I)});let z;async function ze(s,h){if(s&&!h&&window.self===window.top){z&&(z.remove(),z=void 0);const m=await ut(s);m&&(z=m.element)}}yt(()=>{z?.remove()});function Oe(s){N=s,r(10,N)}function Ue(s){T=s,r(11,T)}function qe(s){M=s,r(15,M)}function Te(s){I=s,r(9,I)}return t.$$set=s=>{"autoscroll"in s&&r(0,o=s.autoscroll),"version"in s&&r(1,u=s.version),"initial_height"in s&&r(2,d=s.initial_height),"app_mode"in s&&r(3,f=s.app_mode),"is_embed"in s&&r(4,l=s.is_embed),"theme_mode"in s&&r(29,c=s.theme_mode),"control_page_title"in s&&r(5,_=s.control_page_title),"container"in s&&r(6,A=s.container),"info"in s&&r(7,ee=s.info),"eager"in s&&r(30,Y=s.eager),"mount_css"in s&&r(31,U=s.mount_css),"Client"in s&&r(32,q=s.Client),"worker_proxy"in s&&r(33,S=s.worker_proxy),"space"in s&&r(8,K=s.space),"src"in s&&r(34,Q=s.src)},t.$$.update=()=>{t.$$.dirty[0]&4096&&p?.app_id&&p.app_id,t.$$.dirty[0]&32768|t.$$.dirty[1]&16&&M&&j&&(M("Error","Deep link was not valid","error"),r(35,j=!1)),t.$$.dirty[0]&9216&&r(19,oe=!N&&P.load_status!=="error"?"pending":!N&&P.load_status==="error"?"error":P.load_status),t.$$.dirty[0]&1073745920|t.$$.dirty[1]&32&&p&&(Y||i[se])&&Ne(),t.$$.dirty[0]&2560&&T&&I.dispatchEvent(new CustomEvent("render",{bubbles:!0,cancelable:!1,composed:!0})),t.$$.dirty[0]&16400&&w?.config&&ze(w?.config?.space_id,l)},[o,u,d,f,l,_,A,ee,K,I,N,T,p,P,w,M,te,re,ne,oe,ie,ae,X,_e,fe,n,Re,de,De,c,Y,U,q,S,Q,j,i,Oe,Ue,qe,Te]}class Gt extends dt{constructor(e){super(),ht(this,e,Nt,It,wt,{autoscroll:0,version:1,initial_height:2,app_mode:3,is_embed:4,theme_mode:29,control_page_title:5,container:6,info:7,eager:30,mount_css:31,Client:32,worker_proxy:33,space:8,src:34},null,[-1,-1])}get autoscroll(){return this.$$.ctx[0]}set autoscroll(e){this.$$set({autoscroll:e}),g()}get version(){return this.$$.ctx[1]}set version(e){this.$$set({version:e}),g()}get initial_height(){return this.$$.ctx[2]}set initial_height(e){this.$$set({initial_height:e}),g()}get app_mode(){return this.$$.ctx[3]}set app_mode(e){this.$$set({app_mode:e}),g()}get is_embed(){return this.$$.ctx[4]}set is_embed(e){this.$$set({is_embed:e}),g()}get theme_mode(){return this.$$.ctx[29]}set theme_mode(e){this.$$set({theme_mode:e}),g()}get control_page_title(){return this.$$.ctx[5]}set control_page_title(e){this.$$set({control_page_title:e}),g()}get container(){return this.$$.ctx[6]}set container(e){this.$$set({container:e}),g()}get info(){return this.$$.ctx[7]}set info(e){this.$$set({info:e}),g()}get eager(){return this.$$.ctx[30]}set eager(e){this.$$set({eager:e}),g()}get mount_css(){return this.$$.ctx[31]}set mount_css(e){this.$$set({mount_css:e}),g()}get Client(){return this.$$.ctx[32]}set Client(e){this.$$set({Client:e}),g()}get worker_proxy(){return this.$$.ctx[33]}set worker_proxy(e){this.$$set({worker_proxy:e}),g()}get space(){return this.$$.ctx[8]}set space(e){this.$$set({space:e}),g()}get src(){return this.$$.ctx[34]}set src(e){this.$$set({src:e}),g()}}export{Gt as default};
//# sourceMappingURL=Index-BgyqPiU2.js.map
