const __vite__fileDeps=["./vega-embed.module-w60szu7N.js","./vega-tooltip.module-DOd-2H5G.js","./index-DJ2rNx9E.js","./index-rsZ55Oi2.css","./time-Bgyi_H-V.js","./step-Ce-xBr2D.js","./linear-CV3SENcB.js","./init-Dmth1JHB.js","./dsv-DB8NKgIY.js","./range-OtVwhkKS.js","./ordinal-BeghXfj9.js","./arc-Ctxh2KTd.js","./dispatch-kxCwF96_.js"],__vite__mapDeps=i=>i.map(i=>__vite__fileDeps[i]);
import{_ as tt}from"./index-DJ2rNx9E.js";import{B as lt}from"./Block-CJdXVpa7.js";import{B as it}from"./BlockTitle-C6qeQAMx.js";/* empty css                                                        */import{E as nt}from"./Empty-ZqppqzTN.js";import"./MarkdownCode.svelte_svelte_type_style_lang-Bl8p72Ow.js";import{L as st}from"./LineChart-CKh1Fdep.js";import{I as ot}from"./IconButtonWrapper--EIOWuEM.js";import{F as rt}from"./FullscreenButton-BG4mOKmH.js";import{S as at}from"./index-DYtg3pip.js";import"./StreamingBar.svelte_svelte_type_style_lang-DlRzv56K.js";import"./svelte/svelte.js";import"./Info-D7HP20hi.js";import"./MarkdownCode-Cdb8e5t4.js";import"./prism-python-CeMtt1IT.js";import"./IconButton-C_HS7fTi.js";import"./Clear-By3xiIwg.js";const{SvelteComponent:ut,add_flush_callback:ft,append:_t,assign:ct,attr:Le,bind:mt,binding_callbacks:Be,check_outros:xe,create_component:U,destroy_component:G,detach:D,element:De,empty:He,flush:_,get_spread_object:gt,get_spread_update:dt,group_outros:pe,init:ht,insert:H,mount_component:J,noop:Ce,safe_not_equal:bt,set_data:qe,space:fe,text:Re,transition_in:P,transition_out:N}=window.__gradio__svelte__internal,{onMount:yt}=window.__gradio__svelte__internal;function Ve(n){let e,t;const i=[{autoscroll:n[3].autoscroll},{i18n:n[3].i18n},n[11]];let r={};for(let s=0;s<i.length;s+=1)r=ct(r,i[s]);return e=new at({props:r}),e.$on("clear_status",n[48]),{c(){U(e.$$.fragment)},m(s,a){J(e,s,a),t=!0},p(s,a){const c=a[0]&2056?dt(i,[a[0]&8&&{autoscroll:s[3].autoscroll},a[0]&8&&{i18n:s[3].i18n},a[0]&2048&&gt(s[11])]):{};e.$set(c)},i(s){t||(P(e.$$.fragment,s),t=!0)},o(s){N(e.$$.fragment,s),t=!1},d(s){G(e,s)}}}function je(n){let e,t;return e=new ot({props:{$$slots:{default:[vt]},$$scope:{ctx:n}}}),{c(){U(e.$$.fragment)},m(i,r){J(e,i,r),t=!0},p(i,r){const s={};r[0]&8192|r[2]&32&&(s.$$scope={dirty:r,ctx:i}),e.$set(s)},i(i){t||(P(e.$$.fragment,i),t=!0)},o(i){N(e.$$.fragment,i),t=!1},d(i){G(e,i)}}}function vt(n){let e,t;return e=new rt({props:{fullscreen:n[13]}}),e.$on("fullscreen",n[49]),{c(){U(e.$$.fragment)},m(i,r){J(e,i,r),t=!0},p(i,r){const s={};r[0]&8192&&(s.fullscreen=i[13]),e.$set(s)},i(i){t||(P(e.$$.fragment,i),t=!0)},o(i){N(e.$$.fragment,i),t=!1},d(i){G(e,i)}}}function wt(n){let e;return{c(){e=Re(n[4])},m(t,i){H(t,e,i)},p(t,i){i[0]&16&&qe(e,t[4])},d(t){t&&D(e)}}}function kt(n){let e,t;return e=new nt({props:{unpadded_box:!0,$$slots:{default:[pt]},$$scope:{ctx:n}}}),{c(){U(e.$$.fragment)},m(i,r){J(e,i,r),t=!0},p(i,r){const s={};r[2]&32&&(s.$$scope={dirty:r,ctx:i}),e.$set(s)},i(i){t||(P(e.$$.fragment,i),t=!0)},o(i){N(e.$$.fragment,i),t=!1},d(i){G(e,i)}}}function xt(n){let e,t,i,r=n[1]&&Me(n);return{c(){e=De("div"),t=fe(),r&&r.c(),i=He(),Le(e,"class","svelte-19qacdz")},m(s,a){H(s,e,a),n[50](e),H(s,t,a),r&&r.m(s,a),H(s,i,a)},p(s,a){s[1]?r?r.p(s,a):(r=Me(s),r.c(),r.m(i.parentNode,i)):r&&(r.d(1),r=null)},i:Ce,o:Ce,d(s){s&&(D(e),D(t),D(i)),n[50](null),r&&r.d(s)}}}function pt(n){let e,t;return e=new st({}),{c(){U(e.$$.fragment)},m(i,r){J(e,i,r),t=!0},i(i){t||(P(e.$$.fragment,i),t=!0)},o(i){N(e.$$.fragment,i),t=!1},d(i){G(e,i)}}}function Me(n){let e,t;return{c(){e=De("p"),t=Re(n[1]),Le(e,"class","caption svelte-19qacdz")},m(i,r){H(i,e,r),_t(e,t)},p(i,r){r[0]&2&&qe(t,i[1])},d(i){i&&D(e)}}}function Ft(n){let e,t,i,r,s,a,c,F,m=n[11]&&Ve(n),h=n[2]&&je(n);i=new it({props:{show_label:n[8],info:void 0,$$slots:{default:[wt]},$$scope:{ctx:n}}});const I=[xt,kt],C=[];function u(o,f){return o[0]&&o[15]?0:1}return s=u(n),a=C[s]=I[s](n),{c(){m&&m.c(),e=fe(),h&&h.c(),t=fe(),U(i.$$.fragment),r=fe(),a.c(),c=He()},m(o,f){m&&m.m(o,f),H(o,e,f),h&&h.m(o,f),H(o,t,f),J(i,o,f),H(o,r,f),C[s].m(o,f),H(o,c,f),F=!0},p(o,f){o[11]?m?(m.p(o,f),f[0]&2048&&P(m,1)):(m=Ve(o),m.c(),P(m,1),m.m(e.parentNode,e)):m&&(pe(),N(m,1,1,()=>{m=null}),xe()),o[2]?h?(h.p(o,f),f[0]&4&&P(h,1)):(h=je(o),h.c(),P(h,1),h.m(t.parentNode,t)):h&&(pe(),N(h,1,1,()=>{h=null}),xe());const d={};f[0]&256&&(d.show_label=o[8]),f[0]&16|f[2]&32&&(d.$$scope={dirty:f,ctx:o}),i.$set(d);let Y=s;s=u(o),s===Y?C[s].p(o,f):(pe(),N(C[Y],1,1,()=>{C[Y]=null}),xe(),a=C[s],a?a.p(o,f):(a=C[s]=I[s](o),a.c()),P(a,1),a.m(c.parentNode,c))},i(o){F||(P(m),P(h),P(i.$$.fragment,o),P(a),F=!0)},o(o){N(m),N(h),N(i.$$.fragment,o),N(a),F=!1},d(o){o&&(D(e),D(t),D(r),D(c)),m&&m.d(o),h&&h.d(o),G(i,o),C[s].d(o)}}}function Et(n){let e,t,i;function r(a){n[51](a)}let s={visible:n[7],elem_id:n[5],elem_classes:n[6],scale:n[9],min_width:n[10],allow_overflow:!1,padding:!0,height:n[12],$$slots:{default:[Ft]},$$scope:{ctx:n}};return n[13]!==void 0&&(s.fullscreen=n[13]),e=new lt({props:s}),Be.push(()=>mt(e,"fullscreen",r)),{c(){U(e.$$.fragment)},m(a,c){J(e,a,c),i=!0},p(a,c){const F={};c[0]&128&&(F.visible=a[7]),c[0]&32&&(F.elem_id=a[5]),c[0]&64&&(F.elem_classes=a[6]),c[0]&512&&(F.scale=a[9]),c[0]&1024&&(F.min_width=a[10]),c[0]&4096&&(F.height=a[12]),c[0]&26911|c[2]&32&&(F.$$scope={dirty:c,ctx:a}),!t&&c[0]&8192&&(t=!0,F.fullscreen=a[13],ft(()=>t=!1)),e.$set(F)},i(a){i||(P(e.$$.fragment,a),i=!0)},o(a){N(e.$$.fragment,a),i=!1},d(a){G(e,a)}}}function zt(n,e,t){let i,r,s,a,c,F,m,h,I,C,{value:u}=e,{x:o}=e,{y:f}=e,{color:d=null}=e,{title:Y=null}=e,{x_title:ie=null}=e,{y_title:ne=null}=e,{color_title:_e=null}=e,{x_bin:M=null}=e,{y_aggregate:Z=void 0}=e,{color_map:$=null}=e,{x_lim:V=null}=e,{y_lim:X=null}=e,{x_label_angle:se=null}=e,{y_label_angle:oe=null}=e,{x_axis_labels_visible:re=!0}=e,{caption:Fe=null}=e,{sort:ce=null}=e,{tooltip:q="axis"}=e,{show_fullscreen_button:Ee=!1}=e,K=!1;function Ye(l){if(l==="x")return"ascending";if(l==="-x")return"descending";if(l==="y")return{field:f,order:"ascending"};if(l==="-y")return{field:f,order:"descending"};if(l===null)return null;if(Array.isArray(l))return l}let{_selectable:ae=!1}=e,ee,{gradio:te}=e,R,me=!1;const Ue={s:1,m:60,h:60*60,d:24*60*60};let le,Q;function ze(l,b,j,z,E,y){if(l.length<1e3||M!==null||u?.mark!=="line"||u?.datatypes[o]==="nominal")return l;const x=250;let g={};if((E===void 0||y===void 0)&&l.forEach(W=>{let O=W[b];(E===void 0||O<E)&&(E=O),(y===void 0||O>y)&&(y=O)}),E===void 0||y===void 0)return l;const k=(y-E)/x;l.forEach((W,O)=>{const B=W[b],S=W[j],v=z!==null?W[z]:"any",w=Math.floor((B-E)/k);g[v]===void 0&&(g[v]=[]),g[v][w]=g[v][w]||[null,Number.POSITIVE_INFINITY,null,Number.NEGATIVE_INFINITY],S<g[v][w][1]&&(g[v][w][0]=O,g[v][w][1]=S),S>g[v][w][3]&&(g[v][w][2]=O,g[v][w][3]=S)});const L=[];return Object.values(g).forEach(W=>{W.forEach(([O,B,S,v])=>{let w=[];O!==null&&S!==null?w=[Math.min(O,S),Math.max(O,S)]:O!==null?w=[O]:S!==null&&(w=[S]),w.forEach(et=>{L.push(l[et])})})}),L}function Ge(l,b,j){let z=l.columns.indexOf(o),E=l.columns.indexOf(f),y=d?l.columns.indexOf(d):null,x=l.data;if(b!==void 0&&j!==void 0){const g=l.datatypes[o]==="temporal"?1e3:1,p=b*g,k=j*g;let L={},W={};const O=x.filter((B,S)=>{const v=B[z],w=y!==null?B[y]:"any";return v<p&&(L[w]===void 0||v>L[w][1])&&(L[w]=[S,v]),v>k&&(W[w]===void 0||v<W[w][1])&&(W[w]=[S,v]),v>=p&&v<=k});x=[...Object.values(L).map(([B,S])=>x[B]),...ze(O,z,E,y,p,k),...Object.values(W).map(([B,S])=>x[B])]}else x=ze(x,z,E,y,void 0,void 0);return q=="all"||Array.isArray(q)?x.map(g=>{const p={};return l.columns.forEach((k,L)=>{p[k]=g[L]}),p}):x.map(g=>{const p={[o]:g[z],[f]:g[E]};return d&&y!==null&&(p[d]=g[y]),p})}let ge=u;const Je=typeof window<"u";let A,T,de=!1,Ie,he,ue,be;async function ye(){if(me){ve=!0;return}if(T&&T.finalize(),!u||!A)return;Ie=A.offsetWidth,he=A.offsetHeight;const l=Xe();l&&(ue=new ResizeObserver(b=>{!b[0].target||!(b[0].target instanceof HTMLElement)||(Ie===0&&A.offsetWidth!==0&&u.datatypes[o]==="nominal"?ye():T.signal("width",b[0].target.offsetWidth).run(),he!==b[0].target.offsetHeight&&K&&(T.signal("height",b[0].target.offsetHeight).run(),he=b[0].target.offsetHeight))}),be||(be=(await tt(()=>import("./vega-embed.module-w60szu7N.js"),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12]),import.meta.url)).default),be(A,l,{actions:!1}).then(function(b){t(39,T=b.view),ue.observe(A);var j,z=0;T.addEventListener("dblclick",()=>{te.dispatch("double_click")}),A.addEventListener("mousedown",function(E){E.detail>1&&E.preventDefault()},!1),ae&&T.addSignalListener("brush",function(E,y){if(Date.now()-z<1e3||(me=!0,Object.keys(y).length===0))return;clearTimeout(j);let x=y[Object.keys(y)[0]];m&&(x=[x[0]/1e3,x[1]/1e3]),j=setTimeout(function(){me=!1,z=Date.now(),te.dispatch("select",{value:x,index:x,selected:!0}),ve&&(ve=!1,ye())},250)})}))}let ve=!1;yt(()=>(t(40,de=!0),()=>{t(40,de=!1),T&&T.finalize(),ue&&ue.disconnect()}));function Xe(){if(!u||!I)return null;let l=I.getPropertyValue("--color-accent"),b=I.getPropertyValue("--body-text-color"),j=I.getPropertyValue("--border-color-primary"),z=I.fontFamily,E=I.getPropertyValue("--block-title-text-weight");const y=p=>p.endsWith("px")?parseFloat(p.slice(0,-2)):12;let x=y(I.getPropertyValue("--text-md")),g=y(I.getPropertyValue("--text-sm"));return{$schema:"https://vega.github.io/schema/vega-lite/v5.17.0.json",background:"transparent",config:{autosize:{type:"fit",contains:"padding"},axis:{labelFont:z,labelColor:b,titleFont:z,titleColor:b,titlePadding:8,tickColor:j,labelFontSize:g,gridColor:j,titleFontWeight:"normal",titleFontSize:g,labelFontWeight:"normal",domain:!1,labelAngle:0},legend:{labelColor:b,labelFont:z,titleColor:b,titleFont:z,titleFontWeight:"normal",titleFontSize:g,labelFontWeight:"normal",offset:2},title:{color:b,font:z,fontSize:x,fontWeight:E,anchor:"middle"},view:{stroke:j},mark:{stroke:u.mark!=="bar"?l:void 0,fill:u.mark==="bar"?l:void 0,cursor:"crosshair"}},data:{name:"data"},datasets:{data:ee},layer:["plot",...u.mark==="line"?["hover"]:[]].map(p=>({encoding:{size:u.mark==="line"?p=="plot"?{condition:{empty:!1,param:"hoverPlot",value:3},value:2}:{condition:{empty:!1,param:"hover",value:100},value:0}:void 0,opacity:p==="plot"?void 0:{condition:{empty:!1,param:"hover",value:1},value:0},x:{axis:{...se!==null&&{labelAngle:se},labels:re,ticks:re},field:o,title:ie||o,type:u.datatypes[o],scale:h?{domain:h}:void 0,bin:R?{step:R}:void 0,sort:F},y:{axis:oe?{labelAngle:oe}:{},field:f,title:ne||f,type:u.datatypes[f],scale:{zero:!1,domainMin:a??void 0,domainMax:c??void 0},aggregate:Q?le:void 0},color:d?{field:d,legend:{orient:"bottom",title:_e},scale:u.datatypes[d]==="nominal"?{domain:i,range:$?i.map(k=>$[k]):void 0}:{range:[100,200,300,400,500,600,700,800,900].map(k=>I.getPropertyValue("--primary-"+k)),interpolate:"hsl"},type:u.datatypes[d]}:void 0,tooltip:q=="none"?void 0:[{field:f,type:u.datatypes[f],aggregate:Q?le:void 0,title:ne||f},{field:o,type:u.datatypes[o],title:ie||o,format:m?"%Y-%m-%d %H:%M:%S":void 0,bin:R?{step:R}:void 0},...d?[{field:d,type:u.datatypes[d]}]:[],...q==="axis"?[]:u?.columns.filter(k=>k!==o&&k!==f&&k!==d&&(q==="all"||q.includes(k))).map(k=>({field:k,type:u.datatypes[k]}))]},strokeDash:{},mark:{clip:!0,type:p==="hover"?"point":u.mark},name:p})),params:[...u.mark==="line"?[{name:"hoverPlot",select:{clear:"mouseout",fields:d?[d]:[],nearest:!0,on:"mouseover",type:"point"},views:["hover"]},{name:"hover",select:{clear:"mouseout",nearest:!0,on:"mouseover",type:"point"},views:["hover"]}]:[],...ae?[{name:"brush",select:{encodings:["x"],mark:{fill:"gray",fillOpacity:.3,stroke:"none"},type:"interval"},views:["plot"]}]:[]],width:A.offsetWidth,height:ke||K?"container":void 0,title:Y||void 0}}let{label:Oe="Textbox"}=e,{elem_id:Se=""}=e,{elem_classes:Pe=[]}=e,{visible:Ae=!0}=e,{show_label:Ne}=e,{scale:Te=null}=e,{min_width:We=void 0}=e,{loading_status:we=void 0}=e,{height:ke=void 0}=e;const Ke=()=>te.dispatch("clear_status",we),Qe=({detail:l})=>{t(13,K=l)};function Ze(l){Be[l?"unshift":"push"](()=>{A=l,t(14,A)})}function $e(l){K=l,t(13,K)}return n.$$set=l=>{"value"in l&&t(0,u=l.value),"x"in l&&t(18,o=l.x),"y"in l&&t(19,f=l.y),"color"in l&&t(20,d=l.color),"title"in l&&t(21,Y=l.title),"x_title"in l&&t(22,ie=l.x_title),"y_title"in l&&t(23,ne=l.y_title),"color_title"in l&&t(24,_e=l.color_title),"x_bin"in l&&t(25,M=l.x_bin),"y_aggregate"in l&&t(26,Z=l.y_aggregate),"color_map"in l&&t(27,$=l.color_map),"x_lim"in l&&t(16,V=l.x_lim),"y_lim"in l&&t(17,X=l.y_lim),"x_label_angle"in l&&t(28,se=l.x_label_angle),"y_label_angle"in l&&t(29,oe=l.y_label_angle),"x_axis_labels_visible"in l&&t(30,re=l.x_axis_labels_visible),"caption"in l&&t(1,Fe=l.caption),"sort"in l&&t(31,ce=l.sort),"tooltip"in l&&t(32,q=l.tooltip),"show_fullscreen_button"in l&&t(2,Ee=l.show_fullscreen_button),"_selectable"in l&&t(33,ae=l._selectable),"gradio"in l&&t(3,te=l.gradio),"label"in l&&t(4,Oe=l.label),"elem_id"in l&&t(5,Se=l.elem_id),"elem_classes"in l&&t(6,Pe=l.elem_classes),"visible"in l&&t(7,Ae=l.visible),"show_label"in l&&t(8,Ne=l.show_label),"scale"in l&&t(9,Te=l.scale),"min_width"in l&&t(10,We=l.min_width),"loading_status"in l&&t(11,we=l.loading_status),"height"in l&&t(12,ke=l.height)},n.$$.update=()=>{n.$$.dirty[0]&65536&&t(16,V=V||null),n.$$.dirty[0]&65536&&t(46,[r,s]=V===null?[void 0,void 0]:V,r,(t(45,s),t(16,V))),n.$$.dirty[0]&1|n.$$.dirty[1]&49152&&t(34,ee=u?Ge(u,r,s):[]),n.$$.dirty[0]&1048577|n.$$.dirty[1]&8&&(i=d&&u&&u.datatypes[d]==="nominal"?Array.from(new Set(ee.map(l=>l[d]))):[]),n.$$.dirty[0]&131072&&t(17,X=X||null),n.$$.dirty[0]&131072&&t(44,[a,c]=X||[void 0,void 0],a,(t(43,c),t(17,X))),n.$$.dirty[1]&1&&(F=Ye(ce)),n.$$.dirty[0]&262145&&t(41,m=u&&u.datatypes[o]==="temporal"),n.$$.dirty[0]&65536|n.$$.dirty[1]&1024&&(h=V&&m?[V[0]*1e3,V[1]*1e3]:V),n.$$.dirty[0]&33554432&&t(35,R=M?typeof M=="string"?1e3*parseInt(M.substring(0,M.length-1))*Ue[M[M.length-1]]:M:void 0),n.$$.dirty[0]&67371009|n.$$.dirty[1]&80&&u&&(u.mark==="point"?(t(37,Q=R!==void 0),t(36,le=Z||Q?"sum":void 0)):(t(37,Q=R!==void 0||u.datatypes[o]==="nominal"),t(36,le=Z||"sum"))),n.$$.dirty[0]&1|n.$$.dirty[1]&392&&ge!==u&&T&&(t(38,ge=u),T.data("data",ee).runAsync()),n.$$.dirty[0]&16384&&t(42,I=A?window.getComputedStyle(A):null),n.$$.dirty[0]&134217728&&t(47,C=JSON.stringify($)),n.$$.dirty[0]&66871298|n.$$.dirty[1]&129569&&I&&requestAnimationFrame(ye)},[u,Fe,Ee,te,Oe,Se,Pe,Ae,Ne,Te,We,we,ke,K,A,Je,V,X,o,f,d,Y,ie,ne,_e,M,Z,$,se,oe,re,ce,q,ae,ee,R,le,Q,ge,T,de,m,I,c,a,s,r,C,Ke,Qe,Ze,$e]}class Rt extends ut{constructor(e){super(),ht(this,e,zt,Et,bt,{value:0,x:18,y:19,color:20,title:21,x_title:22,y_title:23,color_title:24,x_bin:25,y_aggregate:26,color_map:27,x_lim:16,y_lim:17,x_label_angle:28,y_label_angle:29,x_axis_labels_visible:30,caption:1,sort:31,tooltip:32,show_fullscreen_button:2,_selectable:33,gradio:3,label:4,elem_id:5,elem_classes:6,visible:7,show_label:8,scale:9,min_width:10,loading_status:11,height:12},null,[-1,-1,-1])}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),_()}get x(){return this.$$.ctx[18]}set x(e){this.$$set({x:e}),_()}get y(){return this.$$.ctx[19]}set y(e){this.$$set({y:e}),_()}get color(){return this.$$.ctx[20]}set color(e){this.$$set({color:e}),_()}get title(){return this.$$.ctx[21]}set title(e){this.$$set({title:e}),_()}get x_title(){return this.$$.ctx[22]}set x_title(e){this.$$set({x_title:e}),_()}get y_title(){return this.$$.ctx[23]}set y_title(e){this.$$set({y_title:e}),_()}get color_title(){return this.$$.ctx[24]}set color_title(e){this.$$set({color_title:e}),_()}get x_bin(){return this.$$.ctx[25]}set x_bin(e){this.$$set({x_bin:e}),_()}get y_aggregate(){return this.$$.ctx[26]}set y_aggregate(e){this.$$set({y_aggregate:e}),_()}get color_map(){return this.$$.ctx[27]}set color_map(e){this.$$set({color_map:e}),_()}get x_lim(){return this.$$.ctx[16]}set x_lim(e){this.$$set({x_lim:e}),_()}get y_lim(){return this.$$.ctx[17]}set y_lim(e){this.$$set({y_lim:e}),_()}get x_label_angle(){return this.$$.ctx[28]}set x_label_angle(e){this.$$set({x_label_angle:e}),_()}get y_label_angle(){return this.$$.ctx[29]}set y_label_angle(e){this.$$set({y_label_angle:e}),_()}get x_axis_labels_visible(){return this.$$.ctx[30]}set x_axis_labels_visible(e){this.$$set({x_axis_labels_visible:e}),_()}get caption(){return this.$$.ctx[1]}set caption(e){this.$$set({caption:e}),_()}get sort(){return this.$$.ctx[31]}set sort(e){this.$$set({sort:e}),_()}get tooltip(){return this.$$.ctx[32]}set tooltip(e){this.$$set({tooltip:e}),_()}get show_fullscreen_button(){return this.$$.ctx[2]}set show_fullscreen_button(e){this.$$set({show_fullscreen_button:e}),_()}get _selectable(){return this.$$.ctx[33]}set _selectable(e){this.$$set({_selectable:e}),_()}get gradio(){return this.$$.ctx[3]}set gradio(e){this.$$set({gradio:e}),_()}get label(){return this.$$.ctx[4]}set label(e){this.$$set({label:e}),_()}get elem_id(){return this.$$.ctx[5]}set elem_id(e){this.$$set({elem_id:e}),_()}get elem_classes(){return this.$$.ctx[6]}set elem_classes(e){this.$$set({elem_classes:e}),_()}get visible(){return this.$$.ctx[7]}set visible(e){this.$$set({visible:e}),_()}get show_label(){return this.$$.ctx[8]}set show_label(e){this.$$set({show_label:e}),_()}get scale(){return this.$$.ctx[9]}set scale(e){this.$$set({scale:e}),_()}get min_width(){return this.$$.ctx[10]}set min_width(e){this.$$set({min_width:e}),_()}get loading_status(){return this.$$.ctx[11]}set loading_status(e){this.$$set({loading_status:e}),_()}get height(){return this.$$.ctx[12]}set height(e){this.$$set({height:e}),_()}}export{Rt as default};
//# sourceMappingURL=Index-BmjPKxr3.js.map
