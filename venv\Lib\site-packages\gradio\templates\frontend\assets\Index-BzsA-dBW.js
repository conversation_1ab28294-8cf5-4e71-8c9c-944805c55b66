import{B as se}from"./Block-CJdXVpa7.js";import"./MarkdownCode.svelte_svelte_type_style_lang-Bl8p72Ow.js";/* empty css                                                        */import"./index-DJ2rNx9E.js";import{U as oe}from"./UploadText-Wd7ORU21.js";import re from"./Gallery-GP9wgZoX.js";import{S as _e}from"./index-DYtg3pip.js";import"./StreamingBar.svelte_svelte_type_style_lang-DlRzv56K.js";import{B as ae}from"./FileUpload-DH9xd7bM.js";/* empty css                                              */import"./prism-python-CeMtt1IT.js";import"./svelte/svelte.js";import"./Upload-8igJ-HYX.js";/* empty css                                             */import"./BlockLabel-3KxTaaiM.js";import"./IconButton-C_HS7fTi.js";import"./Empty-ZqppqzTN.js";import"./ShareButton-CuOwy-FH.js";import"./Community-Dw1micSV.js";import"./utils-BsGrhMNe.js";import"./Clear-By3xiIwg.js";import"./Download-DVtk-Jv3.js";import"./Image-Bsh8Umrh.js";import"./Play-B0Q0U1Qz.js";import"./IconButtonWrapper--EIOWuEM.js";import"./FullscreenButton-BG4mOKmH.js";import"./ModifyUpload-Bl9a-zWl.js";import"./Edit-BpRIf5rU.js";import"./Undo-DCjBnnSO.js";import"./DownloadLink-QIttOhoR.js";import"./DownloadLink.svelte_svelte_type_style_lang-C_5UIfol.js";import"./file-url-DoxvUUVV.js";import"./Image-CnqB5dbD.js";/* empty css                                                   */import"./Video-DtShVFLe.js";import"./hls-CnVhpNcu.js";import"./index-tFQomdd2.js";import"./File-BQ_9P3Ye.js";const{SvelteComponent:ue,add_flush_callback:U,assign:fe,bind:S,binding_callbacks:A,check_outros:ce,create_component:j,destroy_component:B,detach:T,empty:he,flush:c,get_spread_object:me,get_spread_update:we,group_outros:ge,init:de,insert:W,mount_component:z,safe_not_equal:be,space:ve,transition_in:d,transition_out:b}=window.__gradio__svelte__internal,{createEventDispatcher:ke}=window.__gradio__svelte__internal;function pe(l){let e,n,i,o;function u(s){l[32](s)}function r(s){l[33](s)}let h={label:l[5],show_label:l[4],columns:l[14],rows:l[15],height:l[16],preview:l[17],object_fit:l[19],interactive:l[21],allow_preview:l[18],show_share_button:l[20],show_download_button:l[22],i18n:l[23].i18n,_fetch:l[31],show_fullscreen_button:l[24],fullscreen:l[3]};return l[1]!==void 0&&(h.selected_index=l[1]),l[0]!==void 0&&(h.value=l[0]),e=new re({props:h}),A.push(()=>S(e,"selected_index",u)),A.push(()=>S(e,"value",r)),e.$on("change",l[34]),e.$on("select",l[35]),e.$on("share",l[36]),e.$on("error",l[37]),e.$on("preview_open",l[38]),e.$on("preview_close",l[39]),e.$on("fullscreen",l[40]),{c(){j(e.$$.fragment)},m(s,f){z(e,s,f),o=!0},p(s,f){const _={};f[0]&32&&(_.label=s[5]),f[0]&16&&(_.show_label=s[4]),f[0]&16384&&(_.columns=s[14]),f[0]&32768&&(_.rows=s[15]),f[0]&65536&&(_.height=s[16]),f[0]&131072&&(_.preview=s[17]),f[0]&524288&&(_.object_fit=s[19]),f[0]&2097152&&(_.interactive=s[21]),f[0]&262144&&(_.allow_preview=s[18]),f[0]&1048576&&(_.show_share_button=s[20]),f[0]&4194304&&(_.show_download_button=s[22]),f[0]&8388608&&(_.i18n=s[23].i18n),f[0]&8388608&&(_._fetch=s[31]),f[0]&16777216&&(_.show_fullscreen_button=s[24]),f[0]&8&&(_.fullscreen=s[3]),!n&&f[0]&2&&(n=!0,_.selected_index=s[1],U(()=>n=!1)),!i&&f[0]&1&&(i=!0,_.value=s[0],U(()=>i=!1)),e.$set(_)},i(s){o||(d(e.$$.fragment,s),o=!0)},o(s){b(e.$$.fragment,s),o=!1},d(s){B(e,s)}}}function je(l){let e,n;return e=new ae({props:{value:null,root:l[6],label:l[5],max_file_size:l[23].max_file_size,file_count:"multiple",file_types:l[10],i18n:l[23].i18n,upload:l[27],stream_handler:l[28],$$slots:{default:[Be]},$$scope:{ctx:l}}}),e.$on("upload",l[29]),e.$on("error",l[30]),{c(){j(e.$$.fragment)},m(i,o){z(e,i,o),n=!0},p(i,o){const u={};o[0]&64&&(u.root=i[6]),o[0]&32&&(u.label=i[5]),o[0]&8388608&&(u.max_file_size=i[23].max_file_size),o[0]&1024&&(u.file_types=i[10]),o[0]&8388608&&(u.i18n=i[23].i18n),o[0]&8388608&&(u.upload=i[27]),o[0]&8388608&&(u.stream_handler=i[28]),o[0]&8388608|o[1]&4096&&(u.$$scope={dirty:o,ctx:i}),e.$set(u)},i(i){n||(d(e.$$.fragment,i),n=!0)},o(i){b(e.$$.fragment,i),n=!1},d(i){B(e,i)}}}function Be(l){let e,n;return e=new oe({props:{i18n:l[23].i18n,type:"gallery"}}),{c(){j(e.$$.fragment)},m(i,o){z(e,i,o),n=!0},p(i,o){const u={};o[0]&8388608&&(u.i18n=i[23].i18n),e.$set(u)},i(i){n||(d(e.$$.fragment,i),n=!0)},o(i){b(e.$$.fragment,i),n=!1},d(i){B(e,i)}}}function ze(l){let e,n,i,o,u,r;const h=[{autoscroll:l[23].autoscroll},{i18n:l[23].i18n},l[2]];let s={};for(let a=0;a<h.length;a+=1)s=fe(s,h[a]);e=new _e({props:s}),e.$on("clear_status",l[26]);const f=[je,pe],_=[];function g(a,m){return a[21]&&a[25]?0:1}return i=g(l),o=_[i]=f[i](l),{c(){j(e.$$.fragment),n=ve(),o.c(),u=he()},m(a,m){z(e,a,m),W(a,n,m),_[i].m(a,m),W(a,u,m),r=!0},p(a,m){const C=m[0]&8388612?we(h,[m[0]&8388608&&{autoscroll:a[23].autoscroll},m[0]&8388608&&{i18n:a[23].i18n},m[0]&4&&me(a[2])]):{};e.$set(C);let v=i;i=g(a),i===v?_[i].p(a,m):(ge(),b(_[v],1,1,()=>{_[v]=null}),ce(),o=_[i],o?o.p(a,m):(o=_[i]=f[i](a),o.c()),d(o,1),o.m(u.parentNode,u))},i(a){r||(d(e.$$.fragment,a),d(o),r=!0)},o(a){b(e.$$.fragment,a),b(o),r=!1},d(a){a&&(T(n),T(u)),B(e,a),_[i].d(a)}}}function Ce(l){let e,n,i;function o(r){l[41](r)}let u={visible:l[9],variant:"solid",padding:!1,elem_id:l[7],elem_classes:l[8],container:l[11],scale:l[12],min_width:l[13],allow_overflow:!1,height:typeof l[16]=="number"?l[16]:void 0,$$slots:{default:[ze]},$$scope:{ctx:l}};return l[3]!==void 0&&(u.fullscreen=l[3]),e=new se({props:u}),A.push(()=>S(e,"fullscreen",o)),{c(){j(e.$$.fragment)},m(r,h){z(e,r,h),i=!0},p(r,h){const s={};h[0]&512&&(s.visible=r[9]),h[0]&128&&(s.elem_id=r[7]),h[0]&256&&(s.elem_classes=r[8]),h[0]&2048&&(s.container=r[11]),h[0]&4096&&(s.scale=r[12]),h[0]&8192&&(s.min_width=r[13]),h[0]&65536&&(s.height=typeof r[16]=="number"?r[16]:void 0),h[0]&67093631|h[1]&4096&&(s.$$scope={dirty:h,ctx:r}),!n&&h[0]&8&&(n=!0,s.fullscreen=r[3],U(()=>n=!1)),e.$set(s)},i(r){i||(d(e.$$.fragment,r),i=!0)},o(r){b(e.$$.fragment,r),i=!1},d(r){B(e,r)}}}async function Ue(l){return(await Promise.all(l.map(async n=>{if(n.path?.toLowerCase().endsWith(".svg")&&n.url){const o=await(await fetch(n.url)).text();return{...n,url:`data:image/svg+xml,${encodeURIComponent(o)}`}}return n}))).map(n=>n.mime_type?.includes("video")?{video:n,caption:null}:{image:n,caption:null})}function Se(l,e,n){let i,{loading_status:o}=e,{show_label:u}=e,{label:r}=e,{root:h}=e,{elem_id:s=""}=e,{elem_classes:f=[]}=e,{visible:_=!0}=e,{value:g=null}=e,{file_types:a=["image","video"]}=e,{container:m=!0}=e,{scale:C=null}=e,{min_width:v=void 0}=e,{columns:G=[2]}=e,{rows:I=void 0}=e,{height:q="auto"}=e,{preview:D}=e,{allow_preview:E=!0}=e,{selected_index:k=null}=e,{object_fit:F="cover"}=e,{show_share_button:L=!1}=e,{interactive:N}=e,{show_download_button:P=!1}=e,{gradio:w}=e,{show_fullscreen_button:R=!0}=e,{fullscreen:p=!1}=e;const H=ke(),J=()=>w.dispatch("clear_status",o),K=(...t)=>w.client.upload(...t),M=(...t)=>w.client.stream(...t),O=async t=>{const ie=Array.isArray(t.detail)?t.detail:[t.detail];n(0,g=await Ue(ie)),w.dispatch("upload",g),w.dispatch("change",g)},Q=({detail:t})=>{n(2,o=o||{}),n(2,o.status="error",o),w.dispatch("error",t)},V=(...t)=>w.client.fetch(...t);function X(t){k=t,n(1,k)}function Y(t){g=t,n(0,g)}const Z=()=>w.dispatch("change",g),y=t=>w.dispatch("select",t.detail),x=t=>w.dispatch("share",t.detail),$=t=>w.dispatch("error",t.detail),ee=()=>w.dispatch("preview_open"),te=()=>w.dispatch("preview_close"),le=({detail:t})=>{n(3,p=t)};function ne(t){p=t,n(3,p)}return l.$$set=t=>{"loading_status"in t&&n(2,o=t.loading_status),"show_label"in t&&n(4,u=t.show_label),"label"in t&&n(5,r=t.label),"root"in t&&n(6,h=t.root),"elem_id"in t&&n(7,s=t.elem_id),"elem_classes"in t&&n(8,f=t.elem_classes),"visible"in t&&n(9,_=t.visible),"value"in t&&n(0,g=t.value),"file_types"in t&&n(10,a=t.file_types),"container"in t&&n(11,m=t.container),"scale"in t&&n(12,C=t.scale),"min_width"in t&&n(13,v=t.min_width),"columns"in t&&n(14,G=t.columns),"rows"in t&&n(15,I=t.rows),"height"in t&&n(16,q=t.height),"preview"in t&&n(17,D=t.preview),"allow_preview"in t&&n(18,E=t.allow_preview),"selected_index"in t&&n(1,k=t.selected_index),"object_fit"in t&&n(19,F=t.object_fit),"show_share_button"in t&&n(20,L=t.show_share_button),"interactive"in t&&n(21,N=t.interactive),"show_download_button"in t&&n(22,P=t.show_download_button),"gradio"in t&&n(23,w=t.gradio),"show_fullscreen_button"in t&&n(24,R=t.show_fullscreen_button),"fullscreen"in t&&n(3,p=t.fullscreen)},l.$$.update=()=>{l.$$.dirty[0]&1&&n(25,i=g===null?!0:g.length===0),l.$$.dirty[0]&2&&H("prop_change",{selected_index:k})},[g,k,o,p,u,r,h,s,f,_,a,m,C,v,G,I,q,D,E,F,L,N,P,w,R,i,J,K,M,O,Q,V,X,Y,Z,y,x,$,ee,te,le,ne]}class ct extends ue{constructor(e){super(),de(this,e,Se,Ce,be,{loading_status:2,show_label:4,label:5,root:6,elem_id:7,elem_classes:8,visible:9,value:0,file_types:10,container:11,scale:12,min_width:13,columns:14,rows:15,height:16,preview:17,allow_preview:18,selected_index:1,object_fit:19,show_share_button:20,interactive:21,show_download_button:22,gradio:23,show_fullscreen_button:24,fullscreen:3},null,[-1,-1])}get loading_status(){return this.$$.ctx[2]}set loading_status(e){this.$$set({loading_status:e}),c()}get show_label(){return this.$$.ctx[4]}set show_label(e){this.$$set({show_label:e}),c()}get label(){return this.$$.ctx[5]}set label(e){this.$$set({label:e}),c()}get root(){return this.$$.ctx[6]}set root(e){this.$$set({root:e}),c()}get elem_id(){return this.$$.ctx[7]}set elem_id(e){this.$$set({elem_id:e}),c()}get elem_classes(){return this.$$.ctx[8]}set elem_classes(e){this.$$set({elem_classes:e}),c()}get visible(){return this.$$.ctx[9]}set visible(e){this.$$set({visible:e}),c()}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),c()}get file_types(){return this.$$.ctx[10]}set file_types(e){this.$$set({file_types:e}),c()}get container(){return this.$$.ctx[11]}set container(e){this.$$set({container:e}),c()}get scale(){return this.$$.ctx[12]}set scale(e){this.$$set({scale:e}),c()}get min_width(){return this.$$.ctx[13]}set min_width(e){this.$$set({min_width:e}),c()}get columns(){return this.$$.ctx[14]}set columns(e){this.$$set({columns:e}),c()}get rows(){return this.$$.ctx[15]}set rows(e){this.$$set({rows:e}),c()}get height(){return this.$$.ctx[16]}set height(e){this.$$set({height:e}),c()}get preview(){return this.$$.ctx[17]}set preview(e){this.$$set({preview:e}),c()}get allow_preview(){return this.$$.ctx[18]}set allow_preview(e){this.$$set({allow_preview:e}),c()}get selected_index(){return this.$$.ctx[1]}set selected_index(e){this.$$set({selected_index:e}),c()}get object_fit(){return this.$$.ctx[19]}set object_fit(e){this.$$set({object_fit:e}),c()}get show_share_button(){return this.$$.ctx[20]}set show_share_button(e){this.$$set({show_share_button:e}),c()}get interactive(){return this.$$.ctx[21]}set interactive(e){this.$$set({interactive:e}),c()}get show_download_button(){return this.$$.ctx[22]}set show_download_button(e){this.$$set({show_download_button:e}),c()}get gradio(){return this.$$.ctx[23]}set gradio(e){this.$$set({gradio:e}),c()}get show_fullscreen_button(){return this.$$.ctx[24]}set show_fullscreen_button(e){this.$$set({show_fullscreen_button:e}),c()}get fullscreen(){return this.$$.ctx[3]}set fullscreen(e){this.$$set({fullscreen:e}),c()}}export{re as BaseGallery,ct as default};
//# sourceMappingURL=Index-BzsA-dBW.js.map
