{"version": 3, "file": "Index-C0bxGwtU.js", "sources": ["../../../../js/html/shared/HTML.svelte", "../../../../js/html/Index.svelte"], "sourcesContent": ["<script lang=\"ts\">\n\timport { createEventDispatcher } from \"svelte\";\n\n\texport let elem_classes: string[] = [];\n\texport let value: string;\n\texport let visible = true;\n\n\tconst dispatch = createEventDispatcher<{\n\t\tchange: undefined;\n\t\tclick: undefined;\n\t}>();\n\n\t$: value, dispatch(\"change\");\n</script>\n\n<!-- svelte-ignore a11y-click-events-have-key-events a11y-no-static-element-interactions -->\n<div\n\tclass=\"prose {elem_classes.join(' ')}\"\n\tclass:hide={!visible}\n\ton:click={() => dispatch(\"click\")}\n>\n\t{@html value}\n</div>\n\n<style>\n\t.hide {\n\t\tdisplay: none;\n\t}\n</style>\n", "<script lang=\"ts\">\n\timport type { Gradio } from \"@gradio/utils\";\n\timport HTML from \"./shared/HTML.svelte\";\n\timport { StatusTracker } from \"@gradio/statustracker\";\n\timport type { LoadingStatus } from \"@gradio/statustracker\";\n\timport { Block, BlockLabel } from \"@gradio/atoms\";\n\timport { Code as CodeIcon } from \"@gradio/icons\";\n\timport { css_units } from \"@gradio/utils\";\n\n\texport let label = \"HTML\";\n\texport let elem_id = \"\";\n\texport let elem_classes: string[] = [];\n\texport let visible = true;\n\texport let value = \"\";\n\texport let loading_status: LoadingStatus;\n\texport let gradio: Gradio<{\n\t\tchange: never;\n\t\tclick: never;\n\t\tclear_status: LoadingStatus;\n\t}>;\n\texport let show_label = false;\n\texport let min_height: number | undefined = undefined;\n\texport let max_height: number | undefined = undefined;\n\texport let container = false;\n\texport let padding = true;\n</script>\n\n<Block {visible} {elem_id} {elem_classes} {container} padding={false}>\n\t{#if show_label}\n\t\t<BlockLabel Icon={CodeIcon} {show_label} {label} float={false} />\n\t{/if}\n\n\t<StatusTracker\n\t\tautoscroll={gradio.autoscroll}\n\t\ti18n={gradio.i18n}\n\t\t{...loading_status}\n\t\tvariant=\"center\"\n\t\ton:clear_status={() => gradio.dispatch(\"clear_status\", loading_status)}\n\t/>\n\t<div\n\t\tclass=\"html-container\"\n\t\tclass:padding\n\t\tclass:pending={loading_status?.status === \"pending\"}\n\t\tstyle:min-height={min_height && loading_status?.status !== \"pending\"\n\t\t\t? css_units(min_height)\n\t\t\t: undefined}\n\t\tstyle:max-height={max_height ? css_units(max_height) : undefined}\n\t>\n\t\t<HTML\n\t\t\t{value}\n\t\t\t{elem_classes}\n\t\t\t{visible}\n\t\t\ton:change={() => gradio.dispatch(\"change\")}\n\t\t\ton:click={() => gradio.dispatch(\"click\")}\n\t\t/>\n\t</div>\n</Block>\n\n<style>\n\t.padding {\n\t\tpadding: var(--block-padding);\n\t}\n\n\tdiv {\n\t\ttransition: 150ms;\n\t}\n\n\t.pending {\n\t\topacity: 0.2;\n\t}\n</style>\n"], "names": ["ctx", "insert", "target", "div", "anchor", "elem_classes", "$$props", "value", "visible", "dispatch", "createEventDispatcher", "click_handler", "CodeIcon", "create_if_block", "set_style", "css_units", "dirty", "label", "elem_id", "loading_status", "gradio", "show_label", "min_height", "max_height", "container", "padding", "clear_status_handler"], "mappings": "guBACuC,EAAA,OAAA,kGAgBxBA,EAAY,CAAA,EAAC,KAAK,GAAG,EAAA,gBAAA,cACtBA,EAAO,CAAA,CAAA,UAFrBC,EAMKC,EAAAC,EAAAC,CAAA,cADGJ,EAAK,CAAA,4DAALA,EAAK,CAAA,wBAJEA,EAAY,CAAA,EAAC,KAAK,GAAG,EAAA,mDACtBA,EAAO,CAAA,CAAA,qDAfT,GAAA,CAAA,aAAAK,EAAA,EAAA,EAAAC,EACA,CAAA,MAAAC,CAAA,EAAAD,GACA,QAAAE,EAAU,EAAA,EAAAF,QAEfG,EAAWC,IAYDC,EAAA,IAAAF,EAAS,OAAO,2JAPtBA,EAAS,QAAQ,quBCiBRG,mCAAsC,+OADpDZ,EAAU,CAAA,GAAAa,EAAAb,CAAA,WAKF,CAAA,WAAAA,KAAO,UAAU,EACvB,CAAA,KAAAA,KAAO,IAAI,EACbA,EAAc,CAAA,4XAOHA,EAAc,CAAA,GAAE,SAAW,SAAS,EACjCc,EAAAX,EAAA,aAAAH,EAAc,CAAA,GAAAA,EAAgB,CAAA,GAAA,SAAW,UACxDe,EAAUf,EAAU,CAAA,CAAA,EACpB,MAAS,mBACMA,EAAU,CAAA,EAAGe,EAAUf,EAAU,CAAA,CAAA,EAAI,MAAS,iDAPjEC,EAgBKC,EAAAC,EAAAC,CAAA,2BA3BAJ,EAAU,CAAA,+HAKFgB,EAAA,IAAA,CAAA,WAAAhB,KAAO,UAAU,EACvBgB,EAAA,IAAA,CAAA,KAAAhB,KAAO,IAAI,WACbA,EAAc,CAAA,CAAA,8KAOHA,EAAc,CAAA,GAAE,SAAW,SAAS,SACjCc,EAAAX,EAAA,aAAAH,EAAc,CAAA,GAAAA,EAAgB,CAAA,GAAA,SAAW,UACxDe,EAAUf,EAAU,CAAA,CAAA,EACpB,MAAS,0BACMA,EAAU,CAAA,EAAGe,EAAUf,EAAU,CAAA,CAAA,EAAI,MAAS,uRAnBH,uWAlBnD,MAAAiB,EAAQ,MAAA,EAAAX,GACR,QAAAY,EAAU,EAAA,EAAAZ,EACV,CAAA,aAAAD,EAAA,EAAA,EAAAC,GACA,QAAAE,EAAU,EAAA,EAAAF,GACV,MAAAC,EAAQ,EAAA,EAAAD,EACR,CAAA,eAAAa,CAAA,EAAAb,EACA,CAAA,OAAAc,CAAA,EAAAd,GAKA,WAAAe,EAAa,EAAA,EAAAf,GACb,WAAAgB,EAAiC,MAAA,EAAAhB,GACjC,WAAAiB,EAAiC,MAAA,EAAAjB,GACjC,UAAAkB,EAAY,EAAA,EAAAlB,GACZ,QAAAmB,EAAU,EAAA,EAAAnB,EAaG,MAAAoB,EAAA,IAAAN,EAAO,SAAS,eAAgBD,CAAc,QAenDC,EAAO,SAAS,QAAQ,QACzBA,EAAO,SAAS,OAAO"}