import{s as Me}from"./select-BigU4G0v.js";import{d as ln}from"./dispatch-kxCwF96_.js";import{r as on}from"./file-url-DoxvUUVV.js";import"./DownloadLink.svelte_svelte_type_style_lang-C_5UIfol.js";/* empty css                                                        */import"./MarkdownCode.svelte_svelte_type_style_lang-Bl8p72Ow.js";import{B as Rt}from"./BlockLabel-3KxTaaiM.js";import{I as Re}from"./IconButton-C_HS7fTi.js";import{E as ft}from"./Empty-ZqppqzTN.js";import{C as It}from"./Clear-By3xiIwg.js";import{D as Lt}from"./Download-DVtk-Jv3.js";import{I as Ie}from"./Image-Bsh8Umrh.js";import{U as rn}from"./Undo-DCjBnnSO.js";import{w as an,z as _t,f as un}from"./index-DJ2rNx9E.js";import{I as fn}from"./IconButtonWrapper--EIOWuEM.js";import{F as _n}from"./FullscreenButton-BG4mOKmH.js";import{D as Bt}from"./DownloadLink-QIttOhoR.js";import{n as cn,l as hn,i as ct,S as qt}from"./index-DYtg3pip.js";import{a as Pt}from"./Upload-8igJ-HYX.js";import{B as Ut}from"./Block-CJdXVpa7.js";import{U as Nt}from"./UploadText-Wd7ORU21.js";import"./StreamingBar.svelte_svelte_type_style_lang-DlRzv56K.js";import"./prism-python-CeMtt1IT.js";import"./svelte/svelte.js";/* empty css                                             */function ot(i,e){if(i===e||i!==i)return()=>i;const t=typeof i;if(t!==typeof e||Array.isArray(i)!==Array.isArray(e))throw new Error("Cannot interpolate values of different type");if(Array.isArray(i)){const n=e.map((s,l)=>ot(i[l],s));return s=>n.map(l=>l(s))}if(t==="object"){if(!i||!e)throw new Error("Object cannot be null");if(ct(i)&&ct(e)){i=i.getTime(),e=e.getTime();const l=e-i;return r=>new Date(i+r*l)}const n=Object.keys(e),s={};return n.forEach(l=>{s[l]=ot(i[l],e[l])}),l=>{const r={};return n.forEach(u=>{r[u]=s[u](l)}),r}}if(t==="number"){const n=e-i;return s=>i+s*n}throw new Error(`Cannot interpolate ${t} values`)}function dn(i,e={}){const t=an(i);let n,s=i;function l(r,u){if(i==null)return t.set(i=r),Promise.resolve();s=r;let o=n,f=!1,{delay:a=0,duration:_=400,easing:h=un,interpolate:p=ot}=_t(_t({},e),u);if(_===0)return o&&(o.abort(),o=null),t.set(i=s),Promise.resolve();const g=cn()+a;let d;return n=hn(w=>{if(w<g)return!0;f||(d=p(i,r),typeof _=="function"&&(_=_(i,r)),f=!0),o&&(o.abort(),o=null);const $=w-g;return $>_?(t.set(i=r),!1):(t.set(i=d(h($/_))),!0)}),n.promise}return{set:l,update:(r,u)=>l(r(s,i),u),subscribe:t.subscribe}}function gn(i){let e;for(;e=i.sourceEvent;)i=e;return i}function ht(i,e){if(i=gn(i),e===void 0&&(e=i.currentTarget),e){var t=e.ownerSVGElement||e;if(t.createSVGPoint){var n=t.createSVGPoint();return n.x=i.clientX,n.y=i.clientY,n=n.matrixTransform(e.getScreenCTM().inverse()),[n.x,n.y]}if(e.getBoundingClientRect){var s=e.getBoundingClientRect();return[i.clientX-s.left-e.clientLeft,i.clientY-s.top-e.clientTop]}}return[i.pageX,i.pageY]}const mn={passive:!1},Te={capture:!0,passive:!1};function it(i){i.stopImmediatePropagation()}function we(i){i.preventDefault(),i.stopImmediatePropagation()}function bn(i){var e=i.document.documentElement,t=Me(i).on("dragstart.drag",we,Te);"onselectstart"in e?t.on("selectstart.drag",we,Te):(e.__noselect=e.style.MozUserSelect,e.style.MozUserSelect="none")}function pn(i,e){var t=i.document.documentElement,n=Me(i).on("dragstart.drag",null);e&&(n.on("click.drag",we,Te),setTimeout(function(){n.on("click.drag",null)},0)),"onselectstart"in t?n.on("selectstart.drag",null):(t.style.MozUserSelect=t.__noselect,delete t.__noselect)}const Be=i=>()=>i;function rt(i,{sourceEvent:e,subject:t,target:n,identifier:s,active:l,x:r,y:u,dx:o,dy:f,dispatch:a}){Object.defineProperties(this,{type:{value:i,enumerable:!0,configurable:!0},sourceEvent:{value:e,enumerable:!0,configurable:!0},subject:{value:t,enumerable:!0,configurable:!0},target:{value:n,enumerable:!0,configurable:!0},identifier:{value:s,enumerable:!0,configurable:!0},active:{value:l,enumerable:!0,configurable:!0},x:{value:r,enumerable:!0,configurable:!0},y:{value:u,enumerable:!0,configurable:!0},dx:{value:o,enumerable:!0,configurable:!0},dy:{value:f,enumerable:!0,configurable:!0},_:{value:a}})}rt.prototype.on=function(){var i=this._.on.apply(this._,arguments);return i===this._?this:i};function wn(i){return!i.ctrlKey&&!i.button}function vn(){return this.parentNode}function kn(i,e){return e??{x:i.x,y:i.y}}function zn(){return navigator.maxTouchPoints||"ontouchstart"in this}function $n(){var i=wn,e=vn,t=kn,n=zn,s={},l=ln("start","drag","end"),r=0,u,o,f,a,_=0;function h(m){m.on("mousedown.drag",p).filter(n).on("touchstart.drag",w).on("touchmove.drag",$,mn).on("touchend.drag touchcancel.drag",z).style("touch-action","none").style("-webkit-tap-highlight-color","rgba(0,0,0,0)")}function p(m,X){if(!(a||!i.call(this,m,X))){var Y=D(this,e.call(this,m,X),m,X,"mouse");Y&&(Me(m.view).on("mousemove.drag",g,Te).on("mouseup.drag",d,Te),bn(m.view),it(m),f=!1,u=m.clientX,o=m.clientY,Y("start",m))}}function g(m){if(we(m),!f){var X=m.clientX-u,Y=m.clientY-o;f=X*X+Y*Y>_}s.mouse("drag",m)}function d(m){Me(m.view).on("mousemove.drag mouseup.drag",null),pn(m.view,f),we(m),s.mouse("end",m)}function w(m,X){if(i.call(this,m,X)){var Y=m.changedTouches,E=e.call(this,m,X),M=Y.length,c,P;for(c=0;c<M;++c)(P=D(this,E,m,X,Y[c].identifier,Y[c]))&&(it(m),P("start",m,Y[c]))}}function $(m){var X=m.changedTouches,Y=X.length,E,M;for(E=0;E<Y;++E)(M=s[X[E].identifier])&&(we(m),M("drag",m,X[E]))}function z(m){var X=m.changedTouches,Y=X.length,E,M;for(a&&clearTimeout(a),a=setTimeout(function(){a=null},500),E=0;E<Y;++E)(M=s[X[E].identifier])&&(it(m),M("end",m,X[E]))}function D(m,X,Y,E,M,c){var P=l.copy(),I=ht(c||Y,X),j,L,q;if((q=t.call(m,new rt("beforestart",{sourceEvent:Y,target:h,identifier:M,active:r,x:I[0],y:I[1],dx:0,dy:0,dispatch:P}),E))!=null)return j=q.x-I[0]||0,L=q.y-I[1]||0,function U(se,re,k){var x=I,H;switch(se){case"start":s[M]=U,H=r++;break;case"end":delete s[M],--r;case"drag":I=ht(k||re,X),H=r;break}P.call(se,m,new rt(se,{sourceEvent:re,subject:q,target:h,identifier:M,active:H,x:I[0]+j,y:I[1]+L,dx:I[0]-x[0],dy:I[1]-x[1],dispatch:P}),E)}}return h.filter=function(m){return arguments.length?(i=typeof m=="function"?m:Be(!!m),h):i},h.container=function(m){return arguments.length?(e=typeof m=="function"?m:Be(m),h):e},h.subject=function(m){return arguments.length?(t=typeof m=="function"?m:Be(m),h):t},h.touchable=function(m){return arguments.length?(n=typeof m=="function"?m:Be(!!m),h):n},h.on=function(){var m=l.on.apply(l,arguments);return m===l?h:m},h.clickDistance=function(m){return arguments.length?(_=(m=+m)*m,h):Math.sqrt(_)},h}const{SvelteComponent:Xn,append:oe,attr:ee,binding_callbacks:st,create_slot:Yn,detach:En,element:ue,flush:ge,get_all_dirty_from_scope:Mn,get_slot_changes:Tn,init:Dn,insert:Sn,listen:Cn,safe_not_equal:Rn,set_style:me,space:dt,toggle_class:fe,transition_in:In,transition_out:Ln,update_slot_base:Bn}=window.__gradio__svelte__internal,{onMount:qn}=window.__gradio__svelte__internal;function Pn(i){let e,t,n,s,l,r,u,o,f,a,_,h,p;const g=i[11].default,d=Yn(g,i,i[10],null);return{c(){e=ue("div"),t=ue("div"),d&&d.c(),n=dt(),s=ue("div"),l=ue("span"),r=ue("span"),r.textContent="◢",u=ue("span"),o=ue("span"),o.textContent="◢",f=dt(),a=ue("div"),ee(t,"class","content svelte-fpmna9"),ee(r,"class","icon left svelte-fpmna9"),ee(u,"class","icon center svelte-fpmna9"),me(u,"--color",i[4]),ee(o,"class","icon right svelte-fpmna9"),ee(l,"class","icon-wrap svelte-fpmna9"),fe(l,"active",i[7]),fe(l,"disabled",i[3]),ee(a,"class","inner svelte-fpmna9"),me(a,"--color",i[4]),ee(s,"class","outer svelte-fpmna9"),ee(s,"role","none"),me(s,"transform","translateX("+i[6]+"px)"),fe(s,"disabled",i[3]),fe(s,"grab",i[7]),ee(e,"class","wrap svelte-fpmna9"),ee(e,"role","none")},m(w,$){Sn(w,e,$),oe(e,t),d&&d.m(t,null),i[13](t),oe(e,n),oe(e,s),oe(s,l),oe(l,r),oe(l,u),oe(l,o),oe(s,f),oe(s,a),i[14](s),i[15](e),_=!0,h||(p=Cn(window,"resize",i[12]),h=!0)},p(w,[$]){d&&d.p&&(!_||$&1024)&&Bn(d,g,w,w[10],_?Tn(g,w[10],$,null):Mn(w[10]),null),$&16&&me(u,"--color",w[4]),(!_||$&128)&&fe(l,"active",w[7]),(!_||$&8)&&fe(l,"disabled",w[3]),$&16&&me(a,"--color",w[4]),(!_||$&64)&&me(s,"transform","translateX("+w[6]+"px)"),(!_||$&8)&&fe(s,"disabled",w[3]),(!_||$&128)&&fe(s,"grab",w[7])},i(w){_||(In(d,w),_=!0)},o(w){Ln(d,w),_=!1},d(w){w&&En(e),d&&d.d(w),i[13](null),i[14](null),i[15](null),h=!1,p()}}}function lt(i,e,t){return Math.min(Math.max(i,e),t)}function Un(i,e){const t=Math.pow(10,e);return Math.round((i+Number.EPSILON)*t)/t}function Nn(i,e,t){let{$$slots:n={},$$scope:s}=e,{position:l=.5}=e,{disabled:r=!1}=e,{slider_color:u="var(--border-color-primary)"}=e,{image_size:o={top:0,left:0,width:0,height:0}}=e,{el:f=void 0}=e,{parent_el:a=void 0}=e,_,h=0,p=!1,g=0;function d(c){g=a?.getBoundingClientRect().width||0,c===0&&t(0,o.width=f?.getBoundingClientRect().width||0,o),t(6,h=lt(o.width*l+o.left,0,g))}function w(c){t(6,h=lt(c,0,g)),t(9,l=Un((c-o.left)/o.width,5))}function $(c){r||(t(7,p=!0),w(c.x))}function z(c){r||w(c.x)}function D(){r||t(7,p=!1)}function m(c){t(6,h=lt(o.width*c+o.left,0,g))}qn(()=>{d(o.width);const c=$n().on("start",$).on("drag",z).on("end",D);Me(_).call(c)});const X=()=>d(o.width);function Y(c){st[c?"unshift":"push"](()=>{f=c,t(1,f)})}function E(c){st[c?"unshift":"push"](()=>{_=c,t(5,_)})}function M(c){st[c?"unshift":"push"](()=>{a=c,t(2,a)})}return i.$$set=c=>{"position"in c&&t(9,l=c.position),"disabled"in c&&t(3,r=c.disabled),"slider_color"in c&&t(4,u=c.slider_color),"image_size"in c&&t(0,o=c.image_size),"el"in c&&t(1,f=c.el),"parent_el"in c&&t(2,a=c.parent_el),"$$scope"in c&&t(10,s=c.$$scope)},i.$$.update=()=>{i.$$.dirty&1&&d(o.width),i.$$.dirty&512&&m(l)},[o,f,a,r,u,_,h,p,d,l,s,n,X,Y,E,M]}class Wt extends Xn{constructor(e){super(),Dn(this,e,Nn,Pn,Rn,{position:9,disabled:3,slider_color:4,image_size:0,el:1,parent_el:2})}get position(){return this.$$.ctx[9]}set position(e){this.$$set({position:e}),ge()}get disabled(){return this.$$.ctx[3]}set disabled(e){this.$$set({disabled:e}),ge()}get slider_color(){return this.$$.ctx[4]}set slider_color(e){this.$$set({slider_color:e}),ge()}get image_size(){return this.$$.ctx[0]}set image_size(e){this.$$set({image_size:e}),ge()}get el(){return this.$$.ctx[1]}set el(e){this.$$set({el:e}),ge()}get parent_el(){return this.$$.ctx[2]}set parent_el(e){this.$$set({parent_el:e}),ge()}}const{SvelteComponent:Wn,assign:at,binding_callbacks:On,compute_rest_props:gt,detach:jn,element:An,exclude_internal_props:Fn,flush:_e,get_spread_update:Hn,init:Jn,insert:Gn,listen:Vn,noop:mt,safe_not_equal:yn,set_attributes:bt,set_style:qe,src_url_equal:Kn,toggle_class:N}=window.__gradio__svelte__internal,{createEventDispatcher:Zn,onMount:Qn,tick:xn}=window.__gradio__svelte__internal;function ei(i){let e,t,n,s,l=[{src:t=i[7]},i[9]],r={};for(let u=0;u<l.length;u+=1)r=at(r,l[u]);return{c(){e=An("img"),bt(e,r),N(e,"fixed",i[2]),N(e,"hidden",i[4]),N(e,"preview",i[5]==="preview"),N(e,"slider",i[5]==="upload"),N(e,"fullscreen",i[1]),N(e,"small",!i[1]),qe(e,"transform",i[3]),qe(e,"max-height",i[6]&&!i[1]?`${i[6]}px`:null),N(e,"svelte-k63p1v",!0)},m(u,o){Gn(u,e,o),i[12](e),n||(s=Vn(e,"load",i[13]),n=!0)},p(u,[o]){bt(e,r=Hn(l,[o&128&&!Kn(e.src,t=u[7])&&{src:t},o&512&&u[9]])),N(e,"fixed",u[2]),N(e,"hidden",u[4]),N(e,"preview",u[5]==="preview"),N(e,"slider",u[5]==="upload"),N(e,"fullscreen",u[1]),N(e,"small",!u[1]),qe(e,"transform",u[3]),qe(e,"max-height",u[6]&&!u[1]?`${u[6]}px`:null),N(e,"svelte-k63p1v",!0)},i:mt,o:mt,d(u){u&&jn(e),i[12](null),n=!1,s()}}}function pt(i){if(!i)return{top:0,left:0,width:0,height:0};const e=i.parentElement?.getBoundingClientRect();if(!e)return{top:0,left:0,width:0,height:0};const t=i.naturalWidth/i.naturalHeight,n=e.width/e.height;let s,l;t>n?(s=e.width,l=e.width/t):(l=e.height,s=e.height*t);const r=(e.width-s)/2;return{top:(e.height-l)/2,left:r,width:s,height:l}}function ti(i,e,t){const n=["src","fullscreen","fixed","transform","img_el","hidden","variant","max_height"];let s=gt(e,n),{src:l=void 0}=e,{fullscreen:r=!1}=e,u,{fixed:o=!1}=e,{transform:f="translate(0px, 0px) scale(1)"}=e,{img_el:a=null}=e,{hidden:_=!1}=e,{variant:h="upload"}=e,{max_height:p=500}=e,g;const d=Zn();Qn(()=>{const z=new ResizeObserver(async D=>{for(const m of D)await xn(),d("load",pt(a))});return z.observe(a),()=>{z.disconnect()}});function w(z){On[z?"unshift":"push"](()=>{a=z,t(0,a)})}const $=()=>d("load",pt(a));return i.$$set=z=>{e=at(at({},e),Fn(z)),t(9,s=gt(e,n)),"src"in z&&t(10,l=z.src),"fullscreen"in z&&t(1,r=z.fullscreen),"fixed"in z&&t(2,o=z.fixed),"transform"in z&&t(3,f=z.transform),"img_el"in z&&t(0,a=z.img_el),"hidden"in z&&t(4,_=z.hidden),"variant"in z&&t(5,h=z.variant),"max_height"in z&&t(6,p=z.max_height)},i.$$.update=()=>{if(i.$$.dirty&3072){t(7,u=l),t(11,g=l);const z=l;on(z).then(D=>{g===z&&t(7,u=D)})}},[a,r,o,f,_,h,p,u,d,s,l,g,w,$]}class je extends Wn{constructor(e){super(),Jn(this,e,ti,ei,yn,{src:10,fullscreen:1,fixed:2,transform:3,img_el:0,hidden:4,variant:5,max_height:6})}get src(){return this.$$.ctx[10]}set src(e){this.$$set({src:e}),_e()}get fullscreen(){return this.$$.ctx[1]}set fullscreen(e){this.$$set({fullscreen:e}),_e()}get fixed(){return this.$$.ctx[2]}set fixed(e){this.$$set({fixed:e}),_e()}get transform(){return this.$$.ctx[3]}set transform(e){this.$$set({transform:e}),_e()}get img_el(){return this.$$.ctx[0]}set img_el(e){this.$$set({img_el:e}),_e()}get hidden(){return this.$$.ctx[4]}set hidden(e){this.$$set({hidden:e}),_e()}get variant(){return this.$$.ctx[5]}set variant(e){this.$$set({variant:e}),_e()}get max_height(){return this.$$.ctx[6]}set max_height(e){this.$$set({max_height:e}),_e()}}class ni{container;image;scale;offsetX;offsetY;isDragging;lastX;lastY;initial_left_padding;initial_top_padding;initial_width;initial_height;subscribers;handleImageLoad;real_image_size={top:0,left:0,width:0,height:0};last_touch_distance;constructor(e,t){this.container=e,this.image=t,this.scale=1,this.offsetX=0,this.offsetY=0,this.isDragging=!1,this.lastX=0,this.lastY=0,this.initial_left_padding=0,this.initial_top_padding=0,this.initial_width=0,this.initial_height=0,this.subscribers=[],this.last_touch_distance=0,this.handleWheel=this.handleWheel.bind(this),this.handleMouseDown=this.handleMouseDown.bind(this),this.handleMouseMove=this.handleMouseMove.bind(this),this.handleMouseUp=this.handleMouseUp.bind(this),this.handleImageLoad=this.init.bind(this),this.handleTouchStart=this.handleTouchStart.bind(this),this.handleTouchMove=this.handleTouchMove.bind(this),this.handleTouchEnd=this.handleTouchEnd.bind(this),this.image.addEventListener("load",this.handleImageLoad),this.container.addEventListener("wheel",this.handleWheel),this.container.addEventListener("mousedown",this.handleMouseDown),document.addEventListener("mousemove",this.handleMouseMove),document.addEventListener("mouseup",this.handleMouseUp),this.container.addEventListener("touchstart",this.handleTouchStart),document.addEventListener("touchmove",this.handleTouchMove),document.addEventListener("touchend",this.handleTouchEnd),new ResizeObserver(s=>{for(const l of s)l.target===this.container&&(this.handleResize(),this.get_image_size(this.image))}).observe(this.container)}handleResize(){this.init()}init(){const e=this.container.getBoundingClientRect(),t=this.image.getBoundingClientRect();this.initial_left_padding=t.left-e.left,this.initial_top_padding=t.top-e.top,this.initial_width=t.width,this.initial_height=t.height,this.reset_zoom(),this.updateTransform()}reset_zoom(){this.scale=1,this.offsetX=0,this.offsetY=0,this.updateTransform()}handleMouseDown(e){const t=this.image.getBoundingClientRect();if(e.clientX>=t.left&&e.clientX<=t.right&&e.clientY>=t.top&&e.clientY<=t.bottom){if(e.preventDefault(),this.scale===1)return;this.isDragging=!0,this.lastX=e.clientX,this.lastY=e.clientY,this.image.style.cursor="grabbing"}}handleMouseMove(e){if(!this.isDragging)return;const t=e.clientX-this.lastX,n=e.clientY-this.lastY;this.offsetX+=t,this.offsetY+=n,this.lastX=e.clientX,this.lastY=e.clientY,this.updateTransform(),this.updateTransform()}handleMouseUp(){this.isDragging&&(this.constrain_to_bounds(!0),this.updateTransform(),this.isDragging=!1,this.image.style.cursor=this.scale>1?"grab":"zoom-in")}async handleWheel(e){e.preventDefault();const t=this.container.getBoundingClientRect(),n=this.image.getBoundingClientRect();if(e.clientX<n.left||e.clientX>n.right||e.clientY<n.top||e.clientY>n.bottom)return;const s=1.05,l=this.scale,r=-Math.sign(e.deltaY)>0?Math.min(15,l*s):Math.max(1,l/s);if(r===l)return;const u=e.clientX-t.left-this.initial_left_padding,o=e.clientY-t.top-this.initial_top_padding;this.scale=r,this.offsetX=this.compute_new_offset({cursor_position:u,current_offset:this.offsetX,new_scale:r,old_scale:l}),this.offsetY=this.compute_new_offset({cursor_position:o,current_offset:this.offsetY,new_scale:r,old_scale:l}),this.updateTransform(),this.constrain_to_bounds(),this.updateTransform(),this.image.style.cursor=this.scale>1?"grab":"zoom-in"}compute_new_position({position:e,scale:t,anchor_position:n}){return e-(e-n)*(t/this.scale)}compute_new_offset({cursor_position:e,current_offset:t,new_scale:n,old_scale:s}){return e-n/s*(e-t)}constrain_to_bounds(e=!1){if(this.scale===1){this.offsetX=0,this.offsetY=0;return}const t={top:this.real_image_size.top*this.scale+this.offsetY,left:this.real_image_size.left*this.scale+this.offsetX,width:this.real_image_size.width*this.scale,height:this.real_image_size.height*this.scale,bottom:this.real_image_size.top*this.scale+this.offsetY+this.real_image_size.height*this.scale,right:this.real_image_size.left*this.scale+this.offsetX+this.real_image_size.width*this.scale},n=this.real_image_size.left+this.real_image_size.width,s=this.real_image_size.top+this.real_image_size.height;e&&(t.top>this.real_image_size.top?this.offsetY=this.calculate_position(this.real_image_size.top,0,"y"):t.bottom<s&&(this.offsetY=this.calculate_position(s,1,"y")),t.left>this.real_image_size.left?this.offsetX=this.calculate_position(this.real_image_size.left,0,"x"):t.right<n&&(this.offsetX=this.calculate_position(n,1,"x")))}updateTransform(){this.notify({x:this.offsetX,y:this.offsetY,scale:this.scale})}destroy(){this.container.removeEventListener("wheel",this.handleWheel),this.container.removeEventListener("mousedown",this.handleMouseDown),document.removeEventListener("mousemove",this.handleMouseMove),document.removeEventListener("mouseup",this.handleMouseUp),this.container.removeEventListener("touchstart",this.handleTouchStart),document.removeEventListener("touchmove",this.handleTouchMove),document.removeEventListener("touchend",this.handleTouchEnd),this.image.removeEventListener("load",this.handleImageLoad)}subscribe(e){this.subscribers.push(e)}unsubscribe(e){this.subscribers=this.subscribers.filter(t=>t!==e)}notify({x:e,y:t,scale:n}){this.subscribers.forEach(s=>s({x:e,y:t,scale:n}))}handleTouchStart(e){e.preventDefault();const t=this.image.getBoundingClientRect(),n=e.touches[0];if(n.clientX>=t.left&&n.clientX<=t.right&&n.clientY>=t.top&&n.clientY<=t.bottom){if(e.touches.length===1&&this.scale>1)this.isDragging=!0,this.lastX=n.clientX,this.lastY=n.clientY;else if(e.touches.length===2){const s=e.touches[0],l=e.touches[1];this.last_touch_distance=Math.hypot(l.clientX-s.clientX,l.clientY-s.clientY)}}}get_image_size(e){if(!e)return;const t=e.parentElement?.getBoundingClientRect();if(!t)return;const n=e.naturalWidth/e.naturalHeight,s=t.width/t.height;let l,r;n>s?(l=t.width,r=t.width/n):(r=t.height,l=t.height*n);const u=(t.width-l)/2,o=(t.height-r)/2;this.real_image_size={top:o,left:u,width:l,height:r}}handleTouchMove(e){if(e.touches.length===1&&this.isDragging){e.preventDefault();const t=e.touches[0],n=t.clientX-this.lastX,s=t.clientY-this.lastY;this.offsetX+=n,this.offsetY+=s,this.lastX=t.clientX,this.lastY=t.clientY,this.updateTransform()}else if(e.touches.length===2){e.preventDefault();const t=e.touches[0],n=e.touches[1],s=Math.hypot(n.clientX-t.clientX,n.clientY-t.clientY);if(this.last_touch_distance===0){this.last_touch_distance=s;return}const l=s/this.last_touch_distance,r=this.scale,u=Math.min(15,Math.max(1,r*l));if(u===r){this.last_touch_distance=s;return}const o=this.container.getBoundingClientRect(),f=(t.clientX+n.clientX)/2-o.left-this.initial_left_padding,a=(t.clientY+n.clientY)/2-o.top-this.initial_top_padding;this.scale=u,this.offsetX=this.compute_new_offset({cursor_position:f,current_offset:this.offsetX,new_scale:u,old_scale:r}),this.offsetY=this.compute_new_offset({cursor_position:a,current_offset:this.offsetY,new_scale:u,old_scale:r}),this.updateTransform(),this.constrain_to_bounds(),this.updateTransform(),this.last_touch_distance=s,this.image.style.cursor=this.scale>1?"grab":"zoom-in"}}handleTouchEnd(e){this.isDragging&&(this.constrain_to_bounds(!0),this.updateTransform(),this.isDragging=!1),e.touches.length===0&&(this.last_touch_distance=0)}calculate_position(e,t,n){if(this.container.getBoundingClientRect(),n==="x"){const s=e,l=this.real_image_size.left+t*this.real_image_size.width;return s-l*this.scale}if(n==="y"){const s=e,l=this.real_image_size.top+t*this.real_image_size.height;return s-l*this.scale}return 0}}const{SvelteComponent:ii,add_flush_callback:Pe,add_iframe_resize_listener:si,add_render_callback:li,append:wt,attr:vt,bind:Ue,binding_callbacks:ve,bubble:oi,check_outros:Ne,component_subscribe:ri,create_component:J,destroy_component:G,detach:ce,element:kt,empty:Ot,flush:W,group_outros:We,init:ai,insert:he,mount_component:V,noop:ui,safe_not_equal:fi,space:ke,toggle_class:zt,transition_in:C,transition_out:B}=window.__gradio__svelte__internal,{onMount:_i}=window.__gradio__svelte__internal,{createEventDispatcher:ci}=window.__gradio__svelte__internal;function hi(i){let e,t,n,s,l,r,u,o,f,a;t=new fn({props:{$$slots:{default:[mi]},$$scope:{ctx:i}}});function _(d){i[32](d)}function h(d){i[33](d)}function p(d){i[34](d)}let g={slider_color:i[9],image_size:i[16],$$slots:{default:[bi]},$$scope:{ctx:i}};return i[0]!==void 0&&(g.position=i[0]),i[15]!==void 0&&(g.el=i[15]),i[19]!==void 0&&(g.parent_el=i[19]),l=new Wt({props:g}),ve.push(()=>Ue(l,"position",_)),ve.push(()=>Ue(l,"el",h)),ve.push(()=>Ue(l,"parent_el",p)),{c(){e=kt("div"),J(t.$$.fragment),n=ke(),s=kt("div"),J(l.$$.fragment),vt(s,"class","slider-wrap svelte-eb87wk"),li(()=>i[36].call(s)),zt(s,"limit_height",!i[11]),vt(e,"class","image-container svelte-eb87wk")},m(d,w){he(d,e,w),V(t,e,null),wt(e,n),wt(e,s),V(l,s,null),i[35](s),f=si(s,i[36].bind(s)),i[37](e),a=!0},p(d,w){const $={};w[0]&1190994|w[1]&1024&&($.$$scope={dirty:w,ctx:d}),t.$set($);const z={};w[0]&512&&(z.slider_color=d[9]),w[0]&65536&&(z.image_size=d[16]),w[0]&4348034|w[1]&1024&&(z.$$scope={dirty:w,ctx:d}),!r&&w[0]&1&&(r=!0,z.position=d[0],Pe(()=>r=!1)),!u&&w[0]&32768&&(u=!0,z.el=d[15],Pe(()=>u=!1)),!o&&w[0]&524288&&(o=!0,z.parent_el=d[19],Pe(()=>o=!1)),l.$set(z),(!a||w[0]&2048)&&zt(s,"limit_height",!d[11])},i(d){a||(C(t.$$.fragment,d),C(l.$$.fragment,d),a=!0)},o(d){B(t.$$.fragment,d),B(l.$$.fragment,d),a=!1},d(d){d&&ce(e),G(t),G(l),i[35](null),f(),i[37](null)}}}function di(i){let e,t;return e=new ft({props:{unpadded_box:!0,size:"large",$$slots:{default:[pi]},$$scope:{ctx:i}}}),{c(){J(e.$$.fragment)},m(n,s){V(e,n,s),t=!0},p(n,s){const l={};s[1]&1024&&(l.$$scope={dirty:s,ctx:n}),e.$set(l)},i(n){t||(C(e.$$.fragment,n),t=!0)},o(n){B(e.$$.fragment,n),t=!1},d(n){G(e,n)}}}function $t(i){let e,t;return e=new _n({props:{fullscreen:i[11]}}),e.$on("fullscreen",i[29]),{c(){J(e.$$.fragment)},m(n,s){V(e,n,s),t=!0},p(n,s){const l={};s[0]&2048&&(l.fullscreen=n[11]),e.$set(l)},i(n){t||(C(e.$$.fragment,n),t=!0)},o(n){B(e.$$.fragment,n),t=!1},d(n){G(e,n)}}}function Xt(i){let e,t;return e=new Bt({props:{href:i[1][1]?.url,download:i[1][1]?.orig_name||"image",$$slots:{default:[gi]},$$scope:{ctx:i}}}),{c(){J(e.$$.fragment)},m(n,s){V(e,n,s),t=!0},p(n,s){const l={};s[0]&2&&(l.href=n[1][1]?.url),s[0]&2&&(l.download=n[1][1]?.orig_name||"image"),s[0]&64|s[1]&1024&&(l.$$scope={dirty:s,ctx:n}),e.$set(l)},i(n){t||(C(e.$$.fragment,n),t=!0)},o(n){B(e.$$.fragment,n),t=!1},d(n){G(e,n)}}}function gi(i){let e,t;return e=new Re({props:{Icon:Lt,label:i[6]("common.download")}}),{c(){J(e.$$.fragment)},m(n,s){V(e,n,s),t=!0},p(n,s){const l={};s[0]&64&&(l.label=n[6]("common.download")),e.$set(l)},i(n){t||(C(e.$$.fragment,n),t=!0)},o(n){B(e.$$.fragment,n),t=!1},d(n){G(e,n)}}}function Yt(i){let e,t;return e=new Re({props:{Icon:It,label:"Remove Image"}}),e.$on("click",i[30]),{c(){J(e.$$.fragment)},m(n,s){V(e,n,s),t=!0},p:ui,i(n){t||(C(e.$$.fragment,n),t=!0)},o(n){B(e.$$.fragment,n),t=!1},d(n){G(e,n)}}}function mi(i){let e,t,n,s,l,r;e=new Re({props:{Icon:rn,label:i[6]("common.undo"),disabled:i[17].z===1}}),e.$on("click",i[28]);let u=i[10]&&$t(i),o=i[4]&&Xt(i),f=i[13]&&Yt(i);return{c(){J(e.$$.fragment),t=ke(),u&&u.c(),n=ke(),o&&o.c(),s=ke(),f&&f.c(),l=Ot()},m(a,_){V(e,a,_),he(a,t,_),u&&u.m(a,_),he(a,n,_),o&&o.m(a,_),he(a,s,_),f&&f.m(a,_),he(a,l,_),r=!0},p(a,_){const h={};_[0]&64&&(h.label=a[6]("common.undo")),_[0]&131072&&(h.disabled=a[17].z===1),e.$set(h),a[10]?u?(u.p(a,_),_[0]&1024&&C(u,1)):(u=$t(a),u.c(),C(u,1),u.m(n.parentNode,n)):u&&(We(),B(u,1,1,()=>{u=null}),Ne()),a[4]?o?(o.p(a,_),_[0]&16&&C(o,1)):(o=Xt(a),o.c(),C(o,1),o.m(s.parentNode,s)):o&&(We(),B(o,1,1,()=>{o=null}),Ne()),a[13]?f?(f.p(a,_),_[0]&8192&&C(f,1)):(f=Yt(a),f.c(),C(f,1),f.m(l.parentNode,l)):f&&(We(),B(f,1,1,()=>{f=null}),Ne())},i(a){r||(C(e.$$.fragment,a),C(u),C(o),C(f),r=!0)},o(a){B(e.$$.fragment,a),B(u),B(o),B(f),r=!1},d(a){a&&(ce(t),ce(n),ce(s),ce(l)),G(e,a),u&&u.d(a),o&&o.d(a),f&&f.d(a)}}}function bi(i){let e,t,n,s,l;function r(o){i[31](o)}let u={src:i[1]?.[0]?.url,alt:"",loading:"lazy",variant:"preview",transform:"translate("+i[17].x+"px, "+i[17].y+"px) scale("+i[17].z+")",fullscreen:i[11],max_height:i[12]};return i[14]!==void 0&&(u.img_el=i[14]),e=new je({props:u}),ve.push(()=>Ue(e,"img_el",r)),e.$on("load",i[25]),s=new je({props:{variant:"preview",fixed:i[7],hidden:!i[1]?.[1]?.url,src:i[1]?.[1]?.url,alt:"",loading:"lazy",style:i[22]+"; background: var(--block-background-fill);",transform:"translate("+i[17].x+"px, "+i[17].y+"px) scale("+i[17].z+")",fullscreen:i[11],max_height:i[12]}}),s.$on("load",i[25]),{c(){J(e.$$.fragment),n=ke(),J(s.$$.fragment)},m(o,f){V(e,o,f),he(o,n,f),V(s,o,f),l=!0},p(o,f){const a={};f[0]&2&&(a.src=o[1]?.[0]?.url),f[0]&131072&&(a.transform="translate("+o[17].x+"px, "+o[17].y+"px) scale("+o[17].z+")"),f[0]&2048&&(a.fullscreen=o[11]),f[0]&4096&&(a.max_height=o[12]),!t&&f[0]&16384&&(t=!0,a.img_el=o[14],Pe(()=>t=!1)),e.$set(a);const _={};f[0]&128&&(_.fixed=o[7]),f[0]&2&&(_.hidden=!o[1]?.[1]?.url),f[0]&2&&(_.src=o[1]?.[1]?.url),f[0]&4194304&&(_.style=o[22]+"; background: var(--block-background-fill);"),f[0]&131072&&(_.transform="translate("+o[17].x+"px, "+o[17].y+"px) scale("+o[17].z+")"),f[0]&2048&&(_.fullscreen=o[11]),f[0]&4096&&(_.max_height=o[12]),s.$set(_)},i(o){l||(C(e.$$.fragment,o),C(s.$$.fragment,o),l=!0)},o(o){B(e.$$.fragment,o),B(s.$$.fragment,o),l=!1},d(o){o&&ce(n),G(e,o),G(s,o)}}}function pi(i){let e,t;return e=new Ie({}),{c(){J(e.$$.fragment)},m(n,s){V(e,n,s),t=!0},i(n){t||(C(e.$$.fragment,n),t=!0)},o(n){B(e.$$.fragment,n),t=!1},d(n){G(e,n)}}}function wi(i){let e,t,n,s,l,r;e=new Rt({props:{show_label:i[5],Icon:Ie,label:i[3]||i[6]("image.image")}});const u=[di,hi],o=[];function f(a,_){return(a[1]===null||a[1][0]===null||a[1][1]===null)&&!a[8]?0:1}return n=f(i),s=o[n]=u[n](i),{c(){J(e.$$.fragment),t=ke(),s.c(),l=Ot()},m(a,_){V(e,a,_),he(a,t,_),o[n].m(a,_),he(a,l,_),r=!0},p(a,_){const h={};_[0]&32&&(h.show_label=a[5]),_[0]&72&&(h.label=a[3]||a[6]("image.image")),e.$set(h);let p=n;n=f(a),n===p?o[n].p(a,_):(We(),B(o[p],1,1,()=>{o[p]=null}),Ne(),s=o[n],s?s.p(a,_):(s=o[n]=u[n](a),s.c()),C(s,1),s.m(l.parentNode,l))},i(a){r||(C(e.$$.fragment,a),C(s),r=!0)},o(a){B(e.$$.fragment,a),B(s),r=!1},d(a){a&&(ce(t),ce(l)),G(e,a),o[n].d(a)}}}function vi(i,e,t,n,s,l){return(i*t+n-s)/l/e}function ki(i,e,t){let n,s,l,{value:r=[null,null]}=e,{label:u=void 0}=e,{show_download_button:o=!0}=e,{show_label:f}=e,{i18n:a}=e,{position:_}=e,{layer_images:h=!0}=e,{show_single:p=!1}=e,{slider_color:g}=e,{show_fullscreen_button:d=!0}=e,{fullscreen:w=!1}=e,{el_width:$=0}=e,{max_height:z}=e,{interactive:D=!0}=e;const m=ci();let X,Y,E,M=dn({x:0,y:0,z:1},{duration:75});ri(i,M,v=>t(17,l=v));let c,P=0,I=null,j=null;function L(v,de){!v||!de||(I?.destroy(),j?.disconnect(),v?.getBoundingClientRect().width,t(26,P=de?.getBoundingClientRect().width||0),t(20,I=new ni(de,v)),I.subscribe(({x:Ee,y:ae,scale:nt})=>{M.set({x:Ee,y:ae,z:nt})}),j=new ResizeObserver(Ee=>{for(const ae of Ee)ae.target===de&&t(26,P=ae.contentRect.width),ae.target===v&&ae.contentRect.width}),j.observe(de),j.observe(v))}_i(()=>()=>{I?.destroy(),j?.disconnect()});let q,U={top:0,left:0,width:0,height:0};function se(v){t(16,U=v.detail)}const re=()=>I?.reset_zoom();function k(v){oi.call(this,i,v)}const x=v=>{t(1,r=[null,null]),m("clear"),v.stopPropagation()};function H(v){X=v,t(14,X)}function Ke(v){_=v,t(0,_)}function Ze(v){Y=v,t(15,Y)}function Qe(v){c=v,t(19,c)}function xe(v){ve[v?"unshift":"push"](()=>{q=v,t(21,q)})}function et(){$=this.clientWidth,t(2,$)}function tt(v){ve[v?"unshift":"push"](()=>{E=v,t(18,E)})}return i.$$set=v=>{"value"in v&&t(1,r=v.value),"label"in v&&t(3,u=v.label),"show_download_button"in v&&t(4,o=v.show_download_button),"show_label"in v&&t(5,f=v.show_label),"i18n"in v&&t(6,a=v.i18n),"position"in v&&t(0,_=v.position),"layer_images"in v&&t(7,h=v.layer_images),"show_single"in v&&t(8,p=v.show_single),"slider_color"in v&&t(9,g=v.slider_color),"show_fullscreen_button"in v&&t(10,d=v.show_fullscreen_button),"fullscreen"in v&&t(11,w=v.fullscreen),"el_width"in v&&t(2,$=v.el_width),"max_height"in v&&t(12,z=v.max_height),"interactive"in v&&t(13,D=v.interactive)},i.$$.update=()=>{i.$$.dirty[0]&67305473&&t(27,n=vi(_,P,U.width,U.left,l.x,l.z)),i.$$.dirty[0]&134217856&&t(22,s=h?`clip-path: inset(0 0 0 ${n*100}%)`:""),i.$$.dirty[0]&49152&&L(X,Y)},[_,r,$,u,o,f,a,h,p,g,d,w,z,D,X,Y,U,l,E,c,I,q,s,m,M,se,P,n,re,k,x,H,Ke,Ze,Qe,xe,et,tt]}class zi extends ii{constructor(e){super(),ai(this,e,ki,wi,fi,{value:1,label:3,show_download_button:4,show_label:5,i18n:6,position:0,layer_images:7,show_single:8,slider_color:9,show_fullscreen_button:10,fullscreen:11,el_width:2,max_height:12,interactive:13},null,[-1,-1])}get value(){return this.$$.ctx[1]}set value(e){this.$$set({value:e}),W()}get label(){return this.$$.ctx[3]}set label(e){this.$$set({label:e}),W()}get show_download_button(){return this.$$.ctx[4]}set show_download_button(e){this.$$set({show_download_button:e}),W()}get show_label(){return this.$$.ctx[5]}set show_label(e){this.$$set({show_label:e}),W()}get i18n(){return this.$$.ctx[6]}set i18n(e){this.$$set({i18n:e}),W()}get position(){return this.$$.ctx[0]}set position(e){this.$$set({position:e}),W()}get layer_images(){return this.$$.ctx[7]}set layer_images(e){this.$$set({layer_images:e}),W()}get show_single(){return this.$$.ctx[8]}set show_single(e){this.$$set({show_single:e}),W()}get slider_color(){return this.$$.ctx[9]}set slider_color(e){this.$$set({slider_color:e}),W()}get show_fullscreen_button(){return this.$$.ctx[10]}set show_fullscreen_button(e){this.$$set({show_fullscreen_button:e}),W()}get fullscreen(){return this.$$.ctx[11]}set fullscreen(e){this.$$set({fullscreen:e}),W()}get el_width(){return this.$$.ctx[2]}set el_width(e){this.$$set({el_width:e}),W()}get max_height(){return this.$$.ctx[12]}set max_height(e){this.$$set({max_height:e}),W()}get interactive(){return this.$$.ctx[13]}set interactive(e){this.$$set({interactive:e}),W()}}const{SvelteComponent:$i,attr:Xi,create_component:Yi,destroy_component:Ei,detach:Mi,element:Ti,init:Di,insert:Si,mount_component:Ci,noop:Ri,safe_not_equal:Ii,transition_in:Li,transition_out:Bi}=window.__gradio__svelte__internal,{createEventDispatcher:qi}=window.__gradio__svelte__internal;function Pi(i){let e,t,n;return t=new Re({props:{Icon:It,label:"Remove Image"}}),t.$on("click",i[1]),{c(){e=Ti("div"),Yi(t.$$.fragment),Xi(e,"class","svelte-s6ybro")},m(s,l){Si(s,e,l),Ci(t,e,null),n=!0},p:Ri,i(s){n||(Li(t.$$.fragment,s),n=!0)},o(s){Bi(t.$$.fragment,s),n=!1},d(s){s&&Mi(e),Ei(t)}}}function Ui(i){const e=qi();return[e,n=>{e("remove_image"),n.stopPropagation()}]}class Ni extends $i{constructor(e){super(),Di(this,e,Ui,Pi,Ii,{})}}const{SvelteComponent:Wi,add_flush_callback:He,add_iframe_resize_listener:Oi,add_render_callback:ji,append:ut,attr:ze,bind:Je,binding_callbacks:Ge,check_outros:De,create_component:K,create_slot:jt,destroy_component:Z,detach:$e,element:Le,flush:O,get_all_dirty_from_scope:At,get_slot_changes:Ft,group_outros:Se,init:Ai,insert:Xe,mount_component:Q,noop:Ht,safe_not_equal:Fi,set_style:pe,space:Oe,toggle_class:Ye,transition_in:T,transition_out:R,update_slot_base:Jt}=window.__gradio__svelte__internal,{createEventDispatcher:Hi,tick:Ji}=window.__gradio__svelte__internal;function Et(i){let e,t;return e=new Ni({}),e.$on("remove_image",i[22]),{c(){K(e.$$.fragment)},m(n,s){Q(e,n,s),t=!0},p:Ht,i(n){t||(T(e.$$.fragment,n),t=!0)},o(n){R(e.$$.fragment,n),t=!1},d(n){Z(e,n)}}}function Mt(i){let e,t,n=i[7]&&Tt(i);return{c(){e=Le("div"),n&&n.c(),ze(e,"class","icon-buttons svelte-143b07a")},m(s,l){Xe(s,e,l),n&&n.m(e,null),t=!0},p(s,l){s[7]?n?(n.p(s,l),l&128&&T(n,1)):(n=Tt(s),n.c(),T(n,1),n.m(e,null)):n&&(Se(),R(n,1,1,()=>{n=null}),De())},i(s){t||(T(n),t=!0)},o(s){R(n),t=!1},d(s){s&&$e(e),n&&n.d()}}}function Tt(i){let e,t;return e=new Bt({props:{href:i[0][1].url,download:i[0][1].orig_name||"image",$$slots:{default:[Gi]},$$scope:{ctx:i}}}),{c(){K(e.$$.fragment)},m(n,s){Q(e,n,s),t=!0},p(n,s){const l={};s&1&&(l.href=n[0][1].url),s&1&&(l.download=n[0][1].orig_name||"image"),s&1073741824&&(l.$$scope={dirty:s,ctx:n}),e.$set(l)},i(n){t||(T(e.$$.fragment,n),t=!0)},o(n){R(e.$$.fragment,n),t=!1},d(n){Z(e,n)}}}function Gi(i){let e,t;return e=new Re({props:{Icon:Lt}}),{c(){K(e.$$.fragment)},m(n,s){Q(e,n,s),t=!0},p:Ht,i(n){t||(T(e.$$.fragment,n),t=!0)},o(n){R(e.$$.fragment,n),t=!1},d(n){Z(e,n)}}}function Vi(i){let e,t,n;function s(r){i[25](r)}let l={variant:"upload",src:i[14][0]?.url,alt:"",max_height:i[13]};return i[15]!==void 0&&(l.img_el=i[15]),e=new je({props:l}),Ge.push(()=>Je(e,"img_el",s)),{c(){K(e.$$.fragment)},m(r,u){Q(e,r,u),n=!0},p(r,u){const o={};u&16384&&(o.src=r[14][0]?.url),u&8192&&(o.max_height=r[13]),!t&&u&32768&&(t=!0,o.img_el=r[15],He(()=>t=!1)),e.$set(o)},i(r){n||(T(e.$$.fragment,r),n=!0)},o(r){R(e.$$.fragment,r),n=!1},d(r){Z(e,r)}}}function yi(i){let e,t,n,s;function l(u){i[23](u)}let r={filetype:"image/*",disable_click:!!i[0]?.[0],root:i[5],file_count:"multiple",upload:i[9],stream_handler:i[10],max_file_size:i[11],$$slots:{default:[Ki]},$$scope:{ctx:i}};return i[1]!==void 0&&(r.dragging=i[1]),t=new Pt({props:r}),Ge.push(()=>Je(t,"dragging",l)),t.$on("load",i[24]),{c(){e=Le("div"),K(t.$$.fragment),ze(e,"class","wrap svelte-143b07a"),Ye(e,"half-wrap",i[6]===1)},m(u,o){Xe(u,e,o),Q(t,e,null),s=!0},p(u,o){const f={};o&1&&(f.disable_click=!!u[0]?.[0]),o&32&&(f.root=u[5]),o&512&&(f.upload=u[9]),o&1024&&(f.stream_handler=u[10]),o&2048&&(f.max_file_size=u[11]),o&1073741824&&(f.$$scope={dirty:o,ctx:u}),!n&&o&2&&(n=!0,f.dragging=u[1],He(()=>n=!1)),t.$set(f),(!s||o&64)&&Ye(e,"half-wrap",u[6]===1)},i(u){s||(T(t.$$.fragment,u),s=!0)},o(u){R(t.$$.fragment,u),s=!1},d(u){u&&$e(e),Z(t)}}}function Ki(i){let e;const t=i[21].default,n=jt(t,i,i[30],null);return{c(){n&&n.c()},m(s,l){n&&n.m(s,l),e=!0},p(s,l){n&&n.p&&(!e||l&1073741824)&&Jt(n,t,s,s[30],e?Ft(t,s[30],l,null):At(s[30]),null)},i(s){e||(T(n,s),e=!0)},o(s){R(n,s),e=!1},d(s){n&&n.d(s)}}}function Zi(i){let e,t;return e=new je({props:{variant:"upload",src:i[14][1].url,alt:"",fixed:i[6]===1,transform:"translate(0px, 0px) scale(1)",max_height:i[13]}}),{c(){K(e.$$.fragment)},m(n,s){Q(e,n,s),t=!0},p(n,s){const l={};s&16384&&(l.src=n[14][1].url),s&64&&(l.fixed=n[6]===1),s&8192&&(l.max_height=n[13]),e.$set(l)},i(n){t||(T(e.$$.fragment,n),t=!0)},o(n){R(e.$$.fragment,n),t=!1},d(n){Z(e,n)}}}function Qi(i){let e,t,n=`${i[16]*(1-i[2])}px`,s=`translateX(${i[16]*i[2]}px)`,l;return t=new ft({props:{unpadded_box:!0,size:"large",$$slots:{default:[es]},$$scope:{ctx:i}}}),{c(){e=Le("div"),K(t.$$.fragment),ze(e,"class","empty-wrap fixed svelte-143b07a"),Ye(e,"white-icon",!i[0]?.[0]?.url),pe(e,"width",n),pe(e,"transform",s)},m(r,u){Xe(r,e,u),Q(t,e,null),l=!0},p(r,u){const o={};u&1073741824&&(o.$$scope={dirty:u,ctx:r}),t.$set(o),(!l||u&1)&&Ye(e,"white-icon",!r[0]?.[0]?.url),u&65540&&n!==(n=`${r[16]*(1-r[2])}px`)&&pe(e,"width",n),u&65540&&s!==(s=`translateX(${r[16]*r[2]}px)`)&&pe(e,"transform",s)},i(r){l||(T(t.$$.fragment,r),l=!0)},o(r){R(t.$$.fragment,r),l=!1},d(r){r&&$e(e),Z(t)}}}function xi(i){let e,t,n;function s(r){i[26](r)}let l={filetype:"image/*",disable_click:!!i[0]?.[1],root:i[5],file_count:"multiple",upload:i[9],stream_handler:i[10],max_file_size:i[11],$$slots:{default:[ts]},$$scope:{ctx:i}};return i[1]!==void 0&&(l.dragging=i[1]),e=new Pt({props:l}),Ge.push(()=>Je(e,"dragging",s)),e.$on("load",i[27]),{c(){K(e.$$.fragment)},m(r,u){Q(e,r,u),n=!0},p(r,u){const o={};u&1&&(o.disable_click=!!r[0]?.[1]),u&32&&(o.root=r[5]),u&512&&(o.upload=r[9]),u&1024&&(o.stream_handler=r[10]),u&2048&&(o.max_file_size=r[11]),u&1073741824&&(o.$$scope={dirty:u,ctx:r}),!t&&u&2&&(t=!0,o.dragging=r[1],He(()=>t=!1)),e.$set(o)},i(r){n||(T(e.$$.fragment,r),n=!0)},o(r){R(e.$$.fragment,r),n=!1},d(r){Z(e,r)}}}function es(i){let e,t;return e=new Ie({}),{c(){K(e.$$.fragment)},m(n,s){Q(e,n,s),t=!0},i(n){t||(T(e.$$.fragment,n),t=!0)},o(n){R(e.$$.fragment,n),t=!1},d(n){Z(e,n)}}}function ts(i){let e;const t=i[21].default,n=jt(t,i,i[30],null);return{c(){n&&n.c()},m(s,l){n&&n.m(s,l),e=!0},p(s,l){n&&n.p&&(!e||l&1073741824)&&Jt(n,t,s,s[30],e?Ft(t,s[30],l,null):At(s[30]),null)},i(s){e||(T(n,s),e=!0)},o(s){R(n,s),e=!1},d(s){n&&n.d(s)}}}function ns(i){let e,t,n,s,l,r,u;const o=[yi,Vi],f=[];function a(g,d){return g[14]?.[0]?1:0}t=a(i),n=f[t]=o[t](i);const _=[xi,Qi,Zi],h=[];function p(g,d){return!g[14]?.[1]&&g[6]===2?0:!g[14]?.[1]&&g[6]===1?1:g[14]?.[1]?2:-1}return~(l=p(i))&&(r=h[l]=_[l](i)),{c(){e=Le("div"),n.c(),s=Oe(),r&&r.c(),ze(e,"class","upload-wrap svelte-143b07a"),Ye(e,"side-by-side",i[6]===2),pe(e,"display",i[6]===2?"flex":"block")},m(g,d){Xe(g,e,d),f[t].m(e,null),ut(e,s),~l&&h[l].m(e,null),u=!0},p(g,d){let w=t;t=a(g),t===w?f[t].p(g,d):(Se(),R(f[w],1,1,()=>{f[w]=null}),De(),n=f[t],n?n.p(g,d):(n=f[t]=o[t](g),n.c()),T(n,1),n.m(e,s));let $=l;l=p(g),l===$?~l&&h[l].p(g,d):(r&&(Se(),R(h[$],1,1,()=>{h[$]=null}),De()),~l?(r=h[l],r?r.p(g,d):(r=h[l]=_[l](g),r.c()),T(r,1),r.m(e,null)):r=null),(!u||d&64)&&Ye(e,"side-by-side",g[6]===2),d&64&&pe(e,"display",g[6]===2?"flex":"block")},i(g){u||(T(n),T(r),u=!0)},o(g){R(n),R(r),u=!1},d(g){g&&$e(e),f[t].d(),~l&&h[l].d()}}}function is(i){let e,t,n,s,l,r,u,o,f;e=new Rt({props:{show_label:i[4],Icon:Ie,label:i[3]||i[12]("image.image")}});let a=(i[0]?.[0]?.url||i[0]?.[1]?.url)&&Et(i),_=i[0]?.[1]?.url&&Mt(i);function h(g){i[28](g)}let p={disabled:i[6]==2||!i[0]?.[0],slider_color:i[8],$$slots:{default:[ns]},$$scope:{ctx:i}};return i[2]!==void 0&&(p.position=i[2]),r=new Wt({props:p}),Ge.push(()=>Je(r,"position",h)),{c(){K(e.$$.fragment),t=Oe(),n=Le("div"),a&&a.c(),s=Oe(),_&&_.c(),l=Oe(),K(r.$$.fragment),ze(n,"data-testid","image"),ze(n,"class","image-container svelte-143b07a"),ji(()=>i[29].call(n))},m(g,d){Q(e,g,d),Xe(g,t,d),Xe(g,n,d),a&&a.m(n,null),ut(n,s),_&&_.m(n,null),ut(n,l),Q(r,n,null),o=Oi(n,i[29].bind(n)),f=!0},p(g,[d]){const w={};d&16&&(w.show_label=g[4]),d&4104&&(w.label=g[3]||g[12]("image.image")),e.$set(w),g[0]?.[0]?.url||g[0]?.[1]?.url?a?(a.p(g,d),d&1&&T(a,1)):(a=Et(g),a.c(),T(a,1),a.m(n,s)):a&&(Se(),R(a,1,1,()=>{a=null}),De()),g[0]?.[1]?.url?_?(_.p(g,d),d&1&&T(_,1)):(_=Mt(g),_.c(),T(_,1),_.m(n,l)):_&&(Se(),R(_,1,1,()=>{_=null}),De());const $={};d&65&&($.disabled=g[6]==2||!g[0]?.[0]),d&256&&($.slider_color=g[8]),d&1073868391&&($.$$scope={dirty:d,ctx:g}),!u&&d&4&&(u=!0,$.position=g[2],He(()=>u=!1)),r.$set($)},i(g){f||(T(e.$$.fragment,g),T(a),T(_),T(r.$$.fragment,g),f=!0)},o(g){R(e.$$.fragment,g),R(a),R(_),R(r.$$.fragment,g),f=!1},d(g){g&&($e(t),$e(n)),Z(e,g),a&&a.d(),_&&_.d(),Z(r),o()}}}function ss(i,e,t){let{$$slots:n={},$$scope:s}=e,{value:l}=e,{label:r=void 0}=e,{show_label:u}=e,{root:o}=e,{position:f}=e,{upload_count:a=2}=e,{show_download_button:_=!0}=e,{slider_color:h}=e,{upload:p}=e,{stream_handler:g}=e,{max_file_size:d=null}=e,{i18n:w}=e,{max_height:$}=e,z=l||[null,null],D,m,X;async function Y({detail:k},x){const H=[l[0],l[1]];k.length>1?H[x]=k[0]:H[x]=k[x],t(0,l=H),await Ji(),M("upload",H)}let E="";const M=Hi();let{dragging:c=!1}=e;const P=()=>{t(2,f=.5),t(0,l=[null,null]),M("clear")};function I(k){c=k,t(1,c)}const j=k=>Y(k,0);function L(k){D=k,t(15,D)}function q(k){c=k,t(1,c)}const U=k=>Y(k,1);function se(k){f=k,t(2,f)}function re(){m=this.clientWidth,X=this.clientHeight,t(16,m),t(17,X)}return i.$$set=k=>{"value"in k&&t(0,l=k.value),"label"in k&&t(3,r=k.label),"show_label"in k&&t(4,u=k.show_label),"root"in k&&t(5,o=k.root),"position"in k&&t(2,f=k.position),"upload_count"in k&&t(6,a=k.upload_count),"show_download_button"in k&&t(7,_=k.show_download_button),"slider_color"in k&&t(8,h=k.slider_color),"upload"in k&&t(9,p=k.upload),"stream_handler"in k&&t(10,g=k.stream_handler),"max_file_size"in k&&t(11,d=k.max_file_size),"i18n"in k&&t(12,w=k.i18n),"max_height"in k&&t(13,$=k.max_height),"dragging"in k&&t(1,c=k.dragging),"$$scope"in k&&t(30,s=k.$$scope)},i.$$.update=()=>{i.$$.dirty&1048577&&JSON.stringify(l)!==E&&(t(20,E=JSON.stringify(l)),t(14,z=l)),i.$$.dirty&2&&M("drag",c)},[l,c,f,r,u,o,a,_,h,p,g,d,w,$,z,D,m,X,Y,M,E,n,P,I,j,L,q,U,se,re,s]}class ls extends Wi{constructor(e){super(),Ai(this,e,ss,is,Fi,{value:0,label:3,show_label:4,root:5,position:2,upload_count:6,show_download_button:7,slider_color:8,upload:9,stream_handler:10,max_file_size:11,i18n:12,max_height:13,dragging:1})}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),O()}get label(){return this.$$.ctx[3]}set label(e){this.$$set({label:e}),O()}get show_label(){return this.$$.ctx[4]}set show_label(e){this.$$set({show_label:e}),O()}get root(){return this.$$.ctx[5]}set root(e){this.$$set({root:e}),O()}get position(){return this.$$.ctx[2]}set position(e){this.$$set({position:e}),O()}get upload_count(){return this.$$.ctx[6]}set upload_count(e){this.$$set({upload_count:e}),O()}get show_download_button(){return this.$$.ctx[7]}set show_download_button(e){this.$$set({show_download_button:e}),O()}get slider_color(){return this.$$.ctx[8]}set slider_color(e){this.$$set({slider_color:e}),O()}get upload(){return this.$$.ctx[9]}set upload(e){this.$$set({upload:e}),O()}get stream_handler(){return this.$$.ctx[10]}set stream_handler(e){this.$$set({stream_handler:e}),O()}get max_file_size(){return this.$$.ctx[11]}set max_file_size(e){this.$$set({max_file_size:e}),O()}get i18n(){return this.$$.ctx[12]}set i18n(e){this.$$set({i18n:e}),O()}get max_height(){return this.$$.ctx[13]}set max_height(e){this.$$set({max_height:e}),O()}get dragging(){return this.$$.ctx[1]}set dragging(e){this.$$set({dragging:e}),O()}}const{SvelteComponent:os,add_flush_callback:Dt,bind:St,binding_callbacks:Ct,bubble:be,create_component:rs,create_slot:as,destroy_component:us,flush:y,get_all_dirty_from_scope:fs,get_slot_changes:_s,init:cs,mount_component:hs,safe_not_equal:ds,transition_in:Gt,transition_out:Vt,update_slot_base:gs}=window.__gradio__svelte__internal;function ms(i){let e;const t=i[11].default,n=as(t,i,i[21],null);return{c(){n&&n.c()},m(s,l){n&&n.m(s,l),e=!0},p(s,l){n&&n.p&&(!e||l&2097152)&&gs(n,t,s,s[21],e?_s(t,s[21],l,null):fs(s[21]),null)},i(s){e||(Gt(n,s),e=!0)},o(s){Vt(n,s),e=!1},d(s){n&&n.d(s)}}}function bs(i){let e,t,n,s;function l(o){i[12](o)}function r(o){i[13](o)}let u={slider_color:"var(--border-color-primary)",position:.5,root:i[7],label:i[4],show_label:i[5],upload_count:i[8],stream_handler:i[3],upload:i[2],max_file_size:i[10],max_height:i[9],i18n:i[6],$$slots:{default:[ms]},$$scope:{ctx:i}};return i[0]!==void 0&&(u.value=i[0]),i[1]!==void 0&&(u.dragging=i[1]),e=new ls({props:u}),Ct.push(()=>St(e,"value",l)),Ct.push(()=>St(e,"dragging",r)),e.$on("edit",i[14]),e.$on("clear",i[15]),e.$on("stream",i[16]),e.$on("drag",i[17]),e.$on("upload",i[18]),e.$on("select",i[19]),e.$on("share",i[20]),{c(){rs(e.$$.fragment)},m(o,f){hs(e,o,f),s=!0},p(o,[f]){const a={};f&128&&(a.root=o[7]),f&16&&(a.label=o[4]),f&32&&(a.show_label=o[5]),f&256&&(a.upload_count=o[8]),f&8&&(a.stream_handler=o[3]),f&4&&(a.upload=o[2]),f&1024&&(a.max_file_size=o[10]),f&512&&(a.max_height=o[9]),f&64&&(a.i18n=o[6]),f&2097152&&(a.$$scope={dirty:f,ctx:o}),!t&&f&1&&(t=!0,a.value=o[0],Dt(()=>t=!1)),!n&&f&2&&(n=!0,a.dragging=o[1],Dt(()=>n=!1)),e.$set(a)},i(o){s||(Gt(e.$$.fragment,o),s=!0)},o(o){Vt(e.$$.fragment,o),s=!1},d(o){us(e,o)}}}function ps(i,e,t){let{$$slots:n={},$$scope:s}=e,{value:l=[null,null]}=e,{upload:r}=e,{stream_handler:u}=e,{label:o}=e,{show_label:f}=e,{i18n:a}=e,{root:_}=e,{upload_count:h=1}=e,{dragging:p}=e,{max_height:g}=e,{max_file_size:d=null}=e;function w(c){l=c,t(0,l)}function $(c){p=c,t(1,p)}function z(c){be.call(this,i,c)}function D(c){be.call(this,i,c)}function m(c){be.call(this,i,c)}const X=({detail:c})=>t(1,p=c);function Y(c){be.call(this,i,c)}function E(c){be.call(this,i,c)}function M(c){be.call(this,i,c)}return i.$$set=c=>{"value"in c&&t(0,l=c.value),"upload"in c&&t(2,r=c.upload),"stream_handler"in c&&t(3,u=c.stream_handler),"label"in c&&t(4,o=c.label),"show_label"in c&&t(5,f=c.show_label),"i18n"in c&&t(6,a=c.i18n),"root"in c&&t(7,_=c.root),"upload_count"in c&&t(8,h=c.upload_count),"dragging"in c&&t(1,p=c.dragging),"max_height"in c&&t(9,g=c.max_height),"max_file_size"in c&&t(10,d=c.max_file_size),"$$scope"in c&&t(21,s=c.$$scope)},[l,p,r,u,o,f,a,_,h,g,d,n,w,$,z,D,m,X,Y,E,M,s]}class ws extends os{constructor(e){super(),cs(this,e,ps,bs,ds,{value:0,upload:2,stream_handler:3,label:4,show_label:5,i18n:6,root:7,upload_count:8,dragging:1,max_height:9,max_file_size:10})}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),y()}get upload(){return this.$$.ctx[2]}set upload(e){this.$$set({upload:e}),y()}get stream_handler(){return this.$$.ctx[3]}set stream_handler(e){this.$$set({stream_handler:e}),y()}get label(){return this.$$.ctx[4]}set label(e){this.$$set({label:e}),y()}get show_label(){return this.$$.ctx[5]}set show_label(e){this.$$set({show_label:e}),y()}get i18n(){return this.$$.ctx[6]}set i18n(e){this.$$set({i18n:e}),y()}get root(){return this.$$.ctx[7]}set root(e){this.$$set({root:e}),y()}get upload_count(){return this.$$.ctx[8]}set upload_count(e){this.$$set({upload_count:e}),y()}get dragging(){return this.$$.ctx[1]}set dragging(e){this.$$set({dragging:e}),y()}get max_height(){return this.$$.ctx[9]}set max_height(e){this.$$set({max_height:e}),y()}get max_file_size(){return this.$$.ctx[10]}set max_file_size(e){this.$$set({max_file_size:e}),y()}}const{SvelteComponent:vs,add_flush_callback:Ae,assign:yt,bind:Fe,binding_callbacks:Ce,check_outros:ks,create_component:te,destroy_component:ne,detach:Ve,empty:Kt,flush:S,get_spread_object:Zt,get_spread_update:Qt,group_outros:zs,init:$s,insert:ye,mount_component:ie,safe_not_equal:Xs,space:xt,transition_in:A,transition_out:F}=window.__gradio__svelte__internal,{afterUpdate:Ys}=window.__gradio__svelte__internal;function Es(i){let e,t;return e=new Ut({props:{visible:i[4],variant:i[0]===null?"dashed":"solid",border_mode:i[22]?"focus":"base",padding:!1,elem_id:i[2],elem_classes:i[3],height:i[9]||void 0,width:i[10],allow_overflow:!1,container:i[11],scale:i[12],min_width:i[13],$$slots:{default:[Is]},$$scope:{ctx:i}}}),e.$on("dragenter",i[25]),e.$on("dragleave",i[25]),e.$on("dragover",i[25]),e.$on("drop",i[26]),{c(){te(e.$$.fragment)},m(n,s){ie(e,n,s),t=!0},p(n,s){const l={};s[0]&16&&(l.visible=n[4]),s[0]&1&&(l.variant=n[0]===null?"dashed":"solid"),s[0]&4194304&&(l.border_mode=n[22]?"focus":"base"),s[0]&4&&(l.elem_id=n[2]),s[0]&8&&(l.elem_classes=n[3]),s[0]&512&&(l.height=n[9]||void 0),s[0]&1024&&(l.width=n[10]),s[0]&2048&&(l.container=n[11]),s[0]&4096&&(l.scale=n[12]),s[0]&8192&&(l.min_width=n[13]),s[0]&14319971|s[1]&262144&&(l.$$scope={dirty:s,ctx:n}),e.$set(l)},i(n){t||(A(e.$$.fragment,n),t=!0)},o(n){F(e.$$.fragment,n),t=!1},d(n){ne(e,n)}}}function Ms(i){let e,t,n;function s(r){i[37](r)}let l={visible:i[4],variant:"solid",border_mode:i[22]?"focus":"base",padding:!1,elem_id:i[2],elem_classes:i[3],height:i[9]||void 0,width:i[10],allow_overflow:!1,container:i[11],scale:i[12],min_width:i[13],$$slots:{default:[Ls]},$$scope:{ctx:i}};return i[21]!==void 0&&(l.fullscreen=i[21]),e=new Ut({props:l}),Ce.push(()=>Fe(e,"fullscreen",s)),{c(){te(e.$$.fragment)},m(r,u){ie(e,r,u),n=!0},p(r,u){const o={};u[0]&16&&(o.visible=r[4]),u[0]&4194304&&(o.border_mode=r[22]?"focus":"base"),u[0]&4&&(o.elem_id=r[2]),u[0]&8&&(o.elem_classes=r[3]),u[0]&512&&(o.height=r[9]||void 0),u[0]&1024&&(o.width=r[10]),u[0]&2048&&(o.container=r[11]),u[0]&4096&&(o.scale=r[12]),u[0]&8192&&(o.min_width=r[13]),u[0]&20791523|u[1]&262144&&(o.$$scope={dirty:u,ctx:r}),!t&&u[0]&2097152&&(t=!0,o.fullscreen=r[21],Ae(()=>t=!1)),e.$set(o)},i(r){n||(A(e.$$.fragment,r),n=!0)},o(r){F(e.$$.fragment,r),n=!1},d(r){ne(e,r)}}}function Ts(i){let e,t;return e=new ft({props:{unpadded_box:!0,size:"large",$$slots:{default:[Cs]},$$scope:{ctx:i}}}),{c(){te(e.$$.fragment)},m(n,s){ie(e,n,s),t=!0},p(n,s){const l={};s[1]&262144&&(l.$$scope={dirty:s,ctx:n}),e.$set(l)},i(n){t||(A(e.$$.fragment,n),t=!0)},o(n){F(e.$$.fragment,n),t=!1},d(n){ne(e,n)}}}function Ds(i){let e,t;return e=new Nt({props:{i18n:i[20].i18n,type:"clipboard",mode:"short"}}),{c(){te(e.$$.fragment)},m(n,s){ie(e,n,s),t=!0},p(n,s){const l={};s[0]&1048576&&(l.i18n=n[20].i18n),e.$set(l)},i(n){t||(A(e.$$.fragment,n),t=!0)},o(n){F(e.$$.fragment,n),t=!1},d(n){ne(e,n)}}}function Ss(i){let e,t;return e=new Nt({props:{i18n:i[20].i18n,type:"image",placeholder:i[15]}}),{c(){te(e.$$.fragment)},m(n,s){ie(e,n,s),t=!0},p(n,s){const l={};s[0]&1048576&&(l.i18n=n[20].i18n),s[0]&32768&&(l.placeholder=n[15]),e.$set(l)},i(n){t||(A(e.$$.fragment,n),t=!0)},o(n){F(e.$$.fragment,n),t=!1},d(n){ne(e,n)}}}function Cs(i){let e,t;return e=new Ie({}),{c(){te(e.$$.fragment)},m(n,s){ie(e,n,s),t=!0},i(n){t||(A(e.$$.fragment,n),t=!0)},o(n){F(e.$$.fragment,n),t=!1},d(n){ne(e,n)}}}function Rs(i){let e,t,n,s;const l=[Ss,Ds,Ts],r=[];function u(o,f){return 0}return e=u(),t=r[e]=l[e](i),{c(){t.c(),n=Kt()},m(o,f){r[e].m(o,f),ye(o,n,f),s=!0},p(o,f){t.p(o,f)},i(o){s||(A(t),s=!0)},o(o){F(t),s=!1},d(o){o&&Ve(n),r[e].d(o)}}}function Is(i){let e,t,n,s,l,r;const u=[{autoscroll:i[20].autoscroll},{i18n:i[20].i18n},i[1]];let o={};for(let h=0;h<u.length;h+=1)o=yt(o,u[h]);e=new qt({props:o}),e.$on("clear_status",i[38]);function f(h){i[41](h)}function a(h){i[42](h)}let _={root:i[8],label:i[5],show_label:i[6],upload_count:i[17],max_file_size:i[20].max_file_size,i18n:i[20].i18n,upload:i[39],stream_handler:i[20].client?.stream,max_height:i[19],$$slots:{default:[Rs]},$$scope:{ctx:i}};return i[0]!==void 0&&(_.value=i[0]),i[22]!==void 0&&(_.dragging=i[22]),n=new ws({props:_}),i[40](n),Ce.push(()=>Fe(n,"value",f)),Ce.push(()=>Fe(n,"dragging",a)),n.$on("edit",i[43]),n.$on("clear",i[44]),n.$on("drag",i[45]),n.$on("upload",i[46]),n.$on("error",i[47]),n.$on("close_stream",i[48]),{c(){te(e.$$.fragment),t=xt(),te(n.$$.fragment)},m(h,p){ie(e,h,p),ye(h,t,p),ie(n,h,p),r=!0},p(h,p){const g=p[0]&1048578?Qt(u,[p[0]&1048576&&{autoscroll:h[20].autoscroll},p[0]&1048576&&{i18n:h[20].i18n},p[0]&2&&Zt(h[1])]):{};e.$set(g);const d={};p[0]&256&&(d.root=h[8]),p[0]&32&&(d.label=h[5]),p[0]&64&&(d.show_label=h[6]),p[0]&131072&&(d.upload_count=h[17]),p[0]&1048576&&(d.max_file_size=h[20].max_file_size),p[0]&1048576&&(d.i18n=h[20].i18n),p[0]&1048576&&(d.upload=h[39]),p[0]&1048576&&(d.stream_handler=h[20].client?.stream),p[0]&524288&&(d.max_height=h[19]),p[0]&1081344|p[1]&262144&&(d.$$scope={dirty:p,ctx:h}),!s&&p[0]&1&&(s=!0,d.value=h[0],Ae(()=>s=!1)),!l&&p[0]&4194304&&(l=!0,d.dragging=h[22],Ae(()=>l=!1)),n.$set(d)},i(h){r||(A(e.$$.fragment,h),A(n.$$.fragment,h),r=!0)},o(h){F(e.$$.fragment,h),F(n.$$.fragment,h),r=!1},d(h){h&&Ve(t),ne(e,h),i[40](null),ne(n,h)}}}function Ls(i){let e,t,n,s,l;const r=[{autoscroll:i[20].autoscroll},{i18n:i[20].i18n},i[1]];let u={};for(let a=0;a<r.length;a+=1)u=yt(u,r[a]);e=new qt({props:u});function o(a){i[31](a)}let f={fullscreen:i[21],interactive:i[14],label:i[5],show_label:i[6],show_download_button:i[7],i18n:i[20].i18n,show_fullscreen_button:i[16],position:i[24],slider_color:i[18],max_height:i[19]};return i[0]!==void 0&&(f.value=i[0]),n=new zi({props:f}),Ce.push(()=>Fe(n,"value",o)),n.$on("select",i[32]),n.$on("share",i[33]),n.$on("error",i[34]),n.$on("clear",i[35]),n.$on("fullscreen",i[36]),{c(){te(e.$$.fragment),t=xt(),te(n.$$.fragment)},m(a,_){ie(e,a,_),ye(a,t,_),ie(n,a,_),l=!0},p(a,_){const h=_[0]&1048578?Qt(r,[_[0]&1048576&&{autoscroll:a[20].autoscroll},_[0]&1048576&&{i18n:a[20].i18n},_[0]&2&&Zt(a[1])]):{};e.$set(h);const p={};_[0]&2097152&&(p.fullscreen=a[21]),_[0]&16384&&(p.interactive=a[14]),_[0]&32&&(p.label=a[5]),_[0]&64&&(p.show_label=a[6]),_[0]&128&&(p.show_download_button=a[7]),_[0]&1048576&&(p.i18n=a[20].i18n),_[0]&65536&&(p.show_fullscreen_button=a[16]),_[0]&16777216&&(p.position=a[24]),_[0]&262144&&(p.slider_color=a[18]),_[0]&524288&&(p.max_height=a[19]),!s&&_[0]&1&&(s=!0,p.value=a[0],Ae(()=>s=!1)),n.$set(p)},i(a){l||(A(e.$$.fragment,a),A(n.$$.fragment,a),l=!0)},o(a){F(e.$$.fragment,a),F(n.$$.fragment,a),l=!1},d(a){a&&Ve(t),ne(e,a),ne(n,a)}}}function Bs(i){let e,t,n,s;const l=[Ms,Es],r=[];function u(o,f){return!o[14]||o[0]?.[1]&&o[0]?.[0]?0:1}return e=u(i),t=r[e]=l[e](i),{c(){t.c(),n=Kt()},m(o,f){r[e].m(o,f),ye(o,n,f),s=!0},p(o,f){let a=e;e=u(o),e===a?r[e].p(o,f):(zs(),F(r[a],1,1,()=>{r[a]=null}),ks(),t=r[e],t?t.p(o,f):(t=r[e]=l[e](o),t.c()),A(t,1),t.m(n.parentNode,n))},i(o){s||(A(t),s=!0)},o(o){F(t),s=!1},d(o){o&&Ve(n),r[e].d(o)}}}let qs=!1;function Ps(i,e,t){let n,{value_is_output:s=!1}=e,{elem_id:l=""}=e,{elem_classes:r=[]}=e,{visible:u=!0}=e,{value:o=[null,null]}=e,f=[null,null],{label:a}=e,{show_label:_}=e,{show_download_button:h}=e,{root:p}=e,{height:g}=e,{width:d}=e,{container:w=!0}=e,{scale:$=null}=e,{min_width:z=void 0}=e,{loading_status:D}=e,{interactive:m}=e,{placeholder:X=void 0}=e,{show_fullscreen_button:Y}=e,E=!1,{input_ready:M}=e,{slider_position:c}=e,{upload_count:P=1}=e,{slider_color:I="var(--border-color-primary)"}=e,{max_height:j}=e,{gradio:L}=e;Ys(()=>{t(27,s=!1)});let q,U;const se=b=>{const le=b;le.preventDefault(),le.stopPropagation(),le.type==="dragenter"||le.type==="dragover"?t(22,q=!0):le.type==="dragleave"&&t(22,q=!1)},re=b=>{if(m){const le=b;le.preventDefault(),le.stopPropagation(),t(22,q=!1),U&&U.loadFilesFromDrop(le)}};function k(b){o=b,t(0,o)}const x=({detail:b})=>L.dispatch("select",b),H=({detail:b})=>L.dispatch("share",b),Ke=({detail:b})=>L.dispatch("error",b),Ze=()=>L.dispatch("clear"),Qe=({detail:b})=>{t(21,E=b)};function xe(b){E=b,t(21,E)}const et=()=>L.dispatch("clear_status",D),tt=(...b)=>L.client.upload(...b);function v(b){Ce[b?"unshift":"push"](()=>{U=b,t(23,U)})}function de(b){o=b,t(0,o)}function Ee(b){q=b,t(22,q)}const ae=()=>L.dispatch("edit"),nt=()=>{L.dispatch("clear")},en=({detail:b})=>t(22,q=b),tn=()=>L.dispatch("upload"),nn=({detail:b})=>{t(1,D=D||{}),t(1,D.status="error",D),L.dispatch("error",b)},sn=()=>{L.dispatch("close_stream","stream")};return i.$$set=b=>{"value_is_output"in b&&t(27,s=b.value_is_output),"elem_id"in b&&t(2,l=b.elem_id),"elem_classes"in b&&t(3,r=b.elem_classes),"visible"in b&&t(4,u=b.visible),"value"in b&&t(0,o=b.value),"label"in b&&t(5,a=b.label),"show_label"in b&&t(6,_=b.show_label),"show_download_button"in b&&t(7,h=b.show_download_button),"root"in b&&t(8,p=b.root),"height"in b&&t(9,g=b.height),"width"in b&&t(10,d=b.width),"container"in b&&t(11,w=b.container),"scale"in b&&t(12,$=b.scale),"min_width"in b&&t(13,z=b.min_width),"loading_status"in b&&t(1,D=b.loading_status),"interactive"in b&&t(14,m=b.interactive),"placeholder"in b&&t(15,X=b.placeholder),"show_fullscreen_button"in b&&t(16,Y=b.show_fullscreen_button),"input_ready"in b&&t(28,M=b.input_ready),"slider_position"in b&&t(29,c=b.slider_position),"upload_count"in b&&t(17,P=b.upload_count),"slider_color"in b&&t(18,I=b.slider_color),"max_height"in b&&t(19,j=b.max_height),"gradio"in b&&t(20,L=b.gradio)},i.$$.update=()=>{i.$$.dirty[0]&536870912&&t(24,n=Math.max(0,Math.min(100,c))/100),i.$$.dirty[0]&1209008129&&JSON.stringify(o)!==JSON.stringify(f)&&(t(30,f=o),L.dispatch("change"),s||L.dispatch("input"))},t(28,M=!qs),[o,D,l,r,u,a,_,h,p,g,d,w,$,z,m,X,Y,P,I,j,L,E,q,U,n,se,re,s,M,c,f,k,x,H,Ke,Ze,Qe,xe,et,tt,v,de,Ee,ae,nt,en,tn,nn,sn]}class ul extends vs{constructor(e){super(),$s(this,e,Ps,Bs,Xs,{value_is_output:27,elem_id:2,elem_classes:3,visible:4,value:0,label:5,show_label:6,show_download_button:7,root:8,height:9,width:10,container:11,scale:12,min_width:13,loading_status:1,interactive:14,placeholder:15,show_fullscreen_button:16,input_ready:28,slider_position:29,upload_count:17,slider_color:18,max_height:19,gradio:20},null,[-1,-1])}get value_is_output(){return this.$$.ctx[27]}set value_is_output(e){this.$$set({value_is_output:e}),S()}get elem_id(){return this.$$.ctx[2]}set elem_id(e){this.$$set({elem_id:e}),S()}get elem_classes(){return this.$$.ctx[3]}set elem_classes(e){this.$$set({elem_classes:e}),S()}get visible(){return this.$$.ctx[4]}set visible(e){this.$$set({visible:e}),S()}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),S()}get label(){return this.$$.ctx[5]}set label(e){this.$$set({label:e}),S()}get show_label(){return this.$$.ctx[6]}set show_label(e){this.$$set({show_label:e}),S()}get show_download_button(){return this.$$.ctx[7]}set show_download_button(e){this.$$set({show_download_button:e}),S()}get root(){return this.$$.ctx[8]}set root(e){this.$$set({root:e}),S()}get height(){return this.$$.ctx[9]}set height(e){this.$$set({height:e}),S()}get width(){return this.$$.ctx[10]}set width(e){this.$$set({width:e}),S()}get container(){return this.$$.ctx[11]}set container(e){this.$$set({container:e}),S()}get scale(){return this.$$.ctx[12]}set scale(e){this.$$set({scale:e}),S()}get min_width(){return this.$$.ctx[13]}set min_width(e){this.$$set({min_width:e}),S()}get loading_status(){return this.$$.ctx[1]}set loading_status(e){this.$$set({loading_status:e}),S()}get interactive(){return this.$$.ctx[14]}set interactive(e){this.$$set({interactive:e}),S()}get placeholder(){return this.$$.ctx[15]}set placeholder(e){this.$$set({placeholder:e}),S()}get show_fullscreen_button(){return this.$$.ctx[16]}set show_fullscreen_button(e){this.$$set({show_fullscreen_button:e}),S()}get input_ready(){return this.$$.ctx[28]}set input_ready(e){this.$$set({input_ready:e}),S()}get slider_position(){return this.$$.ctx[29]}set slider_position(e){this.$$set({slider_position:e}),S()}get upload_count(){return this.$$.ctx[17]}set upload_count(e){this.$$set({upload_count:e}),S()}get slider_color(){return this.$$.ctx[18]}set slider_color(e){this.$$set({slider_color:e}),S()}get max_height(){return this.$$.ctx[19]}set max_height(e){this.$$set({max_height:e}),S()}get gradio(){return this.$$.ctx[20]}set gradio(e){this.$$set({gradio:e}),S()}}export{ul as default};
//# sourceMappingURL=Index-CVEjRJ8U.js.map
