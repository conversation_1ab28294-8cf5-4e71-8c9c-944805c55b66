{"version": 3, "file": "Index-CaC1GRz3.js", "sources": ["../../../../js/accordion/shared/Accordion.svelte", "../../../../js/accordion/Index.svelte"], "sourcesContent": ["<script lang=\"ts\">\n\timport { createEventDispatcher } from \"svelte\";\n\tconst dispatch = createEventDispatcher<{\n\t\texpand: void;\n\t\tcollapse: void;\n\t}>();\n\n\texport let open = true;\n\texport let label = \"\";\n</script>\n\n<button\n\ton:click={() => {\n\t\topen = !open;\n\t\tif (open) {\n\t\t\tdispatch(\"expand\");\n\t\t} else {\n\t\t\tdispatch(\"collapse\");\n\t\t}\n\t}}\n\tclass=\"label-wrap\"\n\tclass:open\n>\n\t<span>{label}</span>\n\t<span style:transform={open ? \"rotate(0)\" : \"rotate(90deg)\"} class=\"icon\">\n\t\t▼\n\t</span>\n</button>\n<div style:display={open ? \"block\" : \"none\"}>\n\t<slot />\n</div>\n\n<style>\n\tspan {\n\t\tfont-weight: var(--section-header-text-weight);\n\t\tfont-size: var(--section-header-text-size);\n\t}\n\t.label-wrap {\n\t\tdisplay: flex;\n\t\tjustify-content: space-between;\n\t\tcursor: pointer;\n\t\twidth: var(--size-full);\n\t\tcolor: var(--accordion-text-color);\n\t}\n\t.label-wrap.open {\n\t\tmargin-bottom: var(--size-2);\n\t}\n\n\t.icon {\n\t\ttransition: 150ms;\n\t}\n</style>\n", "<script lang=\"ts\">\n\timport Accordion from \"./shared/Accordion.svelte\";\n\timport { Block } from \"@gradio/atoms\";\n\timport { StatusTracker } from \"@gradio/statustracker\";\n\timport type { LoadingStatus } from \"@gradio/statustracker\";\n\n\timport Column from \"@gradio/column\";\n\timport type { Gradio } from \"@gradio/utils\";\n\n\texport let label: string;\n\texport let elem_id: string;\n\texport let elem_classes: string[];\n\texport let visible = true;\n\texport let open = true;\n\texport let loading_status: LoadingStatus;\n\texport let gradio: Gradio<{\n\t\texpand: never;\n\t\tcollapse: never;\n\t}>;\n</script>\n\n<Block {elem_id} {elem_classes} {visible}>\n\t<StatusTracker\n\t\tautoscroll={gradio.autoscroll}\n\t\ti18n={gradio.i18n}\n\t\t{...loading_status}\n\t/>\n\n\t<Accordion\n\t\t{label}\n\t\tbind:open\n\t\ton:expand={() => gradio.dispatch(\"expand\")}\n\t\ton:collapse={() => gradio.dispatch(\"collapse\")}\n\t>\n\t\t<Column>\n\t\t\t<slot />\n\t\t</Column>\n\t</Accordion>\n</Block>\n"], "names": ["ctx", "insert", "target", "button", "anchor", "append", "span0", "span1", "div", "dispatch", "createEventDispatcher", "open", "$$props", "label", "$$invalidate", "dirty", "elem_id", "elem_classes", "visible", "loading_status", "gradio"], "mappings": "+xBACuC,EAAA,OAAA,kJAsB/BA,EAAK,CAAA,CAAA,iJACWA,EAAI,CAAA,EAAG,YAAc,eAAe,0EAIxCA,EAAI,CAAA,EAAG,QAAU,MAAM,UAjB3CC,EAgBQC,EAAAC,EAAAC,CAAA,EAJPC,EAAmBF,EAAAG,CAAA,gBACnBD,EAEMF,EAAAI,CAAA,WAEPN,EAEKC,EAAAM,EAAAJ,CAAA,6EAPGJ,EAAK,CAAA,CAAA,uBACWA,EAAI,CAAA,EAAG,YAAc,eAAe,kHAIxCA,EAAI,CAAA,EAAG,QAAU,MAAM,gJA1BpCS,EAAWC,QAKN,KAAAC,EAAO,EAAA,EAAAC,GACP,MAAAC,EAAQ,EAAA,EAAAD,eAKlBE,EAAA,EAAAH,GAAQA,CAAI,EAEXF,EADGE,EACM,SAEA,UAFQ,oxCCQN,CAAA,WAAAX,KAAO,UAAU,EACvB,CAAA,KAAAA,KAAO,IAAI,EACbA,EAAc,CAAA,wYAFNe,EAAA,IAAA,CAAA,WAAAf,KAAO,UAAU,EACvBe,EAAA,IAAA,CAAA,KAAAf,KAAO,IAAI,WACbA,EAAc,CAAA,CAAA,wsBAhBR,CAAA,MAAAa,CAAA,EAAAD,EACA,CAAA,QAAAI,CAAA,EAAAJ,EACA,CAAA,aAAAK,CAAA,EAAAL,GACA,QAAAM,EAAU,EAAA,EAAAN,GACV,KAAAD,EAAO,EAAA,EAAAC,EACP,CAAA,eAAAO,CAAA,EAAAP,EACA,CAAA,OAAAQ,CAAA,EAAAR,uCAgBOQ,EAAO,SAAS,QAAQ,QACtBA,EAAO,SAAS,UAAU"}