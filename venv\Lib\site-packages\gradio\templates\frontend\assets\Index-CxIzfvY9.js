const __vite__fileDeps=["./Canvas3D-CLFmIvhV.js","./index-DJ2rNx9E.js","./index-rsZ55Oi2.css","./file-url-DoxvUUVV.js","./DownloadLink.svelte_svelte_type_style_lang-C_5UIfol.js","./DownloadLink-CqD3Uu0l.css","./Canvas3DGS-Cm1kuxqL.js"],__vite__mapDeps=i=>i.map(i=>__vite__fileDeps[i]);
import{_ as ae}from"./index-DJ2rNx9E.js";/* empty css                                                        */import"./MarkdownCode.svelte_svelte_type_style_lang-Bl8p72Ow.js";import{B as pe}from"./BlockLabel-3KxTaaiM.js";import{I as Me}from"./IconButton-C_HS7fTi.js";import{D as Fe}from"./Download-DVtk-Jv3.js";import{F as re}from"./File-BQ_9P3Ye.js";import{U as He}from"./Undo-DCjBnnSO.js";import{I as Je}from"./IconButtonWrapper--EIOWuEM.js";import{a as Ke}from"./Upload-8igJ-HYX.js";import{M as Qe}from"./ModifyUpload-Bl9a-zWl.js";import{B as Ue}from"./Block-CJdXVpa7.js";import{E as Xe}from"./Empty-ZqppqzTN.js";import{U as Ye}from"./UploadText-Wd7ORU21.js";import{S as ye}from"./index-DYtg3pip.js";import"./StreamingBar.svelte_svelte_type_style_lang-DlRzv56K.js";import{default as Dn}from"./Example-uQ8MuYg6.js";import"./svelte/svelte.js";import"./prism-python-CeMtt1IT.js";/* empty css                                             */import"./Clear-By3xiIwg.js";import"./Edit-BpRIf5rU.js";import"./DownloadLink-QIttOhoR.js";import"./DownloadLink.svelte_svelte_type_style_lang-C_5UIfol.js";import"./file-url-DoxvUUVV.js";var ve=Object.prototype.hasOwnProperty;function ze(l,e,n){for(n of l.keys())if(X(n,e))return n}function X(l,e){var n,t,s;if(l===e)return!0;if(l&&e&&(n=l.constructor)===e.constructor){if(n===Date)return l.getTime()===e.getTime();if(n===RegExp)return l.toString()===e.toString();if(n===Array){if((t=l.length)===e.length)for(;t--&&X(l[t],e[t]););return t===-1}if(n===Set){if(l.size!==e.size)return!1;for(t of l)if(s=t,s&&typeof s=="object"&&(s=ze(e,s),!s)||!e.has(s))return!1;return!0}if(n===Map){if(l.size!==e.size)return!1;for(t of l)if(s=t[0],s&&typeof s=="object"&&(s=ze(e,s),!s)||!X(t[1],e.get(s)))return!1;return!0}if(n===ArrayBuffer)l=new Uint8Array(l),e=new Uint8Array(e);else if(n===DataView){if((t=l.byteLength)===e.byteLength)for(;t--&&l.getInt8(t)===e.getInt8(t););return t===-1}if(ArrayBuffer.isView(l)){if((t=l.byteLength)===e.byteLength)for(;t--&&l[t]===e[t];);return t===-1}if(!n||typeof l=="object"){t=0;for(n in l)if(ve.call(l,n)&&++t&&!ve.call(e,n)||!(n in e)||!X(l[n],e[n]))return!1;return Object.keys(e).length===t}}return l!==l&&e!==e}const{SvelteComponent:Ze,add_flush_callback:Be,append:xe,attr:W,bind:se,binding_callbacks:Y,check_outros:ne,construct_svelte_component:ie,create_component:O,destroy_component:T,detach:G,element:je,empty:be,flush:U,group_outros:le,init:et,insert:F,mount_component:q,safe_not_equal:tt,space:we,transition_in:w,transition_out:v}=window.__gradio__svelte__internal;function De(l){let e,n,t,s,o,r;n=new Je({props:{$$slots:{default:[nt]},$$scope:{ctx:l}}});const _=[st,lt],i=[];function u(a,f){return a[10]?0:1}return s=u(l),o=i[s]=_[s](l),{c(){e=je("div"),O(n.$$.fragment),t=we(),o.c(),W(e,"class","model3D svelte-1mxwah3"),W(e,"data-testid","model3d")},m(a,f){F(a,e,f),q(n,e,null),xe(e,t),i[s].m(e,null),r=!0},p(a,f){const d={};f&2115105&&(d.$$scope={dirty:f,ctx:a}),n.$set(d);let m=s;s=u(a),s===m?i[s].p(a,f):(le(),v(i[m],1,1,()=>{i[m]=null}),ne(),o=i[s],o?o.p(a,f):(o=i[s]=_[s](a),o.c()),w(o,1),o.m(e,null))},i(a){r||(w(n.$$.fragment,a),w(o),r=!0)},o(a){v(n.$$.fragment,a),v(o),r=!1},d(a){a&&G(e),T(n),i[s].d()}}}function Ce(l){let e,n;return e=new Me({props:{Icon:He,label:"Undo",disabled:!l[9]}}),e.$on("click",l[17]),{c(){O(e.$$.fragment)},m(t,s){q(e,t,s),n=!0},p(t,s){const o={};s&512&&(o.disabled=!t[9]),e.$set(o)},i(t){n||(w(e.$$.fragment,t),n=!0)},o(t){v(e.$$.fragment,t),n=!1},d(t){T(e,t)}}}function nt(l){let e,n,t,s,o,r=!l[10]&&Ce(l);return t=new Me({props:{Icon:Fe,label:l[5]("common.download")}}),{c(){r&&r.c(),e=we(),n=je("a"),O(t.$$.fragment),W(n,"href",l[14]),W(n,"target",window.__is_colab__?"_blank":null),W(n,"download",s=window.__is_colab__?null:l[0].orig_name||l[0].path)},m(_,i){r&&r.m(_,i),F(_,e,i),F(_,n,i),q(t,n,null),o=!0},p(_,i){_[10]?r&&(le(),v(r,1,1,()=>{r=null}),ne()):r?(r.p(_,i),i&1024&&w(r,1)):(r=Ce(_),r.c(),w(r,1),r.m(e.parentNode,e));const u={};i&32&&(u.label=_[5]("common.download")),t.$set(u),(!o||i&16384)&&W(n,"href",_[14]),(!o||i&1&&s!==(s=window.__is_colab__?null:_[0].orig_name||_[0].path))&&W(n,"download",s)},i(_){o||(w(r),w(t.$$.fragment,_),o=!0)},o(_){v(r),v(t.$$.fragment,_),o=!1},d(_){_&&(G(e),G(n)),r&&r.d(_),T(t)}}}function lt(l){let e,n,t,s;function o(i){l[20](i)}var r=l[13];function _(i,u){let a={value:i[0],display_mode:i[1],clear_color:i[2],camera_position:i[8],zoom_speed:i[6],pan_speed:i[7]};return i[14]!==void 0&&(a.resolved_url=i[14]),{props:a}}return r&&(e=ie(r,_(l)),l[19](e),Y.push(()=>se(e,"resolved_url",o))),{c(){e&&O(e.$$.fragment),t=be()},m(i,u){e&&q(e,i,u),F(i,t,u),s=!0},p(i,u){if(u&8192&&r!==(r=i[13])){if(e){le();const a=e;v(a.$$.fragment,1,0,()=>{T(a,1)}),ne()}r?(e=ie(r,_(i)),i[19](e),Y.push(()=>se(e,"resolved_url",o)),O(e.$$.fragment),w(e.$$.fragment,1),q(e,t.parentNode,t)):e=null}else if(r){const a={};u&1&&(a.value=i[0]),u&2&&(a.display_mode=i[1]),u&4&&(a.clear_color=i[2]),u&256&&(a.camera_position=i[8]),u&64&&(a.zoom_speed=i[6]),u&128&&(a.pan_speed=i[7]),!n&&u&16384&&(n=!0,a.resolved_url=i[14],Be(()=>n=!1)),e.$set(a)}},i(i){s||(e&&w(e.$$.fragment,i),s=!0)},o(i){e&&v(e.$$.fragment,i),s=!1},d(i){i&&G(t),l[19](null),e&&T(e,i)}}}function st(l){let e,n,t,s;function o(i){l[18](i)}var r=l[12];function _(i,u){let a={value:i[0],zoom_speed:i[6],pan_speed:i[7]};return i[14]!==void 0&&(a.resolved_url=i[14]),{props:a}}return r&&(e=ie(r,_(l)),Y.push(()=>se(e,"resolved_url",o))),{c(){e&&O(e.$$.fragment),t=be()},m(i,u){e&&q(e,i,u),F(i,t,u),s=!0},p(i,u){if(u&4096&&r!==(r=i[12])){if(e){le();const a=e;v(a.$$.fragment,1,0,()=>{T(a,1)}),ne()}r?(e=ie(r,_(i)),Y.push(()=>se(e,"resolved_url",o)),O(e.$$.fragment),w(e.$$.fragment,1),q(e,t.parentNode,t)):e=null}else if(r){const a={};u&1&&(a.value=i[0]),u&64&&(a.zoom_speed=i[6]),u&128&&(a.pan_speed=i[7]),!n&&u&16384&&(n=!0,a.resolved_url=i[14],Be(()=>n=!1)),e.$set(a)}},i(i){s||(e&&w(e.$$.fragment,i),s=!0)},o(i){e&&v(e.$$.fragment,i),s=!1},d(i){i&&G(t),e&&T(e,i)}}}function it(l){let e,n,t,s;e=new pe({props:{show_label:l[4],Icon:re,label:l[3]||l[5]("3D_model.3d_model")}});let o=l[0]&&De(l);return{c(){O(e.$$.fragment),n=we(),o&&o.c(),t=be()},m(r,_){q(e,r,_),F(r,n,_),o&&o.m(r,_),F(r,t,_),s=!0},p(r,[_]){const i={};_&16&&(i.show_label=r[4]),_&40&&(i.label=r[3]||r[5]("3D_model.3d_model")),e.$set(i),r[0]?o?(o.p(r,_),_&1&&w(o,1)):(o=De(r),o.c(),w(o,1),o.m(t.parentNode,t)):o&&(le(),v(o,1,1,()=>{o=null}),ne())},i(r){s||(w(e.$$.fragment,r),w(o),s=!0)},o(r){v(e.$$.fragment,r),v(o),s=!1},d(r){r&&(G(n),G(t)),T(e,r),o&&o.d(r)}}}async function ot(){return(await ae(()=>import("./Canvas3D-CLFmIvhV.js"),__vite__mapDeps([0,1,2,3,4,5]),import.meta.url)).default}async function at(){return(await ae(()=>import("./Canvas3DGS-Cm1kuxqL.js"),__vite__mapDeps([6,3,4,5]),import.meta.url)).default}function rt(l,e,n){let{value:t}=e,{display_mode:s="solid"}=e,{clear_color:o=[0,0,0,0]}=e,{label:r=""}=e,{show_label:_}=e,{i18n:i}=e,{zoom_speed:u=1}=e,{pan_speed:a=1}=e,{camera_position:f=[null,null,null]}=e,{has_change_history:d=!1}=e,m={camera_position:f,zoom_speed:u,pan_speed:a},S=!1,k,b,E;function L(){E?.reset_camera_position()}let M;const N=()=>L();function A(g){M=g,n(14,M)}function P(g){Y[g?"unshift":"push"](()=>{E=g,n(11,E)})}function V(g){M=g,n(14,M)}return l.$$set=g=>{"value"in g&&n(0,t=g.value),"display_mode"in g&&n(1,s=g.display_mode),"clear_color"in g&&n(2,o=g.clear_color),"label"in g&&n(3,r=g.label),"show_label"in g&&n(4,_=g.show_label),"i18n"in g&&n(5,i=g.i18n),"zoom_speed"in g&&n(6,u=g.zoom_speed),"pan_speed"in g&&n(7,a=g.pan_speed),"camera_position"in g&&n(8,f=g.camera_position),"has_change_history"in g&&n(9,d=g.has_change_history)},l.$$.update=()=>{l.$$.dirty&1025&&t&&(n(10,S=t.path.endsWith(".splat")||t.path.endsWith(".ply")),S?at().then(g=>{n(12,k=g)}):ot().then(g=>{n(13,b=g)})),l.$$.dirty&68032&&(!X(m.camera_position,f)||m.zoom_speed!==u||m.pan_speed!==a)&&(E?.update_camera(f,u,a),n(16,m={camera_position:f,zoom_speed:u,pan_speed:a}))},[t,s,o,r,_,i,u,a,f,d,S,E,k,b,M,L,m,N,A,P,V]}class _t extends Ze{constructor(e){super(),et(this,e,rt,it,tt,{value:0,display_mode:1,clear_color:2,label:3,show_label:4,i18n:5,zoom_speed:6,pan_speed:7,camera_position:8,has_change_history:9})}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),U()}get display_mode(){return this.$$.ctx[1]}set display_mode(e){this.$$set({display_mode:e}),U()}get clear_color(){return this.$$.ctx[2]}set clear_color(e){this.$$set({clear_color:e}),U()}get label(){return this.$$.ctx[3]}set label(e){this.$$set({label:e}),U()}get show_label(){return this.$$.ctx[4]}set show_label(e){this.$$set({show_label:e}),U()}get i18n(){return this.$$.ctx[5]}set i18n(e){this.$$set({i18n:e}),U()}get zoom_speed(){return this.$$.ctx[6]}set zoom_speed(e){this.$$set({zoom_speed:e}),U()}get pan_speed(){return this.$$.ctx[7]}set pan_speed(e){this.$$set({pan_speed:e}),U()}get camera_position(){return this.$$.ctx[8]}set camera_position(e){this.$$set({camera_position:e}),U()}get has_change_history(){return this.$$.ctx[9]}set has_change_history(e){this.$$set({has_change_history:e}),U()}}const ut=_t,{SvelteComponent:ft,add_flush_callback:Ie,append:ct,attr:mt,bind:Se,binding_callbacks:ge,bubble:dt,check_outros:_e,construct_svelte_component:oe,create_component:H,create_slot:ht,destroy_component:J,detach:Z,element:gt,empty:ke,flush:$,get_all_dirty_from_scope:pt,get_slot_changes:bt,group_outros:ue,init:wt,insert:x,mount_component:K,safe_not_equal:kt,space:Ae,transition_in:C,transition_out:I,update_slot_base:$t}=window.__gradio__svelte__internal,{createEventDispatcher:vt,tick:Ee}=window.__gradio__svelte__internal;function zt(l){let e,n,t,s,o,r;n=new Qe({props:{undoable:!l[14],i18n:l[7]}}),n.$on("clear",l[20]),n.$on("undo",l[21]);const _=[It,Ct],i=[];function u(a,f){return a[14]?0:1}return s=u(l),o=i[s]=_[s](l),{c(){e=gt("div"),H(n.$$.fragment),t=Ae(),o.c(),mt(e,"class","input-model svelte-jub4pj")},m(a,f){x(a,e,f),K(n,e,null),ct(e,t),i[s].m(e,null),r=!0},p(a,f){const d={};f&16384&&(d.undoable=!a[14]),f&128&&(d.i18n=a[7]),n.$set(d);let m=s;s=u(a),s===m?i[s].p(a,f):(ue(),I(i[m],1,1,()=>{i[m]=null}),_e(),o=i[s],o?o.p(a,f):(o=i[s]=_[s](a),o.c()),C(o,1),o.m(e,null))},i(a){r||(C(n.$$.fragment,a),C(o),r=!0)},o(a){I(n.$$.fragment,a),I(o),r=!1},d(a){a&&Z(e),J(n),i[s].d()}}}function Dt(l){let e,n,t,s;function o(i){l[23](i)}function r(i){l[24](i)}let _={upload:l[12],stream_handler:l[13],root:l[6],max_file_size:l[10],filetype:[".stl",".obj",".gltf",".glb","model/obj",".splat",".ply"],aria_label:l[7]("model3d.drop_to_upload"),$$slots:{default:[St]},$$scope:{ctx:l}};return l[15]!==void 0&&(_.dragging=l[15]),l[1]!==void 0&&(_.uploading=l[1]),e=new Ke({props:_}),ge.push(()=>Se(e,"dragging",o)),ge.push(()=>Se(e,"uploading",r)),e.$on("load",l[19]),e.$on("error",l[25]),{c(){H(e.$$.fragment)},m(i,u){K(e,i,u),s=!0},p(i,u){const a={};u&4096&&(a.upload=i[12]),u&8192&&(a.stream_handler=i[13]),u&64&&(a.root=i[6]),u&1024&&(a.max_file_size=i[10]),u&128&&(a.aria_label=i[7]("model3d.drop_to_upload")),u&134217728&&(a.$$scope={dirty:u,ctx:i}),!n&&u&32768&&(n=!0,a.dragging=i[15],Ie(()=>n=!1)),!t&&u&2&&(t=!0,a.uploading=i[1],Ie(()=>t=!1)),e.$set(a)},i(i){s||(C(e.$$.fragment,i),s=!0)},o(i){I(e.$$.fragment,i),s=!1},d(i){J(e,i)}}}function Ct(l){let e,n,t;var s=l[17];function o(r,_){return{props:{value:r[0],display_mode:r[2],clear_color:r[3],camera_position:r[11],zoom_speed:r[8],pan_speed:r[9]}}}return s&&(e=oe(s,o(l)),l[26](e)),{c(){e&&H(e.$$.fragment),n=ke()},m(r,_){e&&K(e,r,_),x(r,n,_),t=!0},p(r,_){if(_&131072&&s!==(s=r[17])){if(e){ue();const i=e;I(i.$$.fragment,1,0,()=>{J(i,1)}),_e()}s?(e=oe(s,o(r)),r[26](e),H(e.$$.fragment),C(e.$$.fragment,1),K(e,n.parentNode,n)):e=null}else if(s){const i={};_&1&&(i.value=r[0]),_&4&&(i.display_mode=r[2]),_&8&&(i.clear_color=r[3]),_&2048&&(i.camera_position=r[11]),_&256&&(i.zoom_speed=r[8]),_&512&&(i.pan_speed=r[9]),e.$set(i)}},i(r){t||(e&&C(e.$$.fragment,r),t=!0)},o(r){e&&I(e.$$.fragment,r),t=!1},d(r){r&&Z(n),l[26](null),e&&J(e,r)}}}function It(l){let e,n,t;var s=l[16];function o(r,_){return{props:{value:r[0],zoom_speed:r[8],pan_speed:r[9]}}}return s&&(e=oe(s,o(l))),{c(){e&&H(e.$$.fragment),n=ke()},m(r,_){e&&K(e,r,_),x(r,n,_),t=!0},p(r,_){if(_&65536&&s!==(s=r[16])){if(e){ue();const i=e;I(i.$$.fragment,1,0,()=>{J(i,1)}),_e()}s?(e=oe(s,o(r)),H(e.$$.fragment),C(e.$$.fragment,1),K(e,n.parentNode,n)):e=null}else if(s){const i={};_&1&&(i.value=r[0]),_&256&&(i.zoom_speed=r[8]),_&512&&(i.pan_speed=r[9]),e.$set(i)}},i(r){t||(e&&C(e.$$.fragment,r),t=!0)},o(r){e&&I(e.$$.fragment,r),t=!1},d(r){r&&Z(n),e&&J(e,r)}}}function St(l){let e;const n=l[22].default,t=ht(n,l,l[27],null);return{c(){t&&t.c()},m(s,o){t&&t.m(s,o),e=!0},p(s,o){t&&t.p&&(!e||o&134217728)&&$t(t,n,s,s[27],e?bt(n,s[27],o,null):pt(s[27]),null)},i(s){e||(C(t,s),e=!0)},o(s){I(t,s),e=!1},d(s){t&&t.d(s)}}}function Et(l){let e,n,t,s,o,r;e=new pe({props:{show_label:l[5],Icon:re,label:l[4]||"3D Model"}});const _=[Dt,zt],i=[];function u(a,f){return a[0]===null?0:1}return t=u(l),s=i[t]=_[t](l),{c(){H(e.$$.fragment),n=Ae(),s.c(),o=ke()},m(a,f){K(e,a,f),x(a,n,f),i[t].m(a,f),x(a,o,f),r=!0},p(a,[f]){const d={};f&32&&(d.show_label=a[5]),f&16&&(d.label=a[4]||"3D Model"),e.$set(d);let m=t;t=u(a),t===m?i[t].p(a,f):(ue(),I(i[m],1,1,()=>{i[m]=null}),_e(),s=i[t],s?s.p(a,f):(s=i[t]=_[t](a),s.c()),C(s,1),s.m(o.parentNode,o))},i(a){r||(C(e.$$.fragment,a),C(s),r=!0)},o(a){I(e.$$.fragment,a),I(s),r=!1},d(a){a&&(Z(n),Z(o)),J(e,a),i[t].d(a)}}}async function Mt(){return(await ae(()=>import("./Canvas3D-CLFmIvhV.js"),__vite__mapDeps([0,1,2,3,4,5]),import.meta.url)).default}async function Ut(){return(await ae(()=>import("./Canvas3DGS-Cm1kuxqL.js"),__vite__mapDeps([6,3,4,5]),import.meta.url)).default}function yt(l,e,n){let{$$slots:t={},$$scope:s}=e,{value:o}=e,{display_mode:r="solid"}=e,{clear_color:_=[0,0,0,0]}=e,{label:i=""}=e,{show_label:u}=e,{root:a}=e,{i18n:f}=e,{zoom_speed:d=1}=e,{pan_speed:m=1}=e,{max_file_size:S=null}=e,{uploading:k=!1}=e,{camera_position:b=[null,null,null]}=e,{upload:E}=e,{stream_handler:L}=e;async function M({detail:h}){n(0,o=h),await Ee(),R("change",o),R("load",o)}async function N(){n(0,o=null),await Ee(),R("clear"),R("change")}let A=!1,P,V,g;async function fe(){g?.reset_camera_position()}const R=vt();let Q=!1;function ce(h){Q=h,n(15,Q)}function me(h){k=h,n(1,k)}function de(h){dt.call(this,l,h)}function he(h){ge[h?"unshift":"push"](()=>{g=h,n(18,g)})}return l.$$set=h=>{"value"in h&&n(0,o=h.value),"display_mode"in h&&n(2,r=h.display_mode),"clear_color"in h&&n(3,_=h.clear_color),"label"in h&&n(4,i=h.label),"show_label"in h&&n(5,u=h.show_label),"root"in h&&n(6,a=h.root),"i18n"in h&&n(7,f=h.i18n),"zoom_speed"in h&&n(8,d=h.zoom_speed),"pan_speed"in h&&n(9,m=h.pan_speed),"max_file_size"in h&&n(10,S=h.max_file_size),"uploading"in h&&n(1,k=h.uploading),"camera_position"in h&&n(11,b=h.camera_position),"upload"in h&&n(12,E=h.upload),"stream_handler"in h&&n(13,L=h.stream_handler),"$$scope"in h&&n(27,s=h.$$scope)},l.$$.update=()=>{l.$$.dirty&16385&&o&&(n(14,A=o.path.endsWith(".splat")||o.path.endsWith(".ply")),A?Ut().then(h=>{n(16,P=h)}):Mt().then(h=>{n(17,V=h)})),l.$$.dirty&32768&&R("drag",Q)},[o,k,r,_,i,u,a,f,d,m,S,b,E,L,A,Q,P,V,g,M,N,fe,t,ce,me,de,he,s]}class Bt extends ft{constructor(e){super(),wt(this,e,yt,Et,kt,{value:0,display_mode:2,clear_color:3,label:4,show_label:5,root:6,i18n:7,zoom_speed:8,pan_speed:9,max_file_size:10,uploading:1,camera_position:11,upload:12,stream_handler:13})}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),$()}get display_mode(){return this.$$.ctx[2]}set display_mode(e){this.$$set({display_mode:e}),$()}get clear_color(){return this.$$.ctx[3]}set clear_color(e){this.$$set({clear_color:e}),$()}get label(){return this.$$.ctx[4]}set label(e){this.$$set({label:e}),$()}get show_label(){return this.$$.ctx[5]}set show_label(e){this.$$set({show_label:e}),$()}get root(){return this.$$.ctx[6]}set root(e){this.$$set({root:e}),$()}get i18n(){return this.$$.ctx[7]}set i18n(e){this.$$set({i18n:e}),$()}get zoom_speed(){return this.$$.ctx[8]}set zoom_speed(e){this.$$set({zoom_speed:e}),$()}get pan_speed(){return this.$$.ctx[9]}set pan_speed(e){this.$$set({pan_speed:e}),$()}get max_file_size(){return this.$$.ctx[10]}set max_file_size(e){this.$$set({max_file_size:e}),$()}get uploading(){return this.$$.ctx[1]}set uploading(e){this.$$set({uploading:e}),$()}get camera_position(){return this.$$.ctx[11]}set camera_position(e){this.$$set({camera_position:e}),$()}get upload(){return this.$$.ctx[12]}set upload(e){this.$$set({upload:e}),$()}get stream_handler(){return this.$$.ctx[13]}set stream_handler(e){this.$$set({stream_handler:e}),$()}}const jt=Bt,{SvelteComponent:At,add_flush_callback:Lt,assign:Le,bind:Nt,binding_callbacks:Ot,check_outros:Ne,create_component:y,destroy_component:B,detach:ee,empty:Oe,flush:p,get_spread_object:Te,get_spread_update:qe,group_outros:Pe,init:Tt,insert:te,mount_component:j,safe_not_equal:qt,space:$e,transition_in:z,transition_out:D}=window.__gradio__svelte__internal;function Pt(l){let e,n;return e=new Ue({props:{visible:l[5],variant:l[0]===null?"dashed":"solid",border_mode:l[20]?"focus":"base",padding:!1,elem_id:l[3],elem_classes:l[4],container:l[11],scale:l[12],min_width:l[13],height:l[15],$$slots:{default:[Wt]},$$scope:{ctx:l}}}),{c(){y(e.$$.fragment)},m(t,s){j(e,t,s),n=!0},p(t,s){const o={};s[0]&32&&(o.visible=t[5]),s[0]&1&&(o.variant=t[0]===null?"dashed":"solid"),s[0]&1048576&&(o.border_mode=t[20]?"focus":"base"),s[0]&8&&(o.elem_id=t[3]),s[0]&16&&(o.elem_classes=t[4]),s[0]&2048&&(o.container=t[11]),s[0]&4096&&(o.scale=t[12]),s[0]&8192&&(o.min_width=t[13]),s[0]&32768&&(o.height=t[15]),s[0]&1787847|s[1]&8&&(o.$$scope={dirty:s,ctx:t}),e.$set(o)},i(t){n||(z(e.$$.fragment,t),n=!0)},o(t){D(e.$$.fragment,t),n=!1},d(t){B(e,t)}}}function Vt(l){let e,n;return e=new Ue({props:{visible:l[5],variant:l[0]===null?"dashed":"solid",border_mode:l[20]?"focus":"base",padding:!1,elem_id:l[3],elem_classes:l[4],container:l[11],scale:l[12],min_width:l[13],height:l[15],$$slots:{default:[Jt]},$$scope:{ctx:l}}}),{c(){y(e.$$.fragment)},m(t,s){j(e,t,s),n=!0},p(t,s){const o={};s[0]&32&&(o.visible=t[5]),s[0]&1&&(o.variant=t[0]===null?"dashed":"solid"),s[0]&1048576&&(o.border_mode=t[20]?"focus":"base"),s[0]&8&&(o.elem_id=t[3]),s[0]&16&&(o.elem_classes=t[4]),s[0]&2048&&(o.container=t[11]),s[0]&4096&&(o.scale=t[12]),s[0]&8192&&(o.min_width=t[13]),s[0]&32768&&(o.height=t[15]),s[0]&214919|s[1]&8&&(o.$$scope={dirty:s,ctx:t}),e.$set(o)},i(t){n||(z(e.$$.fragment,t),n=!0)},o(t){D(e.$$.fragment,t),n=!1},d(t){B(e,t)}}}function Rt(l){let e,n;return e=new Ye({props:{i18n:l[14].i18n,type:"file"}}),{c(){y(e.$$.fragment)},m(t,s){j(e,t,s),n=!0},p(t,s){const o={};s[0]&16384&&(o.i18n=t[14].i18n),e.$set(o)},i(t){n||(z(e.$$.fragment,t),n=!0)},o(t){D(e.$$.fragment,t),n=!1},d(t){B(e,t)}}}function Wt(l){let e,n,t,s,o;const r=[{autoscroll:l[14].autoscroll},{i18n:l[14].i18n},l[1]];let _={};for(let a=0;a<r.length;a+=1)_=Le(_,r[a]);e=new ye({props:_}),e.$on("clear_status",l[24]);function i(a){l[27](a)}let u={label:l[9],show_label:l[10],root:l[6],display_mode:l[7],clear_color:l[8],value:l[0],camera_position:l[17],zoom_speed:l[16],i18n:l[14].i18n,max_file_size:l[14].max_file_size,upload:l[25],stream_handler:l[26],$$slots:{default:[Rt]},$$scope:{ctx:l}};return l[19]!==void 0&&(u.uploading=l[19]),t=new jt({props:u}),Ot.push(()=>Nt(t,"uploading",i)),t.$on("change",l[28]),t.$on("drag",l[29]),t.$on("change",l[30]),t.$on("clear",l[31]),t.$on("load",l[32]),t.$on("error",l[33]),{c(){y(e.$$.fragment),n=$e(),y(t.$$.fragment)},m(a,f){j(e,a,f),te(a,n,f),j(t,a,f),o=!0},p(a,f){const d=f[0]&16386?qe(r,[f[0]&16384&&{autoscroll:a[14].autoscroll},f[0]&16384&&{i18n:a[14].i18n},f[0]&2&&Te(a[1])]):{};e.$set(d);const m={};f[0]&512&&(m.label=a[9]),f[0]&1024&&(m.show_label=a[10]),f[0]&64&&(m.root=a[6]),f[0]&128&&(m.display_mode=a[7]),f[0]&256&&(m.clear_color=a[8]),f[0]&1&&(m.value=a[0]),f[0]&131072&&(m.camera_position=a[17]),f[0]&65536&&(m.zoom_speed=a[16]),f[0]&16384&&(m.i18n=a[14].i18n),f[0]&16384&&(m.max_file_size=a[14].max_file_size),f[0]&16384&&(m.upload=a[25]),f[0]&16384&&(m.stream_handler=a[26]),f[0]&16384|f[1]&8&&(m.$$scope={dirty:f,ctx:a}),!s&&f[0]&524288&&(s=!0,m.uploading=a[19],Lt(()=>s=!1)),t.$set(m)},i(a){o||(z(e.$$.fragment,a),z(t.$$.fragment,a),o=!0)},o(a){D(e.$$.fragment,a),D(t.$$.fragment,a),o=!1},d(a){a&&ee(n),B(e,a),B(t,a)}}}function Gt(l){let e,n,t,s;return e=new pe({props:{show_label:l[10],Icon:re,label:l[9]||"3D Model"}}),t=new Xe({props:{unpadded_box:!0,size:"large",$$slots:{default:[Ht]},$$scope:{ctx:l}}}),{c(){y(e.$$.fragment),n=$e(),y(t.$$.fragment)},m(o,r){j(e,o,r),te(o,n,r),j(t,o,r),s=!0},p(o,r){const _={};r[0]&1024&&(_.show_label=o[10]),r[0]&512&&(_.label=o[9]||"3D Model"),e.$set(_);const i={};r[1]&8&&(i.$$scope={dirty:r,ctx:o}),t.$set(i)},i(o){s||(z(e.$$.fragment,o),z(t.$$.fragment,o),s=!0)},o(o){D(e.$$.fragment,o),D(t.$$.fragment,o),s=!1},d(o){o&&ee(n),B(e,o),B(t,o)}}}function Ft(l){let e,n;return e=new ut({props:{value:l[0],i18n:l[14].i18n,display_mode:l[7],clear_color:l[8],label:l[9],show_label:l[10],camera_position:l[17],zoom_speed:l[16],has_change_history:l[2]}}),{c(){y(e.$$.fragment)},m(t,s){j(e,t,s),n=!0},p(t,s){const o={};s[0]&1&&(o.value=t[0]),s[0]&16384&&(o.i18n=t[14].i18n),s[0]&128&&(o.display_mode=t[7]),s[0]&256&&(o.clear_color=t[8]),s[0]&512&&(o.label=t[9]),s[0]&1024&&(o.show_label=t[10]),s[0]&131072&&(o.camera_position=t[17]),s[0]&65536&&(o.zoom_speed=t[16]),s[0]&4&&(o.has_change_history=t[2]),e.$set(o)},i(t){n||(z(e.$$.fragment,t),n=!0)},o(t){D(e.$$.fragment,t),n=!1},d(t){B(e,t)}}}function Ht(l){let e,n;return e=new re({}),{c(){y(e.$$.fragment)},m(t,s){j(e,t,s),n=!0},i(t){n||(z(e.$$.fragment,t),n=!0)},o(t){D(e.$$.fragment,t),n=!1},d(t){B(e,t)}}}function Jt(l){let e,n,t,s,o,r;const _=[{autoscroll:l[14].autoscroll},{i18n:l[14].i18n},l[1]];let i={};for(let d=0;d<_.length;d+=1)i=Le(i,_[d]);e=new ye({props:i}),e.$on("clear_status",l[23]);const u=[Ft,Gt],a=[];function f(d,m){return d[0]&&d[21]?0:1}return t=f(l),s=a[t]=u[t](l),{c(){y(e.$$.fragment),n=$e(),s.c(),o=Oe()},m(d,m){j(e,d,m),te(d,n,m),a[t].m(d,m),te(d,o,m),r=!0},p(d,m){const S=m[0]&16386?qe(_,[m[0]&16384&&{autoscroll:d[14].autoscroll},m[0]&16384&&{i18n:d[14].i18n},m[0]&2&&Te(d[1])]):{};e.$set(S);let k=t;t=f(d),t===k?a[t].p(d,m):(Pe(),D(a[k],1,1,()=>{a[k]=null}),Ne(),s=a[t],s?s.p(d,m):(s=a[t]=u[t](d),s.c()),z(s,1),s.m(o.parentNode,o))},i(d){r||(z(e.$$.fragment,d),z(s),r=!0)},o(d){D(e.$$.fragment,d),D(s),r=!1},d(d){d&&(ee(n),ee(o)),B(e,d),a[t].d(d)}}}function Kt(l){let e,n,t,s;const o=[Vt,Pt],r=[];function _(i,u){return i[18]?1:0}return e=_(l),n=r[e]=o[e](l),{c(){n.c(),t=Oe()},m(i,u){r[e].m(i,u),te(i,t,u),s=!0},p(i,u){let a=e;e=_(i),e===a?r[e].p(i,u):(Pe(),D(r[a],1,1,()=>{r[a]=null}),Ne(),n=r[e],n?n.p(i,u):(n=r[e]=o[e](i),n.c()),z(n,1),n.m(t.parentNode,t))},i(i){s||(z(n),s=!0)},o(i){D(n),s=!1},d(i){i&&ee(t),r[e].d(i)}}}function Qt(l,e,n){let{elem_id:t=""}=e,{elem_classes:s=[]}=e,{visible:o=!0}=e,{value:r=null}=e,{root:_}=e,{display_mode:i="solid"}=e,{clear_color:u}=e,{loading_status:a}=e,{label:f}=e,{show_label:d}=e,{container:m=!0}=e,{scale:S=null}=e,{min_width:k=void 0}=e,{gradio:b}=e,{height:E=void 0}=e,{zoom_speed:L=1}=e,{input_ready:M}=e,N=!1,{has_change_history:A=!1}=e,{camera_position:P=[null,null,null]}=e,{interactive:V}=e,g=!1;const fe=typeof window<"u",R=()=>b.dispatch("clear_status",a),Q=()=>b.dispatch("clear_status",a),ce=(...c)=>b.client.upload(...c),me=(...c)=>b.client.stream(...c);function de(c){N=c,n(19,N)}const he=({detail:c})=>n(0,r=c),h=({detail:c})=>n(20,g=c),Ve=({detail:c})=>{b.dispatch("change",c),n(2,A=!0)},Re=()=>{n(0,r=null),b.dispatch("clear")},We=({detail:c})=>{n(0,r=c),b.dispatch("upload")},Ge=({detail:c})=>{n(1,a=a||{}),n(1,a.status="error",a),b.dispatch("error",c)};return l.$$set=c=>{"elem_id"in c&&n(3,t=c.elem_id),"elem_classes"in c&&n(4,s=c.elem_classes),"visible"in c&&n(5,o=c.visible),"value"in c&&n(0,r=c.value),"root"in c&&n(6,_=c.root),"display_mode"in c&&n(7,i=c.display_mode),"clear_color"in c&&n(8,u=c.clear_color),"loading_status"in c&&n(1,a=c.loading_status),"label"in c&&n(9,f=c.label),"show_label"in c&&n(10,d=c.show_label),"container"in c&&n(11,m=c.container),"scale"in c&&n(12,S=c.scale),"min_width"in c&&n(13,k=c.min_width),"gradio"in c&&n(14,b=c.gradio),"height"in c&&n(15,E=c.height),"zoom_speed"in c&&n(16,L=c.zoom_speed),"input_ready"in c&&n(22,M=c.input_ready),"has_change_history"in c&&n(2,A=c.has_change_history),"camera_position"in c&&n(17,P=c.camera_position),"interactive"in c&&n(18,V=c.interactive)},l.$$.update=()=>{l.$$.dirty[0]&524288&&n(22,M=!N)},[r,a,A,t,s,o,_,i,u,f,d,m,S,k,b,E,L,P,V,N,g,fe,M,R,Q,ce,me,de,he,h,Ve,Re,We,Ge]}class $n extends At{constructor(e){super(),Tt(this,e,Qt,Kt,qt,{elem_id:3,elem_classes:4,visible:5,value:0,root:6,display_mode:7,clear_color:8,loading_status:1,label:9,show_label:10,container:11,scale:12,min_width:13,gradio:14,height:15,zoom_speed:16,input_ready:22,has_change_history:2,camera_position:17,interactive:18},null,[-1,-1])}get elem_id(){return this.$$.ctx[3]}set elem_id(e){this.$$set({elem_id:e}),p()}get elem_classes(){return this.$$.ctx[4]}set elem_classes(e){this.$$set({elem_classes:e}),p()}get visible(){return this.$$.ctx[5]}set visible(e){this.$$set({visible:e}),p()}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),p()}get root(){return this.$$.ctx[6]}set root(e){this.$$set({root:e}),p()}get display_mode(){return this.$$.ctx[7]}set display_mode(e){this.$$set({display_mode:e}),p()}get clear_color(){return this.$$.ctx[8]}set clear_color(e){this.$$set({clear_color:e}),p()}get loading_status(){return this.$$.ctx[1]}set loading_status(e){this.$$set({loading_status:e}),p()}get label(){return this.$$.ctx[9]}set label(e){this.$$set({label:e}),p()}get show_label(){return this.$$.ctx[10]}set show_label(e){this.$$set({show_label:e}),p()}get container(){return this.$$.ctx[11]}set container(e){this.$$set({container:e}),p()}get scale(){return this.$$.ctx[12]}set scale(e){this.$$set({scale:e}),p()}get min_width(){return this.$$.ctx[13]}set min_width(e){this.$$set({min_width:e}),p()}get gradio(){return this.$$.ctx[14]}set gradio(e){this.$$set({gradio:e}),p()}get height(){return this.$$.ctx[15]}set height(e){this.$$set({height:e}),p()}get zoom_speed(){return this.$$.ctx[16]}set zoom_speed(e){this.$$set({zoom_speed:e}),p()}get input_ready(){return this.$$.ctx[22]}set input_ready(e){this.$$set({input_ready:e}),p()}get has_change_history(){return this.$$.ctx[2]}set has_change_history(e){this.$$set({has_change_history:e}),p()}get camera_position(){return this.$$.ctx[17]}set camera_position(e){this.$$set({camera_position:e}),p()}get interactive(){return this.$$.ctx[18]}set interactive(e){this.$$set({interactive:e}),p()}}export{Dn as BaseExample,ut as BaseModel3D,jt as BaseModel3DUpload,$n as default};
//# sourceMappingURL=Index-CxIzfvY9.js.map
