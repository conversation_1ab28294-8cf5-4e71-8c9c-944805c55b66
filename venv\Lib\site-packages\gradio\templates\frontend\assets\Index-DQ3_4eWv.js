import{t as O,E as Te}from"./tinycolor-DhRrpXkc.js";/* empty css                                                        */import{B as Le}from"./BlockTitle-C6qeQAMx.js";import"./MarkdownCode.svelte_svelte_type_style_lang-Bl8p72Ow.js";import"./index-DJ2rNx9E.js";import{B as Pe}from"./Block-CJdXVpa7.js";import{S as Ge}from"./index-DYtg3pip.js";import"./StreamingBar.svelte_svelte_type_style_lang-DlRzv56K.js";import{default as qt}from"./Example-BaLyJYAe.js";import"./Info-D7HP20hi.js";import"./MarkdownCode-Cdb8e5t4.js";import"./prism-python-CeMtt1IT.js";import"./svelte/svelte.js";import"./IconButton-C_HS7fTi.js";import"./Clear-By3xiIwg.js";function Ye(t,e){const n=l=>{t&&!t.contains(l.target)&&!l.defaultPrevented&&e(l)};return document.addEventListener("mousedown",n,!0),{destroy(){document.removeEventListener("mousedown",n,!0)}}}function ee(t){const e=t.s,n=t.v;let l=e*n;const u=t.h/60;let a=l*(1-Math.abs(u%2-1));const _=n-l;l=l+_,a=a+_;const m=Math.floor(u)%6,w=[l,a,_,_,a,l][m],f=[a,l,l,a,_,_][m],r=[_,_,a,l,l,a][m];return`rgba(${w*255}, ${f*255}, ${r*255}, ${t.a})`}function je(t,e){return e==="hex"?O(t).toHexString():e==="rgb"?O(t).toRgbString():O(t).toHslString()}const{SvelteComponent:Ie,action_destroyer:Ne,append:z,attr:S,binding_callbacks:te,bubble:ne,check_outros:de,create_component:me,destroy_component:ge,destroy_each:Ue,detach:U,element:E,empty:Ae,ensure_array_like:le,flush:Y,globals:Fe,group_outros:be,init:Je,insert:A,listen:q,mount_component:we,run_all:ve,safe_not_equal:Ke,set_data:Oe,set_input_value:ie,set_style:R,space:N,text:Qe,toggle_class:se,transition_in:j,transition_out:F}=window.__gradio__svelte__internal,{window:oe}=Fe,{createEventDispatcher:Ve,afterUpdate:We,onMount:Ze,tick:ye}=window.__gradio__svelte__internal;function ue(t,e,n){const l=t.slice();return l[6]=e[n][0],l[1]=e[n][1],l}function xe(t){let e;return{c(){e=Qe(t[6])},m(n,l){A(n,e,l)},p(n,l){l[0]&64&&Oe(e,n[6])},d(n){n&&U(e)}}}function ae(t){let e,n,l,u=`translate(${t[11][0]}px,${t[11][1]}px)`,a,_,m,w=`translateX(${t[13]}px)`,f,r,c,o,d,p,h,L,b,P,X,D,G,C,g=t[8]&&re(),T=le(t[20]),v=[];for(let i=0;i<T.length;i+=1)v[i]=_e(ue(t,T,i));return{c(){e=E("div"),n=E("div"),l=E("div"),a=N(),_=E("div"),m=E("div"),f=N(),r=E("div"),c=E("button"),o=N(),d=E("div"),p=E("div"),h=E("input"),L=N(),b=E("button"),g&&g.c(),P=N(),X=E("div");for(let i=0;i<v.length;i+=1)v[i].c();S(l,"class","marker svelte-1oxhzww"),R(l,"transform",u),R(l,"background",t[1]),S(n,"class","color-gradient svelte-1oxhzww"),R(n,"--hue",t[12]),S(m,"class","marker svelte-1oxhzww"),R(m,"background","hsl("+t[12]+", 100%, 50%)"),R(m,"transform",w),S(_,"class","hue-slider svelte-1oxhzww"),S(c,"class","swatch svelte-1oxhzww"),R(c,"background",t[1]),S(h,"type","text"),S(h,"class","svelte-1oxhzww"),S(b,"class","eyedropper svelte-1oxhzww"),S(p,"class","input-wrap svelte-1oxhzww"),S(X,"class","buttons svelte-1oxhzww"),S(r,"class","input svelte-1oxhzww"),S(e,"class","color-picker svelte-1oxhzww")},m(i,M){A(i,e,M),z(e,n),z(n,l),t[27](n),z(e,a),z(e,_),z(_,m),t[28](_),z(e,f),z(e,r),z(r,c),z(r,o),z(r,d),z(d,p),z(p,h),ie(h,t[7]),z(p,L),z(p,b),g&&g.m(b,null),z(d,P),z(d,X);for(let k=0;k<v.length;k+=1)v[k]&&v[k].m(X,null);D=!0,G||(C=[q(n,"mousedown",t[15]),q(_,"mousedown",t[14]),q(c,"click",t[22]),q(h,"input",t[29]),q(h,"change",t[30]),q(b,"click",t[19]),q(e,"focus",t[24]),q(e,"blur",t[25]),Ne(Ye.call(null,e,t[21]))],G=!0)},p(i,M){if(M[0]&2048&&u!==(u=`translate(${i[11][0]}px,${i[11][1]}px)`)&&R(l,"transform",u),M[0]&2&&R(l,"background",i[1]),(!D||M[0]&4096)&&R(n,"--hue",i[12]),M[0]&4096&&R(m,"background","hsl("+i[12]+", 100%, 50%)"),M[0]&8192&&w!==(w=`translateX(${i[13]}px)`)&&R(m,"transform",w),M[0]&2&&R(c,"background",i[1]),M[0]&128&&h.value!==i[7]&&ie(h,i[7]),i[8]?g?M[0]&256&&j(g,1):(g=re(),g.c(),j(g,1),g.m(b,null)):g&&(be(),F(g,1,1,()=>{g=null}),de()),M[0]&1048577){T=le(i[20]);let k;for(k=0;k<T.length;k+=1){const J=ue(i,T,k);v[k]?v[k].p(J,M):(v[k]=_e(J),v[k].c(),v[k].m(X,null))}for(;k<v.length;k+=1)v[k].d(1);v.length=T.length}},i(i){D||(j(g),D=!0)},o(i){F(g),D=!1},d(i){i&&U(e),t[27](null),t[28](null),g&&g.d(),Ue(v,i),G=!1,ve(C)}}}function re(t){let e,n;return e=new Te({}),{c(){me(e.$$.fragment)},m(l,u){we(e,l,u),n=!0},i(l){n||(j(e.$$.fragment,l),n=!0)},o(l){F(e.$$.fragment,l),n=!1},d(l){ge(e,l)}}}function _e(t){let e,n,l;function u(){return t[31](t[1])}return{c(){e=E("button"),e.textContent=`${t[6]}`,S(e,"class","button svelte-1oxhzww"),se(e,"active",t[0]===t[1])},m(a,_){A(a,e,_),n||(l=q(e,"click",u),n=!0)},p(a,_){t=a,_[0]&1048577&&se(e,"active",t[0]===t[1])},d(a){a&&U(e),n=!1,l()}}}function $e(t){let e,n,l,u,a,_,m,w;e=new Le({props:{show_label:t[5],info:t[3],$$slots:{default:[xe]},$$scope:{ctx:t}}});let f=t[2]&&ae(t);return{c(){me(e.$$.fragment),n=N(),l=E("button"),u=N(),f&&f.c(),a=Ae(),S(l,"class","dialog-button svelte-1oxhzww"),l.disabled=t[4],R(l,"background",t[1])},m(r,c){we(e,r,c),A(r,n,c),A(r,l,c),A(r,u,c),f&&f.m(r,c),A(r,a,c),_=!0,m||(w=[q(oe,"mousemove",t[16]),q(oe,"mouseup",t[17]),q(l,"click",t[26])],m=!0)},p(r,c){const o={};c[0]&32&&(o.show_label=r[5]),c[0]&8&&(o.info=r[3]),c[0]&64|c[1]&4096&&(o.$$scope={dirty:c,ctx:r}),e.$set(o),(!_||c[0]&16)&&(l.disabled=r[4]),c[0]&2&&R(l,"background",r[1]),r[2]?f?(f.p(r,c),c[0]&4&&j(f,1)):(f=ae(r),f.c(),j(f,1),f.m(a.parentNode,a)):f&&(be(),F(f,1,1,()=>{f=null}),de())},i(r){_||(j(e.$$.fragment,r),j(f),_=!0)},o(r){F(e.$$.fragment,r),F(f),_=!1},d(r){r&&(U(n),U(l),U(u),U(a)),ge(e,r),f&&f.d(r),m=!1,ve(w)}}}function et(t,e,n){let l,{value:u="#000000"}=e,{value_is_output:a=!1}=e,{label:_}=e,{info:m=void 0}=e,{disabled:w=!1}=e,{show_label:f=!0}=e,{current_mode:r="hex"}=e,{dialog_open:c=!1}=e,o=!1,d,p;const h=Ve();let L=[0,0],b=null,P=!1,X=[0,0],D=0,G=0,C=null,g=!1;function T(s){C=s.currentTarget.getBoundingClientRect(),g=!0,v(s.clientX)}function v(s){if(!C)return;const H=Math.max(0,Math.min(s-C.left,C.width));n(13,G=H);const I=H/C.width*360;n(12,D=I),n(1,u=ee({h:I,s:X[0],v:X[1],a:1}))}function i(s,H){if(!b)return;const I=Math.max(0,Math.min(s-b.left,b.width)),K=Math.max(0,Math.min(H-b.top,b.height));n(11,L=[I,K]);const V={h:D*1,s:I/b.width,v:1-K/b.height,a:1};X=[V.s,V.v],n(1,u=ee(V))}function M(s){P=!0,b=s.currentTarget.getBoundingClientRect(),i(s.clientX,s.clientY)}function k(s){P&&i(s.clientX,s.clientY),g&&v(s.clientX)}function J(){P=!1,g=!1}async function Q(s){if(P||g||(await ye(),!s)||(!b&&d&&(b=d.getBoundingClientRect()),!C&&p&&(C=p.getBoundingClientRect()),!b||!C))return;const H=O(s).toHsv(),I=H.s*b.width,K=(1-H.v)*b.height;n(11,L=[I,K]),X=[H.s,H.v],n(12,D=H.h),n(13,G=H.h/360*C.width)}function ke(){new EyeDropper().open().then(H=>{n(1,u=H.sRGBHex)})}const pe=[["Hex","hex"],["RGB","rgb"],["HSL","hsl"]];Ze(async()=>{n(8,o=window!==void 0&&!!window.EyeDropper)});function ze(){n(2,c=!1)}function Be(){h("change",u),a||h("input")}We(()=>{n(23,a=!1)});function Ce(){h("selected",l),h("close")}function Me(s){ne.call(this,t,s)}function Se(s){ne.call(this,t,s)}const Ee=()=>{Q(u),n(2,c=!c)};function Re(s){te[s?"unshift":"push"](()=>{d=s,n(9,d)})}function He(s){te[s?"unshift":"push"](()=>{p=s,n(10,p)})}function Xe(){l=this.value,n(7,l),n(1,u),n(0,r)}const De=s=>n(1,u=s.currentTarget.value),qe=s=>n(0,r=s);return t.$$set=s=>{"value"in s&&n(1,u=s.value),"value_is_output"in s&&n(23,a=s.value_is_output),"label"in s&&n(6,_=s.label),"info"in s&&n(3,m=s.info),"disabled"in s&&n(4,w=s.disabled),"show_label"in s&&n(5,f=s.show_label),"current_mode"in s&&n(0,r=s.current_mode),"dialog_open"in s&&n(2,c=s.dialog_open)},t.$$.update=()=>{t.$$.dirty[0]&3&&n(7,l=je(u,r)),t.$$.dirty[0]&128&&l&&h("selected",l),t.$$.dirty[0]&2&&Q(u),t.$$.dirty[0]&2&&Be()},[r,u,c,m,w,f,_,l,o,d,p,L,D,G,T,M,k,J,Q,ke,pe,ze,Ce,a,Me,Se,Ee,Re,He,Xe,De,qe]}class tt extends Ie{constructor(e){super(),Je(this,e,et,$e,Ke,{value:1,value_is_output:23,label:6,info:3,disabled:4,show_label:5,current_mode:0,dialog_open:2},null,[-1,-1])}get value(){return this.$$.ctx[1]}set value(e){this.$$set({value:e}),Y()}get value_is_output(){return this.$$.ctx[23]}set value_is_output(e){this.$$set({value_is_output:e}),Y()}get label(){return this.$$.ctx[6]}set label(e){this.$$set({label:e}),Y()}get info(){return this.$$.ctx[3]}set info(e){this.$$set({info:e}),Y()}get disabled(){return this.$$.ctx[4]}set disabled(e){this.$$set({disabled:e}),Y()}get show_label(){return this.$$.ctx[5]}set show_label(e){this.$$set({show_label:e}),Y()}get current_mode(){return this.$$.ctx[0]}set current_mode(e){this.$$set({current_mode:e}),Y()}get dialog_open(){return this.$$.ctx[2]}set dialog_open(e){this.$$set({dialog_open:e}),Y()}}const nt=tt,{SvelteComponent:lt,add_flush_callback:ce,assign:it,bind:fe,binding_callbacks:he,create_component:W,destroy_component:Z,detach:st,flush:B,get_spread_object:ot,get_spread_update:ut,init:at,insert:rt,mount_component:y,safe_not_equal:_t,space:ct,transition_in:x,transition_out:$}=window.__gradio__svelte__internal;function ft(t){let e,n,l,u,a,_;const m=[{autoscroll:t[12].autoscroll},{i18n:t[12].i18n},t[11]];let w={};for(let o=0;o<m.length;o+=1)w=it(w,m[o]);e=new Ge({props:w}),e.$on("clear_status",t[15]);function f(o){t[16](o)}function r(o){t[17](o)}let c={label:t[2],info:t[3],show_label:t[7],disabled:!t[13]||t[14]};return t[0]!==void 0&&(c.value=t[0]),t[1]!==void 0&&(c.value_is_output=t[1]),l=new nt({props:c}),he.push(()=>fe(l,"value",f)),he.push(()=>fe(l,"value_is_output",r)),l.$on("change",t[18]),l.$on("input",t[19]),l.$on("submit",t[20]),l.$on("blur",t[21]),l.$on("focus",t[22]),{c(){W(e.$$.fragment),n=ct(),W(l.$$.fragment)},m(o,d){y(e,o,d),rt(o,n,d),y(l,o,d),_=!0},p(o,d){const p=d&6144?ut(m,[d&4096&&{autoscroll:o[12].autoscroll},d&4096&&{i18n:o[12].i18n},d&2048&&ot(o[11])]):{};e.$set(p);const h={};d&4&&(h.label=o[2]),d&8&&(h.info=o[3]),d&128&&(h.show_label=o[7]),d&24576&&(h.disabled=!o[13]||o[14]),!u&&d&1&&(u=!0,h.value=o[0],ce(()=>u=!1)),!a&&d&2&&(a=!0,h.value_is_output=o[1],ce(()=>a=!1)),l.$set(h)},i(o){_||(x(e.$$.fragment,o),x(l.$$.fragment,o),_=!0)},o(o){$(e.$$.fragment,o),$(l.$$.fragment,o),_=!1},d(o){o&&st(n),Z(e,o),Z(l,o)}}}function ht(t){let e,n;return e=new Pe({props:{visible:t[6],elem_id:t[4],elem_classes:t[5],container:t[8],scale:t[9],min_width:t[10],$$slots:{default:[ft]},$$scope:{ctx:t}}}),{c(){W(e.$$.fragment)},m(l,u){y(e,l,u),n=!0},p(l,[u]){const a={};u&64&&(a.visible=l[6]),u&16&&(a.elem_id=l[4]),u&32&&(a.elem_classes=l[5]),u&256&&(a.container=l[8]),u&512&&(a.scale=l[9]),u&1024&&(a.min_width=l[10]),u&8419471&&(a.$$scope={dirty:u,ctx:l}),e.$set(a)},i(l){n||(x(e.$$.fragment,l),n=!0)},o(l){$(e.$$.fragment,l),n=!1},d(l){Z(e,l)}}}function dt(t,e,n){let{label:l="ColorPicker"}=e,{info:u=void 0}=e,{elem_id:a=""}=e,{elem_classes:_=[]}=e,{visible:m=!0}=e,{value:w}=e,{value_is_output:f=!1}=e,{show_label:r}=e,{container:c=!0}=e,{scale:o=null}=e,{min_width:d=void 0}=e,{loading_status:p}=e,{gradio:h}=e,{interactive:L}=e,{disabled:b=!1}=e;const P=()=>h.dispatch("clear_status",p);function X(i){w=i,n(0,w)}function D(i){f=i,n(1,f)}const G=()=>h.dispatch("change"),C=()=>h.dispatch("input"),g=()=>h.dispatch("submit"),T=()=>h.dispatch("blur"),v=()=>h.dispatch("focus");return t.$$set=i=>{"label"in i&&n(2,l=i.label),"info"in i&&n(3,u=i.info),"elem_id"in i&&n(4,a=i.elem_id),"elem_classes"in i&&n(5,_=i.elem_classes),"visible"in i&&n(6,m=i.visible),"value"in i&&n(0,w=i.value),"value_is_output"in i&&n(1,f=i.value_is_output),"show_label"in i&&n(7,r=i.show_label),"container"in i&&n(8,c=i.container),"scale"in i&&n(9,o=i.scale),"min_width"in i&&n(10,d=i.min_width),"loading_status"in i&&n(11,p=i.loading_status),"gradio"in i&&n(12,h=i.gradio),"interactive"in i&&n(13,L=i.interactive),"disabled"in i&&n(14,b=i.disabled)},[w,f,l,u,a,_,m,r,c,o,d,p,h,L,b,P,X,D,G,C,g,T,v]}class Ht extends lt{constructor(e){super(),at(this,e,dt,ht,_t,{label:2,info:3,elem_id:4,elem_classes:5,visible:6,value:0,value_is_output:1,show_label:7,container:8,scale:9,min_width:10,loading_status:11,gradio:12,interactive:13,disabled:14})}get label(){return this.$$.ctx[2]}set label(e){this.$$set({label:e}),B()}get info(){return this.$$.ctx[3]}set info(e){this.$$set({info:e}),B()}get elem_id(){return this.$$.ctx[4]}set elem_id(e){this.$$set({elem_id:e}),B()}get elem_classes(){return this.$$.ctx[5]}set elem_classes(e){this.$$set({elem_classes:e}),B()}get visible(){return this.$$.ctx[6]}set visible(e){this.$$set({visible:e}),B()}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),B()}get value_is_output(){return this.$$.ctx[1]}set value_is_output(e){this.$$set({value_is_output:e}),B()}get show_label(){return this.$$.ctx[7]}set show_label(e){this.$$set({show_label:e}),B()}get container(){return this.$$.ctx[8]}set container(e){this.$$set({container:e}),B()}get scale(){return this.$$.ctx[9]}set scale(e){this.$$set({scale:e}),B()}get min_width(){return this.$$.ctx[10]}set min_width(e){this.$$set({min_width:e}),B()}get loading_status(){return this.$$.ctx[11]}set loading_status(e){this.$$set({loading_status:e}),B()}get gradio(){return this.$$.ctx[12]}set gradio(e){this.$$set({gradio:e}),B()}get interactive(){return this.$$.ctx[13]}set interactive(e){this.$$set({interactive:e}),B()}get disabled(){return this.$$.ctx[14]}set disabled(e){this.$$set({disabled:e}),B()}}export{nt as BaseColorPicker,qt as BaseExample,Ht as default};
//# sourceMappingURL=Index-DQ3_4eWv.js.map
