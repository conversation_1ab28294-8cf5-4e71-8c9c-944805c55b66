{"version": 3, "file": "Index-DR-TuqpS.js", "sources": ["../../../../js/icons/src/Remove.svelte", "../../../../js/dropdown/shared/Multiselect.svelte", "../../../../js/dropdown/Index.svelte"], "sourcesContent": ["<svg\n\txmlns=\"http://www.w3.org/2000/svg\"\n\tviewBox=\"0 0 24 24\"\n\twidth=\"100%\"\n\theight=\"100%\"\n>\n\t<path\n\t\td=\"M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z\"\n\t/>\n</svg>\n", "<script lang=\"ts\">\n\timport { afterUpdate, createEventDispatcher } from \"svelte\";\n\timport { _ } from \"svelte-i18n\";\n\timport { BlockTitle } from \"@gradio/atoms\";\n\timport { Remove, DropdownArrow } from \"@gradio/icons\";\n\timport type { KeyUpD<PERSON>, SelectData, I18nFormatter } from \"@gradio/utils\";\n\timport DropdownOptions from \"./DropdownOptions.svelte\";\n\timport { handle_filter, handle_change, handle_shared_keys } from \"./utils\";\n\n\ttype Item = string | number;\n\n\texport let label: string;\n\texport let info: string | undefined = undefined;\n\texport let value: Item | Item[] | undefined = [];\n\tlet old_value: typeof value = [];\n\texport let value_is_output = false;\n\texport let max_choices: number | null = null;\n\texport let choices: [string, Item][];\n\tlet old_choices: typeof choices;\n\texport let disabled = false;\n\texport let show_label: boolean;\n\texport let container = true;\n\texport let allow_custom_value = false;\n\texport let filterable = true;\n\texport let i18n: I18nFormatter;\n\n\tlet filter_input: HTMLElement;\n\tlet input_text = \"\";\n\tlet old_input_text = \"\";\n\tlet show_options = false;\n\tlet choices_names: string[];\n\tlet choices_values: (string | number)[];\n\n\t// All of these are indices with respect to the choices array\n\tlet filtered_indices: number[] = [];\n\tlet active_index: number | null = null;\n\t// selected_index consists of indices from choices or strings if allow_custom_value is true and user types in a custom value\n\tlet selected_indices: (number | string)[] = [];\n\tlet old_selected_index: (number | string)[] = [];\n\n\tconst dispatch = createEventDispatcher<{\n\t\tchange: string | string[] | undefined;\n\t\tinput: undefined;\n\t\tselect: SelectData;\n\t\tblur: undefined;\n\t\tfocus: undefined;\n\t\tkey_up: KeyUpData;\n\t}>();\n\n\t// Setting the initial value of the multiselect dropdown\n\tif (Array.isArray(value)) {\n\t\tvalue.forEach((element) => {\n\t\t\tconst index = choices.map((c) => c[1]).indexOf(element);\n\t\t\tif (index !== -1) {\n\t\t\t\tselected_indices.push(index);\n\t\t\t} else {\n\t\t\t\tselected_indices.push(element);\n\t\t\t}\n\t\t});\n\t}\n\n\t$: {\n\t\tchoices_names = choices.map((c) => c[0]);\n\t\tchoices_values = choices.map((c) => c[1]);\n\t}\n\n\t$: {\n\t\tif (choices !== old_choices || input_text !== old_input_text) {\n\t\t\tfiltered_indices = handle_filter(choices, input_text);\n\t\t\told_choices = choices;\n\t\t\told_input_text = input_text;\n\t\t\tif (!allow_custom_value) {\n\t\t\t\tactive_index = filtered_indices[0];\n\t\t\t}\n\t\t}\n\t}\n\n\t$: {\n\t\tif (JSON.stringify(value) != JSON.stringify(old_value)) {\n\t\t\thandle_change(dispatch, value, value_is_output);\n\t\t\told_value = Array.isArray(value) ? value.slice() : value;\n\t\t}\n\t}\n\n\t$: {\n\t\tif (\n\t\t\tJSON.stringify(selected_indices) != JSON.stringify(old_selected_index)\n\t\t) {\n\t\t\tvalue = selected_indices.map((index) =>\n\t\t\t\ttypeof index === \"number\" ? choices_values[index] : index\n\t\t\t);\n\t\t\told_selected_index = selected_indices.slice();\n\t\t}\n\t}\n\n\tfunction handle_blur(): void {\n\t\tif (!allow_custom_value) {\n\t\t\tinput_text = \"\";\n\t\t}\n\n\t\tif (allow_custom_value && input_text !== \"\") {\n\t\t\tadd_selected_choice(input_text);\n\t\t\tinput_text = \"\";\n\t\t}\n\n\t\tshow_options = false;\n\t\tactive_index = null;\n\t\tdispatch(\"blur\");\n\t}\n\n\tfunction remove_selected_choice(option_index: number | string): void {\n\t\tselected_indices = selected_indices.filter((v) => v !== option_index);\n\t\tdispatch(\"select\", {\n\t\t\tindex: typeof option_index === \"number\" ? option_index : -1,\n\t\t\tvalue:\n\t\t\t\ttypeof option_index === \"number\"\n\t\t\t\t\t? choices_values[option_index]\n\t\t\t\t\t: option_index,\n\t\t\tselected: false\n\t\t});\n\t}\n\n\tfunction add_selected_choice(option_index: number | string): void {\n\t\tif (max_choices === null || selected_indices.length < max_choices) {\n\t\t\tselected_indices = [...selected_indices, option_index];\n\t\t\tdispatch(\"select\", {\n\t\t\t\tindex: typeof option_index === \"number\" ? option_index : -1,\n\t\t\t\tvalue:\n\t\t\t\t\ttypeof option_index === \"number\"\n\t\t\t\t\t\t? choices_values[option_index]\n\t\t\t\t\t\t: option_index,\n\t\t\t\tselected: true\n\t\t\t});\n\t\t}\n\t\tif (selected_indices.length === max_choices) {\n\t\t\tshow_options = false;\n\t\t\tactive_index = null;\n\t\t\tfilter_input.blur();\n\t\t}\n\t}\n\n\tfunction handle_option_selected(e: any): void {\n\t\tconst option_index = parseInt(e.detail.target.dataset.index);\n\t\tadd_or_remove_index(option_index);\n\t}\n\n\tfunction add_or_remove_index(option_index: number): void {\n\t\tif (selected_indices.includes(option_index)) {\n\t\t\tremove_selected_choice(option_index);\n\t\t} else {\n\t\t\tadd_selected_choice(option_index);\n\t\t}\n\t\tinput_text = \"\";\n\t}\n\n\tfunction remove_all(e: any): void {\n\t\tselected_indices = [];\n\t\tinput_text = \"\";\n\t\te.preventDefault();\n\t}\n\n\tfunction handle_focus(e: FocusEvent): void {\n\t\tfiltered_indices = choices.map((_, i) => i);\n\t\tif (max_choices === null || selected_indices.length < max_choices) {\n\t\t\tshow_options = true;\n\t\t}\n\t\tdispatch(\"focus\");\n\t}\n\n\tfunction handle_key_down(e: KeyboardEvent): void {\n\t\t[show_options, active_index] = handle_shared_keys(\n\t\t\te,\n\t\t\tactive_index,\n\t\t\tfiltered_indices\n\t\t);\n\t\tif (e.key === \"Enter\") {\n\t\t\tif (active_index !== null) {\n\t\t\t\tadd_or_remove_index(active_index);\n\t\t\t} else {\n\t\t\t\tif (allow_custom_value) {\n\t\t\t\t\tadd_selected_choice(input_text);\n\t\t\t\t\tinput_text = \"\";\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\tif (e.key === \"Backspace\" && input_text === \"\") {\n\t\t\tselected_indices = [...selected_indices.slice(0, -1)];\n\t\t}\n\t\tif (selected_indices.length === max_choices) {\n\t\t\tshow_options = false;\n\t\t\tactive_index = null;\n\t\t}\n\t}\n\n\tfunction set_selected_indices(): void {\n\t\tif (value === undefined) {\n\t\t\tselected_indices = [];\n\t\t} else if (Array.isArray(value)) {\n\t\t\tselected_indices = value\n\t\t\t\t.map((v) => {\n\t\t\t\t\tconst index = choices_values.indexOf(v);\n\t\t\t\t\tif (index !== -1) {\n\t\t\t\t\t\treturn index;\n\t\t\t\t\t}\n\t\t\t\t\tif (allow_custom_value) {\n\t\t\t\t\t\treturn v;\n\t\t\t\t\t}\n\t\t\t\t\t// Instead of returning null, skip this iteration\n\t\t\t\t\treturn undefined;\n\t\t\t\t})\n\t\t\t\t.filter((val): val is string | number => val !== undefined);\n\t\t}\n\t}\n\n\t$: value, set_selected_indices();\n\n\tafterUpdate(() => {\n\t\tvalue_is_output = false;\n\t});\n</script>\n\n<label class:container>\n\t<BlockTitle {show_label} {info}>{label}</BlockTitle>\n\n\t<div class=\"wrap\">\n\t\t<div class=\"wrap-inner\" class:show_options>\n\t\t\t{#each selected_indices as s}\n\t\t\t\t<div class=\"token\">\n\t\t\t\t\t<span>\n\t\t\t\t\t\t{#if typeof s === \"number\"}\n\t\t\t\t\t\t\t{choices_names[s]}\n\t\t\t\t\t\t{:else}\n\t\t\t\t\t\t\t{s}\n\t\t\t\t\t\t{/if}\n\t\t\t\t\t</span>\n\t\t\t\t\t{#if !disabled}\n\t\t\t\t\t\t<div\n\t\t\t\t\t\t\tclass=\"token-remove\"\n\t\t\t\t\t\t\ton:click|preventDefault={() => remove_selected_choice(s)}\n\t\t\t\t\t\t\ton:keydown|preventDefault={(event) => {\n\t\t\t\t\t\t\t\tif (event.key === \"Enter\") {\n\t\t\t\t\t\t\t\t\tremove_selected_choice(s);\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}}\n\t\t\t\t\t\t\trole=\"button\"\n\t\t\t\t\t\t\ttabindex=\"0\"\n\t\t\t\t\t\t\ttitle={i18n(\"common.remove\") + \" \" + s}\n\t\t\t\t\t\t>\n\t\t\t\t\t\t\t<Remove />\n\t\t\t\t\t\t</div>\n\t\t\t\t\t{/if}\n\t\t\t\t</div>\n\t\t\t{/each}\n\t\t\t<div class=\"secondary-wrap\">\n\t\t\t\t<input\n\t\t\t\t\tclass=\"border-none\"\n\t\t\t\t\tclass:subdued={(!choices_names.includes(input_text) &&\n\t\t\t\t\t\t!allow_custom_value) ||\n\t\t\t\t\t\tselected_indices.length === max_choices}\n\t\t\t\t\t{disabled}\n\t\t\t\t\tautocomplete=\"off\"\n\t\t\t\t\tbind:value={input_text}\n\t\t\t\t\tbind:this={filter_input}\n\t\t\t\t\ton:keydown={handle_key_down}\n\t\t\t\t\ton:keyup={(e) =>\n\t\t\t\t\t\tdispatch(\"key_up\", {\n\t\t\t\t\t\t\tkey: e.key,\n\t\t\t\t\t\t\tinput_value: input_text\n\t\t\t\t\t\t})}\n\t\t\t\t\ton:blur={handle_blur}\n\t\t\t\t\ton:focus={handle_focus}\n\t\t\t\t\treadonly={!filterable}\n\t\t\t\t/>\n\n\t\t\t\t{#if !disabled}\n\t\t\t\t\t{#if selected_indices.length > 0}\n\t\t\t\t\t\t<div\n\t\t\t\t\t\t\trole=\"button\"\n\t\t\t\t\t\t\ttabindex=\"0\"\n\t\t\t\t\t\t\tclass=\"token-remove remove-all\"\n\t\t\t\t\t\t\ttitle={i18n(\"common.clear\")}\n\t\t\t\t\t\t\ton:click={remove_all}\n\t\t\t\t\t\t\ton:keydown={(event) => {\n\t\t\t\t\t\t\t\tif (event.key === \"Enter\") {\n\t\t\t\t\t\t\t\t\tremove_all(event);\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}}\n\t\t\t\t\t\t>\n\t\t\t\t\t\t\t<Remove />\n\t\t\t\t\t\t</div>\n\t\t\t\t\t{/if}\n\t\t\t\t\t<span class=\"icon-wrap\"> <DropdownArrow /></span>\n\t\t\t\t{/if}\n\t\t\t</div>\n\t\t</div>\n\t\t<DropdownOptions\n\t\t\t{show_options}\n\t\t\t{choices}\n\t\t\t{filtered_indices}\n\t\t\t{disabled}\n\t\t\t{selected_indices}\n\t\t\t{active_index}\n\t\t\tremember_scroll={true}\n\t\t\ton:change={handle_option_selected}\n\t\t/>\n\t</div>\n</label>\n\n<style>\n\t.icon-wrap {\n\t\tcolor: var(--body-text-color);\n\t\tmargin-right: var(--size-2);\n\t\twidth: var(--size-5);\n\t}\n\tlabel:not(.container),\n\tlabel:not(.container) .wrap,\n\tlabel:not(.container) .wrap-inner,\n\tlabel:not(.container) .secondary-wrap,\n\tlabel:not(.container) .token,\n\tlabel:not(.container) input {\n\t\theight: 100%;\n\t}\n\t.container .wrap {\n\t\tbox-shadow: var(--input-shadow);\n\t\tborder: var(--input-border-width) solid var(--border-color-primary);\n\t}\n\n\t.wrap {\n\t\tposition: relative;\n\t\tborder-radius: var(--input-radius);\n\t\tbackground: var(--input-background-fill);\n\t}\n\n\t.wrap:focus-within {\n\t\tbox-shadow: var(--input-shadow-focus);\n\t\tborder-color: var(--input-border-color-focus);\n\t}\n\n\t.wrap-inner {\n\t\tdisplay: flex;\n\t\tposition: relative;\n\t\tflex-wrap: wrap;\n\t\talign-items: center;\n\t\tgap: var(--checkbox-label-gap);\n\t\tpadding: var(--checkbox-label-padding);\n\t}\n\n\t.token {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\ttransition: var(--button-transition);\n\t\tcursor: pointer;\n\t\tbox-shadow: var(--checkbox-label-shadow);\n\t\tborder: var(--checkbox-label-border-width) solid\n\t\t\tvar(--checkbox-label-border-color);\n\t\tborder-radius: var(--button-small-radius);\n\t\tbackground: var(--checkbox-label-background-fill);\n\t\tpadding: var(--checkbox-label-padding);\n\t\tcolor: var(--checkbox-label-text-color);\n\t\tfont-weight: var(--checkbox-label-text-weight);\n\t\tfont-size: var(--checkbox-label-text-size);\n\t\tline-height: var(--line-md);\n\t\tword-break: break-word;\n\t}\n\n\t.token > * + * {\n\t\tmargin-left: var(--size-2);\n\t}\n\n\t.token-remove {\n\t\tfill: var(--body-text-color);\n\t\tdisplay: flex;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t\tcursor: pointer;\n\t\tborder: var(--checkbox-border-width) solid var(--border-color-primary);\n\t\tborder-radius: var(--radius-full);\n\t\tbackground: var(--background-fill-primary);\n\t\tpadding: var(--size-0-5);\n\t\twidth: 16px;\n\t\theight: 16px;\n\t\tflex-shrink: 0;\n\t}\n\n\t.secondary-wrap {\n\t\tdisplay: flex;\n\t\tflex: 1 1 0%;\n\t\talign-items: center;\n\t\tborder: none;\n\t\tmin-width: min-content;\n\t}\n\n\tinput {\n\t\tmargin: var(--spacing-sm);\n\t\toutline: none;\n\t\tborder: none;\n\t\tbackground: inherit;\n\t\twidth: var(--size-full);\n\t\tcolor: var(--body-text-color);\n\t\tfont-size: var(--input-text-size);\n\t}\n\n\tinput:disabled {\n\t\t-webkit-text-fill-color: var(--body-text-color);\n\t\t-webkit-opacity: 1;\n\t\topacity: 1;\n\t\tcursor: not-allowed;\n\t}\n\n\t.remove-all {\n\t\tmargin-left: var(--size-1);\n\t\twidth: 20px;\n\t\theight: 20px;\n\t}\n\t.subdued {\n\t\tcolor: var(--body-text-color-subdued);\n\t}\n\tinput[readonly] {\n\t\tcursor: pointer;\n\t}\n</style>\n", "<script context=\"module\" lang=\"ts\">\n\texport { default as BaseDropdown } from \"./shared/Dropdown.svelte\";\n\texport { default as BaseMultiselect } from \"./shared/Multiselect.svelte\";\n\texport { default as BaseExample } from \"./Example.svelte\";\n</script>\n\n<script lang=\"ts\">\n\timport type { Gradio, KeyUpData, SelectData } from \"@gradio/utils\";\n\timport Multiselect from \"./shared/Multiselect.svelte\";\n\timport Dropdown from \"./shared/Dropdown.svelte\";\n\timport { Block } from \"@gradio/atoms\";\n\timport { StatusTracker } from \"@gradio/statustracker\";\n\timport type { LoadingStatus } from \"@gradio/statustracker\";\n\n\ttype Item = string | number;\n\n\texport let label = \"Dropdown\";\n\texport let info: string | undefined = undefined;\n\texport let elem_id = \"\";\n\texport let elem_classes: string[] = [];\n\texport let visible = true;\n\texport let multiselect = false;\n\texport let value: Item | Item[] | undefined = multiselect ? [] : undefined;\n\texport let value_is_output = false;\n\texport let max_choices: number | null = null;\n\texport let choices: [string, Item][];\n\texport let show_label: boolean;\n\texport let filterable: boolean;\n\texport let container = true;\n\texport let scale: number | null = null;\n\texport let min_width: number | undefined = undefined;\n\texport let loading_status: LoadingStatus;\n\texport let allow_custom_value = false;\n\texport let gradio: Gradio<{\n\t\tchange: never;\n\t\tinput: never;\n\t\tselect: SelectData;\n\t\tblur: never;\n\t\tfocus: never;\n\t\tkey_up: KeyUpData;\n\t\tclear_status: LoadingStatus;\n\t}>;\n\texport let interactive: boolean;\n</script>\n\n<Block\n\t{visible}\n\t{elem_id}\n\t{elem_classes}\n\tpadding={container}\n\tallow_overflow={false}\n\t{scale}\n\t{min_width}\n>\n\t<StatusTracker\n\t\tautoscroll={gradio.autoscroll}\n\t\ti18n={gradio.i18n}\n\t\t{...loading_status}\n\t\ton:clear_status={() => gradio.dispatch(\"clear_status\", loading_status)}\n\t/>\n\n\t{#if multiselect}\n\t\t<Multiselect\n\t\t\tbind:value\n\t\t\tbind:value_is_output\n\t\t\t{choices}\n\t\t\t{max_choices}\n\t\t\t{label}\n\t\t\t{info}\n\t\t\t{show_label}\n\t\t\t{allow_custom_value}\n\t\t\t{filterable}\n\t\t\t{container}\n\t\t\ti18n={gradio.i18n}\n\t\t\ton:change={() => gradio.dispatch(\"change\")}\n\t\t\ton:input={() => gradio.dispatch(\"input\")}\n\t\t\ton:select={(e) => gradio.dispatch(\"select\", e.detail)}\n\t\t\ton:blur={() => gradio.dispatch(\"blur\")}\n\t\t\ton:focus={() => gradio.dispatch(\"focus\")}\n\t\t\ton:key_up={() => gradio.dispatch(\"key_up\")}\n\t\t\tdisabled={!interactive}\n\t\t/>\n\t{:else}\n\t\t<Dropdown\n\t\t\tbind:value\n\t\t\tbind:value_is_output\n\t\t\t{choices}\n\t\t\t{label}\n\t\t\t{info}\n\t\t\t{show_label}\n\t\t\t{filterable}\n\t\t\t{allow_custom_value}\n\t\t\t{container}\n\t\t\ton:change={() => gradio.dispatch(\"change\")}\n\t\t\ton:input={() => gradio.dispatch(\"input\")}\n\t\t\ton:select={(e) => gradio.dispatch(\"select\", e.detail)}\n\t\t\ton:blur={() => gradio.dispatch(\"blur\")}\n\t\t\ton:focus={() => gradio.dispatch(\"focus\")}\n\t\t\ton:key_up={(e) => gradio.dispatch(\"key_up\", e.detail)}\n\t\t\tdisabled={!interactive}\n\t\t/>\n\t{/if}\n</Block>\n"], "names": ["insert", "target", "svg", "anchor", "append", "path", "afterUpdate", "createEventDispatcher", "ctx", "set_data", "t_value", "dirty", "attr", "div", "div_title_value", "current", "create_if_block_3", "create_if_block_2", "span", "create_if_block_1", "i", "create_if_block", "label_1", "div2", "div1", "div0", "input", "each_blocks", "label", "$$props", "info", "value", "old_value", "value_is_output", "max_choices", "choices", "old_choices", "disabled", "show_label", "container", "allow_custom_value", "filterable", "i18n", "filter_input", "input_text", "old_input_text", "show_options", "choices_names", "choices_values", "filtered_indices", "active_index", "selected_indices", "old_selected_index", "dispatch", "element", "index", "c", "handle_blur", "add_selected_choice", "remove_selected_choice", "option_index", "$$invalidate", "v", "handle_option_selected", "e", "add_or_remove_index", "remove_all", "handle_focus", "_", "handle_key_down", "handle_shared_keys", "set_selected_indices", "val", "click_handler", "s", "event", "$$value", "keyup_handler", "handle_filter", "handle_change", "multiselect_1_changes", "elem_id", "elem_classes", "visible", "multiselect", "scale", "min_width", "loading_status", "gradio", "interactive", "clear_status_handler"], "mappings": "utCAAAA,GASKC,EAAAC,EAAAC,CAAA,EAHJC,GAECF,EAAAG,CAAA,ygBCPQ,CAAA,YAAAC,GAAA,sBAAAC,IAA0C,OAAA,yHA6NlBC,EAAK,CAAA,CAAA,wCAALA,EAAK,CAAA,CAAA,uCAU/BA,EAAC,EAAA,EAAA,mEAADA,EAAC,EAAA,EAAA,KAAAC,GAAA,EAAAC,CAAA,iCAFD,IAAAA,EAAAF,MAAcA,EAAC,EAAA,CAAA,EAAA,iDAAfG,EAAA,CAAA,EAAA,OAAAD,KAAAA,EAAAF,MAAcA,EAAC,EAAA,CAAA,EAAA,KAAAC,GAAA,EAAAC,CAAA,oQAgBTE,EAAAC,EAAA,QAAAC,EAAAN,EAAK,CAAA,EAAA,eAAe,EAAI,IAAMA,EAAC,EAAA,CAAA,UAVvCR,EAaKC,EAAAY,EAAAV,CAAA,sFAHG,CAAAY,GAAAJ,EAAA,CAAA,EAAA,MAAAG,KAAAA,EAAAN,EAAK,CAAA,EAAA,eAAe,EAAI,IAAMA,EAAC,EAAA,gKAjB3B,OAAA,OAAAA,OAAM,SAAQQ,2BAMrBR,EAAQ,CAAA,GAAAS,GAAAT,CAAA,mIARfR,EAwBKC,EAAAY,EAAAV,CAAA,EAvBJC,EAMMS,EAAAK,CAAA,iHACAV,EAAQ,CAAA,uMAwCTA,EAAgB,EAAA,EAAC,OAAS,GAACW,GAAAX,CAAA,4IAgBhCR,EAAgDC,EAAAiB,EAAAf,CAAA,2BAhB3CK,EAAgB,EAAA,EAAC,OAAS,oZAKtBI,EAAAC,EAAA,QAAAC,EAAAN,KAAK,cAAc,CAAA,UAJ3BR,EAaKC,EAAAY,EAAAV,CAAA,sCARMK,EAAU,EAAA,CAAA,uCADb,CAAAO,GAAAJ,EAAA,CAAA,EAAA,KAAAG,KAAAA,EAAAN,KAAK,cAAc,oQAtDvBA,EAAgB,EAAA,CAAA,uBAArB,OAAIY,GAAA,mEAgDCZ,EAAQ,CAAA,GAAAa,GAAAb,CAAA,yJA4BE,qBACNA,EAAsB,EAAA,CAAA,4QAhCpBA,EAAU,CAAA,iBAfJA,EAAa,EAAA,EAAC,SAASA,EAAU,EAAA,CAAA,GAAA,CAChDA,EAAkB,CAAA,GACnBA,EAAgB,EAAA,EAAC,SAAWA,EAAW,CAAA,CAAA,iNArC7CR,EAqFOC,EAAAqB,EAAAnB,CAAA,qBAlFNC,EAiFKkB,EAAAC,CAAA,EAhFJnB,EAqEKmB,EAAAC,CAAA,0DAzCJpB,EAwCKoB,EAAAC,CAAA,EAvCJrB,EAkBCqB,EAAAC,CAAA,OAXYlB,EAAU,EAAA,CAAA,iGAEVA,EAAe,EAAA,CAAA,gCAMlBA,EAAW,EAAA,CAAA,cACVA,EAAY,EAAA,CAAA,4JA5CjBA,EAAgB,EAAA,CAAA,oBAArB,OAAIY,GAAA,EAAA,yGAAJ,OAAIA,EAAAO,EAAA,OAAAP,GAAA,mEA6COZ,EAAU,CAAA,yCAVTA,EAAU,EAAA,QAAVA,EAAU,EAAA,CAAA,mCALLA,EAAa,EAAA,EAAC,SAASA,EAAU,EAAA,CAAA,GAAA,CAChDA,EAAkB,CAAA,GACnBA,EAAgB,EAAA,EAAC,SAAWA,EAAW,CAAA,CAAA,EAgBnCA,EAAQ,CAAA,kcAhDb,OAAIY,GAAA,2OAvNG,GAAA,CAAA,MAAAQ,CAAA,EAAAC,GACA,KAAAC,EAA2B,MAAA,EAAAD,EAC3B,CAAA,MAAAE,EAAA,EAAA,EAAAF,EACPG,EAAA,CAAA,GACO,gBAAAC,EAAkB,EAAA,EAAAJ,GAClB,YAAAK,EAA6B,IAAA,EAAAL,EAC7B,CAAA,QAAAM,CAAA,EAAAN,EACPO,GACO,SAAAC,EAAW,EAAA,EAAAR,EACX,CAAA,WAAAS,CAAA,EAAAT,GACA,UAAAU,EAAY,EAAA,EAAAV,GACZ,mBAAAW,EAAqB,EAAA,EAAAX,GACrB,WAAAY,EAAa,EAAA,EAAAZ,EACb,CAAA,KAAAa,CAAA,EAAAb,EAEPc,EACAC,EAAa,GACbC,EAAiB,GACjBC,EAAe,GACfC,EACAC,EAGAC,EAAA,CAAA,EACAC,EAA8B,KAE9BC,EAAA,CAAA,EACAC,EAAA,CAAA,QAEEC,EAAW9C,KAUb,MAAM,QAAQwB,CAAK,GACtBA,EAAM,QAASuB,GAAA,CACR,MAAAC,EAAQpB,EAAQ,IAAKqB,IAAMA,GAAE,CAAC,CAAA,EAAG,QAAQF,CAAO,EAClDC,IAAU,GACbJ,EAAiB,KAAKI,CAAK,EAE3BJ,EAAiB,KAAKG,CAAO,IAuCvB,SAAAG,IAAA,CACHjB,QACJI,EAAa,EAAA,EAGVJ,GAAsBI,IAAe,KACxCc,EAAoBd,CAAU,OAC9BA,EAAa,EAAA,QAGdE,EAAe,EAAA,OACfI,EAAe,IAAA,EACfG,EAAS,MAAM,WAGPM,EAAuBC,EAAA,CAC/BC,EAAA,GAAAV,EAAmBA,EAAiB,OAAQW,GAAMA,IAAMF,CAAY,CAAA,EACpEP,EAAS,SAAA,CACR,MAAc,OAAAO,GAAiB,SAAWA,EAAe,GACzD,MAAA,OACQA,GAAiB,SACrBZ,EAAeY,CAAY,EAC3BA,EACJ,SAAU,cAIHF,EAAoBE,EAAA,EACxB1B,IAAgB,MAAQiB,EAAiB,OAASjB,KACrD2B,EAAA,GAAAV,EAAA,CAAA,GAAuBA,EAAkBS,CAAY,CAAA,EACrDP,EAAS,SAAA,CACR,MAAc,OAAAO,GAAiB,SAAWA,EAAe,GACzD,MAAA,OACQA,GAAiB,SACrBZ,EAAeY,CAAY,EAC3BA,EACJ,SAAU,MAGRT,EAAiB,SAAWjB,SAC/BY,EAAe,EAAA,OACfI,EAAe,IAAA,EACfP,EAAa,KAAA,YAINoB,GAAuBC,EAAA,CACzB,MAAAJ,EAAe,SAASI,EAAE,OAAO,OAAO,QAAQ,KAAK,EAC3DC,EAAoBL,CAAY,WAGxBK,EAAoBL,EAAA,CACxBT,EAAiB,SAASS,CAAY,EACzCD,EAAuBC,CAAY,EAEnCF,EAAoBE,CAAY,OAEjChB,EAAa,EAAA,WAGLsB,EAAWF,EAAA,CACnBH,EAAA,GAAAV,EAAA,CAAA,CAAA,OACAP,EAAa,EAAA,EACboB,EAAE,eAAA,WAGMG,GAAaH,EAAA,CACrBH,EAAA,GAAAZ,EAAmBd,EAAQ,IAAA,CAAKiC,EAAGhD,KAAMA,EAAC,CAAA,GACtCc,IAAgB,MAAQiB,EAAiB,OAASjB,SACrDY,EAAe,EAAA,EAEhBO,EAAS,OAAO,WAGRgB,GAAgBL,EAAA,CACvBH,EAAA,GAAA,CAAAf,EAAcI,CAAY,EAAIoB,GAC9BN,EACAd,EACAD,CAAA,EAAAH,GAAAe,EAAA,GAAAX,CAAA,EAAAW,EAAA,EAAA1B,CAAA,EAAA0B,EAAA,GAAAzB,CAAA,EAAAyB,EAAA,GAAAjB,CAAA,EAAAiB,EAAA,GAAAhB,CAAA,EAAAgB,EAAA,EAAArB,CAAA,EAAAqB,EAAA,GAAAZ,CAAA,IAEGe,EAAE,MAAQ,UACTd,IAAiB,KACpBe,EAAoBf,CAAY,EAE5BV,IACHkB,EAAoBd,CAAU,OAC9BA,EAAa,EAAA,IAIZoB,EAAE,MAAQ,aAAepB,IAAe,SAC3CO,EAAuB,CAAA,GAAAA,EAAiB,MAAM,EAAA,EAAK,CAAA,CAAA,EAEhDA,EAAiB,SAAWjB,SAC/BY,EAAe,EAAA,OACfI,EAAe,IAAA,GAIR,SAAAqB,IAAA,CACJxC,IAAU,OACb8B,EAAA,GAAAV,EAAA,CAAA,CAAA,EACU,MAAM,QAAQpB,CAAK,GAC7B8B,EAAA,GAAAV,EAAmBpB,EACjB,IAAK+B,GAAA,OACCP,EAAQP,EAAe,QAAQc,CAAC,KAClCP,IAAU,GACN,OAAAA,EAEJ,GAAAf,EACI,OAAAsB,IAKR,OAAQU,GAAgCA,UAAiB,CAAA,EAM7DlE,GAAA,IAAA,MACC2B,EAAkB,EAAA,IAqBkB,MAAAwC,GAAAC,GAAAf,EAAuBe,CAAC,QAC3BC,IAAK,CAC5BA,EAAM,MAAQ,SACjBhB,EAAuBe,CAAC,gBAoBhB9B,EAAU,KAAA,yDACXD,EAAYiC,YAEZ,MAAAC,GAAAb,GACVX,EAAS,SACR,CAAA,IAAKW,EAAE,IACP,YAAapB,CAAA,CAAA,KAeA+B,GAAK,CACbA,EAAM,MAAQ,SACjBT,EAAWS,CAAK,weA9NvBd,EAAA,GAAAd,EAAgBZ,EAAQ,IAAKqB,GAAMA,EAAE,CAAC,CAAA,CAAA,EACtCK,EAAA,GAAAb,EAAiBb,EAAQ,IAAKqB,GAAMA,EAAE,CAAC,CAAA,CAAA,6BAInCrB,IAAYC,GAAeQ,IAAeC,UAC7CI,EAAmB6B,GAAc3C,EAASS,CAAU,CAAA,OACpDR,EAAcD,CAAA,OACdU,EAAiBD,CAAA,EACZJ,GACJqB,EAAA,GAAAX,EAAeD,EAAiB,CAAC,CAAA,6BAclC,KAAK,UAAUE,CAAgB,GAAK,KAAK,UAAUC,CAAkB,SAErErB,EAAQoB,EAAiB,IAAKI,GAAA,OACtBA,GAAU,SAAWP,EAAeO,CAAK,EAAIA,CAAA,CAAA,EAErDM,EAAA,GAAAT,EAAqBD,EAAiB,MAAA,CAAA,4BAbnC,KAAK,UAAUpB,CAAK,GAAK,KAAK,UAAUC,CAAS,IACpD+C,GAAc1B,EAAUtB,EAAOE,CAAe,EAC9C4B,EAAA,GAAA7B,EAAY,MAAM,QAAQD,CAAK,EAAIA,EAAM,QAAUA,CAAA,2BAsI3CwC,GAAA,81DCnHG/D,EAAW,EAAA,mlBAAXA,EAAW,EAAA,gZA1BhB,KAAAA,MAAO,eAOFA,EAAW,EAAA,wlBAPhBG,EAAA,CAAA,EAAA,SAAAqE,EAAA,KAAAxE,MAAO,gCAOFA,EAAW,EAAA,2OAzBX,WAAAA,MAAO,YACb,CAAA,KAAAA,MAAO,IAAI,EACbA,EAAc,EAAA,iJAIdA,EAAW,CAAA,EAAA,mLANH,WAAAA,MAAO,YACbG,EAAA,CAAA,EAAA,QAAA,CAAA,KAAAH,MAAO,IAAI,iBACbA,EAAc,EAAA,CAAA,uXARVA,EAAS,EAAA,iBACF,mPADPA,EAAS,EAAA,iOAjCP,MAAAoB,EAAQ,UAAA,EAAAC,GACR,KAAAC,EAA2B,MAAA,EAAAD,GAC3B,QAAAoD,EAAU,EAAA,EAAApD,EACV,CAAA,aAAAqD,EAAA,EAAA,EAAArD,GACA,QAAAsD,EAAU,EAAA,EAAAtD,GACV,YAAAuD,EAAc,EAAA,EAAAvD,EACd,CAAA,MAAAE,EAAmCqD,EAAmB,CAAA,EAAA,MAAA,EAAAvD,GACtD,gBAAAI,EAAkB,EAAA,EAAAJ,GAClB,YAAAK,EAA6B,IAAA,EAAAL,EAC7B,CAAA,QAAAM,CAAA,EAAAN,EACA,CAAA,WAAAS,CAAA,EAAAT,EACA,CAAA,WAAAY,CAAA,EAAAZ,GACA,UAAAU,EAAY,EAAA,EAAAV,GACZ,MAAAwD,EAAuB,IAAA,EAAAxD,GACvB,UAAAyD,EAAgC,MAAA,EAAAzD,EAChC,CAAA,eAAA0D,CAAA,EAAA1D,GACA,mBAAAW,EAAqB,EAAA,EAAAX,EACrB,CAAA,OAAA2D,CAAA,EAAA3D,EASA,CAAA,YAAA4D,CAAA,EAAA5D,EAgBa,MAAA6D,EAAA,IAAAF,EAAO,SAAS,eAAgBD,CAAc,gEAgBnDC,EAAO,SAAS,QAAQ,QACzBA,EAAO,SAAS,OAAO,IAC3BxB,GAAMwB,EAAO,SAAS,SAAUxB,EAAE,MAAM,SACrCwB,EAAO,SAAS,MAAM,QACrBA,EAAO,SAAS,OAAO,QACtBA,EAAO,SAAS,QAAQ,iEAcxBA,EAAO,SAAS,QAAQ,SACzBA,EAAO,SAAS,OAAO,KAC3BxB,GAAMwB,EAAO,SAAS,SAAUxB,EAAE,MAAM,SACrCwB,EAAO,SAAS,MAAM,SACrBA,EAAO,SAAS,OAAO,KAC3BxB,GAAMwB,EAAO,SAAS,SAAUxB,EAAE,MAAM"}