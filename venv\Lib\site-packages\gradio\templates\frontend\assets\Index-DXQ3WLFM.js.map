{"version": 3, "file": "Index-DXQ3WLFM.js", "sources": ["../../../../js/button/Index.svelte"], "sourcesContent": ["<script context=\"module\" lang=\"ts\">\n\texport { default as BaseButton } from \"./shared/Button.svelte\";\n</script>\n\n<script lang=\"ts\">\n\timport type { Gradio } from \"@gradio/utils\";\n\timport { type FileData } from \"@gradio/client\";\n\n\timport Button from \"./shared/Button.svelte\";\n\n\texport let elem_id = \"\";\n\texport let elem_classes: string[] = [];\n\texport let visible = true;\n\texport let value: string | null;\n\texport let variant: \"primary\" | \"secondary\" | \"stop\" = \"secondary\";\n\texport let interactive: boolean;\n\texport let size: \"sm\" | \"lg\" = \"lg\";\n\texport let scale: number | null = null;\n\texport let icon: FileData | null = null;\n\texport let link: string | null = null;\n\texport let min_width: number | undefined = undefined;\n\texport let gradio: Gradio<{\n\t\tclick: never;\n\t}>;\n</script>\n\n<Button\n\t{value}\n\t{variant}\n\t{elem_id}\n\t{elem_classes}\n\t{size}\n\t{scale}\n\t{link}\n\t{icon}\n\t{min_width}\n\t{visible}\n\tdisabled={!interactive}\n\ton:click={() => gradio.dispatch(\"click\")}\n>\n\t{value ?? \"\"}\n</Button>\n"], "names": ["t_value", "ctx", "dirty", "set_data", "t", "elem_id", "$$props", "elem_classes", "visible", "value", "variant", "interactive", "size", "scale", "icon", "link", "min_width", "gradio"], "mappings": "8qBAwCE,IAAAA,GAAAC,MAAS,IAAE,gDAAXC,EAAA,GAAAF,KAAAA,GAAAC,MAAS,IAAE,KAAAE,EAAAC,EAAAJ,CAAA,sMAHDC,EAAW,CAAA,sXAAXA,EAAW,CAAA,uJA3BX,QAAAI,EAAU,EAAA,EAAAC,EACV,CAAA,aAAAC,EAAA,EAAA,EAAAD,GACA,QAAAE,EAAU,EAAA,EAAAF,EACV,CAAA,MAAAG,CAAA,EAAAH,GACA,QAAAI,EAA4C,WAAA,EAAAJ,EAC5C,CAAA,YAAAK,CAAA,EAAAL,GACA,KAAAM,EAAoB,IAAA,EAAAN,GACpB,MAAAO,EAAuB,IAAA,EAAAP,GACvB,KAAAQ,EAAwB,IAAA,EAAAR,GACxB,KAAAS,EAAsB,IAAA,EAAAT,GACtB,UAAAU,EAAgC,MAAA,EAAAV,EAChC,CAAA,OAAAW,CAAA,EAAAX,cAiBKW,EAAO,SAAS,OAAO"}