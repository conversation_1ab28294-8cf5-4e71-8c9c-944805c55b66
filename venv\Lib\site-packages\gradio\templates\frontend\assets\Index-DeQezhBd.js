/* empty css                                                        */import{L as le}from"./LineChart-CKh1Fdep.js";import{B as se}from"./Block-CJdXVpa7.js";import"./MarkdownCode.svelte_svelte_type_style_lang-Bl8p72Ow.js";import{B as ie}from"./BlockLabel-3KxTaaiM.js";import{E as ae}from"./Empty-ZqppqzTN.js";import"./index-DJ2rNx9E.js";import{S as oe}from"./index-DYtg3pip.js";import"./StreamingBar.svelte_svelte_type_style_lang-DlRzv56K.js";import"./prism-python-CeMtt1IT.js";import"./svelte/svelte.js";import"./IconButton-C_HS7fTi.js";import"./Clear-By3xiIwg.js";const{SvelteComponent:re,append:b,attr:_,destroy_each:ce,detach:H,element:L,empty:ue,ensure_array_like:V,flush:O,init:fe,insert:K,listen:_e,noop:W,safe_not_equal:de,set_data:T,set_style:E,space:A,text:F,toggle_class:G}=window.__gradio__svelte__internal,{createEventDispatcher:me}=window.__gradio__svelte__internal;function X(n,e,s){const t=n.slice();return t[6]=e[s],t[8]=s,t}function Y(n){let e,s=n[0].label+"",t;return{c(){e=L("h2"),t=F(s),_(e,"class","output-class svelte-1mutzus"),_(e,"data-testid","label-output-value"),G(e,"no-confidence",!("confidences"in n[0])),E(e,"background-color",n[1]||"transparent")},m(l,i){K(l,e,i),b(e,t)},p(l,i){i&1&&s!==(s=l[0].label+"")&&T(t,s),i&1&&G(e,"no-confidence",!("confidences"in l[0])),i&2&&E(e,"background-color",l[1]||"transparent")},d(l){l&&H(e)}}}function Z(n){let e,s=V(n[0].confidences),t=[];for(let l=0;l<s.length;l+=1)t[l]=p(X(n,s,l));return{c(){for(let l=0;l<t.length;l+=1)t[l].c();e=ue()},m(l,i){for(let o=0;o<t.length;o+=1)t[o]&&t[o].m(l,i);K(l,e,i)},p(l,i){if(i&21){s=V(l[0].confidences);let o;for(o=0;o<s.length;o+=1){const d=X(l,s,o);t[o]?t[o].p(d,i):(t[o]=p(d),t[o].c(),t[o].m(e.parentNode,e))}for(;o<t.length;o+=1)t[o].d(1);t.length=s.length}},d(l){l&&H(e),ce(t,l)}}}function p(n){let e,s,t,l,i,o,d,v,u,c,$=n[6].label+"",m,B,a,f,z,w=Math.round(n[6].confidence*100)+"",N,I,J,r,P,U;function ne(){return n[5](n[8],n[6])}return{c(){e=L("button"),s=L("div"),t=L("meter"),v=A(),u=L("dl"),c=L("dt"),m=F($),B=A(),f=L("div"),z=L("dd"),N=F(w),I=F("%"),J=A(),_(t,"aria-labelledby",l=D(`meter-text-${n[6].label}`)),_(t,"aria-label",i=n[6].label),_(t,"aria-valuenow",o=Math.round(n[6].confidence*100)),_(t,"aria-valuemin","0"),_(t,"aria-valuemax","100"),_(t,"class","bar svelte-1mutzus"),_(t,"min","0"),_(t,"max","1"),t.value=d=n[6].confidence,E(t,"width",n[6].confidence*100+"%"),E(t,"background","var(--stat-background-fill)"),_(c,"id",a=D(`meter-text-${n[6].label}`)),_(c,"class","text svelte-1mutzus"),_(f,"class","line svelte-1mutzus"),_(z,"class","confidence svelte-1mutzus"),_(u,"class","label svelte-1mutzus"),_(s,"class","inner-wrap svelte-1mutzus"),_(e,"class","confidence-set group svelte-1mutzus"),_(e,"data-testid",r=`${n[6].label}-confidence-set`),G(e,"selectable",n[2])},m(q,g){K(q,e,g),b(e,s),b(s,t),b(s,v),b(s,u),b(u,c),b(c,m),b(c,B),b(u,f),b(u,z),b(z,N),b(z,I),b(e,J),P||(U=_e(e,"click",ne),P=!0)},p(q,g){n=q,g&1&&l!==(l=D(`meter-text-${n[6].label}`))&&_(t,"aria-labelledby",l),g&1&&i!==(i=n[6].label)&&_(t,"aria-label",i),g&1&&o!==(o=Math.round(n[6].confidence*100))&&_(t,"aria-valuenow",o),g&1&&d!==(d=n[6].confidence)&&(t.value=d),g&1&&E(t,"width",n[6].confidence*100+"%"),g&1&&$!==($=n[6].label+"")&&T(m,$),g&1&&a!==(a=D(`meter-text-${n[6].label}`))&&_(c,"id",a),g&1&&w!==(w=Math.round(n[6].confidence*100)+"")&&T(N,w),g&1&&r!==(r=`${n[6].label}-confidence-set`)&&_(e,"data-testid",r),g&4&&G(e,"selectable",n[2])},d(q){q&&H(e),P=!1,U()}}}function he(n){let e,s,t=(n[3]||!n[0].confidences)&&Y(n),l=typeof n[0]=="object"&&n[0].confidences&&Z(n);return{c(){e=L("div"),t&&t.c(),s=A(),l&&l.c(),_(e,"class","container svelte-1mutzus")},m(i,o){K(i,e,o),t&&t.m(e,null),b(e,s),l&&l.m(e,null)},p(i,[o]){i[3]||!i[0].confidences?t?t.p(i,o):(t=Y(i),t.c(),t.m(e,s)):t&&(t.d(1),t=null),typeof i[0]=="object"&&i[0].confidences?l?l.p(i,o):(l=Z(i),l.c(),l.m(e,null)):l&&(l.d(1),l=null)},i:W,o:W,d(i){i&&H(e),t&&t.d(),l&&l.d()}}}function D(n){return n.replace(/\s/g,"-")}function be(n,e,s){let{value:t}=e;const l=me();let{color:i=void 0}=e,{selectable:o=!1}=e,{show_heading:d=!0}=e;const v=(u,c)=>{l("select",{index:u,value:c.label})};return n.$$set=u=>{"value"in u&&s(0,t=u.value),"color"in u&&s(1,i=u.color),"selectable"in u&&s(2,o=u.selectable),"show_heading"in u&&s(3,d=u.show_heading)},[t,i,o,d,l,v]}class ge extends re{constructor(e){super(),fe(this,e,be,he,de,{value:0,color:1,selectable:2,show_heading:3})}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),O()}get color(){return this.$$.ctx[1]}set color(e){this.$$set({color:e}),O()}get selectable(){return this.$$.ctx[2]}set selectable(e){this.$$set({selectable:e}),O()}get show_heading(){return this.$$.ctx[3]}set show_heading(e){this.$$set({show_heading:e}),O()}}const ve=ge,{SvelteComponent:we,assign:ke,check_outros:y,create_component:C,destroy_component:M,detach:Q,empty:$e,flush:h,get_spread_object:ze,get_spread_update:Se,group_outros:x,init:Le,insert:R,mount_component:j,safe_not_equal:Be,space:ee,transition_in:k,transition_out:S}=window.__gradio__svelte__internal;function te(n){let e,s;return e=new ie({props:{Icon:le,label:n[6],disable:n[7]===!1,float:n[13]===!0}}),{c(){C(e.$$.fragment)},m(t,l){j(e,t,l),s=!0},p(t,l){const i={};l&64&&(i.label=t[6]),l&128&&(i.disable=t[7]===!1),l&8192&&(i.float=t[13]===!0),e.$set(i)},i(t){s||(k(e.$$.fragment,t),s=!0)},o(t){S(e.$$.fragment,t),s=!1},d(t){M(e,t)}}}function Ne(n){let e,s;return e=new ae({props:{unpadded_box:!0,$$slots:{default:[Me]},$$scope:{ctx:n}}}),{c(){C(e.$$.fragment)},m(t,l){j(e,t,l),s=!0},p(t,l){const i={};l&262144&&(i.$$scope={dirty:l,ctx:t}),e.$set(i)},i(t){s||(k(e.$$.fragment,t),s=!0)},o(t){S(e.$$.fragment,t),s=!1},d(t){M(e,t)}}}function Ce(n){let e,s;return e=new ve({props:{selectable:n[12],value:n[5],color:n[4],show_heading:n[13]}}),e.$on("select",n[17]),{c(){C(e.$$.fragment)},m(t,l){j(e,t,l),s=!0},p(t,l){const i={};l&4096&&(i.selectable=t[12]),l&32&&(i.value=t[5]),l&16&&(i.color=t[4]),l&8192&&(i.show_heading=t[13]),e.$set(i)},i(t){s||(k(e.$$.fragment,t),s=!0)},o(t){S(e.$$.fragment,t),s=!1},d(t){M(e,t)}}}function Me(n){let e,s;return e=new le({}),{c(){C(e.$$.fragment)},m(t,l){j(e,t,l),s=!0},i(t){s||(k(e.$$.fragment,t),s=!0)},o(t){S(e.$$.fragment,t),s=!1},d(t){M(e,t)}}}function je(n){let e,s,t,l,i,o,d;const v=[{autoscroll:n[0].autoscroll},{i18n:n[0].i18n},n[10]];let u={};for(let a=0;a<v.length;a+=1)u=ke(u,v[a]);e=new oe({props:u}),e.$on("clear_status",n[16]);let c=n[11]&&te(n);const $=[Ce,Ne],m=[];function B(a,f){return a[14]!==void 0&&a[14]!==null?0:1}return l=B(n),i=m[l]=$[l](n),{c(){C(e.$$.fragment),s=ee(),c&&c.c(),t=ee(),i.c(),o=$e()},m(a,f){j(e,a,f),R(a,s,f),c&&c.m(a,f),R(a,t,f),m[l].m(a,f),R(a,o,f),d=!0},p(a,f){const z=f&1025?Se(v,[f&1&&{autoscroll:a[0].autoscroll},f&1&&{i18n:a[0].i18n},f&1024&&ze(a[10])]):{};e.$set(z),a[11]?c?(c.p(a,f),f&2048&&k(c,1)):(c=te(a),c.c(),k(c,1),c.m(t.parentNode,t)):c&&(x(),S(c,1,1,()=>{c=null}),y());let w=l;l=B(a),l===w?m[l].p(a,f):(x(),S(m[w],1,1,()=>{m[w]=null}),y(),i=m[l],i?i.p(a,f):(i=m[l]=$[l](a),i.c()),k(i,1),i.m(o.parentNode,o))},i(a){d||(k(e.$$.fragment,a),k(c),k(i),d=!0)},o(a){S(e.$$.fragment,a),S(c),S(i),d=!1},d(a){a&&(Q(s),Q(t),Q(o)),M(e,a),c&&c.d(a),m[l].d(a)}}}function qe(n){let e,s;return e=new se({props:{test_id:"label",visible:n[3],elem_id:n[1],elem_classes:n[2],container:n[7],scale:n[8],min_width:n[9],padding:!1,$$slots:{default:[je]},$$scope:{ctx:n}}}),{c(){C(e.$$.fragment)},m(t,l){j(e,t,l),s=!0},p(t,[l]){const i={};l&8&&(i.visible=t[3]),l&2&&(i.elem_id=t[1]),l&4&&(i.elem_classes=t[2]),l&128&&(i.container=t[7]),l&256&&(i.scale=t[8]),l&512&&(i.min_width=t[9]),l&294129&&(i.$$scope={dirty:l,ctx:t}),e.$set(i)},i(t){s||(k(e.$$.fragment,t),s=!0)},o(t){S(e.$$.fragment,t),s=!1},d(t){M(e,t)}}}function Ee(n,e,s){let t,{gradio:l}=e,{elem_id:i=""}=e,{elem_classes:o=[]}=e,{visible:d=!0}=e,{color:v=void 0}=e,{value:u={}}=e,c=null,{label:$=l.i18n("label.label")}=e,{container:m=!0}=e,{scale:B=null}=e,{min_width:a=void 0}=e,{loading_status:f}=e,{show_label:z=!0}=e,{_selectable:w=!1}=e,{show_heading:N=!0}=e;const I=()=>l.dispatch("clear_status",f),J=({detail:r})=>l.dispatch("select",r);return n.$$set=r=>{"gradio"in r&&s(0,l=r.gradio),"elem_id"in r&&s(1,i=r.elem_id),"elem_classes"in r&&s(2,o=r.elem_classes),"visible"in r&&s(3,d=r.visible),"color"in r&&s(4,v=r.color),"value"in r&&s(5,u=r.value),"label"in r&&s(6,$=r.label),"container"in r&&s(7,m=r.container),"scale"in r&&s(8,B=r.scale),"min_width"in r&&s(9,a=r.min_width),"loading_status"in r&&s(10,f=r.loading_status),"show_label"in r&&s(11,z=r.show_label),"_selectable"in r&&s(12,w=r._selectable),"show_heading"in r&&s(13,N=r.show_heading)},n.$$.update=()=>{n.$$.dirty&32801&&JSON.stringify(u)!==JSON.stringify(c)&&(s(15,c=u),l.dispatch("change")),n.$$.dirty&32&&s(14,t=u.label)},[l,i,o,d,v,u,$,m,B,a,f,z,w,N,t,c,I,J]}class Ue extends we{constructor(e){super(),Le(this,e,Ee,qe,Be,{gradio:0,elem_id:1,elem_classes:2,visible:3,color:4,value:5,label:6,container:7,scale:8,min_width:9,loading_status:10,show_label:11,_selectable:12,show_heading:13})}get gradio(){return this.$$.ctx[0]}set gradio(e){this.$$set({gradio:e}),h()}get elem_id(){return this.$$.ctx[1]}set elem_id(e){this.$$set({elem_id:e}),h()}get elem_classes(){return this.$$.ctx[2]}set elem_classes(e){this.$$set({elem_classes:e}),h()}get visible(){return this.$$.ctx[3]}set visible(e){this.$$set({visible:e}),h()}get color(){return this.$$.ctx[4]}set color(e){this.$$set({color:e}),h()}get value(){return this.$$.ctx[5]}set value(e){this.$$set({value:e}),h()}get label(){return this.$$.ctx[6]}set label(e){this.$$set({label:e}),h()}get container(){return this.$$.ctx[7]}set container(e){this.$$set({container:e}),h()}get scale(){return this.$$.ctx[8]}set scale(e){this.$$set({scale:e}),h()}get min_width(){return this.$$.ctx[9]}set min_width(e){this.$$set({min_width:e}),h()}get loading_status(){return this.$$.ctx[10]}set loading_status(e){this.$$set({loading_status:e}),h()}get show_label(){return this.$$.ctx[11]}set show_label(e){this.$$set({show_label:e}),h()}get _selectable(){return this.$$.ctx[12]}set _selectable(e){this.$$set({_selectable:e}),h()}get show_heading(){return this.$$.ctx[13]}set show_heading(e){this.$$set({show_heading:e}),h()}}export{ve as BaseLabel,Ue as default};
//# sourceMappingURL=Index-DeQezhBd.js.map
