{"version": 3, "file": "Index-DeQezhBd.js", "sources": ["../../../../js/label/shared/Label.svelte", "../../../../js/label/Index.svelte"], "sourcesContent": ["<script lang=\"ts\">\n\timport type { SelectData } from \"@gradio/utils\";\n\timport { createEventDispatcher } from \"svelte\";\n\n\texport let value: {\n\t\tlabel?: string;\n\t\tconfidences?: { label: string; confidence: number }[];\n\t};\n\n\tconst dispatch = createEventDispatcher<{ select: SelectData }>();\n\n\texport let color: string | undefined = undefined;\n\texport let selectable = false;\n\texport let show_heading = true;\n\n\tfunction get_aria_referenceable_id(elem_id: string): string {\n\t\t// `aria-labelledby` interprets the value as a space-separated id reference list,\n\t\t// so each single id should not contain any spaces.\n\t\t// Ref: https://developer.mozilla.org/en-US/docs/Web/Accessibility/ARIA/Attributes/aria-labelledby#benefits_and_drawbacks\n\t\treturn elem_id.replace(/\\s/g, \"-\");\n\t}\n</script>\n\n<div class=\"container\">\n\t{#if show_heading || !value.confidences}\n\t\t<h2\n\t\t\tclass=\"output-class\"\n\t\t\tdata-testid=\"label-output-value\"\n\t\t\tclass:no-confidence={!(\"confidences\" in value)}\n\t\t\tstyle:background-color={color || \"transparent\"}\n\t\t>\n\t\t\t{value.label}\n\t\t</h2>\n\t{/if}\n\n\t{#if typeof value === \"object\" && value.confidences}\n\t\t{#each value.confidences as confidence_set, i}\n\t\t\t<button\n\t\t\t\tclass=\"confidence-set group\"\n\t\t\t\tdata-testid={`${confidence_set.label}-confidence-set`}\n\t\t\t\tclass:selectable\n\t\t\t\ton:click={() => {\n\t\t\t\t\tdispatch(\"select\", { index: i, value: confidence_set.label });\n\t\t\t\t}}\n\t\t\t>\n\t\t\t\t<div class=\"inner-wrap\">\n\t\t\t\t\t<meter\n\t\t\t\t\t\taria-labelledby={get_aria_referenceable_id(\n\t\t\t\t\t\t\t`meter-text-${confidence_set.label}`\n\t\t\t\t\t\t)}\n\t\t\t\t\t\taria-label={confidence_set.label}\n\t\t\t\t\t\taria-valuenow={Math.round(confidence_set.confidence * 100)}\n\t\t\t\t\t\taria-valuemin=\"0\"\n\t\t\t\t\t\taria-valuemax=\"100\"\n\t\t\t\t\t\tclass=\"bar\"\n\t\t\t\t\t\tmin=\"0\"\n\t\t\t\t\t\tmax=\"1\"\n\t\t\t\t\t\tvalue={confidence_set.confidence}\n\t\t\t\t\t\tstyle=\"width: {confidence_set.confidence *\n\t\t\t\t\t\t\t100}%; background: var(--stat-background-fill);\n\t\t\t\t\t\t\"\n\t\t\t\t\t/>\n\n\t\t\t\t\t<dl class=\"label\">\n\t\t\t\t\t\t<dt\n\t\t\t\t\t\t\tid={get_aria_referenceable_id(\n\t\t\t\t\t\t\t\t`meter-text-${confidence_set.label}`\n\t\t\t\t\t\t\t)}\n\t\t\t\t\t\t\tclass=\"text\"\n\t\t\t\t\t\t>\n\t\t\t\t\t\t\t{confidence_set.label}\n\t\t\t\t\t\t</dt>\n\t\t\t\t\t\t<div class=\"line\" />\n\t\t\t\t\t\t<dd class=\"confidence\">\n\t\t\t\t\t\t\t{Math.round(confidence_set.confidence * 100)}%\n\t\t\t\t\t\t</dd>\n\t\t\t\t\t</dl>\n\t\t\t\t</div>\n\t\t\t</button>\n\t\t{/each}\n\t{/if}\n</div>\n\n<style>\n\t.container {\n\t\tpadding: var(--block-padding);\n\t}\n\t.output-class {\n\t\tdisplay: flex;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t\tpadding: var(--size-6) var(--size-4);\n\t\tcolor: var(--body-text-color);\n\t\tfont-weight: var(--weight-bold);\n\t\tfont-size: var(--text-xxl);\n\t}\n\n\t.confidence-set {\n\t\tdisplay: flex;\n\t\tjustify-content: space-between;\n\t\talign-items: flex-start;\n\t\tmargin-bottom: var(--size-2);\n\t\tcolor: var(--body-text-color);\n\t\tline-height: var(--line-none);\n\t\tfont-family: var(--font-mono);\n\t\twidth: 100%;\n\t}\n\n\t.confidence-set:last-child {\n\t\tmargin-bottom: 0;\n\t}\n\n\t.inner-wrap {\n\t\tflex: 1 1 0%;\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t}\n\n\t.bar {\n\t\tappearance: none;\n\t\t-webkit-appearance: none;\n\t\t-moz-appearance: none;\n\t\talign-self: flex-start;\n\t\tmargin-bottom: var(--size-1);\n\t\tborder-radius: var(--radius-md);\n\t\tbackground: var(--stat-background-fill);\n\t\theight: var(--size-1);\n\t\tborder: none;\n\t}\n\n\t.bar::-moz-meter-bar {\n\t\tborder-radius: var(--radius-md);\n\t\tbackground: var(--stat-background-fill);\n\t}\n\n\t.bar::-webkit-meter-bar {\n\t\tborder-radius: var(--radius-md);\n\t\tbackground: var(--stat-background-fill);\n\t\tborder: none;\n\t}\n\n\t.bar::-webkit-meter-optimum-value,\n\t.bar::-webkit-meter-suboptimum-value,\n\t.bar::-webkit-meter-even-less-good-value {\n\t\tborder-radius: var(--radius-md);\n\t\tbackground: var(--stat-background-fill);\n\t}\n\n\t.bar::-ms-fill {\n\t\tborder-radius: var(--radius-md);\n\t\tbackground: var(--stat-background-fill);\n\t\tborder: none;\n\t}\n\n\t.label {\n\t\tdisplay: flex;\n\t\talign-items: baseline;\n\t}\n\n\t.label > * + * {\n\t\tmargin-left: var(--size-2);\n\t}\n\n\t.confidence-set:hover .label {\n\t\tcolor: var(--color-accent);\n\t}\n\n\t.confidence-set:focus .label {\n\t\tcolor: var(--color-accent);\n\t}\n\n\t.text {\n\t\tline-height: var(--line-md);\n\t\ttext-align: left;\n\t}\n\n\t.line {\n\t\tflex: 1 1 0%;\n\t\tborder: 1px dashed var(--border-color-primary);\n\t\tpadding-right: var(--size-4);\n\t\tpadding-left: var(--size-4);\n\t}\n\n\t.confidence {\n\t\tmargin-left: auto;\n\t\ttext-align: right;\n\t}\n</style>\n", "<script context=\"module\" lang=\"ts\">\n\texport { default as BaseLabel } from \"./shared/Label.svelte\";\n</script>\n\n<script lang=\"ts\">\n\timport type { Gradio, SelectData } from \"@gradio/utils\";\n\timport Label from \"./shared/Label.svelte\";\n\timport { LineChart as LabelIcon } from \"@gradio/icons\";\n\timport { Block, BlockLabel, Empty } from \"@gradio/atoms\";\n\timport { StatusTracker } from \"@gradio/statustracker\";\n\timport type { LoadingStatus } from \"@gradio/statustracker\";\n\n\texport let gradio: Gradio<{\n\t\tchange: never;\n\t\tselect: SelectData;\n\t\tclear_status: LoadingStatus;\n\t}>;\n\texport let elem_id = \"\";\n\texport let elem_classes: string[] = [];\n\texport let visible = true;\n\texport let color: undefined | string = undefined;\n\texport let value: {\n\t\tlabel?: string;\n\t\tconfidences?: { label: string; confidence: number }[];\n\t} = {};\n\tlet old_value: typeof value | null = null;\n\texport let label = gradio.i18n(\"label.label\");\n\texport let container = true;\n\texport let scale: number | null = null;\n\texport let min_width: number | undefined = undefined;\n\texport let loading_status: LoadingStatus;\n\texport let show_label = true;\n\texport let _selectable = false;\n\texport let show_heading = true;\n\n\t$: {\n\t\tif (JSON.stringify(value) !== JSON.stringify(old_value)) {\n\t\t\told_value = value;\n\t\t\tgradio.dispatch(\"change\");\n\t\t}\n\t}\n\n\t$: _label = value.label;\n</script>\n\n<Block\n\ttest_id=\"label\"\n\t{visible}\n\t{elem_id}\n\t{elem_classes}\n\t{container}\n\t{scale}\n\t{min_width}\n\tpadding={false}\n>\n\t<StatusTracker\n\t\tautoscroll={gradio.autoscroll}\n\t\ti18n={gradio.i18n}\n\t\t{...loading_status}\n\t\ton:clear_status={() => gradio.dispatch(\"clear_status\", loading_status)}\n\t/>\n\t{#if show_label}\n\t\t<BlockLabel\n\t\t\tIcon={LabelIcon}\n\t\t\t{label}\n\t\t\tdisable={container === false}\n\t\t\tfloat={show_heading === true}\n\t\t/>\n\t{/if}\n\t{#if _label !== undefined && _label !== null}\n\t\t<Label\n\t\t\ton:select={({ detail }) => gradio.dispatch(\"select\", detail)}\n\t\t\tselectable={_selectable}\n\t\t\t{value}\n\t\t\t{color}\n\t\t\t{show_heading}\n\t\t/>\n\t{:else}\n\t\t<Empty unpadded_box={true}><LabelIcon /></Empty>\n\t{/if}\n</Block>\n"], "names": ["t_value", "ctx", "toggle_class", "h2", "set_style", "insert", "target", "anchor", "dirty", "set_data", "each_value", "ensure_array_like", "i", "t1_value", "t3_value", "get_aria_referenceable_id", "attr", "meter", "meter_aria_label_value", "meter_aria_valuenow_value", "meter_value_value", "button", "button_data_testid_value", "append", "div1", "dl", "dt", "div0", "dd", "t1", "t3", "create_if_block_1", "if_block1", "create_if_block", "div", "elem_id", "value", "$$props", "dispatch", "createEventDispatcher", "color", "selectable", "show_heading", "confidence_set", "LabelIcon", "blocklabel_changes", "gradio", "elem_classes", "visible", "old_value", "label", "container", "scale", "min_width", "loading_status", "show_label", "_selectable", "clear_status_handler", "select_handler", "detail", "$$invalidate", "_label"], "mappings": "20BAEuC,EAAA,OAAA,6GA6BnCA,EAAAC,KAAM,MAAK,oHAHWC,EAAAC,EAAA,gBAAA,EAAA,gBAAiBF,EAAK,CAAA,EAAA,EACrBG,EAAAD,EAAA,mBAAAF,MAAS,aAAa,UAJ/CI,EAOIC,EAAAH,EAAAI,CAAA,iBADFC,EAAA,GAAAR,KAAAA,EAAAC,KAAM,MAAK,KAAAQ,EAAA,EAAAT,CAAA,OAHWE,EAAAC,EAAA,gBAAA,EAAA,gBAAiBF,EAAK,CAAA,EAAA,OACrBG,EAAAD,EAAA,mBAAAF,MAAS,aAAa,sCAOxCS,EAAAC,EAAAV,KAAM,WAAW,uBAAtB,OAAIW,GAAA,0JAACF,EAAAC,EAAAV,KAAM,WAAW,oBAAtB,OAAIW,GAAA,EAAA,yHAAJ,sEAkCIC,EAAAZ,KAAe,MAAK,aAIpBa,EAAA,KAAK,MAAMb,EAAe,CAAA,EAAA,WAAa,GAAG,EAAA,4KAAE,GAC9C,gCA5BiBc,EAAyB,cAC3Bd,EAAc,CAAA,EAAC,KAAK,EAAA,CAAA,EAEvBe,EAAAC,EAAA,aAAAC,EAAAjB,KAAe,KAAK,EACjBe,EAAAC,EAAA,gBAAAE,EAAA,KAAK,MAAMlB,EAAe,CAAA,EAAA,WAAa,GAAG,CAAA,sHAMlDgB,EAAA,MAAAG,EAAAnB,KAAe,uBACPA,EAAc,CAAA,EAAC,WAC7B,IAAG,GAAA,6DAMCc,EAAyB,cACdd,EAAc,CAAA,EAAC,KAAK,EAAA,CAAA,iPA3BtBe,EAAAK,EAAA,cAAAC,EAAA,GAAArB,KAAe,KAAK,iBAAA,iCAFrCI,EAyCQC,EAAAe,EAAAd,CAAA,EAjCPgB,EAgCKF,EAAAG,CAAA,EA/BJD,EAeCC,EAAAP,CAAA,SAEDM,EAaIC,EAAAC,CAAA,EAZHF,EAOIE,EAAAC,CAAA,gBACJH,EAAmBE,EAAAE,CAAA,EACnBJ,EAEIE,EAAAG,CAAA,4EA5Bab,EAAyB,cAC3Bd,EAAc,CAAA,EAAC,KAAK,EAAA,6BAEvBO,EAAA,GAAAU,KAAAA,EAAAjB,KAAe,4BACZO,EAAA,GAAAW,KAAAA,EAAA,KAAK,MAAMlB,EAAe,CAAA,EAAA,WAAa,GAAG,2BAMlDO,EAAA,GAAAY,KAAAA,EAAAnB,KAAe,0CACPA,EAAc,CAAA,EAAC,WAC7B,IAAG,GAAA,EAWFO,EAAA,GAAAK,KAAAA,EAAAZ,KAAe,MAAK,KAAAQ,EAAAoB,EAAAhB,CAAA,cALjBE,EAAyB,cACdd,EAAc,CAAA,EAAC,KAAK,EAAA,gBAQlCO,EAAA,GAAAM,KAAAA,EAAA,KAAK,MAAMb,EAAe,CAAA,EAAA,WAAa,GAAG,EAAA,KAAAQ,EAAAqB,EAAAhB,CAAA,EAnC9BN,EAAA,GAAAc,KAAAA,EAAA,GAAArB,KAAe,KAAK,wHAflCA,EAAY,CAAA,GAAA,CAAKA,EAAK,CAAA,EAAC,cAAW8B,EAAA9B,CAAA,EAW3B+B,EAAA,OAAA/B,EAAU,CAAA,GAAA,UAAYA,KAAM,aAAWgC,EAAAhC,CAAA,gGAZpDI,EA0DKC,EAAA4B,EAAA3B,CAAA,iDAzDCN,EAAY,CAAA,GAAA,CAAKA,EAAK,CAAA,EAAC,kEAWhB,OAAAA,EAAU,CAAA,GAAA,UAAYA,KAAM,wHApB/Bc,EAA0BoB,EAAA,QAI3BA,EAAQ,QAAQ,MAAO,GAAG,qBAfvB,GAAA,CAAA,MAAAC,CAAA,EAAAC,QAKLC,EAAWC,SAEN,MAAAC,EAA4B,MAAA,EAAAH,GAC5B,WAAAI,EAAa,EAAA,EAAAJ,GACb,aAAAK,EAAe,EAAA,EAAAL,kBA6BtBC,EAAS,SAAQ,CAAI,MAAO1B,EAAG,MAAO+B,EAAe,KAAK,CAAA,48BCqBtDC,cAEG,QAAA3C,OAAc,GAChB,MAAAA,QAAiB,0FADfO,EAAA,MAAAqC,EAAA,QAAA5C,OAAc,IAChBO,EAAA,OAAAqC,EAAA,MAAA5C,QAAiB,0JAYJ,sSANRA,EAAW,EAAA,yJAAXA,EAAW,EAAA,uXAhBZ,CAAA,WAAAA,KAAO,UAAU,EACvB,CAAA,KAAAA,KAAO,IAAI,EACbA,EAAc,EAAA,2GAGdA,EAAU,EAAA,GAAA8B,GAAA9B,CAAA,uCAQV,OAAAA,EAAW,EAAA,IAAA,QAAaA,QAAW,KAAI,iMAb/BO,EAAA,GAAA,CAAA,WAAAP,KAAO,UAAU,EACvBO,EAAA,GAAA,CAAA,KAAAP,KAAO,IAAI,aACbA,EAAc,EAAA,CAAA,iBAGdA,EAAU,EAAA,+hBARN,wZAzCE,CAAA,OAAA6C,CAAA,EAAAT,GAKA,QAAAF,EAAU,EAAA,EAAAE,EACV,CAAA,aAAAU,EAAA,EAAA,EAAAV,GACA,QAAAW,EAAU,EAAA,EAAAX,GACV,MAAAG,EAA4B,MAAA,EAAAH,EAC5B,CAAA,MAAAD,EAAA,EAAA,EAAAC,EAIPY,EAAiC,MAC1B,MAAAC,EAAQJ,EAAO,KAAK,aAAa,CAAA,EAAAT,GACjC,UAAAc,EAAY,EAAA,EAAAd,GACZ,MAAAe,EAAuB,IAAA,EAAAf,GACvB,UAAAgB,EAAgC,MAAA,EAAAhB,EAChC,CAAA,eAAAiB,CAAA,EAAAjB,GACA,WAAAkB,EAAa,EAAA,EAAAlB,GACb,YAAAmB,EAAc,EAAA,EAAAnB,GACd,aAAAK,EAAe,EAAA,EAAAL,EA0BF,MAAAoB,EAAA,IAAAX,EAAO,SAAS,eAAgBQ,CAAc,EAYtDI,EAAA,CAAA,CAAA,OAAAC,KAAab,EAAO,SAAS,SAAUa,CAAM,miBAnCxD,KAAK,UAAUvB,CAAK,IAAM,KAAK,UAAUa,CAAS,SACrDA,EAAYb,CAAA,EACZU,EAAO,SAAS,QAAQ,kBAI1Bc,EAAA,GAAGC,EAASzB,EAAM,KAAA"}