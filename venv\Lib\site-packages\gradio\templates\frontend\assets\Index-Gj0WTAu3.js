import{S as Q}from"./index-DYtg3pip.js";/* empty css                                                        */import"./StreamingBar.svelte_svelte_type_style_lang-DlRzv56K.js";import W from"./Index-BrpXEgHX.js";import"./index-DJ2rNx9E.js";import"./svelte/svelte.js";import"./MarkdownCode.svelte_svelte_type_style_lang-Bl8p72Ow.js";import"./prism-python-CeMtt1IT.js";import"./IconButton-C_HS7fTi.js";import"./Clear-By3xiIwg.js";const{SvelteComponent:A,append:S,attr:g,binding_callbacks:F,create_slot:G,detach:J,element:E,flush:L,get_all_dirty_from_scope:K,get_slot_changes:O,init:U,insert:V,listen:X,safe_not_equal:Y,space:Z,toggle_class:m,transition_in:y,transition_out:x,update_slot_base:ee}=window.__gradio__svelte__internal,{createEventDispatcher:te,onMount:ne}=window.__gradio__svelte__internal;function se(s){let e,n,i,o,_,c,l,t;const r=s[10].default,a=G(r,s,s[9],null);return{c(){e=E("div"),n=E("button"),n.innerHTML='<div class="chevron svelte-1hez9vf"><span class="chevron-left svelte-1hez9vf"></span></div>',i=Z(),o=E("div"),a&&a.c(),g(n,"class","toggle-button svelte-1hez9vf"),g(n,"aria-label","Toggle Sidebar"),g(o,"class","sidebar-content svelte-1hez9vf"),g(e,"class","sidebar svelte-1hez9vf"),g(e,"style",_="width: "+s[6]+"; "+s[1]+": calc("+s[6]+" * -1)"),m(e,"open",s[2]),m(e,"right",s[1]==="right"),m(e,"reduce-motion",s[4])},m(u,p){V(u,e,p),S(e,n),S(e,i),S(e,o),a&&a.m(o,null),s[12](e),c=!0,l||(t=X(n,"click",s[11]),l=!0)},p(u,[p]){a&&a.p&&(!c||p&512)&&ee(a,r,u,u[9],c?O(r,u[9],p,null):K(u[9]),null),(!c||p&2&&_!==(_="width: "+u[6]+"; "+u[1]+": calc("+u[6]+" * -1)"))&&g(e,"style",_),(!c||p&4)&&m(e,"open",u[2]),(!c||p&2)&&m(e,"right",u[1]==="right"),(!c||p&16)&&m(e,"reduce-motion",u[4])},i(u){c||(y(a,u),c=!0)},o(u){x(a,u),c=!1},d(u){u&&J(e),a&&a.d(u),s[12](null),l=!1,t()}}}function ie(s,e,n){let{$$slots:i={},$$scope:o}=e;const _=te();let{open:c=!0}=e,{width:l}=e,{position:t="left"}=e,r=!1,a=!1,u,p=0,z=typeof l=="number"?`${l}px`:l,v;function f(){if(!u.closest(".wrap"))return;const d=u.closest(".wrap")?.getBoundingClientRect();if(!d)return;const k=u.getBoundingClientRect(),$=t==="left"?d.left:window.innerWidth-d.right;p=Math.max(0,k.width-$+30)}ne(()=>{u.closest(".wrap")?.classList.add("sidebar-parent"),f(),window.addEventListener("resize",f),(()=>{document.documentElement.style.setProperty("--overlap-amount",`${p}px`)})(),n(8,r=!0);const k=window.matchMedia("(prefers-reduced-motion: reduce)");n(4,v=k.matches);const $=N=>{n(4,v=N.matches)};return k.addEventListener("change",$),()=>{window.removeEventListener("resize",f),k.removeEventListener("change",$)}});const D=()=>{n(2,a=!a),n(0,c=a),_(a?"expand":"collapse")};function H(d){F[d?"unshift":"push"](()=>{u=d,n(3,u)})}return s.$$set=d=>{"open"in d&&n(0,c=d.open),"width"in d&&n(7,l=d.width),"position"in d&&n(1,t=d.position),"$$scope"in d&&n(9,o=d.$$scope)},s.$$.update=()=>{s.$$.dirty&257&&r&&n(2,a=c)},[c,t,a,u,v,_,z,l,r,o,i,D,H]}class oe extends A{constructor(e){super(),U(this,e,ie,se,Y,{open:0,width:7,position:1})}get open(){return this.$$.ctx[0]}set open(e){this.$$set({open:e}),L()}get width(){return this.$$.ctx[7]}set width(e){this.$$set({width:e}),L()}get position(){return this.$$.ctx[1]}set position(e){this.$$set({position:e}),L()}}const{SvelteComponent:le,add_flush_callback:R,assign:re,bind:B,binding_callbacks:I,check_outros:ae,create_component:M,create_slot:ue,destroy_component:C,detach:P,empty:_e,flush:b,get_all_dirty_from_scope:ce,get_slot_changes:fe,get_spread_object:de,get_spread_update:pe,group_outros:he,init:ge,insert:T,mount_component:q,safe_not_equal:me,space:be,transition_in:h,transition_out:w,update_slot_base:we}=window.__gradio__svelte__internal;function j(s){let e,n,i,o;function _(t){s[7](t)}function c(t){s[8](t)}let l={width:s[4],$$slots:{default:[ke]},$$scope:{ctx:s}};return s[0]!==void 0&&(l.open=s[0]),s[1]!==void 0&&(l.position=s[1]),e=new oe({props:l}),I.push(()=>B(e,"open",_)),I.push(()=>B(e,"position",c)),e.$on("expand",s[9]),e.$on("collapse",s[10]),{c(){M(e.$$.fragment)},m(t,r){q(e,t,r),o=!0},p(t,r){const a={};r&16&&(a.width=t[4]),r&2048&&(a.$$scope={dirty:r,ctx:t}),!n&&r&1&&(n=!0,a.open=t[0],R(()=>n=!1)),!i&&r&2&&(i=!0,a.position=t[1],R(()=>i=!1)),e.$set(a)},i(t){o||(h(e.$$.fragment,t),o=!0)},o(t){w(e.$$.fragment,t),o=!1},d(t){C(e,t)}}}function ve(s){let e;const n=s[6].default,i=ue(n,s,s[11],null);return{c(){i&&i.c()},m(o,_){i&&i.m(o,_),e=!0},p(o,_){i&&i.p&&(!e||_&2048)&&we(i,n,o,o[11],e?fe(n,o[11],_,null):ce(o[11]),null)},i(o){e||(h(i,o),e=!0)},o(o){w(i,o),e=!1},d(o){i&&i.d(o)}}}function ke(s){let e,n;return e=new W({props:{$$slots:{default:[ve]},$$scope:{ctx:s}}}),{c(){M(e.$$.fragment)},m(i,o){q(e,i,o),n=!0},p(i,o){const _={};o&2048&&(_.$$scope={dirty:o,ctx:i}),e.$set(_)},i(i){n||(h(e.$$.fragment,i),n=!0)},o(i){w(e.$$.fragment,i),n=!1},d(i){C(e,i)}}}function $e(s){let e,n,i,o;const _=[{autoscroll:s[3].autoscroll},{i18n:s[3].i18n},s[2]];let c={};for(let t=0;t<_.length;t+=1)c=re(c,_[t]);e=new Q({props:c});let l=s[5]&&j(s);return{c(){M(e.$$.fragment),n=be(),l&&l.c(),i=_e()},m(t,r){q(e,t,r),T(t,n,r),l&&l.m(t,r),T(t,i,r),o=!0},p(t,[r]){const a=r&12?pe(_,[r&8&&{autoscroll:t[3].autoscroll},r&8&&{i18n:t[3].i18n},r&4&&de(t[2])]):{};e.$set(a),t[5]?l?(l.p(t,r),r&32&&h(l,1)):(l=j(t),l.c(),h(l,1),l.m(i.parentNode,i)):l&&(he(),w(l,1,1,()=>{l=null}),ae())},i(t){o||(h(e.$$.fragment,t),h(l),o=!0)},o(t){w(e.$$.fragment,t),w(l),o=!1},d(t){t&&(P(n),P(i)),C(e,t),l&&l.d(t)}}}function ze(s,e,n){let{$$slots:i={},$$scope:o}=e,{open:_=!0}=e,{position:c="left"}=e,{loading_status:l}=e,{gradio:t}=e,{width:r}=e,{visible:a=!0}=e;function u(f){_=f,n(0,_)}function p(f){c=f,n(1,c)}const z=()=>t.dispatch("expand"),v=()=>t.dispatch("collapse");return s.$$set=f=>{"open"in f&&n(0,_=f.open),"position"in f&&n(1,c=f.position),"loading_status"in f&&n(2,l=f.loading_status),"gradio"in f&&n(3,t=f.gradio),"width"in f&&n(4,r=f.width),"visible"in f&&n(5,a=f.visible),"$$scope"in f&&n(11,o=f.$$scope)},[_,c,l,t,r,a,i,u,p,z,v,o]}class Te extends le{constructor(e){super(),ge(this,e,ze,$e,me,{open:0,position:1,loading_status:2,gradio:3,width:4,visible:5})}get open(){return this.$$.ctx[0]}set open(e){this.$$set({open:e}),b()}get position(){return this.$$.ctx[1]}set position(e){this.$$set({position:e}),b()}get loading_status(){return this.$$.ctx[2]}set loading_status(e){this.$$set({loading_status:e}),b()}get gradio(){return this.$$.ctx[3]}set gradio(e){this.$$set({gradio:e}),b()}get width(){return this.$$.ctx[4]}set width(e){this.$$set({width:e}),b()}get visible(){return this.$$.ctx[5]}set visible(e){this.$$set({visible:e}),b()}}export{Te as default};
//# sourceMappingURL=Index-Gj0WTAu3.js.map
