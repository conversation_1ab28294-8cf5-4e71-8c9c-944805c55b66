{"version": 3, "file": "Index-Gj0WTAu3.js", "sources": ["../../../../js/sidebar/shared/Sidebar.svelte", "../../../../js/sidebar/Index.svelte"], "sourcesContent": ["<script lang=\"ts\">\n\timport { createEventDispatcher, onMount } from \"svelte\";\n\tconst dispatch = createEventDispatcher<{\n\t\texpand: void;\n\t\tcollapse: void;\n\t}>();\n\n\texport let open = true;\n\texport let width: number | string;\n\texport let position: \"left\" | \"right\" = \"left\";\n\n\t// Using a temporary variable to animate the sidebar opening at the start\n\tlet mounted = false;\n\tlet _open = false;\n\tlet sidebar_div: HTMLElement;\n\tlet overlap_amount = 0;\n\n\tlet width_css = typeof width === \"number\" ? `${width}px` : width;\n\tlet prefersReducedMotion: boolean;\n\n\t// Check if the sidebar overlaps with the main content\n\tfunction check_overlap(): void {\n\t\tif (!sidebar_div.closest(\".wrap\")) return;\n\t\tconst parent_rect = sidebar_div.closest(\".wrap\")?.getBoundingClientRect();\n\t\tif (!parent_rect) return;\n\t\tconst sidebar_rect = sidebar_div.getBoundingClientRect();\n\t\tconst available_space =\n\t\t\tposition === \"left\"\n\t\t\t\t? parent_rect.left\n\t\t\t\t: window.innerWidth - parent_rect.right;\n\t\toverlap_amount = Math.max(0, sidebar_rect.width - available_space + 30);\n\t}\n\n\tonMount(() => {\n\t\tsidebar_div.closest(\".wrap\")?.classList.add(\"sidebar-parent\");\n\t\tcheck_overlap();\n\t\twindow.addEventListener(\"resize\", check_overlap);\n\t\tconst update_parent_overlap = (): void => {\n\t\t\tdocument.documentElement.style.setProperty(\n\t\t\t\t\"--overlap-amount\",\n\t\t\t\t`${overlap_amount}px`\n\t\t\t);\n\t\t};\n\t\tupdate_parent_overlap();\n\t\tmounted = true;\n\t\tconst mediaQuery = window.matchMedia(\"(prefers-reduced-motion: reduce)\");\n\t\tprefersReducedMotion = mediaQuery.matches;\n\t\tconst updateMotionPreference = (e: MediaQueryListEvent): void => {\n\t\t\tprefersReducedMotion = e.matches;\n\t\t};\n\t\tmediaQuery.addEventListener(\"change\", updateMotionPreference);\n\t\treturn () => {\n\t\t\twindow.removeEventListener(\"resize\", check_overlap);\n\t\t\tmediaQuery.removeEventListener(\"change\", updateMotionPreference);\n\t\t};\n\t});\n\n\t// We need to wait for the component to be mounted before we can set the open state\n\t// so that it animates correctly.\n\t$: if (mounted) _open = open;\n</script>\n\n<div\n\tclass=\"sidebar\"\n\tclass:open={_open}\n\tclass:right={position === \"right\"}\n\tclass:reduce-motion={prefersReducedMotion}\n\tbind:this={sidebar_div}\n\tstyle=\"width: {width_css}; {position}: calc({width_css} * -1)\"\n>\n\t<button\n\t\ton:click={() => {\n\t\t\t_open = !_open;\n\t\t\topen = _open;\n\t\t\tif (_open) {\n\t\t\t\tdispatch(\"expand\");\n\t\t\t} else {\n\t\t\t\tdispatch(\"collapse\");\n\t\t\t}\n\t\t}}\n\t\tclass=\"toggle-button\"\n\t\taria-label=\"Toggle Sidebar\"\n\t>\n\t\t<div class=\"chevron\">\n\t\t\t<span class=\"chevron-left\"></span>\n\t\t</div>\n\t</button>\n\t<div class=\"sidebar-content\">\n\t\t<slot />\n\t</div>\n</div>\n\n<style>\n\t/* Mobile styles (≤ 768px) */\n\t@media (max-width: 768px) {\n\t\t.sidebar {\n\t\t\twidth: 100vw !important;\n\t\t}\n\n\t\t.sidebar:not(.right) {\n\t\t\tleft: -100vw !important;\n\t\t}\n\n\t\t.sidebar.right {\n\t\t\tright: -100vw !important;\n\t\t}\n\n\t\t.sidebar:not(.reduce-motion) {\n\t\t\ttransition: transform 0.3s ease-in-out !important;\n\t\t}\n\n\t\t:global(.sidebar-parent) {\n\t\t\tpadding-left: 0 !important;\n\t\t\tpadding-right: 0 !important;\n\t\t}\n\n\t\t:global(.sidebar-parent:has(.sidebar.open)) {\n\t\t\tpadding-left: 0 !important;\n\t\t\tpadding-right: 0 !important;\n\t\t}\n\t\t.sidebar.open {\n\t\t\tz-index: 1001 !important;\n\t\t}\n\t}\n\n\t:global(.sidebar-parent) {\n\t\tdisplay: flex !important;\n\t\tpadding-left: 0;\n\t\tpadding-right: 0;\n\t}\n\n\t:global(.sidebar-parent:not(.reduce-motion)) {\n\t\ttransition:\n\t\t\tpadding-left 0.3s ease-in-out,\n\t\t\tpadding-right 0.3s ease-in-out;\n\t}\n\n\t:global(.sidebar-parent:has(.sidebar.open:not(.right))) {\n\t\tpadding-left: var(--overlap-amount);\n\t}\n\n\t:global(.sidebar-parent:has(.sidebar.open.right)) {\n\t\tpadding-right: var(--overlap-amount);\n\t}\n\n\t.sidebar {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\tposition: fixed;\n\t\ttop: 0;\n\t\theight: 100%;\n\t\tbackground-color: var(--background-fill-secondary);\n\t\ttransform: translateX(0%);\n\t\tz-index: 1000;\n\t}\n\n\t.sidebar:not(.reduce-motion) {\n\t\ttransition: transform 0.3s ease-in-out;\n\t}\n\n\t.sidebar.open:not(.right) {\n\t\ttransform: translateX(100%);\n\t\tbox-shadow: var(--size-1) 0 var(--size-1) rgba(0, 0, 0, 0.05);\n\t}\n\n\t.sidebar.open.right {\n\t\ttransform: translateX(-100%);\n\t\tbox-shadow: calc(var(--size-1) * -1) 0 var(--size-1) rgba(0, 0, 0, 0.05);\n\t}\n\n\t.toggle-button {\n\t\tposition: absolute;\n\t\ttop: var(--size-4);\n\t\tbackground: var(--background-fill-secondary);\n\t\tborder: 1px solid var(--border-color-primary);\n\t\tcursor: pointer;\n\t\tpadding: var(--size-2);\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\twidth: var(--size-7);\n\t\theight: var(--size-8);\n\t\tz-index: 1001;\n\t\tborder-radius: 0;\n\t}\n\n\t.toggle-button:not(.reduce-motion) {\n\t\ttransition: all 0.3s ease-in-out;\n\t}\n\n\t.sidebar:not(.right) .toggle-button {\n\t\tleft: 100%;\n\t\tborder-radius: 0 var(--size-8) var(--size-8) 0;\n\t\tborder-left: none;\n\t}\n\n\t.sidebar.right .toggle-button {\n\t\tright: 100%;\n\t\ttransform: rotate(180deg);\n\t\tborder-radius: 0 var(--size-8) var(--size-8) 0;\n\t\tborder-left: none;\n\t}\n\n\t.open:not(.right) .toggle-button {\n\t\tright: 0;\n\t\tleft: auto;\n\t\ttransform: rotate(180deg);\n\t\tborder-radius: 0 var(--size-8) var(--size-8) 0;\n\t\tborder-left: none;\n\t\tborder-right: 1px solid var(--border-color-primary);\n\t}\n\n\t.open.right .toggle-button {\n\t\tleft: 0;\n\t\tright: auto;\n\t\ttransform: rotate(0deg);\n\t\tborder-radius: 0 var(--size-8) var(--size-8) 0;\n\t\tborder-left: none;\n\t\tborder-right: 1px solid var(--border-color-primary);\n\t}\n\n\t.chevron {\n\t\tposition: relative;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tpadding-right: 8px;\n\t}\n\n\t.chevron-left {\n\t\tposition: relative;\n\t\twidth: var(--size-3);\n\t\theight: var(--size-3);\n\t\tborder-top: var(--size-0-5) solid var(--body-text-color);\n\t\tborder-right: var(--size-0-5) solid var(--body-text-color);\n\t\ttransform: rotate(45deg);\n\t}\n\n\t.sidebar-content {\n\t\tpadding: var(--size-5);\n\t\tpadding-right: var(--size-8);\n\t\toverflow-y: auto;\n\t}\n\n\t.sidebar.right .sidebar-content {\n\t\tpadding-left: var(--size-8);\n\t}\n</style>\n", "<script lang=\"ts\">\n\timport Sidebar from \"./shared/Sidebar.svelte\";\n\timport { StatusTracker } from \"@gradio/statustracker\";\n\timport type { LoadingStatus } from \"@gradio/statustracker\";\n\timport type { Gradio } from \"@gradio/utils\";\n\timport Column from \"@gradio/column\";\n\texport let open = true;\n\texport let position: \"left\" | \"right\" = \"left\";\n\texport let loading_status: LoadingStatus;\n\texport let gradio: Gradio<{\n\t\texpand: never;\n\t\tcollapse: never;\n\t}>;\n\texport let width: number | string;\n\texport let visible = true;\n</script>\n\n<StatusTracker\n\tautoscroll={gradio.autoscroll}\n\ti18n={gradio.i18n}\n\t{...loading_status}\n/>\n\n{#if visible}\n\t<Sidebar\n\t\tbind:open\n\t\tbind:position\n\t\t{width}\n\t\ton:expand={() => gradio.dispatch(\"expand\")}\n\t\ton:collapse={() => gradio.dispatch(\"collapse\")}\n\t>\n\t\t<Column>\n\t\t\t<slot />\n\t\t</Column>\n\t</Sidebar>\n{/if}\n"], "names": ["createEventDispatcher", "onMount", "ctx", "toggle_class", "div2", "insert", "target", "anchor", "append", "button", "div1", "dispatch", "open", "$$props", "width", "position", "mounted", "_open", "sidebar_div", "overlap_amount", "width_css", "prefersReducedMotion", "check_overlap", "parent_rect", "sidebar_rect", "available_space", "mediaQuery", "$$invalidate", "updateMotionPreference", "e", "$$value", "create_if_block", "dirty", "loading_status", "gradio", "visible"], "mappings": "stBACU,CAAA,sBAAAA,GAAA,QAAAC,IAAsC,OAAA,0cAmEhCC,EAAS,CAAA,EAAA,KAAIA,EAAQ,CAAA,EAAA,UAASA,EAAS,CAAA,EAAA,QAAA,aAJ1CA,EAAK,CAAA,CAAA,EACJC,EAAAC,EAAA,QAAAF,OAAa,OAAO,sBACZA,EAAoB,CAAA,CAAA,UAJ1CG,EA4BKC,EAAAF,EAAAG,CAAA,EApBJC,EAgBQJ,EAAAK,CAAA,SACRD,EAEKJ,EAAAM,CAAA,uKArBUR,EAAS,CAAA,EAAA,KAAIA,EAAQ,CAAA,EAAA,UAASA,EAAS,CAAA,EAAA,iDAJ1CA,EAAK,CAAA,CAAA,aACJC,EAAAC,EAAA,QAAAF,OAAa,OAAO,kCACZA,EAAoB,CAAA,CAAA,iJAhEnCS,EAAWX,SAKN,KAAAY,EAAO,EAAA,EAAAC,EACP,CAAA,MAAAC,CAAA,EAAAD,GACA,SAAAE,EAA6B,MAAA,EAAAF,EAGpCG,EAAU,GACVC,EAAQ,GACRC,EACAC,EAAiB,EAEjBC,EAAmB,OAAAN,GAAU,SAAc,GAAAA,CAAK,KAAOA,EACvDO,EAGK,SAAAC,GAAA,CACH,GAAA,CAAAJ,EAAY,QAAQ,OAAO,EAAA,aAC1BK,EAAcL,EAAY,QAAQ,OAAO,GAAG,sBAAA,EAC7C,GAAA,CAAAK,EAAA,OACC,MAAAC,EAAeN,EAAY,wBAC3BO,EACLV,IAAa,OACVQ,EAAY,KACZ,OAAO,WAAaA,EAAY,MACpCJ,EAAiB,KAAK,IAAI,EAAGK,EAAa,MAAQC,EAAkB,EAAE,EAGvExB,GAAA,IAAA,CACCiB,EAAY,QAAQ,OAAO,GAAG,UAAU,IAAI,gBAAgB,EAC5DI,IACA,OAAO,iBAAiB,SAAUA,CAAa,GACzC,IAAA,CACL,SAAS,gBAAgB,MAAM,YAC9B,mBAAA,GACGH,CAAc,IAAA,UAInBH,EAAU,EAAA,QACJU,EAAa,OAAO,WAAW,kCAAkC,EACvEC,EAAA,EAAAN,EAAuBK,EAAW,OAAA,QAC5BE,EAA0BC,GAAA,CAC/BF,EAAA,EAAAN,EAAuBQ,EAAE,OAAA,GAE1B,OAAAH,EAAW,iBAAiB,SAAUE,CAAsB,OAE3D,OAAO,oBAAoB,SAAUN,CAAa,EAClDI,EAAW,oBAAoB,SAAUE,CAAsB,kBAmB/DD,EAAA,EAAAV,GAASA,CAAK,EACdU,EAAA,EAAAf,EAAOK,CAAK,EAEXN,EADGM,EACM,SAEA,UAFQ,4CARTC,EAAWY,wLARfd,OAASC,EAAQL,CAAA,k6DCzCZ,CAAA,WAAAV,KAAO,UAAU,EACvB,CAAA,KAAAA,KAAO,IAAI,EACbA,EAAc,CAAA,8EAGdA,EAAO,CAAA,GAAA6B,EAAA7B,CAAA,sIALC8B,EAAA,GAAA,CAAA,WAAA9B,KAAO,UAAU,EACvB8B,EAAA,GAAA,CAAA,KAAA9B,KAAO,IAAI,UACbA,EAAc,CAAA,CAAA,iBAGdA,EAAO,CAAA,gRAjBA,KAAAU,EAAO,EAAA,EAAAC,GACP,SAAAE,EAA6B,MAAA,EAAAF,EAC7B,CAAA,eAAAoB,CAAA,EAAApB,EACA,CAAA,OAAAqB,CAAA,EAAArB,EAIA,CAAA,MAAAC,CAAA,EAAAD,GACA,QAAAsB,EAAU,EAAA,EAAAtB,gEAcHqB,EAAO,SAAS,QAAQ,QACtBA,EAAO,SAAS,UAAU"}