import{B as pe}from"./Block-CJdXVpa7.js";import"./MarkdownCode.svelte_svelte_type_style_lang-Bl8p72Ow.js";/* empty css                                                        */import{B as Be}from"./BlockLabel-3KxTaaiM.js";import{E as Ie}from"./Empty-ZqppqzTN.js";import{I as ne}from"./Image-Bsh8Umrh.js";import"./index-DJ2rNx9E.js";import{I as Me}from"./IconButtonWrapper--EIOWuEM.js";import{F as Se}from"./FullscreenButton-BG4mOKmH.js";import{S as qe}from"./index-DYtg3pip.js";import"./StreamingBar.svelte_svelte_type_style_lang-DlRzv56K.js";import{r as Y}from"./file-url-DoxvUUVV.js";import"./DownloadLink.svelte_svelte_type_style_lang-C_5UIfol.js";import"./prism-python-CeMtt1IT.js";import"./svelte/svelte.js";import"./IconButton-C_HS7fTi.js";import"./Clear-By3xiIwg.js";const{SvelteComponent:ze,add_flush_callback:Ee,append:L,assign:Fe,attr:k,bind:Ne,binding_callbacks:ie,check_outros:se,create_component:q,destroy_component:z,destroy_each:oe,detach:B,element:j,empty:ae,ensure_array_like:D,flush:w,get_spread_object:Pe,get_spread_update:je,group_outros:re,init:Ce,insert:I,listen:C,mount_component:E,run_all:Le,safe_not_equal:We,set_data:Ae,set_style:Z,space:P,src_url_equal:G,text:De,toggle_class:S,transition_in:v,transition_out:p}=window.__gradio__svelte__internal;function $(t,e,n){const l=t.slice();return l[33]=e[n],l[35]=n,l}function y(t,e,n){const l=t.slice();return l[33]=e[n],l[35]=n,l}function Ge(t){let e,n,l,s,o,i,f,_,b;n=new Me({props:{$$slots:{default:[Je]},$$scope:{ctx:t}}});let g=D(t[15]?t[15]?.annotations:[]),c=[];for(let u=0;u<g.length;u+=1)c[u]=ee(y(t,g,u));let m=t[6]&&t[15]&&le(t);return{c(){e=j("div"),q(n.$$.fragment),l=P(),s=j("img"),i=P();for(let u=0;u<c.length;u+=1)c[u].c();f=P(),m&&m.c(),_=ae(),k(s,"class","base-image svelte-303fln"),G(s.src,o=t[15]?t[15].image.url:null)||k(s,"src",o),k(s,"alt","the base file that is annotated"),S(s,"fit-height",t[7]&&!t[18]),k(e,"class","image-container svelte-303fln")},m(u,r){I(u,e,r),E(n,e,null),L(e,l),L(e,s),L(e,i);for(let h=0;h<c.length;h+=1)c[h]&&c[h].m(e,null);t[26](e),I(u,f,r),m&&m.m(u,r),I(u,_,r),b=!0},p(u,r){const h={};if(r[0]&278528|r[1]&64&&(h.$$scope={dirty:r,ctx:u}),n.$set(h),(!b||r[0]&32768&&!G(s.src,o=u[15]?u[15].image.url:null))&&k(s,"src",o),(!b||r[0]&262272)&&S(s,"fit-height",u[7]&&!u[18]),r[0]&360976){g=D(u[15]?u[15]?.annotations:[]);let d;for(d=0;d<g.length;d+=1){const M=y(u,g,d);c[d]?c[d].p(M,r):(c[d]=ee(M),c[d].c(),c[d].m(e,null))}for(;d<c.length;d+=1)c[d].d(1);c.length=g.length}u[6]&&u[15]?m?m.p(u,r):(m=le(u),m.c(),m.m(_.parentNode,_)):m&&(m.d(1),m=null)},i(u){b||(v(n.$$.fragment,u),b=!0)},o(u){p(n.$$.fragment,u),b=!1},d(u){u&&(B(e),B(f),B(_)),z(n),oe(c,u),t[26](null),m&&m.d(u)}}}function He(t){let e,n;return e=new Ie({props:{size:"large",unpadded_box:!0,$$slots:{default:[Ke]},$$scope:{ctx:t}}}),{c(){q(e.$$.fragment)},m(l,s){E(e,l,s),n=!0},p(l,s){const o={};s[1]&64&&(o.$$scope={dirty:s,ctx:l}),e.$set(o)},i(l){n||(v(e.$$.fragment,l),n=!0)},o(l){p(e.$$.fragment,l),n=!1},d(l){z(e,l)}}}function x(t){let e,n;return e=new Se({props:{fullscreen:t[18]}}),e.$on("fullscreen",t[25]),{c(){q(e.$$.fragment)},m(l,s){E(e,l,s),n=!0},p(l,s){const o={};s[0]&262144&&(o.fullscreen=l[18]),e.$set(o)},i(l){n||(v(e.$$.fragment,l),n=!0)},o(l){p(e.$$.fragment,l),n=!1},d(l){z(e,l)}}}function Je(t){let e,n,l=t[14]&&x(t);return{c(){l&&l.c(),e=ae()},m(s,o){l&&l.m(s,o),I(s,e,o),n=!0},p(s,o){s[14]?l?(l.p(s,o),o[0]&16384&&v(l,1)):(l=x(s),l.c(),v(l,1),l.m(e.parentNode,e)):l&&(re(),p(l,1,1,()=>{l=null}),se())},i(s){n||(v(l),n=!0)},o(s){p(l),n=!1},d(s){s&&B(e),l&&l.d(s)}}}function ee(t){let e,n,l,s;return{c(){e=j("img"),k(e,"alt",n="segmentation mask identifying "+t[4]+" within the uploaded file"),k(e,"class","mask fit-height svelte-303fln"),G(e.src,l=t[33].image.url)||k(e,"src",l),k(e,"style",s=t[9]&&t[33].label in t[9]?null:`filter: hue-rotate(${Math.round(t[35]*360/t[15]?.annotations.length)}deg);`),S(e,"fit-height",!t[18]),S(e,"active",t[16]==t[33].label),S(e,"inactive",t[16]!=t[33].label&&t[16]!=null)},m(o,i){I(o,e,i)},p(o,i){i[0]&16&&n!==(n="segmentation mask identifying "+o[4]+" within the uploaded file")&&k(e,"alt",n),i[0]&32768&&!G(e.src,l=o[33].image.url)&&k(e,"src",l),i[0]&33280&&s!==(s=o[9]&&o[33].label in o[9]?null:`filter: hue-rotate(${Math.round(o[35]*360/o[15]?.annotations.length)}deg);`)&&k(e,"style",s),i[0]&262144&&S(e,"fit-height",!o[18]),i[0]&98304&&S(e,"active",o[16]==o[33].label),i[0]&98304&&S(e,"inactive",o[16]!=o[33].label&&o[16]!=null)},d(o){o&&B(e)}}}function le(t){let e,n=D(t[15].annotations),l=[];for(let s=0;s<n.length;s+=1)l[s]=te($(t,n,s));return{c(){e=j("div");for(let s=0;s<l.length;s+=1)l[s].c();k(e,"class","legend svelte-303fln")},m(s,o){I(s,e,o);for(let i=0;i<l.length;i+=1)l[i]&&l[i].m(e,null)},p(s,o){if(o[0]&3703296){n=D(s[15].annotations);let i;for(i=0;i<n.length;i+=1){const f=$(s,n,i);l[i]?l[i].p(f,o):(l[i]=te(f),l[i].c(),l[i].m(e,null))}for(;i<l.length;i+=1)l[i].d(1);l.length=n.length}},d(s){s&&B(e),oe(l,s)}}}function te(t){let e,n=t[33].label+"",l,s,o,i;function f(){return t[27](t[33])}function _(){return t[28](t[33])}function b(){return t[31](t[35],t[33])}return{c(){e=j("button"),l=De(n),s=P(),k(e,"class","legend-item svelte-303fln"),Z(e,"background-color",t[9]&&t[33].label in t[9]?t[9][t[33].label]+"88":`hsla(${Math.round(t[35]*360/t[15].annotations.length)}, 100%, 50%, 0.3)`)},m(g,c){I(g,e,c),L(e,l),L(e,s),o||(i=[C(e,"mouseover",f),C(e,"focus",_),C(e,"mouseout",t[29]),C(e,"blur",t[30]),C(e,"click",b)],o=!0)},p(g,c){t=g,c[0]&32768&&n!==(n=t[33].label+"")&&Ae(l,n),c[0]&33280&&Z(e,"background-color",t[9]&&t[33].label in t[9]?t[9][t[33].label]+"88":`hsla(${Math.round(t[35]*360/t[15].annotations.length)}, 100%, 50%, 0.3)`)},d(g){g&&B(e),o=!1,Le(i)}}}function Ke(t){let e,n;return e=new ne({}),{c(){q(e.$$.fragment)},m(l,s){E(e,l,s),n=!0},i(l){n||(v(e.$$.fragment,l),n=!0)},o(l){p(e.$$.fragment,l),n=!1},d(l){z(e,l)}}}function Oe(t){let e,n,l,s,o,i,f,_;const b=[{autoscroll:t[3].autoscroll},{i18n:t[3].i18n},t[13]];let g={};for(let r=0;r<b.length;r+=1)g=Fe(g,b[r]);e=new qe({props:g}),l=new Be({props:{show_label:t[5],Icon:ne,label:t[4]||t[3].i18n("image.image")}});const c=[He,Ge],m=[];function u(r,h){return r[15]==null?0:1}return i=u(t),f=m[i]=c[i](t),{c(){q(e.$$.fragment),n=P(),q(l.$$.fragment),s=P(),o=j("div"),f.c(),k(o,"class","container svelte-303fln")},m(r,h){E(e,r,h),I(r,n,h),E(l,r,h),I(r,s,h),I(r,o,h),m[i].m(o,null),_=!0},p(r,h){const d=h[0]&8200?je(b,[h[0]&8&&{autoscroll:r[3].autoscroll},h[0]&8&&{i18n:r[3].i18n},h[0]&8192&&Pe(r[13])]):{};e.$set(d);const M={};h[0]&32&&(M.show_label=r[5]),h[0]&24&&(M.label=r[4]||r[3].i18n("image.image")),l.$set(M);let F=i;i=u(r),i===F?m[i].p(r,h):(re(),p(m[F],1,1,()=>{m[F]=null}),se(),f=m[i],f?f.p(r,h):(f=m[i]=c[i](r),f.c()),v(f,1),f.m(o,null))},i(r){_||(v(e.$$.fragment,r),v(l.$$.fragment,r),v(f),_=!0)},o(r){p(e.$$.fragment,r),p(l.$$.fragment,r),p(f),_=!1},d(r){r&&(B(n),B(s),B(o)),z(e,r),z(l,r),m[i].d()}}}function Qe(t){let e,n,l;function s(i){t[32](i)}let o={visible:t[2],elem_id:t[0],elem_classes:t[1],padding:!1,height:t[7],width:t[8],allow_overflow:!1,container:t[10],scale:t[11],min_width:t[12],$$slots:{default:[Oe]},$$scope:{ctx:t}};return t[18]!==void 0&&(o.fullscreen=t[18]),e=new pe({props:o}),ie.push(()=>Ne(e,"fullscreen",s)),{c(){q(e.$$.fragment)},m(i,f){E(e,i,f),l=!0},p(i,f){const _={};f[0]&4&&(_.visible=i[2]),f[0]&1&&(_.elem_id=i[0]),f[0]&2&&(_.elem_classes=i[1]),f[0]&128&&(_.height=i[7]),f[0]&256&&(_.width=i[8]),f[0]&1024&&(_.container=i[10]),f[0]&2048&&(_.scale=i[11]),f[0]&4096&&(_.min_width=i[12]),f[0]&516856|f[1]&64&&(_.$$scope={dirty:f,ctx:i}),!n&&f[0]&262144&&(n=!0,_.fullscreen=i[18],Ee(()=>n=!1)),e.$set(_)},i(i){l||(v(e.$$.fragment,i),l=!0)},o(i){p(e.$$.fragment,i),l=!1},d(i){z(e,i)}}}function Re(t,e,n){let{elem_id:l=""}=e,{elem_classes:s=[]}=e,{visible:o=!0}=e,{value:i=null}=e,f=null,_=null,{gradio:b}=e,{label:g=b.i18n("annotated_image.annotated_image")}=e,{show_label:c=!0}=e,{show_legend:m=!0}=e,{height:u}=e,{width:r}=e,{color_map:h}=e,{container:d=!0}=e,{scale:M=null}=e,{min_width:F=void 0}=e,H=null,{loading_status:T}=e,{show_fullscreen_button:U=!0}=e,J,W=!1,K=null;function O(a){n(16,H=a)}function Q(){n(16,H=null)}function V(a,A){b.dispatch("select",{value:g,index:a})}const ue=({detail:a})=>{n(18,W=a)};function fe(a){ie[a?"unshift":"push"](()=>{J=a,n(17,J)})}const _e=a=>O(a.label),ce=a=>O(a.label),me=()=>Q(),he=()=>Q(),ge=(a,A)=>V(a,A.label);function be(a){W=a,n(18,W)}return t.$$set=a=>{"elem_id"in a&&n(0,l=a.elem_id),"elem_classes"in a&&n(1,s=a.elem_classes),"visible"in a&&n(2,o=a.visible),"value"in a&&n(22,i=a.value),"gradio"in a&&n(3,b=a.gradio),"label"in a&&n(4,g=a.label),"show_label"in a&&n(5,c=a.show_label),"show_legend"in a&&n(6,m=a.show_legend),"height"in a&&n(7,u=a.height),"width"in a&&n(8,r=a.width),"color_map"in a&&n(9,h=a.color_map),"container"in a&&n(10,d=a.container),"scale"in a&&n(11,M=a.scale),"min_width"in a&&n(12,F=a.min_width),"loading_status"in a&&n(13,T=a.loading_status),"show_fullscreen_button"in a&&n(14,U=a.show_fullscreen_button)},t.$$.update=()=>{if(t.$$.dirty[0]&29360136)if(i!==f&&(n(23,f=i),b.dispatch("change")),i){const a={image:i.image,annotations:i.annotations.map(N=>({image:N.image,label:N.label}))};n(15,_=a);const A=Y(a.image.url),de=Promise.all(a.annotations.map(N=>Y(N.image.url))),R=Promise.all([A,de]);n(24,K=R),R.then(([N,we])=>{if(K!==R)return;const ke={image:{...a.image,url:N??void 0},annotations:a.annotations.map((X,ve)=>({...X,image:{...X.image,url:we[ve]??void 0}}))};n(15,_=ke)})}else n(15,_=null)},[l,s,o,b,g,c,m,u,r,h,d,M,F,T,U,_,H,J,W,O,Q,V,i,f,K,ue,fe,_e,ce,me,he,ge,be]}class rl extends ze{constructor(e){super(),Ce(this,e,Re,Qe,We,{elem_id:0,elem_classes:1,visible:2,value:22,gradio:3,label:4,show_label:5,show_legend:6,height:7,width:8,color_map:9,container:10,scale:11,min_width:12,loading_status:13,show_fullscreen_button:14},null,[-1,-1])}get elem_id(){return this.$$.ctx[0]}set elem_id(e){this.$$set({elem_id:e}),w()}get elem_classes(){return this.$$.ctx[1]}set elem_classes(e){this.$$set({elem_classes:e}),w()}get visible(){return this.$$.ctx[2]}set visible(e){this.$$set({visible:e}),w()}get value(){return this.$$.ctx[22]}set value(e){this.$$set({value:e}),w()}get gradio(){return this.$$.ctx[3]}set gradio(e){this.$$set({gradio:e}),w()}get label(){return this.$$.ctx[4]}set label(e){this.$$set({label:e}),w()}get show_label(){return this.$$.ctx[5]}set show_label(e){this.$$set({show_label:e}),w()}get show_legend(){return this.$$.ctx[6]}set show_legend(e){this.$$set({show_legend:e}),w()}get height(){return this.$$.ctx[7]}set height(e){this.$$set({height:e}),w()}get width(){return this.$$.ctx[8]}set width(e){this.$$set({width:e}),w()}get color_map(){return this.$$.ctx[9]}set color_map(e){this.$$set({color_map:e}),w()}get container(){return this.$$.ctx[10]}set container(e){this.$$set({container:e}),w()}get scale(){return this.$$.ctx[11]}set scale(e){this.$$set({scale:e}),w()}get min_width(){return this.$$.ctx[12]}set min_width(e){this.$$set({min_width:e}),w()}get loading_status(){return this.$$.ctx[13]}set loading_status(e){this.$$set({loading_status:e}),w()}get show_fullscreen_button(){return this.$$.ctx[14]}set show_fullscreen_button(e){this.$$set({show_fullscreen_button:e}),w()}}export{rl as default};
//# sourceMappingURL=Index-GuD1Tw7E.js.map
