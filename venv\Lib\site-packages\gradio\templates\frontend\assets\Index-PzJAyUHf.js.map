{"version": 3, "file": "Index-PzJAyUHf.js", "sources": ["../../../../js/icons/src/Chat.svelte", "../../../../js/icons/src/DropdownCircularArrow.svelte", "../../../../js/icons/src/Retry.svelte", "../../../../js/icons/src/ScrollDownArrow.svelte", "../../../../js/chatbot/shared/utils.ts", "../../../../js/chatbot/shared/ThumbDownActive.svelte", "../../../../js/chatbot/shared/ThumbDownDefault.svelte", "../../../../js/chatbot/shared/ThumbUpActive.svelte", "../../../../js/chatbot/shared/ThumbUpDefault.svelte", "../../../../js/chatbot/shared/Flag.svelte", "../../../../js/chatbot/shared/FlagActive.svelte", "../../../../js/chatbot/shared/LikeDislike.svelte", "../../../../js/chatbot/shared/Copy.svelte", "../../../../js/chatbot/shared/ButtonPanel.svelte", "../../../../js/chatbot/shared/Component.svelte", "../../../../js/chatbot/shared/MessageContent.svelte", "../../../../js/chatbot/shared/Thought.svelte", "../../../../js/chatbot/shared/Message.svelte", "../../../../js/chatbot/shared/Pending.svelte", "../../../../js/chatbot/shared/Examples.svelte", "../../../../js/chatbot/shared/CopyAll.svelte", "../../../../js/chatbot/shared/ChatBot.svelte", "../../../../js/chatbot/Index.svelte"], "sourcesContent": ["<svg\n\txmlns=\"http://www.w3.org/2000/svg\"\n\txmlns:xlink=\"http://www.w3.org/1999/xlink\"\n\taria-hidden=\"true\"\n\trole=\"img\"\n\tclass=\"iconify iconify--carbon\"\n\twidth=\"100%\"\n\theight=\"100%\"\n\tpreserveAspectRatio=\"xMidYMid meet\"\n\tviewBox=\"0 0 32 32\"\n>\n\t<path\n\t\tfill=\"currentColor\"\n\t\td=\"M17.74 30L16 29l4-7h6a2 2 0 0 0 2-2V8a2 2 0 0 0-2-2H6a2 2 0 0 0-2 2v12a2 2 0 0 0 2 2h9v2H6a4 4 0 0 1-4-4V8a4 4 0 0 1 4-4h20a4 4 0 0 1 4 4v12a4 4 0 0 1-4 4h-4.84Z\"\n\t/>\n\t<path fill=\"currentColor\" d=\"M8 10h16v2H8zm0 6h10v2H8z\" />\n</svg>\n", "<svg\n\tclass=\"dropdown-arrow\"\n\txmlns=\"http://www.w3.org/2000/svg\"\n\twidth=\"100%\"\n\theight=\"100%\"\n\tviewBox=\"0 0 18 18\"\n>\n\t<circle cx=\"9\" cy=\"9\" r=\"8\" class=\"circle\" />\n\t<path d=\"M5 8l4 4 4-4z\" />\n</svg>\n\n<style>\n\t.dropdown-arrow {\n\t\tfill: currentColor;\n\t}\n\n\t.circle {\n\t\tfill: currentColor;\n\t\topacity: 0.1;\n\t}\n</style>\n", "<svg\n\twidth=\"100%\"\n\theight=\"100%\"\n\tstroke-width=\"1.5\"\n\tviewBox=\"0 0 24 24\"\n\tfill=\"none\"\n\txmlns=\"http://www.w3.org/2000/svg\"\n\tcolor=\"currentColor\"\n>\n\t<path\n\t\td=\"M19.1679 9C18.0247 6.46819 15.3006 4.5 11.9999 4.5C8.31459 4.5 5.05104 7.44668 4.54932 11\"\n\t\tstroke=\"currentColor\"\n\t\tstroke-width=\"1.5\"\n\t\tstroke-linecap=\"round\"\n\t\tstroke-linejoin=\"round\"\n\t></path>\n\t<path\n\t\td=\"M16 9H19.4C19.7314 9 20 8.73137 20 8.4V5\"\n\t\tstroke=\"currentColor\"\n\t\tstroke-width=\"1.5\"\n\t\tstroke-linecap=\"round\"\n\t\tstroke-linejoin=\"round\"\n\t></path>\n\t<path\n\t\td=\"M4.88146 15C5.92458 17.5318 8.64874 19.5 12.0494 19.5C15.7347 19.5 18.9983 16.5533 19.5 13\"\n\t\tstroke=\"currentColor\"\n\t\tstroke-width=\"1.5\"\n\t\tstroke-linecap=\"round\"\n\t\tstroke-linejoin=\"round\"\n\t></path>\n\t<path\n\t\td=\"M8.04932 15H4.64932C4.31795 15 4.04932 15.2686 4.04932 15.6V19\"\n\t\tstroke=\"currentColor\"\n\t\tstroke-width=\"1.5\"\n\t\tstroke-linecap=\"round\"\n\t\tstroke-linejoin=\"round\"\n\t></path>\n</svg>\n", "<svg\n\twidth=\"100%\"\n\theight=\"100%\"\n\tviewBox=\"0 0 24 24\"\n\tfill=\"none\"\n\txmlns=\"http://www.w3.org/2000/svg\"\n>\n\t<path\n\t\td=\"M12 20L12 4M12 20L7 15M12 20L17 15\"\n\t\tstroke=\"currentColor\"\n\t\tstroke-width=\"2\"\n\t\tstroke-linecap=\"round\"\n\t\tstroke-linejoin=\"round\"\n\t/>\n</svg>\n", "import type { FileData } from \"@gradio/client\";\nimport type { ComponentType, SvelteComponent } from \"svelte\";\nimport { uploadToHuggingFace } from \"@gradio/utils\";\nimport type {\n\tTupleFormat,\n\tComponentMessage,\n\tComponentData,\n\tTextMessage,\n\tNormalisedMessage,\n\tMessage,\n\tMessageRole,\n\tThoughtNode\n} from \"../types\";\nimport type { LoadedComponent } from \"../../core/src/types\";\nimport { Gradio } from \"@gradio/utils\";\n\nexport const format_chat_for_sharing = async (\n\tchat: NormalisedMessage[],\n\turl_length_limit = 1800\n): Promise<string> => {\n\tlet messages_to_share = [...chat];\n\tlet formatted = await format_messages(messages_to_share);\n\n\tif (formatted.length > url_length_limit && messages_to_share.length > 2) {\n\t\tconst first_message = messages_to_share[0];\n\t\tconst last_message = messages_to_share[messages_to_share.length - 1];\n\t\tmessages_to_share = [first_message, last_message];\n\t\tformatted = await format_messages(messages_to_share);\n\t}\n\n\tif (formatted.length > url_length_limit && messages_to_share.length > 0) {\n\t\tconst truncated_messages = messages_to_share.map((msg) => {\n\t\t\tif (msg.type === \"text\") {\n\t\t\t\tconst max_length =\n\t\t\t\t\tMath.floor(url_length_limit / messages_to_share.length) - 20;\n\t\t\t\tif (msg.content.length > max_length) {\n\t\t\t\t\treturn {\n\t\t\t\t\t\t...msg,\n\t\t\t\t\t\tcontent: msg.content.substring(0, max_length) + \"...\"\n\t\t\t\t\t};\n\t\t\t\t}\n\t\t\t}\n\t\t\treturn msg;\n\t\t});\n\n\t\tmessages_to_share = truncated_messages;\n\t\tformatted = await format_messages(messages_to_share);\n\t}\n\n\treturn formatted;\n};\n\nconst format_messages = async (chat: NormalisedMessage[]): Promise<string> => {\n\tlet messages = await Promise.all(\n\t\tchat.map(async (message) => {\n\t\t\tif (message.role === \"system\") return \"\";\n\t\t\tlet speaker_emoji = message.role === \"user\" ? \"😃\" : \"🤖\";\n\t\t\tlet html_content = \"\";\n\n\t\t\tif (message.type === \"text\") {\n\t\t\t\tconst regexPatterns = {\n\t\t\t\t\taudio: /<audio.*?src=\"(\\/file=.*?)\"/g,\n\t\t\t\t\tvideo: /<video.*?src=\"(\\/file=.*?)\"/g,\n\t\t\t\t\timage: /<img.*?src=\"(\\/file=.*?)\".*?\\/>|!\\[.*?\\]\\((\\/file=.*?)\\)/g\n\t\t\t\t};\n\n\t\t\t\thtml_content = message.content;\n\n\t\t\t\tfor (let [_, regex] of Object.entries(regexPatterns)) {\n\t\t\t\t\tlet match;\n\n\t\t\t\t\twhile ((match = regex.exec(message.content)) !== null) {\n\t\t\t\t\t\tconst fileUrl = match[1] || match[2];\n\t\t\t\t\t\tconst newUrl = await uploadToHuggingFace(fileUrl, \"url\");\n\t\t\t\t\t\thtml_content = html_content.replace(fileUrl, newUrl);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\tif (!message.content.value) return \"\";\n\t\t\t\tconst url =\n\t\t\t\t\tmessage.content.component === \"video\"\n\t\t\t\t\t\t? message.content.value?.video.path\n\t\t\t\t\t\t: message.content.value;\n\t\t\t\tconst file_url = await uploadToHuggingFace(url, \"url\");\n\t\t\t\tif (message.content.component === \"audio\") {\n\t\t\t\t\thtml_content = `<audio controls src=\"${file_url}\"></audio>`;\n\t\t\t\t} else if (message.content.component === \"video\") {\n\t\t\t\t\thtml_content = file_url;\n\t\t\t\t} else if (message.content.component === \"image\") {\n\t\t\t\t\thtml_content = `<img src=\"${file_url}\" />`;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\treturn `${speaker_emoji}: ${html_content}`;\n\t\t})\n\t);\n\treturn messages.filter((msg) => msg !== \"\").join(\"\\n\");\n};\n\nexport interface UndoRetryData {\n\tindex: number | [number, number];\n\tvalue: string | FileData | ComponentData;\n}\n\nexport interface EditData {\n\tindex: number | [number, number];\n\tvalue: string;\n\tprevious_value: string;\n}\n\nconst redirect_src_url = (src: string, root: string): string =>\n\tsrc.replace('src=\"/file', `src=\"${root}file`);\n\nfunction get_component_for_mime_type(\n\tmime_type: string | null | undefined,\n\tfile?: { path?: string }\n): string {\n\tif (!mime_type) {\n\t\tconst path = file?.path;\n\t\tif (path) {\n\t\t\tconst lower_path = path.toLowerCase();\n\t\t\tif (\n\t\t\t\tlower_path.endsWith(\".glb\") ||\n\t\t\t\tlower_path.endsWith(\".gltf\") ||\n\t\t\t\tlower_path.endsWith(\".obj\") ||\n\t\t\t\tlower_path.endsWith(\".stl\") ||\n\t\t\t\tlower_path.endsWith(\".splat\") ||\n\t\t\t\tlower_path.endsWith(\".ply\")\n\t\t\t) {\n\t\t\t\treturn \"model3d\";\n\t\t\t}\n\t\t}\n\t\treturn \"file\";\n\t}\n\tif (mime_type.includes(\"audio\")) return \"audio\";\n\tif (mime_type.includes(\"video\")) return \"video\";\n\tif (mime_type.includes(\"image\")) return \"image\";\n\tif (mime_type.includes(\"model\")) return \"model3d\";\n\treturn \"file\";\n}\n\nfunction convert_file_message_to_component_message(\n\tmessage: any\n): ComponentData {\n\tconst _file = Array.isArray(message.file) ? message.file[0] : message.file;\n\treturn {\n\t\tcomponent: get_component_for_mime_type(_file?.mime_type, _file),\n\t\tvalue: message.file,\n\t\talt_text: message.alt_text,\n\t\tconstructor_args: {},\n\t\tprops: {}\n\t} as ComponentData;\n}\n\nexport function normalise_messages(\n\tmessages: Message[] | null,\n\troot: string\n): NormalisedMessage[] | null {\n\tif (messages === null) return messages;\n\n\tconst thought_map = new Map<string, ThoughtNode>();\n\n\treturn messages\n\t\t.map((message, i) => {\n\t\t\tlet normalized: NormalisedMessage =\n\t\t\t\ttypeof message.content === \"string\"\n\t\t\t\t\t? {\n\t\t\t\t\t\t\trole: message.role,\n\t\t\t\t\t\t\tmetadata: message.metadata,\n\t\t\t\t\t\t\tcontent: redirect_src_url(message.content, root),\n\t\t\t\t\t\t\ttype: \"text\",\n\t\t\t\t\t\t\tindex: i,\n\t\t\t\t\t\t\toptions: message.options\n\t\t\t\t\t\t}\n\t\t\t\t\t: \"file\" in message.content\n\t\t\t\t\t\t? {\n\t\t\t\t\t\t\t\tcontent: convert_file_message_to_component_message(\n\t\t\t\t\t\t\t\t\tmessage.content\n\t\t\t\t\t\t\t\t),\n\t\t\t\t\t\t\t\tmetadata: message.metadata,\n\t\t\t\t\t\t\t\trole: message.role,\n\t\t\t\t\t\t\t\ttype: \"component\",\n\t\t\t\t\t\t\t\tindex: i,\n\t\t\t\t\t\t\t\toptions: message.options\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t: ({ type: \"component\", ...message } as ComponentMessage);\n\n\t\t\t// handle thoughts\n\t\t\tconst { id, title, parent_id } = message.metadata || {};\n\t\t\tif (parent_id) {\n\t\t\t\tconst parent = thought_map.get(String(parent_id));\n\t\t\t\tif (parent) {\n\t\t\t\t\tconst thought = { ...normalized, children: [] } as ThoughtNode;\n\t\t\t\t\tparent.children.push(thought);\n\t\t\t\t\tif (id && title) {\n\t\t\t\t\t\tthought_map.set(String(id), thought);\n\t\t\t\t\t}\n\t\t\t\t\treturn null;\n\t\t\t\t}\n\t\t\t}\n\t\t\tif (id && title) {\n\t\t\t\tconst thought = { ...normalized, children: [] } as ThoughtNode;\n\t\t\t\tthought_map.set(String(id), thought);\n\t\t\t\treturn thought;\n\t\t\t}\n\n\t\t\treturn normalized;\n\t\t})\n\t\t.filter((msg): msg is NormalisedMessage => msg !== null);\n}\n\nexport function normalise_tuples(\n\tmessages: TupleFormat,\n\troot: string\n): NormalisedMessage[] | null {\n\tif (messages === null) return messages;\n\tconst msg = messages.flatMap((message_pair, i) => {\n\t\treturn message_pair.map((message, index) => {\n\t\t\tif (message == null) return null;\n\t\t\tconst role = index == 0 ? \"user\" : \"assistant\";\n\n\t\t\tif (typeof message === \"string\") {\n\t\t\t\treturn {\n\t\t\t\t\trole: role,\n\t\t\t\t\ttype: \"text\",\n\t\t\t\t\tcontent: redirect_src_url(message, root),\n\t\t\t\t\tmetadata: { title: null },\n\t\t\t\t\tindex: [i, index]\n\t\t\t\t} as TextMessage;\n\t\t\t}\n\n\t\t\tif (\"file\" in message) {\n\t\t\t\treturn {\n\t\t\t\t\tcontent: convert_file_message_to_component_message(message),\n\t\t\t\t\trole: role,\n\t\t\t\t\ttype: \"component\",\n\t\t\t\t\tindex: [i, index]\n\t\t\t\t} as ComponentMessage;\n\t\t\t}\n\n\t\t\treturn {\n\t\t\t\trole: role,\n\t\t\t\tcontent: message,\n\t\t\t\ttype: \"component\",\n\t\t\t\tindex: [i, index]\n\t\t\t} as ComponentMessage;\n\t\t});\n\t});\n\treturn msg.filter((message) => message != null) as NormalisedMessage[];\n}\n\nexport function is_component_message(\n\tmessage: NormalisedMessage\n): message is ComponentMessage {\n\treturn message.type === \"component\";\n}\n\nexport function is_last_bot_message(\n\tmessages: NormalisedMessage[],\n\tall_messages: NormalisedMessage[]\n): boolean {\n\tconst is_bot = messages[messages.length - 1].role === \"assistant\";\n\tconst last_index = messages[messages.length - 1].index;\n\t// use JSON.stringify to handle both the number and tuple cases\n\t// when msg_format is tuples, last_index is an array and when it is messages, it is a number\n\tconst is_last =\n\t\tJSON.stringify(last_index) ===\n\t\tJSON.stringify(all_messages[all_messages.length - 1].index);\n\treturn is_last && is_bot;\n}\n\nexport function group_messages(\n\tmessages: NormalisedMessage[],\n\tmsg_format: \"messages\" | \"tuples\"\n): NormalisedMessage[][] {\n\tconst groupedMessages: NormalisedMessage[][] = [];\n\tlet currentGroup: NormalisedMessage[] = [];\n\tlet currentRole: MessageRole | null = null;\n\n\tfor (const message of messages) {\n\t\tif (!(message.role === \"assistant\" || message.role === \"user\")) {\n\t\t\tcontinue;\n\t\t}\n\t\tif (message.role === currentRole) {\n\t\t\tcurrentGroup.push(message);\n\t\t} else {\n\t\t\tif (currentGroup.length > 0) {\n\t\t\t\tgroupedMessages.push(currentGroup);\n\t\t\t}\n\t\t\tcurrentGroup = [message];\n\t\t\tcurrentRole = message.role;\n\t\t}\n\t}\n\n\tif (currentGroup.length > 0) {\n\t\tgroupedMessages.push(currentGroup);\n\t}\n\n\treturn groupedMessages;\n}\n\nexport async function load_components(\n\tcomponent_names: string[],\n\t_components: Record<string, ComponentType<SvelteComponent>>,\n\tload_component: Gradio[\"load_component\"]\n): Promise<Record<string, ComponentType<SvelteComponent>>> {\n\tlet names: string[] = [];\n\tlet components: ReturnType<typeof load_component>[\"component\"][] = [];\n\n\tcomponent_names.forEach((component_name) => {\n\t\tif (_components[component_name] || component_name === \"file\") {\n\t\t\treturn;\n\t\t}\n\t\tconst variant =\n\t\t\tcomponent_name === \"dataframe\" || component_name === \"model3d\"\n\t\t\t\t? \"component\"\n\t\t\t\t: \"base\";\n\t\tconst { name, component } = load_component(component_name, variant);\n\t\tnames.push(name);\n\t\tcomponents.push(component);\n\t\tcomponent_name;\n\t});\n\n\tconst resolved_components = await Promise.allSettled(components);\n\tconst supported_components: [number, LoadedComponent][] = resolved_components\n\t\t.map((result, index) =>\n\t\t\tresult.status === \"fulfilled\" ? [index, result.value] : null\n\t\t)\n\t\t.filter((item): item is [number, LoadedComponent] => item !== null);\n\n\tsupported_components.forEach(([originalIndex, component]) => {\n\t\t_components[names[originalIndex]] = component.default;\n\t});\n\n\treturn _components;\n}\n\nexport function get_components_from_messages(\n\tmessages: NormalisedMessage[] | null\n): string[] {\n\tif (!messages) return [];\n\tlet components: Set<string> = new Set();\n\tmessages.forEach((message) => {\n\t\tif (message.type === \"component\") {\n\t\t\tcomponents.add(message.content.component);\n\t\t}\n\t});\n\treturn Array.from(components);\n}\n\nexport function get_thought_content(msg: NormalisedMessage, depth = 0): string {\n\tlet content = \"\";\n\tconst indent = \"  \".repeat(depth);\n\n\tif (msg.metadata?.title) {\n\t\tcontent += `${indent}${depth > 0 ? \"- \" : \"\"}${msg.metadata.title}\\n`;\n\t}\n\tif (typeof msg.content === \"string\") {\n\t\tcontent += `${indent}  ${msg.content}\\n`;\n\t}\n\tconst thought = msg as ThoughtNode;\n\tif (thought.children?.length > 0) {\n\t\tcontent += thought.children\n\t\t\t.map((child) => get_thought_content(child, depth + 1))\n\t\t\t.join(\"\");\n\t}\n\treturn content;\n}\n\nexport function all_text(message: TextMessage[] | TextMessage): string {\n\tif (Array.isArray(message)) {\n\t\treturn message\n\t\t\t.map((m) => {\n\t\t\t\tif (m.metadata?.title) {\n\t\t\t\t\treturn get_thought_content(m);\n\t\t\t\t}\n\t\t\t\treturn m.content;\n\t\t\t})\n\t\t\t.join(\"\\n\");\n\t}\n\tif (message.metadata?.title) {\n\t\treturn get_thought_content(message);\n\t}\n\treturn message.content;\n}\n\nexport function is_all_text(\n\tmessage: NormalisedMessage[] | NormalisedMessage\n): message is TextMessage[] | TextMessage {\n\treturn (\n\t\t(Array.isArray(message) &&\n\t\t\tmessage.every((m) => typeof m.content === \"string\")) ||\n\t\t(!Array.isArray(message) && typeof message.content === \"string\")\n\t);\n}\n", "<svg\n\twidth=\"100%\"\n\theight=\"100%\"\n\tviewBox=\"0 0 12 12\"\n\tfill=\"none\"\n\txmlns=\"http://www.w3.org/2000/svg\"\n>\n\t<path\n\t\td=\"M11.25 6.61523H9.375V1.36523H11.25V6.61523ZM3.375 1.36523H8.625V6.91636L7.48425 8.62748L7.16737 10.8464C7.14108 11.0248 7.05166 11.1879 6.91535 11.3061C6.77904 11.4242 6.60488 11.4896 6.4245 11.4902H6.375C6.07672 11.4899 5.79075 11.3713 5.57983 11.1604C5.36892 10.9495 5.2503 10.6635 5.25 10.3652V8.11523H2.25C1.85233 8.11474 1.47109 7.95654 1.18989 7.67535C0.908691 7.39415 0.750496 7.01291 0.75 6.61523V3.99023C0.750992 3.29435 1.02787 2.62724 1.51994 2.13517C2.01201 1.64311 2.67911 1.36623 3.375 1.36523Z\"\n\t\tfill=\"currentColor\"\n\t/>\n</svg>\n", "<svg\n\twidth=\"100%\"\n\theight=\"100%\"\n\tviewBox=\"0 0 12 12\"\n\tfill=\"none\"\n\txmlns=\"http://www.w3.org/2000/svg\"\n>\n\t<path\n\t\td=\"M2.25 8.11523H4.5V10.3652C4.5003 10.6635 4.61892 10.9495 4.82983 11.1604C5.04075 11.3713 5.32672 11.4899 5.625 11.4902H6.42488C6.60519 11.4895 6.77926 11.4241 6.91549 11.3059C7.05172 11.1878 7.14109 11.0248 7.16737 10.8464L7.48425 8.62748L8.82562 6.61523H11.25V1.36523H3.375C2.67911 1.36623 2.01201 1.64311 1.51994 2.13517C1.02787 2.62724 0.750992 3.29435 0.75 3.99023V6.61523C0.750496 7.01291 0.908691 7.39415 1.18989 7.67535C1.47109 7.95654 1.85233 8.11474 2.25 8.11523ZM9 2.11523H10.5V5.86523H9V2.11523ZM1.5 3.99023C1.5006 3.49314 1.69833 3.01657 2.04983 2.66507C2.40133 2.31356 2.8779 2.11583 3.375 2.11523H8.25V6.12661L6.76575 8.35298L6.4245 10.7402H5.625C5.52554 10.7402 5.43016 10.7007 5.35983 10.6304C5.28951 10.5601 5.25 10.4647 5.25 10.3652V7.36523H2.25C2.05118 7.36494 1.86059 7.28582 1.72 7.14524C1.57941 7.00465 1.5003 6.81406 1.5 6.61523V3.99023Z\"\n\t\tfill=\"currentColor\"\n\t/>\n</svg>\n", "<svg\n\twidth=\"100%\"\n\theight=\"100%\"\n\tviewBox=\"0 0 12 12\"\n\tfill=\"none\"\n\txmlns=\"http://www.w3.org/2000/svg\"\n>\n\t<path\n\t\td=\"M0.75 6.24023H2.625V11.4902H0.75V6.24023ZM8.625 11.4902H3.375V5.93911L4.51575 4.22798L4.83263 2.00911C4.85892 1.83065 4.94834 1.66754 5.08465 1.5494C5.22096 1.43125 5.39512 1.36591 5.5755 1.36523H5.625C5.92328 1.36553 6.20925 1.48415 6.42017 1.69507C6.63108 1.90598 6.7497 2.19196 6.75 2.49023V4.74023H9.75C10.1477 4.74073 10.5289 4.89893 10.8101 5.18012C11.0913 5.46132 11.2495 5.84256 11.25 6.24023V8.86523C11.249 9.56112 10.9721 10.2282 10.4801 10.7203C9.98799 11.2124 9.32089 11.4892 8.625 11.4902Z\"\n\t\tfill=\"currentColor\"\n\t/>\n</svg>\n", "<svg\n\twidth=\"100%\"\n\theight=\"100%\"\n\tviewBox=\"0 0 12 12\"\n\tfill=\"none\"\n\txmlns=\"http://www.w3.org/2000/svg\"\n>\n\t<path\n\t\td=\"M9.75 4.74023H7.5V2.49023C7.4997 2.19196 7.38108 1.90598 7.17017 1.69507C6.95925 1.48415 6.67328 1.36553 6.375 1.36523H5.57512C5.39481 1.366 5.22074 1.43138 5.08451 1.54952C4.94828 1.66766 4.85891 1.83072 4.83262 2.00911L4.51575 4.22798L3.17438 6.24023H0.75V11.4902H8.625C9.32089 11.4892 9.98799 11.2124 10.4801 10.7203C10.9721 10.2282 11.249 9.56112 11.25 8.86523V6.24023C11.2495 5.84256 11.0913 5.46132 10.8101 5.18012C10.5289 4.89893 10.1477 4.74073 9.75 4.74023ZM3 10.7402H1.5V6.99023H3V10.7402ZM10.5 8.86523C10.4994 9.36233 10.3017 9.8389 9.95017 10.1904C9.59867 10.5419 9.1221 10.7396 8.625 10.7402H3.75V6.72886L5.23425 4.50248L5.5755 2.11523H6.375C6.47446 2.11523 6.56984 2.15474 6.64017 2.22507C6.71049 2.2954 6.75 2.39078 6.75 2.49023V5.49023H9.75C9.94882 5.49053 10.1394 5.56965 10.28 5.71023C10.4206 5.85082 10.4997 6.04141 10.5 6.24023V8.86523Z\"\n\t\tfill=\"currentColor\"\n\t/>\n</svg>\n", "<svg\n\tid=\"icon\"\n\txmlns=\"http://www.w3.org/2000/svg\"\n\tviewBox=\"0 0 32 32\"\n\tfill=\"none\"\n\t><path\n\t\tfill=\"currentColor\"\n\t\td=\"M6,30H4V2H28l-5.8,9L28,20H6ZM6,18H24.33L19.8,11l4.53-7H6Z\"\n\t/></svg\n>\n", "<svg\n\tid=\"icon\"\n\txmlns=\"http://www.w3.org/2000/svg\"\n\tviewBox=\"0 0 32 32\"\n\tfill=\"none\"\n\t><path fill=\"currentColor\" d=\"M4,2H28l-5.8,9L28,20H6v10H4V2z\" /></svg\n>\n", "<script lang=\"ts\">\n\timport { IconButton } from \"@gradio/atoms\";\n\timport ThumbDownActive from \"./ThumbDownActive.svelte\";\n\timport ThumbDownDefault from \"./ThumbDownDefault.svelte\";\n\timport ThumbUpActive from \"./ThumbUpActive.svelte\";\n\timport ThumbUpDefault from \"./ThumbUpDefault.svelte\";\n\timport Flag from \"./Flag.svelte\";\n\timport FlagActive from \"./FlagActive.svelte\";\n\timport type { I18nFormatter } from \"js/core/src/gradio_helper\";\n\n\texport let i18n: I18nFormatter;\n\texport let handle_action: (selected: string | null) => void;\n\texport let feedback_options: string[];\n\texport let selected: string | null = null;\n\t$: extra_feedback = feedback_options.filter(\n\t\t(option) => option !== \"Like\" && option !== \"Dislike\"\n\t);\n\n\tfunction toggleSelection(newSelection: string): void {\n\t\tselected = selected === newSelection ? null : newSelection;\n\t\thandle_action(selected);\n\t}\n</script>\n\n{#if feedback_options.includes(\"Like\") || feedback_options.includes(\"Dislike\")}\n\t{#if feedback_options.includes(\"Dislike\")}\n\t\t<IconButton\n\t\t\tIcon={selected === \"Dislike\" ? ThumbDownActive : ThumbDownDefault}\n\t\t\tlabel={selected === \"Dislike\"\n\t\t\t\t? \"clicked dislike\"\n\t\t\t\t: i18n(\"chatbot.dislike\")}\n\t\t\tcolor={selected === \"Dislike\"\n\t\t\t\t? \"var(--color-accent)\"\n\t\t\t\t: \"var(--block-label-text-color)\"}\n\t\t\ton:click={() => toggleSelection(\"Dislike\")}\n\t\t/>\n\t{/if}\n\t{#if feedback_options.includes(\"Like\")}\n\t\t<IconButton\n\t\t\tIcon={selected === \"Like\" ? ThumbUpActive : ThumbUpDefault}\n\t\t\tlabel={selected === \"Like\" ? \"clicked like\" : i18n(\"chatbot.like\")}\n\t\t\tcolor={selected === \"Like\"\n\t\t\t\t? \"var(--color-accent)\"\n\t\t\t\t: \"var(--block-label-text-color)\"}\n\t\t\ton:click={() => toggleSelection(\"Like\")}\n\t\t/>\n\t{/if}\n{/if}\n\n{#if extra_feedback.length > 0}\n\t<div class=\"extra-feedback no-border\">\n\t\t<IconButton\n\t\t\tIcon={selected && extra_feedback.includes(selected) ? FlagActive : Flag}\n\t\t\tlabel=\"Feedback\"\n\t\t\tcolor={selected && extra_feedback.includes(selected)\n\t\t\t\t? \"var(--color-accent)\"\n\t\t\t\t: \"var(--block-label-text-color)\"}\n\t\t/>\n\t\t<div class=\"extra-feedback-options\">\n\t\t\t{#each extra_feedback as option}\n\t\t\t\t<button\n\t\t\t\t\tclass=\"extra-feedback-option\"\n\t\t\t\t\tstyle:font-weight={selected === option ? \"bold\" : \"normal\"}\n\t\t\t\t\ton:click={() => {\n\t\t\t\t\t\ttoggleSelection(option);\n\t\t\t\t\t\thandle_action(selected ? selected : null);\n\t\t\t\t\t}}>{option}</button\n\t\t\t\t>\n\t\t\t{/each}\n\t\t</div>\n\t</div>\n{/if}\n\n<style>\n\t.extra-feedback {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tposition: relative;\n\t}\n\t.extra-feedback-options {\n\t\tdisplay: none;\n\t\tposition: absolute;\n\t\tpadding: var(--spacing-md) 0;\n\t\tflex-direction: column;\n\t\tgap: var(--spacing-sm);\n\t\ttop: 100%;\n\t}\n\t.extra-feedback:hover .extra-feedback-options {\n\t\tdisplay: flex;\n\t}\n\t.extra-feedback-option {\n\t\tborder: 1px solid var(--border-color-primary);\n\t\tborder-radius: var(--radius-sm);\n\t\tcolor: var(--block-label-text-color);\n\t\tbackground-color: var(--block-background-fill);\n\t\tfont-size: var(--text-xs);\n\t\tpadding: var(--spacing-xxs) var(--spacing-sm);\n\t\twidth: max-content;\n\t}\n</style>\n", "<script lang=\"ts\">\n\timport { createEventDispatcher } from \"svelte\";\n\timport { onDestroy } from \"svelte\";\n\timport { Copy, Check } from \"@gradio/icons\";\n\timport { IconButton } from \"@gradio/atoms\";\n\timport type { CopyData } from \"@gradio/utils\";\n\tconst dispatch = createEventDispatcher<{\n\t\tchange: undefined;\n\t\tcopy: CopyData;\n\t}>();\n\n\tlet copied = false;\n\texport let value: string;\n\texport let watermark: string | null = null;\n\tlet timer: NodeJS.Timeout;\n\n\tfunction copy_feedback(): void {\n\t\tcopied = true;\n\t\tif (timer) clearTimeout(timer);\n\t\ttimer = setTimeout(() => {\n\t\t\tcopied = false;\n\t\t}, 2000);\n\t}\n\n\tasync function handle_copy(): Promise<void> {\n\t\tif (\"clipboard\" in navigator) {\n\t\t\tdispatch(\"copy\", { value: value });\n\t\t\tconst text_to_copy = watermark ? `${value}\\n\\n${watermark}` : value;\n\t\t\tawait navigator.clipboard.writeText(text_to_copy);\n\t\t\tcopy_feedback();\n\t\t} else {\n\t\t\tconst textArea = document.createElement(\"textarea\");\n\t\t\tconst text_to_copy = watermark ? `${value}\\n\\n${watermark}` : value;\n\t\t\ttextArea.value = text_to_copy;\n\n\t\t\ttextArea.style.position = \"absolute\";\n\t\t\ttextArea.style.left = \"-999999px\";\n\n\t\t\tdocument.body.prepend(textArea);\n\t\t\ttextArea.select();\n\n\t\t\ttry {\n\t\t\t\tdocument.execCommand(\"copy\");\n\t\t\t\tcopy_feedback();\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error(error);\n\t\t\t} finally {\n\t\t\t\ttextArea.remove();\n\t\t\t}\n\t\t}\n\t}\n\n\tonDestroy(() => {\n\t\tif (timer) clearTimeout(timer);\n\t});\n</script>\n\n<IconButton\n\ton:click={handle_copy}\n\tlabel={copied ? \"Copied message\" : \"Copy message\"}\n\tIcon={copied ? Check : Copy}\n/>\n", "<script lang=\"ts\">\n\timport LikeDislike from \"./LikeDislike.svelte\";\n\timport Copy from \"./Copy.svelte\";\n\timport type { FileData } from \"@gradio/client\";\n\timport type { NormalisedMessage, TextMessage, ThoughtNode } from \"../types\";\n\timport { Retry, Undo, Edit, Check, Clear } from \"@gradio/icons\";\n\timport { IconButtonWrapper, IconButton } from \"@gradio/atoms\";\n\timport { all_text, is_all_text } from \"./utils\";\n\timport type { I18nFormatter } from \"js/core/src/gradio_helper\";\n\n\texport let i18n: I18nFormatter;\n\texport let likeable: boolean;\n\texport let feedback_options: string[];\n\texport let show_retry: boolean;\n\texport let show_undo: boolean;\n\texport let show_edit: boolean;\n\texport let in_edit_mode: boolean;\n\texport let show_copy_button: boolean;\n\texport let watermark: string | null = null;\n\texport let message: NormalisedMessage | NormalisedMessage[];\n\texport let position: \"right\" | \"left\";\n\texport let avatar: FileData | null;\n\texport let generating: boolean;\n\texport let current_feedback: string | null;\n\n\texport let handle_action: (selected: string | null) => void;\n\texport let layout: \"bubble\" | \"panel\";\n\texport let dispatch: any;\n\n\t$: message_text = is_all_text(message) ? all_text(message) : \"\";\n\t$: show_copy = show_copy_button && message && is_all_text(message);\n</script>\n\n{#if show_copy || show_retry || show_undo || show_edit || likeable}\n\t<div\n\t\tclass=\"message-buttons-{position} {layout} message-buttons {avatar !==\n\t\t\tnull && 'with-avatar'}\"\n\t>\n\t\t<IconButtonWrapper top_panel={false}>\n\t\t\t{#if in_edit_mode}\n\t\t\t\t<IconButton\n\t\t\t\t\tlabel={i18n(\"chatbot.submit\")}\n\t\t\t\t\tIcon={Check}\n\t\t\t\t\ton:click={() => handle_action(\"edit_submit\")}\n\t\t\t\t\tdisabled={generating}\n\t\t\t\t/>\n\t\t\t\t<IconButton\n\t\t\t\t\tlabel={i18n(\"chatbot.cancel\")}\n\t\t\t\t\tIcon={Clear}\n\t\t\t\t\ton:click={() => handle_action(\"edit_cancel\")}\n\t\t\t\t\tdisabled={generating}\n\t\t\t\t/>\n\t\t\t{:else}\n\t\t\t\t{#if show_copy}\n\t\t\t\t\t<Copy\n\t\t\t\t\t\tvalue={message_text}\n\t\t\t\t\t\ton:copy={(e) => dispatch(\"copy\", e.detail)}\n\t\t\t\t\t\t{watermark}\n\t\t\t\t\t/>\n\t\t\t\t{/if}\n\t\t\t\t{#if show_retry}\n\t\t\t\t\t<IconButton\n\t\t\t\t\t\tIcon={Retry}\n\t\t\t\t\t\tlabel={i18n(\"chatbot.retry\")}\n\t\t\t\t\t\ton:click={() => handle_action(\"retry\")}\n\t\t\t\t\t\tdisabled={generating}\n\t\t\t\t\t/>\n\t\t\t\t{/if}\n\t\t\t\t{#if show_undo}\n\t\t\t\t\t<IconButton\n\t\t\t\t\t\tlabel={i18n(\"chatbot.undo\")}\n\t\t\t\t\t\tIcon={Undo}\n\t\t\t\t\t\ton:click={() => handle_action(\"undo\")}\n\t\t\t\t\t\tdisabled={generating}\n\t\t\t\t\t/>\n\t\t\t\t{/if}\n\t\t\t\t{#if show_edit}\n\t\t\t\t\t<IconButton\n\t\t\t\t\t\tlabel={i18n(\"chatbot.edit\")}\n\t\t\t\t\t\tIcon={Edit}\n\t\t\t\t\t\ton:click={() => handle_action(\"edit\")}\n\t\t\t\t\t\tdisabled={generating}\n\t\t\t\t\t/>\n\t\t\t\t{/if}\n\t\t\t\t{#if likeable}\n\t\t\t\t\t<LikeDislike\n\t\t\t\t\t\t{handle_action}\n\t\t\t\t\t\t{feedback_options}\n\t\t\t\t\t\tselected={current_feedback}\n\t\t\t\t\t\t{i18n}\n\t\t\t\t\t/>\n\t\t\t\t{/if}\n\t\t\t{/if}\n\t\t</IconButtonWrapper>\n\t</div>\n{/if}\n\n<style>\n\t.bubble :global(.icon-button-wrapper) {\n\t\tmargin: 0px calc(var(--spacing-xl) * 2);\n\t}\n\n\t.message-buttons {\n\t\tz-index: var(--layer-1);\n\t}\n\t.message-buttons-left {\n\t\talign-self: flex-start;\n\t}\n\n\t.bubble.message-buttons-right {\n\t\talign-self: flex-end;\n\t}\n\n\t.message-buttons-right :global(.icon-button-wrapper) {\n\t\tmargin-left: auto;\n\t}\n\n\t.bubble.with-avatar {\n\t\tmargin-left: calc(var(--spacing-xl) * 5);\n\t\tmargin-right: calc(var(--spacing-xl) * 5);\n\t}\n\n\t.panel {\n\t\tdisplay: flex;\n\t\talign-self: flex-start;\n\t\tz-index: var(--layer-1);\n\t}\n</style>\n", "<script lang=\"ts\">\n\texport let type:\n\t\t| \"gallery\"\n\t\t| \"plot\"\n\t\t| \"audio\"\n\t\t| \"video\"\n\t\t| \"image\"\n\t\t| \"dataframe\"\n\t\t| \"model3d\"\n\t\t| string;\n\texport let components;\n\texport let value;\n\texport let target;\n\texport let theme_mode;\n\texport let props;\n\texport let i18n;\n\texport let upload;\n\texport let _fetch;\n\texport let allow_file_downloads: boolean;\n\texport let display_icon_button_wrapper_top_corner = false;\n</script>\n\n{#if type === \"gallery\"}\n\t<svelte:component\n\t\tthis={components[type]}\n\t\t{value}\n\t\t{display_icon_button_wrapper_top_corner}\n\t\tshow_label={false}\n\t\t{i18n}\n\t\tlabel=\"\"\n\t\t{_fetch}\n\t\tallow_preview={false}\n\t\tinteractive={false}\n\t\tmode=\"minimal\"\n\t\tfixed_height={1}\n\t\ton:load\n\t/>\n{:else if type === \"dataframe\"}\n\t<svelte:component\n\t\tthis={components[type]}\n\t\t{value}\n\t\tshow_label={false}\n\t\t{i18n}\n\t\tlabel=\"\"\n\t\tinteractive={false}\n\t\tline_breaks={props.line_breaks}\n\t\twrap={true}\n\t\troot=\"\"\n\t\tgradio={{ dispatch: () => {}, i18n }}\n\t\tdatatype={props.datatype}\n\t\tlatex_delimiters={props.latex_delimiters}\n\t\tcol_count={props.col_count}\n\t\trow_count={props.row_count}\n\t\ton:load\n\t/>\n{:else if type === \"plot\"}\n\t<svelte:component\n\t\tthis={components[type]}\n\t\t{value}\n\t\t{target}\n\t\t{theme_mode}\n\t\tbokeh_version={props.bokeh_version}\n\t\tcaption=\"\"\n\t\tshow_actions_button={true}\n\t\ton:load\n\t/>\n{:else if type === \"audio\"}\n\t<div style=\"position: relative;\">\n\t\t<svelte:component\n\t\t\tthis={components[type]}\n\t\t\t{value}\n\t\t\tshow_label={false}\n\t\t\tshow_share_button={true}\n\t\t\t{i18n}\n\t\t\tlabel=\"\"\n\t\t\twaveform_settings={{ autoplay: props.autoplay }}\n\t\t\tshow_download_button={allow_file_downloads}\n\t\t\t{display_icon_button_wrapper_top_corner}\n\t\t\ton:load\n\t\t/>\n\t</div>\n{:else if type === \"video\"}\n\t<svelte:component\n\t\tthis={components[type]}\n\t\tautoplay={props.autoplay}\n\t\tvalue={value.video || value}\n\t\tshow_label={false}\n\t\tshow_share_button={true}\n\t\t{i18n}\n\t\t{upload}\n\t\t{display_icon_button_wrapper_top_corner}\n\t\tshow_download_button={allow_file_downloads}\n\t\ton:load\n\t>\n\t\t<track kind=\"captions\" />\n\t</svelte:component>\n{:else if type === \"image\"}\n\t<svelte:component\n\t\tthis={components[type]}\n\t\t{value}\n\t\tshow_label={false}\n\t\tlabel=\"chatbot-image\"\n\t\tshow_download_button={allow_file_downloads}\n\t\t{display_icon_button_wrapper_top_corner}\n\t\ton:load\n\t\t{i18n}\n\t/>\n{:else if type === \"html\"}\n\t<svelte:component\n\t\tthis={components[type]}\n\t\t{value}\n\t\tshow_label={false}\n\t\tlabel=\"chatbot-html\"\n\t\tshow_share_button={true}\n\t\t{i18n}\n\t\tgradio={{ dispatch: () => {} }}\n\t\ton:load\n\t/>\n{:else if type === \"model3d\"}\n\t<svelte:component\n\t\tthis={components[type]}\n\t\t{value}\n\t\tclear_color={props.clear_color}\n\t\tdisplay_mode={props.display_mode}\n\t\tzoom_speed={props.zoom_speed}\n\t\tpan_speed={props.pan_speed}\n\t\t{...props.camera_position !== undefined && {\n\t\t\tcamera_position: props.camera_position\n\t\t}}\n\t\thas_change_history={true}\n\t\tshow_label={false}\n\t\troot=\"\"\n\t\tinteractive={false}\n\t\tlabel=\"chatbot-model3d\"\n\t\tshow_share_button={true}\n\t\tgradio={{ dispatch: () => {}, i18n }}\n\t\ton:load\n\t/>\n{/if}\n", "<script lang=\"ts\">\n\timport { File } from \"@gradio/icons\";\n\timport Component from \"./Component.svelte\";\n\timport { MarkdownCode as Markdown } from \"@gradio/markdown-code\";\n\timport type { NormalisedMessage } from \"../types\";\n\timport type { I18nFormatter } from \"js/core/src/gradio_helper\";\n\timport type { Client } from \"@gradio/client\";\n\timport type { ComponentType, SvelteComponent } from \"svelte\";\n\n\texport let latex_delimiters: {\n\t\tleft: string;\n\t\tright: string;\n\t\tdisplay: boolean;\n\t}[];\n\texport let sanitize_html: boolean;\n\texport let _fetch: typeof fetch;\n\texport let i18n: I18nFormatter;\n\texport let line_breaks: boolean;\n\texport let upload: Client[\"upload\"];\n\texport let target: HTMLElement | null;\n\texport let theme_mode: \"light\" | \"dark\" | \"system\";\n\texport let _components: Record<string, ComponentType<SvelteComponent>>;\n\texport let render_markdown: boolean;\n\texport let scroll: () => void;\n\texport let allow_file_downloads: boolean;\n\texport let display_consecutive_in_same_bubble: boolean;\n\texport let thought_index: number;\n\texport let allow_tags: string[] | boolean = false;\n\n\texport let message: NormalisedMessage;\n</script>\n\n{#if message.type === \"text\"}\n\t<div class=\"message-content\">\n\t\t<Markdown\n\t\t\tmessage={message.content}\n\t\t\t{latex_delimiters}\n\t\t\t{sanitize_html}\n\t\t\t{render_markdown}\n\t\t\t{line_breaks}\n\t\t\ton:load={scroll}\n\t\t\t{allow_tags}\n\t\t\t{theme_mode}\n\t\t/>\n\t</div>\n{:else if message.type === \"component\" && message.content.component in _components}\n\t<Component\n\t\t{target}\n\t\t{theme_mode}\n\t\tprops={message.content.props}\n\t\ttype={message.content.component}\n\t\tcomponents={_components}\n\t\tvalue={message.content.value}\n\t\tdisplay_icon_button_wrapper_top_corner={thought_index > 0 &&\n\t\t\tdisplay_consecutive_in_same_bubble}\n\t\t{i18n}\n\t\t{upload}\n\t\t{_fetch}\n\t\ton:load={() => scroll()}\n\t\t{allow_file_downloads}\n\t/>\n{:else if message.type === \"component\" && message.content.component === \"file\"}\n\t<div class=\"file-container\">\n\t\t<div class=\"file-icon\">\n\t\t\t<File />\n\t\t</div>\n\t\t<div class=\"file-info\">\n\t\t\t<a\n\t\t\t\tdata-testid=\"chatbot-file\"\n\t\t\t\tclass=\"file-link\"\n\t\t\t\thref={message.content.value.url}\n\t\t\t\ttarget=\"_blank\"\n\t\t\t\tdownload={window.__is_colab__\n\t\t\t\t\t? null\n\t\t\t\t\t: message.content.value?.orig_name ||\n\t\t\t\t\t\tmessage.content.value?.path.split(\"/\").pop() ||\n\t\t\t\t\t\t\"file\"}\n\t\t\t>\n\t\t\t\t<span class=\"file-name\"\n\t\t\t\t\t>{message.content.value?.orig_name ||\n\t\t\t\t\t\tmessage.content.value?.path.split(\"/\").pop() ||\n\t\t\t\t\t\t\"file\"}</span\n\t\t\t\t>\n\t\t\t</a>\n\t\t\t<span class=\"file-type\"\n\t\t\t\t>{(\n\t\t\t\t\tmessage.content.value?.orig_name ||\n\t\t\t\t\tmessage.content.value?.path ||\n\t\t\t\t\t\"\"\n\t\t\t\t)\n\t\t\t\t\t.split(\".\")\n\t\t\t\t\t.pop()\n\t\t\t\t\t.toUpperCase()}</span\n\t\t\t>\n\t\t</div>\n\t</div>\n{/if}\n\n<style>\n\t.file-container {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tgap: var(--spacing-lg);\n\t\tpadding: var(--spacing-lg);\n\t\tborder-radius: var(--radius-lg);\n\t\twidth: fit-content;\n\t\tmargin: var(--spacing-sm) 0;\n\t}\n\n\t.file-icon {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tcolor: var(--body-text-color);\n\t}\n\n\t.file-icon :global(svg) {\n\t\twidth: var(--size-7);\n\t\theight: var(--size-7);\n\t}\n\n\t.file-info {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t}\n\n\t.file-link {\n\t\ttext-decoration: none;\n\t\tcolor: var(--body-text-color);\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\tgap: var(--spacing-xs);\n\t}\n\n\t.file-name {\n\t\tfont-family: var(--font);\n\t\tfont-size: var(--text-md);\n\t\tfont-weight: 500;\n\t}\n\n\t.file-type {\n\t\tfont-family: var(--font);\n\t\tfont-size: var(--text-sm);\n\t\tcolor: var(--body-text-color-subdued);\n\t\ttext-transform: uppercase;\n\t}\n</style>\n", "<script lang=\"ts\">\n\timport type { Client } from \"@gradio/client\";\n\timport type { NormalisedMessage, ThoughtNode } from \"../types\";\n\timport type { I18nFormatter } from \"js/core/src/gradio_helper\";\n\timport type { ComponentType, SvelteComponent } from \"svelte\";\n\timport MessageContent from \"./MessageContent.svelte\";\n\timport { DropdownCircularArrow } from \"@gradio/icons\";\n\timport { IconButton } from \"@gradio/atoms\";\n\timport { slide } from \"svelte/transition\";\n\timport { MarkdownCode as Markdown } from \"@gradio/markdown-code\";\n\n\texport let thought: NormalisedMessage;\n\texport let rtl = false;\n\texport let sanitize_html: boolean;\n\texport let latex_delimiters: {\n\t\tleft: string;\n\t\tright: string;\n\t\tdisplay: boolean;\n\t}[];\n\texport let render_markdown: boolean;\n\texport let _components: Record<string, ComponentType<SvelteComponent>>;\n\texport let upload: Client[\"upload\"];\n\texport let thought_index: number;\n\texport let target: HTMLElement | null;\n\texport let theme_mode: \"light\" | \"dark\" | \"system\";\n\texport let _fetch: typeof fetch;\n\texport let scroll: () => void;\n\texport let allow_file_downloads: boolean;\n\texport let display_consecutive_in_same_bubble: boolean;\n\texport let i18n: I18nFormatter;\n\texport let line_breaks: boolean;\n\texport let allow_tags: string[] | boolean = false;\n\n\tfunction is_thought_node(msg: NormalisedMessage): msg is ThoughtNode {\n\t\treturn \"children\" in msg;\n\t}\n\n\tlet thought_node: ThoughtNode;\n\tlet expanded = false;\n\tlet user_expanded_toggled = false;\n\tlet content_preview_element: HTMLElement;\n\tlet user_is_scrolling = false;\n\n\t$: thought_node = {\n\t\t...thought,\n\t\tchildren: is_thought_node(thought) ? thought.children : []\n\t} as ThoughtNode;\n\n\t$: if (!user_expanded_toggled) {\n\t\texpanded = thought_node?.metadata?.status !== \"done\";\n\t}\n\n\tfunction toggleExpanded(): void {\n\t\texpanded = !expanded;\n\t\tuser_expanded_toggled = true;\n\t}\n\n\tfunction scrollToBottom(): void {\n\t\tif (content_preview_element && !user_is_scrolling) {\n\t\t\tcontent_preview_element.scrollTop = content_preview_element.scrollHeight;\n\t\t}\n\t}\n\n\tfunction handleScroll(): void {\n\t\tif (content_preview_element) {\n\t\t\tconst is_at_bottom =\n\t\t\t\tcontent_preview_element.scrollHeight -\n\t\t\t\t\tcontent_preview_element.scrollTop <=\n\t\t\t\tcontent_preview_element.clientHeight + 10;\n\t\t\tif (!is_at_bottom) {\n\t\t\t\tuser_is_scrolling = true;\n\t\t\t}\n\t\t}\n\t}\n\n\t$: if (\n\t\tthought_node.content &&\n\t\tcontent_preview_element &&\n\t\tthought_node.metadata?.status !== \"done\"\n\t) {\n\t\tsetTimeout(scrollToBottom, 0);\n\t}\n</script>\n\n<div class=\"thought-group\">\n\t<div\n\t\tclass=\"title\"\n\t\tclass:expanded\n\t\ton:click|stopPropagation={toggleExpanded}\n\t\taria-busy={thought_node.content === \"\" || thought_node.content === null}\n\t\trole=\"button\"\n\t\ttabindex=\"0\"\n\t\ton:keydown={(e) => e.key === \"Enter\" && toggleExpanded()}\n\t>\n\t\t<span\n\t\t\tclass=\"arrow\"\n\t\t\tstyle:transform={expanded ? \"rotate(180deg)\" : \"rotate(0deg)\"}\n\t\t>\n\t\t\t<IconButton Icon={DropdownCircularArrow} />\n\t\t</span>\n\t\t<Markdown\n\t\t\tmessage={thought_node.metadata?.title || \"\"}\n\t\t\t{render_markdown}\n\t\t\t{latex_delimiters}\n\t\t\t{sanitize_html}\n\t\t\t{allow_tags}\n\t\t/>\n\t\t{#if thought_node.metadata?.status === \"pending\"}\n\t\t\t<span class=\"loading-spinner\"></span>\n\t\t{/if}\n\t\t{#if thought_node?.metadata?.log || thought_node?.metadata?.duration}\n\t\t\t<span class=\"duration\">\n\t\t\t\t{#if thought_node.metadata.log}\n\t\t\t\t\t{thought_node.metadata.log}\n\t\t\t\t{/if}\n\t\t\t\t{#if thought_node.metadata.duration !== undefined}\n\t\t\t\t\t({#if Number.isInteger(thought_node.metadata.duration)}{thought_node\n\t\t\t\t\t\t\t.metadata\n\t\t\t\t\t\t\t.duration}s{:else if thought_node.metadata.duration >= 0.1}{thought_node.metadata.duration.toFixed(\n\t\t\t\t\t\t\t1\n\t\t\t\t\t\t)}s{:else}{(thought_node.metadata.duration * 1000).toFixed(\n\t\t\t\t\t\t\t1\n\t\t\t\t\t\t)}ms{/if})\n\t\t\t\t{/if}\n\t\t\t</span>\n\t\t{/if}\n\t</div>\n\n\t{#if expanded}\n\t\t<div\n\t\t\tclass:content={expanded}\n\t\t\tclass:content-preview={!expanded &&\n\t\t\t\tthought_node.metadata?.status !== \"done\"}\n\t\t\tbind:this={content_preview_element}\n\t\t\ton:scroll={handleScroll}\n\t\t\ttransition:slide\n\t\t>\n\t\t\t<MessageContent\n\t\t\t\tmessage={thought_node}\n\t\t\t\t{sanitize_html}\n\t\t\t\t{allow_tags}\n\t\t\t\t{latex_delimiters}\n\t\t\t\t{render_markdown}\n\t\t\t\t{_components}\n\t\t\t\t{upload}\n\t\t\t\t{thought_index}\n\t\t\t\t{target}\n\t\t\t\t{theme_mode}\n\t\t\t\t{_fetch}\n\t\t\t\t{scroll}\n\t\t\t\t{allow_file_downloads}\n\t\t\t\t{display_consecutive_in_same_bubble}\n\t\t\t\t{i18n}\n\t\t\t\t{line_breaks}\n\t\t\t/>\n\n\t\t\t{#if thought_node.children?.length > 0}\n\t\t\t\t<div class=\"children\">\n\t\t\t\t\t{#each thought_node.children as child, index}\n\t\t\t\t\t\t<svelte:self\n\t\t\t\t\t\t\tthought={child}\n\t\t\t\t\t\t\t{rtl}\n\t\t\t\t\t\t\t{sanitize_html}\n\t\t\t\t\t\t\t{latex_delimiters}\n\t\t\t\t\t\t\t{render_markdown}\n\t\t\t\t\t\t\t{_components}\n\t\t\t\t\t\t\t{upload}\n\t\t\t\t\t\t\tthought_index={thought_index + 1}\n\t\t\t\t\t\t\t{target}\n\t\t\t\t\t\t\t{theme_mode}\n\t\t\t\t\t\t\t{_fetch}\n\t\t\t\t\t\t\t{scroll}\n\t\t\t\t\t\t\t{allow_file_downloads}\n\t\t\t\t\t\t\t{display_consecutive_in_same_bubble}\n\t\t\t\t\t\t\t{i18n}\n\t\t\t\t\t\t\t{line_breaks}\n\t\t\t\t\t\t/>\n\t\t\t\t\t{/each}\n\t\t\t\t</div>\n\t\t\t{/if}\n\t\t</div>\n\t{/if}\n</div>\n\n<style>\n\t.thought-group {\n\t\tbackground: var(--background-fill-primary);\n\t\tborder: 1px solid var(--border-color-primary);\n\t\tborder-radius: var(--radius-sm);\n\t\tpadding: var(--spacing-md);\n\t\tmargin: var(--spacing-md) 0;\n\t\tfont-size: var(--text-sm);\n\t}\n\n\t.children :global(.thought-group) {\n\t\tborder: none;\n\t\tmargin: 0;\n\t\tpadding-bottom: 0;\n\t}\n\n\t.children {\n\t\tpadding-left: var(--spacing-md);\n\t}\n\n\t.title {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tcolor: var(--body-text-color);\n\t\tcursor: pointer;\n\t\twidth: 100%;\n\t}\n\n\t.title :global(.md) {\n\t\tfont-size: var(--text-sm) !important;\n\t}\n\n\t.content,\n\t.content-preview {\n\t\toverflow-wrap: break-word;\n\t\tword-break: break-word;\n\t\tmargin-left: var(--spacing-lg);\n\t\tmargin-bottom: var(--spacing-sm);\n\t}\n\n\t.content-preview {\n\t\tposition: relative;\n\t\tmax-height: calc(5 * 1.5em);\n\t\toverflow-y: auto;\n\t\toverscroll-behavior: contain;\n\t\tcursor: default;\n\t}\n\n\t.content :global(*),\n\t.content-preview :global(*) {\n\t\tfont-size: var(--text-sm);\n\t\tcolor: var(--body-text-color);\n\t}\n\n\t.thought-group :global(.thought:not(.nested)) {\n\t\tborder: none;\n\t\tbackground: none;\n\t}\n\n\t.duration {\n\t\tcolor: var(--body-text-color-subdued);\n\t\tfont-size: var(--text-sm);\n\t\tmargin-left: var(--size-1);\n\t}\n\n\t.arrow {\n\t\topacity: 0.8;\n\t\twidth: var(--size-8);\n\t\theight: var(--size-8);\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t}\n\n\t.arrow :global(button) {\n\t\tbackground-color: transparent;\n\t}\n\n\t.loading-spinner {\n\t\tdisplay: inline-block;\n\t\twidth: 12px;\n\t\theight: 12px;\n\t\tborder: 2px solid var(--body-text-color);\n\t\tborder-radius: 50%;\n\t\tborder-top-color: transparent;\n\t\tanimation: spin 1s linear infinite;\n\t\tmargin: 0 var(--size-1) -1px var(--size-2);\n\t\topacity: 0.8;\n\t}\n\n\t@keyframes spin {\n\t\tto {\n\t\t\ttransform: rotate(360deg);\n\t\t}\n\t}\n\n\t.thought-group :global(.message-content) {\n\t\topacity: 0.8;\n\t}\n</style>\n", "<script lang=\"ts\">\n\timport { is_component_message } from \"../shared/utils\";\n\timport { Image } from \"@gradio/image/shared\";\n\timport type { FileData, Client } from \"@gradio/client\";\n\timport type { NormalisedMessage } from \"../types\";\n\n\timport type { I18nFormatter } from \"js/core/src/gradio_helper\";\n\timport type { ComponentType, SvelteComponent } from \"svelte\";\n\timport ButtonPanel from \"./ButtonPanel.svelte\";\n\timport MessageContent from \"./MessageContent.svelte\";\n\timport Thought from \"./Thought.svelte\";\n\n\texport let value: NormalisedMessage[];\n\texport let avatar_img: FileData | null;\n\texport let opposite_avatar_img: FileData | null = null;\n\texport let role = \"user\";\n\texport let messages: NormalisedMessage[] = [];\n\texport let layout: \"bubble\" | \"panel\";\n\texport let render_markdown: boolean;\n\texport let latex_delimiters: {\n\t\tleft: string;\n\t\tright: string;\n\t\tdisplay: boolean;\n\t}[];\n\texport let sanitize_html: boolean;\n\texport let selectable: boolean;\n\texport let _fetch: typeof fetch;\n\texport let rtl: boolean;\n\texport let dispatch: any;\n\texport let i18n: I18nFormatter;\n\texport let line_breaks: boolean;\n\texport let upload: Client[\"upload\"];\n\texport let target: HTMLElement | null;\n\texport let theme_mode: \"light\" | \"dark\" | \"system\";\n\texport let _components: Record<string, ComponentType<SvelteComponent>>;\n\texport let i: number;\n\texport let show_copy_button: boolean;\n\texport let generating: boolean;\n\texport let feedback_options: string[];\n\texport let show_like: boolean;\n\texport let show_edit: boolean;\n\texport let show_retry: boolean;\n\texport let show_undo: boolean;\n\texport let msg_format: \"tuples\" | \"messages\";\n\texport let handle_action: (selected: string | null) => void;\n\texport let scroll: () => void;\n\texport let allow_file_downloads: boolean;\n\texport let in_edit_mode: boolean;\n\texport let edit_messages: string[];\n\texport let display_consecutive_in_same_bubble: boolean;\n\texport let current_feedback: string | null = null;\n\texport let allow_tags: string[] | boolean = false;\n\texport let watermark: string | null = null;\n\tlet messageElements: HTMLDivElement[] = [];\n\tlet previous_edit_mode = false;\n\tlet message_widths: number[] = Array(messages.length).fill(160);\n\tlet message_heights: number[] = Array(messages.length).fill(0);\n\n\t$: if (in_edit_mode && !previous_edit_mode) {\n\t\tconst offset = messageElements.length - messages.length;\n\t\tfor (let idx = offset; idx < messageElements.length; idx++) {\n\t\t\tif (idx >= 0) {\n\t\t\t\tmessage_widths[idx - offset] = messageElements[idx]?.clientWidth;\n\t\t\t\tmessage_heights[idx - offset] = messageElements[idx]?.clientHeight;\n\t\t\t}\n\t\t}\n\t}\n\n\tfunction handle_select(i: number, message: NormalisedMessage): void {\n\t\tdispatch(\"select\", {\n\t\t\tindex: message.index,\n\t\t\tvalue: message.content\n\t\t});\n\t}\n\n\tfunction get_message_label_data(message: NormalisedMessage): string {\n\t\tif (message.type === \"text\") {\n\t\t\treturn message.content;\n\t\t} else if (\n\t\t\tmessage.type === \"component\" &&\n\t\t\tmessage.content.component === \"file\"\n\t\t) {\n\t\t\tif (Array.isArray(message.content.value)) {\n\t\t\t\treturn `file of extension type: ${message.content.value[0].orig_name?.split(\".\").pop()}`;\n\t\t\t}\n\t\t\treturn (\n\t\t\t\t`file of extension type: ${message.content.value?.orig_name?.split(\".\").pop()}` +\n\t\t\t\t(message.content.value?.orig_name ?? \"\")\n\t\t\t);\n\t\t}\n\t\treturn `a component of type ${message.content.component ?? \"unknown\"}`;\n\t}\n\n\ttype ButtonPanelProps = {\n\t\thandle_action: (selected: string | null) => void;\n\t\tlikeable: boolean;\n\t\tfeedback_options: string[];\n\t\tshow_retry: boolean;\n\t\tshow_undo: boolean;\n\t\tshow_edit: boolean;\n\t\tin_edit_mode: boolean;\n\t\tgenerating: boolean;\n\t\tshow_copy_button: boolean;\n\t\tmessage: NormalisedMessage[] | NormalisedMessage;\n\t\tposition: \"left\" | \"right\";\n\t\tlayout: \"bubble\" | \"panel\";\n\t\tavatar: FileData | null;\n\t\tdispatch: any;\n\t\tcurrent_feedback: string | null;\n\t\twatermark: string | null;\n\t};\n\n\tlet button_panel_props: ButtonPanelProps;\n\t$: button_panel_props = {\n\t\thandle_action,\n\t\tlikeable: show_like,\n\t\tfeedback_options,\n\t\tshow_retry,\n\t\tshow_undo,\n\t\tshow_edit,\n\t\tin_edit_mode,\n\t\tgenerating,\n\t\tshow_copy_button,\n\t\tmessage: msg_format === \"tuples\" ? messages[0] : messages,\n\t\tposition: role === \"user\" ? \"right\" : \"left\",\n\t\tavatar: avatar_img,\n\t\tlayout,\n\t\tdispatch,\n\t\tcurrent_feedback,\n\t\twatermark\n\t};\n</script>\n\n<div\n\tclass=\"message-row {layout} {role}-row\"\n\tclass:with_avatar={avatar_img !== null}\n\tclass:with_opposite_avatar={opposite_avatar_img !== null}\n>\n\t{#if avatar_img !== null}\n\t\t<div class=\"avatar-container\">\n\t\t\t<Image class=\"avatar-image\" src={avatar_img?.url} alt=\"{role} avatar\" />\n\t\t</div>\n\t{/if}\n\t<div\n\t\tclass:role\n\t\tclass=\"flex-wrap\"\n\t\tclass:component-wrap={messages[0].type === \"component\"}\n\t>\n\t\t<div\n\t\t\tclass:message={display_consecutive_in_same_bubble}\n\t\t\tclass={display_consecutive_in_same_bubble ? role : \"\"}\n\t\t>\n\t\t\t{#each messages as message, thought_index}\n\t\t\t\t<div\n\t\t\t\t\tclass=\"message {!display_consecutive_in_same_bubble ? role : ''}\"\n\t\t\t\t\tclass:panel-full-width={true}\n\t\t\t\t\tclass:message-markdown-disabled={!render_markdown}\n\t\t\t\t\tclass:component={message.type === \"component\"}\n\t\t\t\t\tclass:html={is_component_message(message) &&\n\t\t\t\t\t\tmessage.content.component === \"html\"}\n\t\t\t\t\tclass:thought={thought_index > 0}\n\t\t\t\t>\n\t\t\t\t\t{#if in_edit_mode && message.type === \"text\"}\n\t\t\t\t\t\t<!-- svelte-ignore a11y-autofocus -->\n\t\t\t\t\t\t<textarea\n\t\t\t\t\t\t\tclass=\"edit-textarea\"\n\t\t\t\t\t\t\tstyle:width={`max(${message_widths[thought_index]}px, 160px)`}\n\t\t\t\t\t\t\tstyle:min-height={`${message_heights[thought_index]}px`}\n\t\t\t\t\t\t\tautofocus\n\t\t\t\t\t\t\tbind:value={edit_messages[thought_index]}\n\t\t\t\t\t\t/>\n\t\t\t\t\t{:else}\n\t\t\t\t\t\t<!-- svelte-ignore a11y-no-static-element-interactions -->\n\t\t\t\t\t\t<div\n\t\t\t\t\t\t\tdata-testid={role}\n\t\t\t\t\t\t\tclass:latest={i === value.length - 1}\n\t\t\t\t\t\t\tclass:message-markdown-disabled={!render_markdown}\n\t\t\t\t\t\t\tstyle:user-select=\"text\"\n\t\t\t\t\t\t\tclass:selectable\n\t\t\t\t\t\t\tstyle:cursor={selectable ? \"pointer\" : \"auto\"}\n\t\t\t\t\t\t\tstyle:text-align={rtl ? \"right\" : \"left\"}\n\t\t\t\t\t\t\tbind:this={messageElements[thought_index]}\n\t\t\t\t\t\t\ton:click={() => handle_select(i, message)}\n\t\t\t\t\t\t\ton:keydown={(e) => {\n\t\t\t\t\t\t\t\tif (e.key === \"Enter\") {\n\t\t\t\t\t\t\t\t\thandle_select(i, message);\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}}\n\t\t\t\t\t\t\tdir={rtl ? \"rtl\" : \"ltr\"}\n\t\t\t\t\t\t\taria-label={role +\n\t\t\t\t\t\t\t\t\"'s message: \" +\n\t\t\t\t\t\t\t\tget_message_label_data(message)}\n\t\t\t\t\t\t>\n\t\t\t\t\t\t\t{#if message?.metadata?.title}\n\t\t\t\t\t\t\t\t<Thought\n\t\t\t\t\t\t\t\t\tthought={message}\n\t\t\t\t\t\t\t\t\t{rtl}\n\t\t\t\t\t\t\t\t\t{sanitize_html}\n\t\t\t\t\t\t\t\t\t{allow_tags}\n\t\t\t\t\t\t\t\t\t{latex_delimiters}\n\t\t\t\t\t\t\t\t\t{render_markdown}\n\t\t\t\t\t\t\t\t\t{_components}\n\t\t\t\t\t\t\t\t\t{upload}\n\t\t\t\t\t\t\t\t\t{thought_index}\n\t\t\t\t\t\t\t\t\t{target}\n\t\t\t\t\t\t\t\t\t{theme_mode}\n\t\t\t\t\t\t\t\t\t{_fetch}\n\t\t\t\t\t\t\t\t\t{scroll}\n\t\t\t\t\t\t\t\t\t{allow_file_downloads}\n\t\t\t\t\t\t\t\t\t{display_consecutive_in_same_bubble}\n\t\t\t\t\t\t\t\t\t{i18n}\n\t\t\t\t\t\t\t\t\t{line_breaks}\n\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t{:else}\n\t\t\t\t\t\t\t\t<MessageContent\n\t\t\t\t\t\t\t\t\t{message}\n\t\t\t\t\t\t\t\t\t{sanitize_html}\n\t\t\t\t\t\t\t\t\t{allow_tags}\n\t\t\t\t\t\t\t\t\t{latex_delimiters}\n\t\t\t\t\t\t\t\t\t{render_markdown}\n\t\t\t\t\t\t\t\t\t{_components}\n\t\t\t\t\t\t\t\t\t{upload}\n\t\t\t\t\t\t\t\t\t{thought_index}\n\t\t\t\t\t\t\t\t\t{target}\n\t\t\t\t\t\t\t\t\t{theme_mode}\n\t\t\t\t\t\t\t\t\t{_fetch}\n\t\t\t\t\t\t\t\t\t{scroll}\n\t\t\t\t\t\t\t\t\t{allow_file_downloads}\n\t\t\t\t\t\t\t\t\t{display_consecutive_in_same_bubble}\n\t\t\t\t\t\t\t\t\t{i18n}\n\t\t\t\t\t\t\t\t\t{line_breaks}\n\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t{/if}\n\t\t\t\t\t\t</div>\n\t\t\t\t\t{/if}\n\t\t\t\t</div>\n\n\t\t\t\t{#if layout === \"panel\"}\n\t\t\t\t\t<ButtonPanel\n\t\t\t\t\t\t{...button_panel_props}\n\t\t\t\t\t\t{current_feedback}\n\t\t\t\t\t\t{watermark}\n\t\t\t\t\t\ton:copy={(e) => dispatch(\"copy\", e.detail)}\n\t\t\t\t\t\t{i18n}\n\t\t\t\t\t/>\n\t\t\t\t{/if}\n\t\t\t{/each}\n\t\t</div>\n\t</div>\n</div>\n\n{#if layout === \"bubble\"}\n\t<ButtonPanel {...button_panel_props} {i18n} />\n{/if}\n\n<style>\n\t.message {\n\t\tposition: relative;\n\t\twidth: 100%;\n\t\tmargin-top: var(--spacing-sm);\n\t}\n\n\t.message.display_consecutive_in_same_bubble {\n\t\tmargin-top: 0;\n\t}\n\n\t/* avatar styles */\n\t.avatar-container {\n\t\tflex-shrink: 0;\n\t\tborder-radius: 50%;\n\t\tborder: 1px solid var(--border-color-primary);\n\t\toverflow: hidden;\n\t}\n\n\t.avatar-container :global(img) {\n\t\tobject-fit: cover;\n\t}\n\n\t/* message wrapper */\n\t.flex-wrap {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\twidth: calc(100% - var(--spacing-xxl));\n\t\tmax-width: 100%;\n\t\tcolor: var(--body-text-color);\n\t\tfont-size: var(--chatbot-text-size);\n\t\toverflow-wrap: break-word;\n\t\twidth: 100%;\n\t\theight: 100%;\n\t}\n\n\t.component {\n\t\tpadding: 0;\n\t\tborder-radius: var(--radius-md);\n\t\twidth: fit-content;\n\t\toverflow: hidden;\n\t}\n\n\t.component.gallery {\n\t\tborder: none;\n\t}\n\n\t.bot:has(.model3D),\n\t.user:has(.model3D) {\n\t\tborder: none;\n\t\tmax-width: 75%;\n\t}\n\n\t.message-row :not(.avatar-container) :global(img) {\n\t\tmargin: var(--size-2);\n\t\tmax-height: 300px;\n\t}\n\n\t.file-pil {\n\t\tdisplay: block;\n\t\twidth: fit-content;\n\t\tpadding: var(--spacing-sm) var(--spacing-lg);\n\t\tborder-radius: var(--radius-md);\n\t\tbackground: var(--background-fill-secondary);\n\t\tcolor: var(--body-text-color);\n\t\ttext-decoration: none;\n\t\tmargin: 0;\n\t\tfont-family: var(--font-mono);\n\t\tfont-size: var(--text-sm);\n\t}\n\n\t.file {\n\t\twidth: auto !important;\n\t\tmax-width: fit-content !important;\n\t}\n\n\t@media (max-width: 600px) or (max-width: 480px) {\n\t\t.component {\n\t\t\twidth: 100%;\n\t\t}\n\t}\n\n\t.message :global(.prose) {\n\t\tfont-size: var(--chatbot-text-size);\n\t}\n\n\t.message-bubble-border {\n\t\tborder-width: 1px;\n\t\tborder-radius: var(--radius-md);\n\t}\n\n\t.panel-full-width {\n\t\twidth: 100%;\n\t}\n\t.message-markdown-disabled {\n\t\twhite-space: pre-line;\n\t}\n\n\t.user {\n\t\tborder-radius: var(--radius-md);\n\t\talign-self: flex-end;\n\t\tborder-bottom-right-radius: 0;\n\t\tbox-shadow: var(--shadow-drop);\n\t\tborder: 1px solid var(--border-color-accent-subdued);\n\t\tbackground-color: var(--color-accent-soft);\n\t\tpadding: var(--spacing-sm) var(--spacing-xl);\n\t}\n\n\t.bot {\n\t\tborder: 1px solid var(--border-color-primary);\n\t\tborder-radius: var(--radius-md);\n\t\tborder-color: var(--border-color-primary);\n\t\tbackground-color: var(--background-fill-secondary);\n\t\tbox-shadow: var(--shadow-drop);\n\t\talign-self: flex-start;\n\t\ttext-align: right;\n\t\tborder-bottom-left-radius: 0;\n\t\tpadding: var(--spacing-sm) var(--spacing-xl);\n\t}\n\n\t.bot:has(.table-wrap) {\n\t\tborder: none;\n\t\tbox-shadow: none;\n\t\tbackground: none;\n\t}\n\n\t.panel .user :global(*) {\n\t\ttext-align: right;\n\t}\n\n\t/* Colors */\n\n\t.message-row {\n\t\tdisplay: flex;\n\t\tposition: relative;\n\t}\n\n\t/* bubble mode styles */\n\t.bubble {\n\t\tmargin: calc(var(--spacing-xl) * 2);\n\t\tmargin-bottom: var(--spacing-xl);\n\t}\n\n\t.bubble.user-row {\n\t\talign-self: flex-end;\n\t\tmax-width: calc(100% - var(--spacing-xl) * 6);\n\t}\n\n\t.bubble.bot-row {\n\t\talign-self: flex-start;\n\t\tmax-width: calc(100% - var(--spacing-xl) * 6);\n\t}\n\n\t.bubble .user-row {\n\t\tflex-direction: row;\n\t\tjustify-content: flex-end;\n\t}\n\n\t.bubble .with_avatar.user-row {\n\t\tmargin-right: calc(var(--spacing-xl) * 2) !important;\n\t}\n\n\t.bubble .with_avatar.bot-row {\n\t\tmargin-left: calc(var(--spacing-xl) * 2) !important;\n\t}\n\n\t.bubble .with_opposite_avatar.user-row {\n\t\tmargin-left: calc(var(--spacing-xxl) + 35px + var(--spacing-xxl));\n\t}\n\n\t/* panel mode styles */\n\t.panel {\n\t\tmargin: 0;\n\t\tpadding: calc(var(--spacing-lg) * 2) calc(var(--spacing-lg) * 2);\n\t}\n\n\t.panel.bot-row {\n\t\tbackground: var(--background-fill-secondary);\n\t}\n\n\t.panel .with_avatar {\n\t\tpadding-left: calc(var(--spacing-xl) * 2) !important;\n\t\tpadding-right: calc(var(--spacing-xl) * 2) !important;\n\t}\n\n\t.panel .panel-full-width {\n\t\twidth: 100%;\n\t}\n\n\t.panel .user :global(*) {\n\t\ttext-align: right;\n\t}\n\n\t/* message content */\n\t.flex-wrap {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\tmax-width: 100%;\n\t\tcolor: var(--body-text-color);\n\t\tfont-size: var(--chatbot-text-size);\n\t\toverflow-wrap: break-word;\n\t}\n\n\t@media (max-width: 480px) {\n\t\t.user-row.bubble {\n\t\t\talign-self: flex-end;\n\t\t}\n\n\t\t.bot-row.bubble {\n\t\t\talign-self: flex-start;\n\t\t}\n\t\t.message {\n\t\t\twidth: 100%;\n\t\t}\n\t}\n\n\t.avatar-container {\n\t\talign-self: flex-start;\n\t\tposition: relative;\n\t\tdisplay: flex;\n\t\tjustify-content: flex-start;\n\t\talign-items: flex-start;\n\t\twidth: 35px;\n\t\theight: 35px;\n\t\tflex-shrink: 0;\n\t\tbottom: 0;\n\t\tborder-radius: 50%;\n\t\tborder: 1px solid var(--border-color-primary);\n\t}\n\t.user-row > .avatar-container {\n\t\torder: 2;\n\t}\n\n\t.user-row.bubble > .avatar-container {\n\t\tmargin-left: var(--spacing-xxl);\n\t}\n\n\t.bot-row.bubble > .avatar-container {\n\t\tmargin-left: var(--spacing-xxl);\n\t}\n\n\t.panel.user-row > .avatar-container {\n\t\torder: 0;\n\t}\n\n\t.bot-row.bubble > .avatar-container {\n\t\tmargin-right: var(--spacing-xxl);\n\t\tmargin-left: 0;\n\t}\n\n\t.avatar-container:not(.thumbnail-item) :global(img) {\n\t\twidth: 100%;\n\t\theight: 100%;\n\t\tobject-fit: cover;\n\t\tborder-radius: 50%;\n\t\tpadding: var(--size-1-5);\n\t}\n\n\t.selectable {\n\t\tcursor: pointer;\n\t}\n\n\t@keyframes dot-flashing {\n\t\t0% {\n\t\t\topacity: 0.8;\n\t\t}\n\t\t50% {\n\t\t\topacity: 0.5;\n\t\t}\n\t\t100% {\n\t\t\topacity: 0.8;\n\t\t}\n\t}\n\n\t/* Image preview */\n\t.message :global(.preview) {\n\t\tobject-fit: contain;\n\t\twidth: 95%;\n\t\tmax-height: 93%;\n\t}\n\t.image-preview {\n\t\tposition: absolute;\n\t\tz-index: 999;\n\t\tleft: 0;\n\t\ttop: 0;\n\t\twidth: 100%;\n\t\theight: 100%;\n\t\toverflow: auto;\n\t\tbackground-color: rgba(0, 0, 0, 0.9);\n\t\tdisplay: flex;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t}\n\t.image-preview :global(svg) {\n\t\tstroke: white;\n\t}\n\t.image-preview-close-button {\n\t\tposition: absolute;\n\t\ttop: 10px;\n\t\tright: 10px;\n\t\tbackground: none;\n\t\tborder: none;\n\t\tfont-size: 1.5em;\n\t\tcursor: pointer;\n\t\theight: 30px;\n\t\twidth: 30px;\n\t\tpadding: 3px;\n\t\tbackground: var(--bg-color);\n\t\tbox-shadow: var(--shadow-drop);\n\t\tborder: 1px solid var(--button-secondary-border-color);\n\t\tborder-radius: var(--radius-lg);\n\t}\n\n\t.message > div {\n\t\twidth: 100%;\n\t}\n\t.html {\n\t\tpadding: 0;\n\t\tborder: none;\n\t\tbackground: none;\n\t}\n\n\t.panel .bot,\n\t.panel .user {\n\t\tborder: none;\n\t\tbox-shadow: none;\n\t\tbackground-color: var(--background-fill-secondary);\n\t}\n\n\ttextarea {\n\t\tbackground: none;\n\t\tborder-radius: var(--radius-lg);\n\t\tborder: none;\n\t\tdisplay: block;\n\t\tmax-width: 100%;\n\t}\n\t.user textarea {\n\t\tborder-bottom-right-radius: 0;\n\t}\n\t.bot textarea {\n\t\tborder-bottom-left-radius: 0;\n\t}\n\t.user textarea:focus {\n\t\toutline: 2px solid var(--border-color-accent);\n\t}\n\t.bot textarea:focus {\n\t\toutline: 2px solid var(--border-color-primary);\n\t}\n\n\t.panel.user-row {\n\t\tbackground-color: var(--color-accent-soft);\n\t}\n\n\t.panel .user-row,\n\t.panel .bot-row {\n\t\talign-self: flex-start;\n\t}\n\n\t.panel .user :global(*),\n\t.panel .bot :global(*) {\n\t\ttext-align: left;\n\t}\n\n\t.panel .user {\n\t\tbackground-color: var(--color-accent-soft);\n\t}\n\n\t.panel .user-row {\n\t\tbackground-color: var(--color-accent-soft);\n\t\talign-self: flex-start;\n\t}\n\n\t.panel .message {\n\t\tmargin-bottom: var(--spacing-md);\n\t}\n</style>\n", "<script lang=\"ts\">\n\timport { Image } from \"@gradio/image/shared\";\n\timport type { FileData } from \"@gradio/client\";\n\n\texport let layout = \"bubble\";\n\texport let avatar_images: [FileData | null, FileData | null] = [null, null];\n</script>\n\n<div class=\"container\">\n\t{#if avatar_images[1] !== null}\n\t\t<div class=\"avatar-container\">\n\t\t\t<Image class=\"avatar-image\" src={avatar_images[1].url} alt=\"bot avatar\" />\n\t\t</div>\n\t{/if}\n\n\t<div\n\t\tclass=\"message bot pending {layout}\"\n\t\tclass:with_avatar={avatar_images[1] !== null}\n\t\tclass:with_opposite_avatar={avatar_images[0] !== null}\n\t\trole=\"status\"\n\t\taria-label=\"Loading response\"\n\t\taria-live=\"polite\"\n\t>\n\t\t<div class=\"message-content\">\n\t\t\t<span class=\"sr-only\">Loading content</span>\n\t\t\t<div class=\"dots\">\n\t\t\t\t<div class=\"dot\" />\n\t\t\t\t<div class=\"dot\" />\n\t\t\t\t<div class=\"dot\" />\n\t\t\t</div>\n\t\t</div>\n\t</div>\n</div>\n\n<style>\n\t.container {\n\t\tdisplay: flex;\n\t\tmargin: calc(var(--spacing-xl) * 2);\n\t}\n\n\t.bubble.pending {\n\t\tborder-width: 1px;\n\t\tborder-radius: var(--radius-lg);\n\t\tborder-bottom-left-radius: 0;\n\t\tborder-color: var(--border-color-primary);\n\t\tbackground-color: var(--background-fill-secondary);\n\t\tbox-shadow: var(--shadow-drop);\n\t\talign-self: flex-start;\n\t\twidth: fit-content;\n\t\tmargin-bottom: var(--spacing-xl);\n\t}\n\n\t.bubble.with_opposite_avatar {\n\t\tmargin-right: calc(var(--spacing-xxl) + 35px + var(--spacing-xxl));\n\t}\n\n\t.panel.pending {\n\t\tmargin: 0;\n\t\tpadding: calc(var(--spacing-lg) * 2) calc(var(--spacing-lg) * 2);\n\t\twidth: 100%;\n\t\tborder: none;\n\t\tbackground: none;\n\t\tbox-shadow: none;\n\t\tborder-radius: 0;\n\t}\n\n\t.panel.with_avatar {\n\t\tpadding-left: calc(var(--spacing-xl) * 2) !important;\n\t\tpadding-right: calc(var(--spacing-xl) * 2) !important;\n\t}\n\n\t.avatar-container {\n\t\talign-self: flex-start;\n\t\tposition: relative;\n\t\tdisplay: flex;\n\t\tjustify-content: flex-start;\n\t\talign-items: flex-start;\n\t\twidth: 35px;\n\t\theight: 35px;\n\t\tflex-shrink: 0;\n\t\tbottom: 0;\n\t\tborder-radius: 50%;\n\t\tborder: 1px solid var(--border-color-primary);\n\t\tmargin-right: var(--spacing-xxl);\n\t}\n\n\t.avatar-container:not(.thumbnail-item) :global(img) {\n\t\twidth: 100%;\n\t\theight: 100%;\n\t\tobject-fit: cover;\n\t\tborder-radius: 50%;\n\t\tpadding: var(--size-1-5);\n\t}\n\n\t.message-content {\n\t\tpadding: var(--spacing-sm) var(--spacing-xl);\n\t\tmin-height: var(--size-8);\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t}\n\n\t.dots {\n\t\tdisplay: flex;\n\t\tgap: var(--spacing-xs);\n\t\talign-items: center;\n\t}\n\n\t.dot {\n\t\twidth: var(--size-1-5);\n\t\theight: var(--size-1-5);\n\t\tmargin-right: var(--spacing-xs);\n\t\tborder-radius: 50%;\n\t\tbackground-color: var(--body-text-color);\n\t\topacity: 0.5;\n\t\tanimation: pulse 1.5s infinite;\n\t}\n\n\t.dot:nth-child(2) {\n\t\tanimation-delay: 0.2s;\n\t}\n\n\t.dot:nth-child(3) {\n\t\tanimation-delay: 0.4s;\n\t}\n\n\t@keyframes pulse {\n\t\t0%,\n\t\t100% {\n\t\t\topacity: 0.4;\n\t\t\ttransform: scale(1);\n\t\t}\n\t\t50% {\n\t\t\topacity: 1;\n\t\t\ttransform: scale(1.1);\n\t\t}\n\t}\n</style>\n", "<script lang=\"ts\">\n\timport { Image } from \"@gradio/image/shared\";\n\timport { MarkdownCode as Markdown } from \"@gradio/markdown-code\";\n\timport { File, Music, Video } from \"@gradio/icons\";\n\timport type { ExampleMessage } from \"../types\";\n\timport { createEventDispatcher } from \"svelte\";\n\timport type { SelectData } from \"@gradio/utils\";\n\timport type { FileData } from \"@gradio/client\";\n\n\texport let examples: ExampleMessage[] | null = null;\n\texport let placeholder: string | null = null;\n\texport let latex_delimiters: {\n\t\tleft: string;\n\t\tright: string;\n\t\tdisplay: boolean;\n\t}[];\n\n\tconst dispatch = createEventDispatcher<{\n\t\texample_select: SelectData;\n\t}>();\n\n\tfunction handle_example_select(\n\t\ti: number,\n\t\texample: ExampleMessage | string\n\t): void {\n\t\tconst example_obj =\n\t\t\ttypeof example === \"string\" ? { text: example } : example;\n\t\tdispatch(\"example_select\", {\n\t\t\tindex: i,\n\t\t\tvalue: { text: example_obj.text, files: example_obj.files }\n\t\t});\n\t}\n</script>\n\n<div class=\"placeholder-content\" role=\"complementary\">\n\t{#if placeholder !== null}\n\t\t<div class=\"placeholder\">\n\t\t\t<Markdown message={placeholder} {latex_delimiters} />\n\t\t</div>\n\t{/if}\n\t{#if examples !== null}\n\t\t<div class=\"examples\" role=\"list\">\n\t\t\t{#each examples as example, i}\n\t\t\t\t<button\n\t\t\t\t\tclass=\"example\"\n\t\t\t\t\ton:click={() =>\n\t\t\t\t\t\thandle_example_select(\n\t\t\t\t\t\t\ti,\n\t\t\t\t\t\t\ttypeof example === \"string\" ? { text: example } : example\n\t\t\t\t\t\t)}\n\t\t\t\t\taria-label={`Select example ${i + 1}: ${example.display_text || example.text}`}\n\t\t\t\t>\n\t\t\t\t\t<div class=\"example-content\">\n\t\t\t\t\t\t{#if example?.icon?.url}\n\t\t\t\t\t\t\t<div class=\"example-image-container\">\n\t\t\t\t\t\t\t\t<Image\n\t\t\t\t\t\t\t\t\tclass=\"example-image\"\n\t\t\t\t\t\t\t\t\tsrc={example.icon.url}\n\t\t\t\t\t\t\t\t\talt=\"Example icon\"\n\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t{:else if example?.icon?.mime_type === \"text\"}\n\t\t\t\t\t\t\t<div class=\"example-icon\" aria-hidden=\"true\">\n\t\t\t\t\t\t\t\t<span class=\"text-icon-aa\">Aa</span>\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t{:else if example.files !== undefined && example.files.length > 0}\n\t\t\t\t\t\t\t{#if example.files.length > 1}\n\t\t\t\t\t\t\t\t<div\n\t\t\t\t\t\t\t\t\tclass=\"example-icons-grid\"\n\t\t\t\t\t\t\t\t\trole=\"group\"\n\t\t\t\t\t\t\t\t\taria-label=\"Example attachments\"\n\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t{#each example.files.slice(0, 4) as file, i}\n\t\t\t\t\t\t\t\t\t\t{#if file.mime_type?.includes(\"image\")}\n\t\t\t\t\t\t\t\t\t\t\t<div class=\"example-image-container\">\n\t\t\t\t\t\t\t\t\t\t\t\t<Image\n\t\t\t\t\t\t\t\t\t\t\t\t\tclass=\"example-image\"\n\t\t\t\t\t\t\t\t\t\t\t\t\tsrc={file.url}\n\t\t\t\t\t\t\t\t\t\t\t\t\talt={file.orig_name || `Example image ${i + 1}`}\n\t\t\t\t\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t\t\t\t\t\t{#if i === 3 && example.files.length > 4}\n\t\t\t\t\t\t\t\t\t\t\t\t\t<div\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tclass=\"image-overlay\"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\trole=\"status\"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\taria-label={`${example.files.length - 4} more files`}\n\t\t\t\t\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t+{example.files.length - 4}\n\t\t\t\t\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t\t\t\t\t{/if}\n\t\t\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t\t\t{:else if file.mime_type?.includes(\"video\")}\n\t\t\t\t\t\t\t\t\t\t\t<div class=\"example-image-container\">\n\t\t\t\t\t\t\t\t\t\t\t\t<video\n\t\t\t\t\t\t\t\t\t\t\t\t\tclass=\"example-image\"\n\t\t\t\t\t\t\t\t\t\t\t\t\tsrc={file.url}\n\t\t\t\t\t\t\t\t\t\t\t\t\taria-hidden=\"true\"\n\t\t\t\t\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t\t\t\t\t\t{#if i === 3 && example.files.length > 4}\n\t\t\t\t\t\t\t\t\t\t\t\t\t<div\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tclass=\"image-overlay\"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\trole=\"status\"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\taria-label={`${example.files.length - 4} more files`}\n\t\t\t\t\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t+{example.files.length - 4}\n\t\t\t\t\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t\t\t\t\t{/if}\n\t\t\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t\t\t{:else}\n\t\t\t\t\t\t\t\t\t\t\t<div\n\t\t\t\t\t\t\t\t\t\t\t\tclass=\"example-icon\"\n\t\t\t\t\t\t\t\t\t\t\t\taria-label={`File: ${file.orig_name}`}\n\t\t\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t\t\t\t{#if file.mime_type?.includes(\"audio\")}\n\t\t\t\t\t\t\t\t\t\t\t\t\t<Music />\n\t\t\t\t\t\t\t\t\t\t\t\t{:else}\n\t\t\t\t\t\t\t\t\t\t\t\t\t<File />\n\t\t\t\t\t\t\t\t\t\t\t\t{/if}\n\t\t\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t\t\t{/if}\n\t\t\t\t\t\t\t\t\t{/each}\n\t\t\t\t\t\t\t\t\t{#if example.files.length > 4}\n\t\t\t\t\t\t\t\t\t\t<div class=\"example-icon\">\n\t\t\t\t\t\t\t\t\t\t\t<div\n\t\t\t\t\t\t\t\t\t\t\t\tclass=\"file-overlay\"\n\t\t\t\t\t\t\t\t\t\t\t\trole=\"status\"\n\t\t\t\t\t\t\t\t\t\t\t\taria-label={`${example.files.length - 4} more files`}\n\t\t\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t\t\t\t+{example.files.length - 4}\n\t\t\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t\t{/if}\n\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t{:else if example.files[0].mime_type?.includes(\"image\")}\n\t\t\t\t\t\t\t\t<div class=\"example-image-container\">\n\t\t\t\t\t\t\t\t\t<Image\n\t\t\t\t\t\t\t\t\t\tclass=\"example-image\"\n\t\t\t\t\t\t\t\t\t\tsrc={example.files[0].url}\n\t\t\t\t\t\t\t\t\t\talt={example.files[0].orig_name || \"Example image\"}\n\t\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t{:else if example.files[0].mime_type?.includes(\"video\")}\n\t\t\t\t\t\t\t\t<div class=\"example-image-container\">\n\t\t\t\t\t\t\t\t\t<video\n\t\t\t\t\t\t\t\t\t\tclass=\"example-image\"\n\t\t\t\t\t\t\t\t\t\tsrc={example.files[0].url}\n\t\t\t\t\t\t\t\t\t\taria-hidden=\"true\"\n\t\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t{:else if example.files[0].mime_type?.includes(\"audio\")}\n\t\t\t\t\t\t\t\t<div\n\t\t\t\t\t\t\t\t\tclass=\"example-icon\"\n\t\t\t\t\t\t\t\t\taria-label={`File: ${example.files[0].orig_name}`}\n\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t<Music />\n\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t{:else}\n\t\t\t\t\t\t\t\t<div\n\t\t\t\t\t\t\t\t\tclass=\"example-icon\"\n\t\t\t\t\t\t\t\t\taria-label={`File: ${example.files[0].orig_name}`}\n\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t<File />\n\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t{/if}\n\t\t\t\t\t\t{/if}\n\n\t\t\t\t\t\t<div class=\"example-text-content\">\n\t\t\t\t\t\t\t<span class=\"example-text\"\n\t\t\t\t\t\t\t\t>{example.display_text || example.text}</span\n\t\t\t\t\t\t\t>\n\t\t\t\t\t\t</div>\n\t\t\t\t\t</div>\n\t\t\t\t</button>\n\t\t\t{/each}\n\t\t</div>\n\t{/if}\n</div>\n\n<style>\n\t.placeholder-content {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\theight: 100%;\n\t}\n\n\t.placeholder {\n\t\talign-items: center;\n\t\tdisplay: flex;\n\t\tjustify-content: center;\n\t\theight: 100%;\n\t\tflex-grow: 1;\n\t}\n\n\t.examples :global(img) {\n\t\tpointer-events: none;\n\t}\n\n\t.examples {\n\t\tmargin: auto;\n\t\tpadding: var(--spacing-xxl);\n\t\tdisplay: grid;\n\t\tgrid-template-columns: repeat(auto-fit, minmax(240px, 1fr));\n\t\tgap: var(--spacing-xl);\n\t\tmax-width: calc(min(4 * 240px + 5 * var(--spacing-xxl), 100%));\n\t}\n\n\t.example {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\talign-items: flex-start;\n\t\tpadding: var(--spacing-xxl);\n\t\tborder: none;\n\t\tborder-radius: var(--radius-lg);\n\t\tbackground-color: var(--block-background-fill);\n\t\tcursor: pointer;\n\t\ttransition: all 150ms ease-in-out;\n\t\twidth: 100%;\n\t\tgap: var(--spacing-sm);\n\t\tborder: var(--block-border-width) solid var(--block-border-color);\n\t\ttransform: translateY(0px);\n\t}\n\n\t.example:hover {\n\t\ttransform: translateY(-2px);\n\t\tbackground-color: var(--color-accent-soft);\n\t}\n\n\t.example-content {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\talign-items: flex-start;\n\t\twidth: 100%;\n\t\theight: 100%;\n\t}\n\n\t.example-text-content {\n\t\tmargin-top: auto;\n\t\ttext-align: left;\n\t}\n\n\t.example-text {\n\t\tfont-size: var(--text-md);\n\t\ttext-align: left;\n\t\toverflow: hidden;\n\t\ttext-overflow: ellipsis;\n\t}\n\n\t.example-icons-grid {\n\t\tdisplay: flex;\n\t\tgap: var(--spacing-sm);\n\t\tmargin-bottom: var(--spacing-lg);\n\t\twidth: 100%;\n\t}\n\n\t.example-icon {\n\t\tflex-shrink: 0;\n\t\twidth: var(--size-8);\n\t\theight: var(--size-8);\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tborder-radius: var(--radius-lg);\n\t\tborder: var(--block-border-width) solid var(--block-border-color);\n\t\tbackground-color: var(--block-background-fill);\n\t\tposition: relative;\n\t}\n\n\t.example-icon :global(svg) {\n\t\twidth: var(--size-4);\n\t\theight: var(--size-4);\n\t\tcolor: var(--color-text-secondary);\n\t}\n\n\t.text-icon-aa {\n\t\tfont-size: var(--text-sm);\n\t\tfont-weight: var(--weight-semibold);\n\t\tcolor: var(--color-text-secondary);\n\t\tline-height: 1;\n\t}\n\n\t.example-image-container {\n\t\twidth: var(--size-8);\n\t\theight: var(--size-8);\n\t\tborder-radius: var(--radius-lg);\n\t\toverflow: hidden;\n\t\tposition: relative;\n\t\tmargin-bottom: var(--spacing-lg);\n\t}\n\n\t.example-image-container :global(img) {\n\t\twidth: 100%;\n\t\theight: 100%;\n\t\tobject-fit: cover;\n\t}\n\n\t.image-overlay {\n\t\tposition: absolute;\n\t\ttop: 0;\n\t\tleft: 0;\n\t\tright: 0;\n\t\tbottom: 0;\n\t\tbackground: rgba(0, 0, 0, 0.6);\n\t\tcolor: white;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tfont-size: var(--text-lg);\n\t\tfont-weight: var(--weight-semibold);\n\t\tborder-radius: var(--radius-lg);\n\t}\n\n\t.file-overlay {\n\t\tposition: absolute;\n\t\tinset: 0;\n\t\tbackground: rgba(0, 0, 0, 0.6);\n\t\tcolor: white;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tfont-size: var(--text-sm);\n\t\tfont-weight: var(--weight-semibold);\n\t\tborder-radius: var(--radius-lg);\n\t}\n</style>\n", "<script lang=\"ts\">\n\timport { onDestroy } from \"svelte\";\n\timport { Copy, Check } from \"@gradio/icons\";\n\timport type { NormalisedMessage } from \"../types\";\n\timport { IconButton } from \"@gradio/atoms\";\n\n\tlet copied = false;\n\texport let value: NormalisedMessage[] | null;\n\texport let watermark: string | null = null;\n\n\tlet timer: NodeJS.Timeout;\n\n\tfunction copy_feedback(): void {\n\t\tcopied = true;\n\t\tif (timer) clearTimeout(timer);\n\t\ttimer = setTimeout(() => {\n\t\t\tcopied = false;\n\t\t}, 1000);\n\t}\n\n\tconst copy_conversation = (): void => {\n\t\tif (value) {\n\t\t\tconst conversation_value = value\n\t\t\t\t.map((message) => {\n\t\t\t\t\tif (message.type === \"text\") {\n\t\t\t\t\t\treturn `${message.role}: ${message.content}`;\n\t\t\t\t\t}\n\t\t\t\t\treturn `${message.role}: ${message.content.value.url}`;\n\t\t\t\t})\n\t\t\t\t.join(\"\\n\\n\");\n\n\t\t\tconst text_to_copy = watermark\n\t\t\t\t? `${conversation_value}\\n\\n${watermark}`\n\t\t\t\t: conversation_value;\n\n\t\t\tnavigator.clipboard.writeText(text_to_copy).catch((err) => {\n\t\t\t\tconsole.error(\"Failed to copy conversation: \", err);\n\t\t\t});\n\t\t}\n\t};\n\n\tasync function handle_copy(): Promise<void> {\n\t\tif (\"clipboard\" in navigator) {\n\t\t\tcopy_conversation();\n\t\t\tcopy_feedback();\n\t\t}\n\t}\n\n\tonDestroy(() => {\n\t\tif (timer) clearTimeout(timer);\n\t});\n</script>\n\n<IconButton\n\tIcon={copied ? Check : Copy}\n\ton:click={handle_copy}\n\tlabel={copied ? \"Copied conversation\" : \"Copy conversation\"}\n></IconButton>\n", "<script lang=\"ts\">\n\timport {\n\t\tformat_chat_for_sharing,\n\t\ttype UndoRetryData,\n\t\ttype EditData,\n\t\tis_last_bot_message,\n\t\tgroup_messages,\n\t\tload_components,\n\t\tget_components_from_messages\n\t} from \"./utils\";\n\timport type { NormalisedMessage, Option } from \"../types\";\n\timport { copy } from \"@gradio/utils\";\n\timport type { CopyData } from \"@gradio/utils\";\n\timport Message from \"./Message.svelte\";\n\n\timport { dequal } from \"dequal/lite\";\n\timport {\n\t\tcreateEventDispatcher,\n\t\ttype SvelteComponent,\n\t\ttype ComponentType,\n\t\ttick,\n\t\tonMount\n\t} from \"svelte\";\n\n\timport { Trash, Community, ScrollDownArrow } from \"@gradio/icons\";\n\timport { IconButtonWrapper, IconButton } from \"@gradio/atoms\";\n\timport type { SelectData, LikeData } from \"@gradio/utils\";\n\timport type { ExampleMessage } from \"../types\";\n\timport type { FileData, Client } from \"@gradio/client\";\n\timport type { I18nFormatter } from \"js/core/src/gradio_helper\";\n\timport Pending from \"./Pending.svelte\";\n\timport { ShareError } from \"@gradio/utils\";\n\timport { Gradio } from \"@gradio/utils\";\n\n\timport Examples from \"./Examples.svelte\";\n\n\texport let value: NormalisedMessage[] | null = [];\n\tlet old_value: NormalisedMessage[] | null = null;\n\n\timport CopyAll from \"./CopyAll.svelte\";\n\n\texport let _fetch: typeof fetch;\n\texport let load_component: Gradio[\"load_component\"];\n\texport let allow_file_downloads: boolean;\n\texport let display_consecutive_in_same_bubble: boolean;\n\n\tlet _components: Record<string, ComponentType<SvelteComponent>> = {};\n\n\tconst is_browser = typeof window !== \"undefined\";\n\n\tasync function update_components(): Promise<void> {\n\t\t_components = await load_components(\n\t\t\tget_components_from_messages(value),\n\t\t\t_components,\n\t\t\tload_component\n\t\t);\n\t}\n\n\t$: value, update_components();\n\n\texport let latex_delimiters: {\n\t\tleft: string;\n\t\tright: string;\n\t\tdisplay: boolean;\n\t}[];\n\texport let pending_message = false;\n\texport let generating = false;\n\texport let selectable = false;\n\texport let likeable = false;\n\texport let feedback_options: string[];\n\texport let feedback_value: (string | null)[] | null = null;\n\texport let editable: \"user\" | \"all\" | null = null;\n\texport let show_share_button = false;\n\texport let show_copy_all_button = false;\n\texport let rtl = false;\n\texport let show_copy_button = false;\n\texport let avatar_images: [FileData | null, FileData | null] = [null, null];\n\texport let sanitize_html = true;\n\texport let render_markdown = true;\n\texport let line_breaks = true;\n\texport let autoscroll = true;\n\texport let theme_mode: \"system\" | \"light\" | \"dark\";\n\texport let i18n: I18nFormatter;\n\texport let layout: \"bubble\" | \"panel\" = \"bubble\";\n\texport let placeholder: string | null = null;\n\texport let upload: Client[\"upload\"];\n\texport let msg_format: \"tuples\" | \"messages\" = \"tuples\";\n\texport let examples: ExampleMessage[] | null = null;\n\texport let _retryable = false;\n\texport let _undoable = false;\n\texport let like_user_message = false;\n\texport let allow_tags: string[] | boolean = false;\n\texport let watermark: string | null = null;\n\texport let show_progress: \"full\" | \"minimal\" | \"hidden\" = \"full\";\n\n\tlet target: HTMLElement | null = null;\n\tlet edit_index: number | null = null;\n\tlet edit_messages: string[] = [];\n\n\tonMount(() => {\n\t\ttarget = document.querySelector(\"div.gradio-container\");\n\t});\n\n\tlet div: HTMLDivElement;\n\n\tlet show_scroll_button = false;\n\n\tconst dispatch = createEventDispatcher<{\n\t\tchange: undefined;\n\t\tselect: SelectData;\n\t\tlike: LikeData;\n\t\tedit: EditData;\n\t\tundo: UndoRetryData;\n\t\tretry: UndoRetryData;\n\t\tclear: undefined;\n\t\tshare: any;\n\t\terror: string;\n\t\texample_select: SelectData;\n\t\toption_select: SelectData;\n\t\tcopy: CopyData;\n\t}>();\n\n\tfunction is_at_bottom(): boolean {\n\t\treturn div && div.offsetHeight + div.scrollTop > div.scrollHeight - 100;\n\t}\n\n\tfunction scroll_to_bottom(): void {\n\t\tif (!div) return;\n\t\tdiv.scrollTo(0, div.scrollHeight);\n\t\tshow_scroll_button = false;\n\t}\n\n\tlet scroll_after_component_load = false;\n\n\tasync function scroll_on_value_update(): Promise<void> {\n\t\tif (!autoscroll) return;\n\t\tif (is_at_bottom()) {\n\t\t\t// Child components may be loaded asynchronously,\n\t\t\t// so trigger the scroll again after they load.\n\t\t\tscroll_after_component_load = true;\n\t\t\tawait tick(); // Wait for the DOM to update so that the scrollHeight is correct\n\t\t\tawait new Promise((resolve) => setTimeout(resolve, 300));\n\t\t\tscroll_to_bottom();\n\t\t}\n\t}\n\tonMount(() => {\n\t\tif (autoscroll) {\n\t\t\tscroll_to_bottom();\n\t\t}\n\t\tscroll_on_value_update();\n\t});\n\t$: if (value || pending_message || _components) {\n\t\tscroll_on_value_update();\n\t}\n\n\tonMount(() => {\n\t\tfunction handle_scroll(): void {\n\t\t\tif (is_at_bottom()) {\n\t\t\t\tshow_scroll_button = false;\n\t\t\t} else {\n\t\t\t\tscroll_after_component_load = false;\n\t\t\t\tshow_scroll_button = true;\n\t\t\t}\n\t\t}\n\n\t\tdiv?.addEventListener(\"scroll\", handle_scroll);\n\t\treturn () => {\n\t\t\tdiv?.removeEventListener(\"scroll\", handle_scroll);\n\t\t};\n\t});\n\n\t$: {\n\t\tif (!dequal(value, old_value)) {\n\t\t\told_value = value;\n\t\t\tdispatch(\"change\");\n\t\t}\n\t}\n\t$: groupedMessages = value && group_messages(value, msg_format);\n\t$: options = value && get_last_bot_options();\n\n\tfunction handle_action(\n\t\ti: number,\n\t\tmessage: NormalisedMessage,\n\t\tselected: string | null\n\t): void {\n\t\tif (selected === \"undo\" || selected === \"retry\") {\n\t\t\tconst val_ = value as NormalisedMessage[];\n\t\t\t// iterate through messages until we find the last user message\n\t\t\t// the index of this message is where the user needs to edit the chat history\n\t\t\tlet last_index = val_.length - 1;\n\t\t\twhile (val_[last_index].role === \"assistant\") {\n\t\t\t\tlast_index--;\n\t\t\t}\n\t\t\tdispatch(selected, {\n\t\t\t\tindex: val_[last_index].index,\n\t\t\t\tvalue: val_[last_index].content\n\t\t\t});\n\t\t} else if (selected == \"edit\") {\n\t\t\tedit_index = i;\n\t\t\tedit_messages.push(message.content as string);\n\t\t} else if (selected == \"edit_cancel\") {\n\t\t\tedit_index = null;\n\t\t} else if (selected == \"edit_submit\") {\n\t\t\tedit_index = null;\n\t\t\tdispatch(\"edit\", {\n\t\t\t\tindex: message.index,\n\t\t\t\tvalue: edit_messages[i].slice(),\n\t\t\t\tprevious_value: message.content as string\n\t\t\t});\n\t\t} else {\n\t\t\tlet feedback =\n\t\t\t\tselected === \"Like\"\n\t\t\t\t\t? true\n\t\t\t\t\t: selected === \"Dislike\"\n\t\t\t\t\t\t? false\n\t\t\t\t\t\t: selected || \"\";\n\t\t\tif (msg_format === \"tuples\") {\n\t\t\t\tdispatch(\"like\", {\n\t\t\t\t\tindex: message.index,\n\t\t\t\t\tvalue: message.content,\n\t\t\t\t\tliked: feedback\n\t\t\t\t});\n\t\t\t} else {\n\t\t\t\tif (!groupedMessages) return;\n\n\t\t\t\tconst message_group = groupedMessages[i];\n\t\t\t\tconst [first, last] = [\n\t\t\t\t\tmessage_group[0],\n\t\t\t\t\tmessage_group[message_group.length - 1]\n\t\t\t\t];\n\n\t\t\t\tdispatch(\"like\", {\n\t\t\t\t\tindex: first.index as number,\n\t\t\t\t\tvalue: message_group.map((m) => m.content),\n\t\t\t\t\tliked: feedback\n\t\t\t\t});\n\t\t\t}\n\t\t}\n\t}\n\n\tfunction get_last_bot_options(): Option[] | undefined {\n\t\tif (!value || !groupedMessages || groupedMessages.length === 0)\n\t\t\treturn undefined;\n\t\tconst last_group = groupedMessages[groupedMessages.length - 1];\n\t\tif (last_group[0].role !== \"assistant\") return undefined;\n\t\treturn last_group[last_group.length - 1].options;\n\t}\n</script>\n\n{#if value !== null && value.length > 0}\n\t<IconButtonWrapper>\n\t\t{#if show_share_button}\n\t\t\t<IconButton\n\t\t\t\tIcon={Community}\n\t\t\t\ton:click={async () => {\n\t\t\t\t\ttry {\n\t\t\t\t\t\t// @ts-ignore\n\t\t\t\t\t\tconst formatted = await format_chat_for_sharing(value);\n\t\t\t\t\t\tdispatch(\"share\", {\n\t\t\t\t\t\t\tdescription: formatted\n\t\t\t\t\t\t});\n\t\t\t\t\t} catch (e) {\n\t\t\t\t\t\tconsole.error(e);\n\t\t\t\t\t\tlet message = e instanceof ShareError ? e.message : \"Share failed.\";\n\t\t\t\t\t\tdispatch(\"error\", message);\n\t\t\t\t\t}\n\t\t\t\t}}\n\t\t\t/>\n\t\t{/if}\n\t\t<IconButton\n\t\t\tIcon={Trash}\n\t\t\ton:click={() => dispatch(\"clear\")}\n\t\t\tlabel={i18n(\"chatbot.clear\")}\n\t\t></IconButton>\n\t\t{#if show_copy_all_button}\n\t\t\t<CopyAll {value} {watermark} />\n\t\t{/if}\n\t</IconButtonWrapper>\n{/if}\n\n<div\n\tclass={layout === \"bubble\" ? \"bubble-wrap\" : \"panel-wrap\"}\n\tbind:this={div}\n\trole=\"log\"\n\taria-label=\"chatbot conversation\"\n\taria-live=\"polite\"\n>\n\t{#if value !== null && value.length > 0 && groupedMessages !== null}\n\t\t<div class=\"message-wrap\" use:copy>\n\t\t\t{#each groupedMessages as messages, i}\n\t\t\t\t{@const role = messages[0].role === \"user\" ? \"user\" : \"bot\"}\n\t\t\t\t{@const avatar_img = avatar_images[role === \"user\" ? 0 : 1]}\n\t\t\t\t{@const opposite_avatar_img = avatar_images[role === \"user\" ? 0 : 1]}\n\t\t\t\t{@const feedback_index = groupedMessages\n\t\t\t\t\t.slice(0, i)\n\t\t\t\t\t.filter((m) => m[0].role === \"assistant\").length}\n\t\t\t\t{@const current_feedback =\n\t\t\t\t\trole === \"bot\" && feedback_value && feedback_value[feedback_index]\n\t\t\t\t\t\t? feedback_value[feedback_index]\n\t\t\t\t\t\t: null}\n\t\t\t\t<Message\n\t\t\t\t\t{messages}\n\t\t\t\t\t{display_consecutive_in_same_bubble}\n\t\t\t\t\t{opposite_avatar_img}\n\t\t\t\t\t{avatar_img}\n\t\t\t\t\t{role}\n\t\t\t\t\t{layout}\n\t\t\t\t\t{dispatch}\n\t\t\t\t\t{i18n}\n\t\t\t\t\t{_fetch}\n\t\t\t\t\t{line_breaks}\n\t\t\t\t\t{theme_mode}\n\t\t\t\t\t{target}\n\t\t\t\t\t{upload}\n\t\t\t\t\t{selectable}\n\t\t\t\t\t{sanitize_html}\n\t\t\t\t\t{render_markdown}\n\t\t\t\t\t{rtl}\n\t\t\t\t\t{i}\n\t\t\t\t\t{value}\n\t\t\t\t\t{latex_delimiters}\n\t\t\t\t\t{_components}\n\t\t\t\t\t{generating}\n\t\t\t\t\t{msg_format}\n\t\t\t\t\t{feedback_options}\n\t\t\t\t\t{current_feedback}\n\t\t\t\t\t{allow_tags}\n\t\t\t\t\t{watermark}\n\t\t\t\t\tshow_like={role === \"user\" ? likeable && like_user_message : likeable}\n\t\t\t\t\tshow_retry={_retryable && is_last_bot_message(messages, value)}\n\t\t\t\t\tshow_undo={_undoable && is_last_bot_message(messages, value)}\n\t\t\t\t\tshow_edit={editable === \"all\" ||\n\t\t\t\t\t\t(editable == \"user\" &&\n\t\t\t\t\t\t\trole === \"user\" &&\n\t\t\t\t\t\t\tmessages.length > 0 &&\n\t\t\t\t\t\t\tmessages[messages.length - 1].type == \"text\")}\n\t\t\t\t\tin_edit_mode={edit_index === i}\n\t\t\t\t\tbind:edit_messages\n\t\t\t\t\t{show_copy_button}\n\t\t\t\t\thandle_action={(selected) => {\n\t\t\t\t\t\tif (selected == \"edit\") {\n\t\t\t\t\t\t\tedit_messages.splice(0, edit_messages.length);\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (selected === \"edit\" || selected === \"edit_submit\") {\n\t\t\t\t\t\t\tmessages.forEach((msg, index) => {\n\t\t\t\t\t\t\t\thandle_action(selected === \"edit\" ? i : index, msg, selected);\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\thandle_action(i, messages[0], selected);\n\t\t\t\t\t\t}\n\t\t\t\t\t}}\n\t\t\t\t\tscroll={is_browser ? scroll : () => {}}\n\t\t\t\t\t{allow_file_downloads}\n\t\t\t\t\ton:copy={(e) => dispatch(\"copy\", e.detail)}\n\t\t\t\t/>\n\t\t\t\t{#if show_progress !== \"hidden\" && generating && messages[messages.length - 1].role === \"assistant\" && messages[messages.length - 1].metadata?.status === \"done\"}\n\t\t\t\t\t<Pending {layout} {avatar_images} />\n\t\t\t\t{/if}\n\t\t\t{/each}\n\t\t\t{#if show_progress !== \"hidden\" && pending_message}\n\t\t\t\t<Pending {layout} {avatar_images} />\n\t\t\t{:else if options}\n\t\t\t\t<div class=\"options\">\n\t\t\t\t\t{#each options as option, index}\n\t\t\t\t\t\t<button\n\t\t\t\t\t\t\tclass=\"option\"\n\t\t\t\t\t\t\ton:click={() =>\n\t\t\t\t\t\t\t\tdispatch(\"option_select\", {\n\t\t\t\t\t\t\t\t\tindex: index,\n\t\t\t\t\t\t\t\t\tvalue: option.value\n\t\t\t\t\t\t\t\t})}\n\t\t\t\t\t\t>\n\t\t\t\t\t\t\t{option.label || option.value}\n\t\t\t\t\t\t</button>\n\t\t\t\t\t{/each}\n\t\t\t\t</div>\n\t\t\t{/if}\n\t\t</div>\n\t{:else}\n\t\t<Examples\n\t\t\t{examples}\n\t\t\t{placeholder}\n\t\t\t{latex_delimiters}\n\t\t\ton:example_select={(e) => dispatch(\"example_select\", e.detail)}\n\t\t/>\n\t{/if}\n</div>\n\n{#if show_scroll_button}\n\t<div class=\"scroll-down-button-container\">\n\t\t<IconButton\n\t\t\tIcon={ScrollDownArrow}\n\t\t\tlabel=\"Scroll down\"\n\t\t\tsize=\"large\"\n\t\t\ton:click={scroll_to_bottom}\n\t\t/>\n\t</div>\n{/if}\n\n<style>\n\t.panel-wrap {\n\t\twidth: 100%;\n\t\toverflow-y: auto;\n\t}\n\n\t.bubble-wrap {\n\t\twidth: 100%;\n\t\toverflow-y: auto;\n\t\theight: 100%;\n\t\tpadding-top: var(--spacing-xxl);\n\t}\n\n\t@media (prefers-color-scheme: dark) {\n\t\t.bubble-wrap {\n\t\t\tbackground: var(--background-fill-secondary);\n\t\t}\n\t}\n\n\t.message-wrap :global(.prose.chatbot.md) {\n\t\topacity: 0.8;\n\t\toverflow-wrap: break-word;\n\t}\n\n\t.message-wrap :global(.message-row .md img) {\n\t\tborder-radius: var(--radius-xl);\n\t\tmargin: var(--size-2);\n\t\twidth: 400px;\n\t\tmax-width: 30vw;\n\t\tmax-height: 30vw;\n\t}\n\n\t/* link styles */\n\t.message-wrap :global(.message a) {\n\t\tcolor: var(--color-text-link);\n\t\ttext-decoration: underline;\n\t}\n\n\t/* table styles */\n\t.message-wrap :global(.bot:not(:has(.table-wrap)) table),\n\t.message-wrap :global(.bot:not(:has(.table-wrap)) tr),\n\t.message-wrap :global(.bot:not(:has(.table-wrap)) td),\n\t.message-wrap :global(.bot:not(:has(.table-wrap)) th) {\n\t\tborder: 1px solid var(--border-color-primary);\n\t}\n\n\t.message-wrap :global(.user table),\n\t.message-wrap :global(.user tr),\n\t.message-wrap :global(.user td),\n\t.message-wrap :global(.user th) {\n\t\tborder: 1px solid var(--border-color-accent);\n\t}\n\n\t/* KaTeX */\n\t.message-wrap :global(span.katex) {\n\t\tfont-size: var(--text-lg);\n\t\tdirection: ltr;\n\t}\n\n\t.message-wrap :global(span.katex-display) {\n\t\tmargin-top: 0;\n\t}\n\n\t.message-wrap :global(pre) {\n\t\tposition: relative;\n\t}\n\n\t.message-wrap :global(.grid-wrap) {\n\t\tmax-height: 80% !important;\n\t\tmax-width: 600px;\n\t\tobject-fit: contain;\n\t}\n\n\t.message-wrap > div :global(p:not(:first-child)) {\n\t\tmargin-top: var(--spacing-xxl);\n\t}\n\n\t.message-wrap {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\tjustify-content: space-between;\n\t\tmargin-bottom: var(--spacing-xxl);\n\t}\n\n\t.panel-wrap :global(.message-row:first-child) {\n\t\tpadding-top: calc(var(--spacing-xxl) * 2);\n\t}\n\n\t.scroll-down-button-container {\n\t\tposition: absolute;\n\t\tbottom: 10px;\n\t\tleft: 50%;\n\t\ttransform: translateX(-50%);\n\t\tz-index: var(--layer-top);\n\t}\n\t.scroll-down-button-container :global(button) {\n\t\tborder-radius: 50%;\n\t\tbox-shadow: var(--shadow-drop);\n\t\ttransition:\n\t\t\tbox-shadow 0.2s ease-in-out,\n\t\t\ttransform 0.2s ease-in-out;\n\t}\n\t.scroll-down-button-container :global(button:hover) {\n\t\tbox-shadow:\n\t\t\tvar(--shadow-drop),\n\t\t\t0 2px 2px rgba(0, 0, 0, 0.05);\n\t\ttransform: translateY(-2px);\n\t}\n\n\t.options {\n\t\tmargin-left: auto;\n\t\tpadding: var(--spacing-xxl);\n\t\tdisplay: grid;\n\t\tgrid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n\t\tgap: var(--spacing-xxl);\n\t\tmax-width: calc(min(4 * 200px + 5 * var(--spacing-xxl), 100%));\n\t\tjustify-content: end;\n\t}\n\n\t.option {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\talign-items: center;\n\t\tpadding: var(--spacing-xl);\n\t\tborder: 1px dashed var(--border-color-primary);\n\t\tborder-radius: var(--radius-md);\n\t\tbackground-color: var(--background-fill-secondary);\n\t\tcursor: pointer;\n\t\ttransition: var(--button-transition);\n\t\tmax-width: var(--size-56);\n\t\twidth: 100%;\n\t\tjustify-content: center;\n\t}\n\n\t.option:hover {\n\t\tbackground-color: var(--color-accent-soft);\n\t\tborder-color: var(--border-color-accent);\n\t}\n</style>\n", "<script context=\"module\" lang=\"ts\">\n\texport { default as BaseChatBot } from \"./shared/ChatBot.svelte\";\n</script>\n\n<script lang=\"ts\">\n\timport type { Gradio, SelectData, LikeData, CopyData } from \"@gradio/utils\";\n\n\timport ChatBot from \"./shared/ChatBot.svelte\";\n\timport type { UndoRetryData } from \"./shared/utils\";\n\timport { Block, BlockLabel } from \"@gradio/atoms\";\n\timport type { LoadingStatus } from \"@gradio/statustracker\";\n\timport { Chat } from \"@gradio/icons\";\n\timport type { FileData } from \"@gradio/client\";\n\timport { StatusTracker } from \"@gradio/statustracker\";\n\timport type {\n\t\tMessage,\n\t\tExampleMessage,\n\t\tTupleFormat,\n\t\tNormalisedMessage\n\t} from \"./types\";\n\n\timport { normalise_tuples, normalise_messages } from \"./shared/utils\";\n\n\texport let elem_id = \"\";\n\texport let elem_classes: string[] = [];\n\texport let visible = true;\n\texport let value: TupleFormat | Message[] = [];\n\texport let scale: number | null = null;\n\texport let min_width: number | undefined = undefined;\n\texport let label: string;\n\texport let show_label = true;\n\texport let root: string;\n\texport let _selectable = false;\n\texport let likeable = false;\n\texport let feedback_options: string[] = [\"Like\", \"Dislike\"];\n\texport let feedback_value: (string | null)[] | null = null;\n\texport let show_share_button = false;\n\texport let rtl = false;\n\texport let show_copy_button = true;\n\texport let show_copy_all_button = false;\n\texport let sanitize_html = true;\n\texport let layout: \"bubble\" | \"panel\" = \"bubble\";\n\texport let type: \"tuples\" | \"messages\" = \"tuples\";\n\texport let render_markdown = true;\n\texport let line_breaks = true;\n\texport let autoscroll = true;\n\texport let _retryable = false;\n\texport let _undoable = false;\n\texport let group_consecutive_messages = true;\n\texport let allow_tags: string[] | boolean = false;\n\texport let latex_delimiters: {\n\t\tleft: string;\n\t\tright: string;\n\t\tdisplay: boolean;\n\t}[];\n\texport let gradio: Gradio<{\n\t\tchange: typeof value;\n\t\tselect: SelectData;\n\t\tshare: ShareData;\n\t\terror: string;\n\t\tlike: LikeData;\n\t\tclear_status: LoadingStatus;\n\t\texample_select: SelectData;\n\t\toption_select: SelectData;\n\t\tedit: SelectData;\n\t\tretry: UndoRetryData;\n\t\tundo: UndoRetryData;\n\t\tclear: null;\n\t\tcopy: CopyData;\n\t}>;\n\n\tlet _value: NormalisedMessage[] | null = [];\n\n\t$: _value =\n\t\ttype === \"tuples\"\n\t\t\t? normalise_tuples(value as TupleFormat, root)\n\t\t\t: normalise_messages(value as Message[], root);\n\n\texport let avatar_images: [FileData | null, FileData | null] = [null, null];\n\texport let like_user_message = false;\n\texport let loading_status: LoadingStatus | undefined = undefined;\n\texport let height: number | string | undefined;\n\texport let resizable: boolean;\n\texport let min_height: number | string | undefined;\n\texport let max_height: number | string | undefined;\n\texport let editable: \"user\" | \"all\" | null = null;\n\texport let placeholder: string | null = null;\n\texport let examples: ExampleMessage[] | null = null;\n\texport let theme_mode: \"system\" | \"light\" | \"dark\";\n\texport let allow_file_downloads = true;\n\texport let watermark: string | null = null;\n</script>\n\n<Block\n\t{elem_id}\n\t{elem_classes}\n\t{visible}\n\tpadding={false}\n\t{scale}\n\t{min_width}\n\t{height}\n\t{resizable}\n\t{min_height}\n\t{max_height}\n\tallow_overflow={true}\n\tflex={true}\n\toverflow_behavior=\"auto\"\n>\n\t{#if loading_status}\n\t\t<StatusTracker\n\t\t\tautoscroll={gradio.autoscroll}\n\t\t\ti18n={gradio.i18n}\n\t\t\t{...loading_status}\n\t\t\tshow_progress={loading_status.show_progress === \"hidden\"\n\t\t\t\t? \"hidden\"\n\t\t\t\t: \"minimal\"}\n\t\t\ton:clear_status={() => gradio.dispatch(\"clear_status\", loading_status)}\n\t\t/>\n\t{/if}\n\t<div class=\"wrapper\">\n\t\t{#if show_label}\n\t\t\t<BlockLabel\n\t\t\t\t{show_label}\n\t\t\t\tIcon={Chat}\n\t\t\t\tfloat={true}\n\t\t\t\tlabel={label || \"Chatbot\"}\n\t\t\t/>\n\t\t{/if}\n\t\t<ChatBot\n\t\t\ti18n={gradio.i18n}\n\t\t\tselectable={_selectable}\n\t\t\t{likeable}\n\t\t\t{feedback_options}\n\t\t\t{feedback_value}\n\t\t\t{show_share_button}\n\t\t\t{show_copy_all_button}\n\t\t\tvalue={_value}\n\t\t\t{latex_delimiters}\n\t\t\tdisplay_consecutive_in_same_bubble={group_consecutive_messages}\n\t\t\t{render_markdown}\n\t\t\t{theme_mode}\n\t\t\t{editable}\n\t\t\tpending_message={loading_status?.status === \"pending\"}\n\t\t\tgenerating={loading_status?.status === \"generating\"}\n\t\t\t{rtl}\n\t\t\t{show_copy_button}\n\t\t\t{like_user_message}\n\t\t\tshow_progress={loading_status?.show_progress || \"full\"}\n\t\t\ton:change={() => gradio.dispatch(\"change\", value)}\n\t\t\ton:select={(e) => gradio.dispatch(\"select\", e.detail)}\n\t\t\ton:like={(e) => gradio.dispatch(\"like\", e.detail)}\n\t\t\ton:share={(e) => gradio.dispatch(\"share\", e.detail)}\n\t\t\ton:error={(e) => gradio.dispatch(\"error\", e.detail)}\n\t\t\ton:example_select={(e) => gradio.dispatch(\"example_select\", e.detail)}\n\t\t\ton:option_select={(e) => gradio.dispatch(\"option_select\", e.detail)}\n\t\t\ton:retry={(e) => gradio.dispatch(\"retry\", e.detail)}\n\t\t\ton:undo={(e) => gradio.dispatch(\"undo\", e.detail)}\n\t\t\ton:clear={() => {\n\t\t\t\tvalue = [];\n\t\t\t\tgradio.dispatch(\"clear\");\n\t\t\t}}\n\t\t\ton:copy={(e) => gradio.dispatch(\"copy\", e.detail)}\n\t\t\ton:edit={(e) => {\n\t\t\t\tif (value === null || value.length === 0) return;\n\t\t\t\tif (type === \"messages\") {\n\t\t\t\t\t//@ts-ignore\n\t\t\t\t\tvalue[e.detail.index].content = e.detail.value;\n\t\t\t\t} else {\n\t\t\t\t\t//@ts-ignore\n\t\t\t\t\tvalue[e.detail.index[0]][e.detail.index[1]] = e.detail.value;\n\t\t\t\t}\n\t\t\t\tvalue = value;\n\t\t\t\tgradio.dispatch(\"edit\", e.detail);\n\t\t\t}}\n\t\t\t{avatar_images}\n\t\t\t{sanitize_html}\n\t\t\t{line_breaks}\n\t\t\t{autoscroll}\n\t\t\t{layout}\n\t\t\t{placeholder}\n\t\t\t{examples}\n\t\t\t{_retryable}\n\t\t\t{_undoable}\n\t\t\tupload={(...args) => gradio.client.upload(...args)}\n\t\t\t_fetch={(...args) => gradio.client.fetch(...args)}\n\t\t\tload_component={gradio.load_component}\n\t\t\tmsg_format={type}\n\t\t\t{allow_file_downloads}\n\t\t\t{allow_tags}\n\t\t\t{watermark}\n\t\t/>\n\t</div>\n</Block>\n\n<style>\n\t.wrapper {\n\t\tdisplay: flex;\n\t\tposition: relative;\n\t\tflex-direction: column;\n\t\talign-items: start;\n\t\twidth: 100%;\n\t\theight: 100%;\n\t\tflex-grow: 1;\n\t}\n\n\t:global(.progress-text) {\n\t\tright: auto;\n\t}\n</style>\n"], "names": ["insert", "target", "svg", "anchor", "append", "path0", "path1", "circle", "path", "path2", "path3", "format_chat_for_sharing", "chat", "url_length_limit", "messages_to_share", "formatted", "format_messages", "first_message", "last_message", "msg", "max_length", "message", "speaker_emoji", "html_content", "regexPatterns", "_", "regex", "match", "fileUrl", "newUrl", "uploadToHuggingFace", "url", "file_url", "redirect_src_url", "src", "root", "get_component_for_mime_type", "mime_type", "file", "lower_path", "convert_file_message_to_component_message", "_file", "normalise_messages", "messages", "thought_map", "normalized", "id", "title", "parent_id", "parent", "thought", "normalise_tuples", "message_pair", "index", "role", "is_component_message", "is_last_bot_message", "all_messages", "is_bot", "last_index", "group_messages", "msg_format", "groupedMessages", "currentGroup", "currentRole", "load_components", "component_names", "_components", "load_component", "names", "components", "component_name", "variant", "name", "component", "result", "item", "originalIndex", "get_components_from_messages", "get_thought_content", "depth", "content", "indent", "child", "all_text", "m", "is_all_text", "ctx", "ThumbDownActive", "ThumbDownDefault", "dirty", "iconbutton_changes", "ThumbUpActive", "ThumbUpDefault", "FlagActive", "Flag", "i", "div1", "div0", "set_style", "button", "set_data", "t", "t_value", "create_if_block", "i18n", "$$props", "handle_action", "feedback_options", "selected", "toggleSelection", "newSelection", "click_handler", "click_handler_1", "option", "$$invalidate", "extra_feedback", "Check", "Copy", "dispatch", "createEventDispatcher", "copied", "value", "watermark", "timer", "copy_feedback", "handle_copy", "text_to_copy", "textArea", "error", "onDestroy", "attr", "div", "div_class_value", "current", "create_if_block_6", "create_if_block_5", "create_if_block_4", "create_if_block_3", "create_if_block_2", "Clear", "iconbutton0_changes", "iconbutton1_changes", "Retry", "Undo", "Edit", "if_block", "likeable", "show_retry", "show_undo", "show_edit", "in_edit_mode", "show_copy_button", "position", "avatar", "generating", "current_feedback", "layout", "copy_handler", "e", "click_handler_2", "click_handler_3", "click_handler_4", "message_text", "show_copy", "func_2", "switch_value", "func_1", "switch_instance_changes", "func", "track", "type", "theme_mode", "props", "upload", "_fetch", "allow_file_downloads", "display_icon_button_wrapper_top_corner", "a", "a_href_value", "a_download_value", "div2", "span0", "span1", "t1", "t1_value", "t3", "t3_value", "is_function", "markdown_changes", "latex_delimiters", "sanitize_html", "line_breaks", "render_markdown", "scroll", "display_consecutive_in_same_bubble", "thought_index", "allow_tags", "span", "if_block1", "show_if", "t0", "t0_value", "create_if_block_1", "toggle_class", "each_value", "ensure_array_like", "each_blocks", "thought_1_changes", "DropdownCircularArrow", "if_block0", "create_if_block_7", "is_thought_node", "rtl", "thought_node", "expanded", "user_expanded_toggled", "content_preview_element", "user_is_scrolling", "toggleExpanded", "scrollToBottom", "handleScroll", "keydown_handler", "$$value", "image_changes", "div_aria_label_value", "get_message_label_data", "textarea", "set_input_value", "div2_class_value", "previous_edit_mode", "avatar_img", "opposite_avatar_img", "selectable", "show_like", "edit_messages", "messageElements", "message_widths", "message_heights", "handle_select", "button_panel_props", "offset", "idx", "div6", "div5", "div4", "avatar_images", "src_url_equal", "video", "video_src_value", "each_value_1", "create_if_block_9", "show_if_1", "div0_aria_label_value", "create_if_block_14", "examples", "placeholder", "handle_example_select", "example", "example_obj", "copy_conversation", "conversation_value", "err", "tick", "constants_0", "child_ctx", "constants_1", "constants_2", "constants_3", "constants_4", "Community", "Trash", "div_1", "message_changes", "ScrollDownArrow", "div_1_class_value", "null_to_empty", "old_value", "is_browser", "update_components", "pending_message", "feedback_value", "editable", "show_share_button", "show_copy_all_button", "autoscroll", "_retryable", "_undoable", "like_user_message", "show_progress", "edit_index", "onMount", "show_scroll_button", "is_at_bottom", "scroll_to_bottom", "scroll_on_value_update", "resolve", "handle_scroll", "val_", "feedback", "message_group", "first", "last", "get_last_bot_options", "last_group", "ShareError", "example_select_handler", "dequal", "options", "Cha<PERSON>", "blocklabel_changes", "chatbot_changes", "elem_id", "elem_classes", "visible", "scale", "min_width", "label", "show_label", "_selectable", "group_consecutive_messages", "gradio", "_value", "loading_status", "height", "resizable", "min_height", "max_height", "clear_status_handler", "args", "change_handler"], "mappings": "mhEAAAA,GAgBKC,EAAAC,EAAAC,CAAA,EALJC,GAGCF,EAAAG,CAAA,EACDD,GAAyDF,EAAAI,CAAA,0lBCf1DN,GASKC,EAAAC,EAAAC,CAAA,EAFJC,GAA4CF,EAAAK,CAAA,EAC5CH,GAAyBF,EAAAM,CAAA,yzCCR1BR,GAqCKC,EAAAC,EAAAC,CAAA,EA5BJC,GAMOF,EAAAG,CAAA,EACPD,GAMOF,EAAAI,CAAA,EACPF,GAMOF,EAAAO,CAAA,EACPL,GAMOF,EAAAQ,CAAA,wmBCpCRV,GAcKC,EAAAC,EAAAC,CAAA,EAPJC,GAMCF,EAAAM,CAAA,uGCGK,MAAMG,GAA0B,MACtCC,EACAC,EAAmB,OACE,CACjB,IAAAC,EAAoB,CAAC,GAAGF,CAAI,EAC5BG,EAAY,MAAMC,GAAgBF,CAAiB,EAEvD,GAAIC,EAAU,OAASF,GAAoBC,EAAkB,OAAS,EAAG,CAClE,MAAAG,EAAgBH,EAAkB,CAAC,EACnCI,EAAeJ,EAAkBA,EAAkB,OAAS,CAAC,EAC/CA,EAAA,CAACG,EAAeC,CAAY,EACpCH,EAAA,MAAMC,GAAgBF,CAAiB,CACpD,CAEA,OAAIC,EAAU,OAASF,GAAoBC,EAAkB,OAAS,IAejDA,EAdOA,EAAkB,IAAKK,GAAQ,CACrD,GAAAA,EAAI,OAAS,OAAQ,CACxB,MAAMC,EACL,KAAK,MAAMP,EAAmBC,EAAkB,MAAM,EAAI,GACvD,GAAAK,EAAI,QAAQ,OAASC,EACjB,MAAA,CACN,GAAGD,EACH,QAASA,EAAI,QAAQ,UAAU,EAAGC,CAAU,EAAI,KAAA,CAGnD,CACO,OAAAD,CAAA,CACP,EAGWJ,EAAA,MAAMC,GAAgBF,CAAiB,GAG7CC,CACR,EAEMC,GAAkB,MAAOJ,IACf,MAAM,QAAQ,IAC5BA,EAAK,IAAI,MAAOS,GAAY,CAC3B,GAAIA,EAAQ,OAAS,SAAiB,MAAA,GACtC,IAAIC,EAAgBD,EAAQ,OAAS,OAAS,KAAO,KACjDE,EAAe,GAEf,GAAAF,EAAQ,OAAS,OAAQ,CAC5B,MAAMG,EAAgB,CACrB,MAAO,+BACP,MAAO,+BACP,MAAO,2DAAA,EAGRD,EAAeF,EAAQ,QAEvB,OAAS,CAACI,EAAGC,CAAK,IAAK,OAAO,QAAQF,CAAa,EAAG,CACjD,IAAAG,EAEJ,MAAQA,EAAQD,EAAM,KAAKL,EAAQ,OAAO,KAAO,MAAM,CACtD,MAAMO,EAAUD,EAAM,CAAC,GAAKA,EAAM,CAAC,EAC7BE,EAAS,MAAMC,GAAoBF,CAAc,EACxCL,EAAAA,EAAa,QAAQK,EAASC,CAAM,CACpD,CACD,CAAA,KACM,CACF,GAAA,CAACR,EAAQ,QAAQ,MAAc,MAAA,GAC7B,MAAAU,EACLV,EAAQ,QAAQ,YAAc,QAC3BA,EAAQ,QAAQ,OAAO,MAAM,KAC7BA,EAAQ,QAAQ,MACdW,EAAW,MAAMF,GAAoBC,CAAU,EACjDV,EAAQ,QAAQ,YAAc,QACjCE,EAAe,wBAAwBS,CAAQ,aACrCX,EAAQ,QAAQ,YAAc,QACzBE,EAAAS,EACLX,EAAQ,QAAQ,YAAc,UACxCE,EAAe,aAAaS,CAAQ,OAEtC,CAEO,MAAA,GAAGV,CAAa,KAAKC,CAAY,EAAA,CACxC,CAAA,GAEc,OAAQJ,GAAQA,IAAQ,EAAE,EAAE,KAAK;AAAA,CAAI,EAchDc,GAAmB,CAACC,EAAaC,IACtCD,EAAI,QAAQ,aAAc,QAAQC,CAAI,MAAM,EAE7C,SAASC,GACRC,EACAC,EACS,CACT,GAAI,CAACD,EAAW,CACf,MAAM7B,EAAO8B,GAAM,KACnB,GAAI9B,EAAM,CACH,MAAA+B,EAAa/B,EAAK,cAEvB,GAAA+B,EAAW,SAAS,MAAM,GAC1BA,EAAW,SAAS,OAAO,GAC3BA,EAAW,SAAS,MAAM,GAC1BA,EAAW,SAAS,MAAM,GAC1BA,EAAW,SAAS,QAAQ,GAC5BA,EAAW,SAAS,MAAM,EAEnB,MAAA,SAET,CACO,MAAA,MACR,CACI,OAAAF,EAAU,SAAS,OAAO,EAAU,QACpCA,EAAU,SAAS,OAAO,EAAU,QACpCA,EAAU,SAAS,OAAO,EAAU,QACpCA,EAAU,SAAS,OAAO,EAAU,UACjC,MACR,CAEA,SAASG,GACRnB,EACgB,CACV,MAAAoB,EAAQ,MAAM,QAAQpB,EAAQ,IAAI,EAAIA,EAAQ,KAAK,CAAC,EAAIA,EAAQ,KAC/D,MAAA,CACN,UAAWe,GAA4BK,GAAO,UAAWA,CAAK,EAC9D,MAAOpB,EAAQ,KACf,SAAUA,EAAQ,SAClB,iBAAkB,CAAC,EACnB,MAAO,CAAC,CAAA,CAEV,CAEgB,SAAAqB,GACfC,EACAR,EAC6B,CAC7B,GAAIQ,IAAa,KAAa,OAAAA,EAExB,MAAAC,MAAkB,IAExB,OAAOD,EACL,IAAI,CAACtB,EAAS,IAAM,CACpB,IAAIwB,EACH,OAAOxB,EAAQ,SAAY,SACxB,CACA,KAAMA,EAAQ,KACd,SAAUA,EAAQ,SAClB,QAASY,GAAiBZ,EAAQ,QAASc,CAAI,EAC/C,KAAM,OACN,MAAO,EACP,QAASd,EAAQ,OAAA,EAEjB,SAAUA,EAAQ,QACjB,CACA,QAASmB,GACRnB,EAAQ,OACT,EACA,SAAUA,EAAQ,SAClB,KAAMA,EAAQ,KACd,KAAM,YACN,MAAO,EACP,QAASA,EAAQ,OAEhB,EAAA,CAAE,KAAM,YAAa,GAAGA,CAAQ,EAGtC,KAAM,CAAE,GAAAyB,EAAI,MAAAC,EAAO,UAAAC,CAAc,EAAA3B,EAAQ,UAAY,GACrD,GAAI2B,EAAW,CACd,MAAMC,EAASL,EAAY,IAAI,OAAOI,CAAS,CAAC,EAChD,GAAIC,EAAQ,CACX,MAAMC,EAAU,CAAE,GAAGL,EAAY,SAAU,CAAG,CAAA,EACvC,OAAAI,EAAA,SAAS,KAAKC,CAAO,EACxBJ,GAAMC,GACTH,EAAY,IAAI,OAAOE,CAAE,EAAGI,CAAO,EAE7B,IACR,CACD,CACA,GAAIJ,GAAMC,EAAO,CAChB,MAAMG,EAAU,CAAE,GAAGL,EAAY,SAAU,CAAG,CAAA,EAC9C,OAAAD,EAAY,IAAI,OAAOE,CAAE,EAAGI,CAAO,EAC5BA,CACR,CAEO,OAAAL,CACP,CAAA,EACA,OAAQ1B,GAAkCA,IAAQ,IAAI,CACzD,CAEgB,SAAAgC,GACfR,EACAR,EAC6B,CAC7B,OAAIQ,IAAa,KAAaA,EAClBA,EAAS,QAAQ,CAACS,EAAc,IACpCA,EAAa,IAAI,CAAC/B,EAASgC,IAAU,CAC3C,GAAIhC,GAAW,KAAa,OAAA,KACtB,MAAAiC,EAAOD,GAAS,EAAI,OAAS,YAE/B,OAAA,OAAOhC,GAAY,SACf,CACN,KAAAiC,EACA,KAAM,OACN,QAASrB,GAAiBZ,EAASc,CAAI,EACvC,SAAU,CAAE,MAAO,IAAK,EACxB,MAAO,CAAC,EAAGkB,CAAK,CAAA,EAId,SAAUhC,EACN,CACN,QAASmB,GAA0CnB,CAAO,EAC1D,KAAAiC,EACA,KAAM,YACN,MAAO,CAAC,EAAGD,CAAK,CAAA,EAIX,CACN,KAAAC,EACA,QAASjC,EACT,KAAM,YACN,MAAO,CAAC,EAAGgC,CAAK,CAAA,CACjB,CACA,CACD,EACU,OAAQhC,GAAYA,GAAW,IAAI,CAC/C,CAEO,SAASkC,GACflC,EAC8B,CAC9B,OAAOA,EAAQ,OAAS,WACzB,CAEgB,SAAAmC,GACfb,EACAc,EACU,CACV,MAAMC,EAASf,EAASA,EAAS,OAAS,CAAC,EAAE,OAAS,YAChDgB,EAAahB,EAASA,EAAS,OAAS,CAAC,EAAE,MAMjD,OAFC,KAAK,UAAUgB,CAAU,IACzB,KAAK,UAAUF,EAAaA,EAAa,OAAS,CAAC,EAAE,KAAK,GACzCC,CACnB,CAEgB,SAAAE,GACfjB,EACAkB,EACwB,CACxB,MAAMC,EAAyC,CAAA,EAC/C,IAAIC,EAAoC,CAAA,EACpCC,EAAkC,KAEtC,UAAW3C,KAAWsB,GACftB,EAAQ,OAAS,aAAeA,EAAQ,OAAS,UAGnDA,EAAQ,OAAS2C,EACpBD,EAAa,KAAK1C,CAAO,GAErB0C,EAAa,OAAS,GACzBD,EAAgB,KAAKC,CAAY,EAElCA,EAAe,CAAC1C,CAAO,EACvB2C,EAAc3C,EAAQ,OAIpB,OAAA0C,EAAa,OAAS,GACzBD,EAAgB,KAAKC,CAAY,EAG3BD,CACR,CAEsB,eAAAG,GACrBC,EACAC,EACAC,EAC0D,CAC1D,IAAIC,EAAkB,CAAA,EAClBC,EAA+D,CAAA,EAEnD,OAAAJ,EAAA,QAASK,GAAmB,CAC3C,GAAIJ,EAAYI,CAAc,GAAKA,IAAmB,OACrD,OAED,MAAMC,EACLD,IAAmB,aAAeA,IAAmB,UAClD,YACA,OACE,CAAE,KAAAE,EAAM,UAAAC,CAAA,EAAcN,EAAeG,EAAgBC,CAAO,EAClEH,EAAM,KAAKI,CAAI,EACfH,EAAW,KAAKI,CAAS,CACzB,CACA,GAE2B,MAAM,QAAQ,WAAWJ,CAAU,GAE7D,IAAI,CAACK,EAAQtB,IACbsB,EAAO,SAAW,YAAc,CAACtB,EAAOsB,EAAO,KAAK,EAAI,IAExD,EAAA,OAAQC,GAA4CA,IAAS,IAAI,EAE9C,QAAQ,CAAC,CAACC,EAAeH,CAAS,IAAM,CAC5DP,EAAYE,EAAMQ,CAAa,CAAC,EAAIH,EAAU,OAAA,CAC9C,EAEMP,CACR,CAEO,SAASW,GACfnC,EACW,CACX,GAAI,CAACA,EAAU,MAAO,GAClB,IAAA2B,MAA8B,IACzB,OAAA3B,EAAA,QAAStB,GAAY,CACzBA,EAAQ,OAAS,aACTiD,EAAA,IAAIjD,EAAQ,QAAQ,SAAS,CACzC,CACA,EACM,MAAM,KAAKiD,CAAU,CAC7B,CAEgB,SAAAS,GAAoB5D,EAAwB6D,EAAQ,EAAW,CAC9E,IAAIC,EAAU,GACR,MAAAC,EAAS,KAAK,OAAOF,CAAK,EAE5B7D,EAAI,UAAU,QACN8D,GAAA,GAAGC,CAAM,GAAGF,EAAQ,EAAI,KAAO,EAAE,GAAG7D,EAAI,SAAS,KAAK;AAAA,GAE9D,OAAOA,EAAI,SAAY,WAC1B8D,GAAW,GAAGC,CAAM,KAAK/D,EAAI,OAAO;AAAA,GAErC,MAAM+B,EAAU/B,EACZ,OAAA+B,EAAQ,UAAU,OAAS,IAC9B+B,GAAW/B,EAAQ,SACjB,IAAKiC,GAAUJ,GAAoBI,EAAOH,EAAQ,CAAC,CAAC,EACpD,KAAK,EAAE,GAEHC,CACR,CAEO,SAASG,GAAS/D,EAA8C,CAClE,OAAA,MAAM,QAAQA,CAAO,EACjBA,EACL,IAAKgE,GACDA,EAAE,UAAU,MACRN,GAAoBM,CAAC,EAEtBA,EAAE,OACT,EACA,KAAK;AAAA,CAAI,EAERhE,EAAQ,UAAU,MACd0D,GAAoB1D,CAAO,EAE5BA,EAAQ,OAChB,CAEO,SAASiE,GACfjE,EACyC,CAEvC,OAAA,MAAM,QAAQA,CAAO,GACrBA,EAAQ,MAAOgE,GAAM,OAAOA,EAAE,SAAY,QAAQ,GAClD,CAAC,MAAM,QAAQhE,CAAO,GAAK,OAAOA,EAAQ,SAAY,QAEzD,k4BC1YArB,GAWKC,EAAAC,EAAAC,CAAA,EAJJC,GAGCF,EAAAM,CAAA,w0CCVFR,GAWKC,EAAAC,EAAAC,CAAA,EAJJC,GAGCF,EAAAM,CAAA,k+BCVFR,GAWKC,EAAAC,EAAAC,CAAA,EAJJC,GAGCF,EAAAM,CAAA,o0CCVFR,GAWKC,EAAAC,EAAAC,CAAA,EAJJC,GAGCF,EAAAM,CAAA,4gBCVFR,GASAC,EAAAC,EAAAC,CAAA,EAJEC,GAGCF,EAAAM,CAAA,ifCRHR,GAMAC,EAAAC,EAAAC,CAAA,EADEC,GAA+DF,EAAAM,CAAA,qiBCoB3D+E,EAAgB,CAAA,EAAC,SAAS,SAAS,MAYnCA,EAAgB,CAAA,EAAC,SAAS,MAAM,gJAZhCA,EAAgB,CAAA,EAAC,SAAS,SAAS,yHAYnCA,EAAgB,CAAA,EAAC,SAAS,MAAM,gQAV7B,KAAAA,OAAa,UAAYC,GAAkBC,GAC1C,MAAAF,OAAa,UACjB,kBACAA,KAAK,iBAAiB,EAClB,MAAAA,OAAa,UACjB,sBACA,wHANGG,EAAA,IAAAC,EAAA,KAAAJ,OAAa,UAAYC,GAAkBC,IAC1CC,EAAA,IAAAC,EAAA,MAAAJ,OAAa,UACjB,kBACAA,KAAK,iBAAiB,GAClBG,EAAA,IAAAC,EAAA,MAAAJ,OAAa,UACjB,sBACA,6KAMG,KAAAA,OAAa,OAASK,GAAgBC,GACrC,MAAAN,OAAa,OAAS,eAAiBA,KAAK,cAAc,EAC1D,MAAAA,OAAa,OACjB,sBACA,wHAJGG,EAAA,IAAAC,EAAA,KAAAJ,OAAa,OAASK,GAAgBC,IACrCH,EAAA,IAAAC,EAAA,MAAAJ,OAAa,OAAS,eAAiBA,KAAK,cAAc,GAC1DG,EAAA,IAAAC,EAAA,MAAAJ,OAAa,OACjB,sBACA,4KASG,KAAAA,EAAY,CAAA,GAAAA,EAAe,CAAA,EAAA,SAASA,EAAQ,CAAA,CAAA,EAAIO,GAAaC,oBAE5D,MAAAR,EAAY,CAAA,GAAAA,EAAe,CAAA,EAAA,SAASA,EAAQ,CAAA,CAAA,EAChD,sBACA,4CAGIA,EAAc,CAAA,CAAA,uBAAnB,OAAIS,GAAA,4OATRhG,GAoBKC,EAAAgG,EAAA9F,CAAA,uBAZJC,GAWK6F,EAAAC,CAAA,8EAjBER,EAAA,KAAAC,EAAA,KAAAJ,EAAY,CAAA,GAAAA,EAAe,CAAA,EAAA,SAASA,EAAQ,CAAA,CAAA,EAAIO,GAAaC,IAE5DL,EAAA,KAAAC,EAAA,MAAAJ,EAAY,CAAA,GAAAA,EAAe,CAAA,EAAA,SAASA,EAAQ,CAAA,CAAA,EAChD,sBACA,sDAGIA,EAAc,CAAA,CAAA,oBAAnB,OAAIS,GAAA,EAAA,mHAAJ,iIAOIT,EAAM,CAAA,EAAA,+HAJSY,GAAAC,EAAA,cAAAb,OAAaA,EAAM,CAAA,EAAG,OAAS,QAAQ,UAF3DvF,GAOAC,EAAAmG,EAAAjG,CAAA,+DADKoF,EAAM,CAAA,EAAA,KAAAc,GAAAC,EAAAC,CAAA,QAJSJ,GAAAC,EAAA,cAAAb,OAAaA,EAAM,CAAA,EAAG,OAAS,QAAQ,iDAtC1DA,EAAgB,CAAA,EAAC,SAAS,MAAM,GAAKA,EAAgB,CAAA,EAAC,SAAS,SAAS,qBAyBxEA,EAAc,CAAA,EAAC,OAAS,GAACiB,GAAAjB,CAAA,wHAzBzBA,EAAgB,CAAA,EAAC,SAAS,MAAM,GAAKA,EAAgB,CAAA,EAAC,SAAS,SAAS,iHAyBxEA,EAAc,CAAA,EAAC,OAAS,wOAvCjB,CAAA,KAAAkB,CAAA,EAAAC,EACA,CAAA,cAAAC,CAAA,EAAAD,EACA,CAAA,iBAAAE,CAAA,EAAAF,GACA,SAAAG,EAA0B,IAAA,EAAAH,WAK5BI,EAAgBC,EAAA,KACxBF,EAAWA,IAAaE,EAAe,KAAOA,CAAA,EAC9CJ,EAAcE,CAAQ,EAcL,MAAAG,EAAA,IAAAF,EAAgB,SAAS,EAUzBG,EAAA,IAAAH,EAAgB,MAAM,QAoBnCA,EAAgBI,CAAM,EACtBP,EAAcE,GAAsB,IAAI,gNAnD7CM,EAAA,EAAGC,EAAiBR,EAAiB,OACnCM,GAAWA,IAAW,QAAUA,IAAW,SAAA,CAAA,4sBCdP,EAAA,OAAA,wCACZ,EAAA,OAAA,gFAyDnB3B,EAAM,CAAA,EAAG,iBAAmB,oBAC7BA,EAAM,CAAA,EAAG8B,GAAQC,oBAFb/B,EAAW,CAAA,CAAA,mFACdA,EAAM,CAAA,EAAG,iBAAmB,6BAC7BA,EAAM,CAAA,EAAG8B,GAAQC,0HAtDjBC,EAAWC,SAKbC,EAAS,GACF,CAAA,MAAAC,CAAA,EAAAhB,GACA,UAAAiB,EAA2B,IAAA,EAAAjB,EAClCkB,EAEK,SAAAC,GAAA,KACRJ,EAAS,EAAA,EACLG,GAAO,aAAaA,CAAK,EAC7BA,EAAQ,oBACPH,EAAS,EAAA,GACP,KAGW,eAAAK,GAAA,IACV,cAAe,UAAA,CAClBP,EAAS,OAAU,CAAA,MAAAG,CAAA,CAAA,QACbK,EAAeJ,KAAeD,CAAK;AAAA;AAAA,EAAOC,CAAS,GAAKD,QACxD,UAAU,UAAU,UAAUK,CAAY,EAChDF,eAEMG,EAAW,SAAS,cAAc,UAAU,EAC5CD,EAAeJ,KAAeD,CAAK;AAAA;AAAA,EAAOC,CAAS,GAAKD,EAC9DM,EAAS,MAAQD,EAEjBC,EAAS,MAAM,SAAW,WAC1BA,EAAS,MAAM,KAAO,YAEtB,SAAS,KAAK,QAAQA,CAAQ,EAC9BA,EAAS,OAAA,MAGR,SAAS,YAAY,MAAM,EAC3BH,GACQ,OAAAI,EAAA,CACR,QAAQ,MAAMA,CAAK,UAEnBD,EAAS,OAAA,IAKZE,OAAAA,GAAA,IAAA,CACKN,GAAO,aAAaA,CAAK,kqBCfC,gFAHNO,GAAAC,EAAA,QAAAC,EAAA,mBAAA9C,SAAWA,EAAM,EAAA,EAAA,qBAAmBA,EAC3D,CAAA,IAAA,MAAQ,eAAa,gBAAA,UAFvBvF,GA4DKC,EAAAmI,EAAAjI,CAAA,0FA3DoB,CAAAmI,GAAA5C,EAAA,MAAA2C,KAAAA,EAAA,mBAAA9C,SAAWA,EAAM,EAAA,EAAA,qBAAmBA,EAC3D,CAAA,IAAA,MAAQ,eAAa,6JAiBfA,EAAS,EAAA,GAAAgD,GAAAhD,CAAA,IAOTA,EAAU,CAAA,GAAAiD,GAAAjD,CAAA,IAQVA,EAAS,CAAA,GAAAkD,GAAAlD,CAAA,IAQTA,EAAS,CAAA,GAAAmD,GAAAnD,CAAA,IAQTA,EAAQ,CAAA,GAAAoD,GAAApD,CAAA,gOA/BRA,EAAS,EAAA,+GAOTA,EAAU,CAAA,2GAQVA,EAAS,CAAA,4GAQTA,EAAS,CAAA,4GAQTA,EAAQ,CAAA,wUA3CL,MAAAA,KAAK,gBAAgB,OACtB8B,YAEI9B,EAAU,EAAA,2CAGb,MAAAA,KAAK,gBAAgB,OACtBqD,YAEIrD,EAAU,EAAA,wIATbG,EAAA,IAAAmD,EAAA,MAAAtD,KAAK,gBAAgB,uBAGlBA,EAAU,EAAA,wBAGbG,EAAA,IAAAoD,EAAA,MAAAvD,KAAK,gBAAgB,uBAGlBA,EAAU,EAAA,0MAKZA,EAAY,EAAA,2HAAZA,EAAY,EAAA,4KAObwD,GACC,MAAAxD,KAAK,eAAe,WAEjBA,EAAU,EAAA,2FAFbG,EAAA,IAAAC,EAAA,MAAAJ,KAAK,eAAe,uBAEjBA,EAAU,EAAA,6IAKb,MAAAA,KAAK,cAAc,OACpByD,YAEIzD,EAAU,EAAA,2FAHbG,EAAA,IAAAC,EAAA,MAAAJ,KAAK,cAAc,uBAGhBA,EAAU,EAAA,6IAKb,MAAAA,KAAK,cAAc,OACpB0D,YAEI1D,EAAU,EAAA,2FAHbG,EAAA,IAAAC,EAAA,MAAAJ,KAAK,cAAc,uBAGhBA,EAAU,EAAA,gMAOVA,EAAgB,EAAA,mKAAhBA,EAAgB,EAAA,wLAjDxBA,EAAY,CAAA,EAAA,kUANf2D,GAAA3D,OAAaA,EAAU,CAAA,GAAIA,EAAa,CAAA,GAAAA,MAAaA,EAAQ,CAAA,IAAAiB,GAAAjB,CAAA,0EAA7DA,OAAaA,EAAU,CAAA,GAAIA,EAAa,CAAA,GAAAA,MAAaA,EAAQ,CAAA,0MAvBtD,CAAA,KAAAkB,CAAA,EAAAC,EACA,CAAA,SAAAyC,CAAA,EAAAzC,EACA,CAAA,iBAAAE,CAAA,EAAAF,EACA,CAAA,WAAA0C,CAAA,EAAA1C,EACA,CAAA,UAAA2C,CAAA,EAAA3C,EACA,CAAA,UAAA4C,CAAA,EAAA5C,EACA,CAAA,aAAA6C,CAAA,EAAA7C,EACA,CAAA,iBAAA8C,CAAA,EAAA9C,GACA,UAAAiB,EAA2B,IAAA,EAAAjB,EAC3B,CAAA,QAAArF,CAAA,EAAAqF,EACA,CAAA,SAAA+C,CAAA,EAAA/C,EACA,CAAA,OAAAgD,CAAA,EAAAhD,EACA,CAAA,WAAAiD,CAAA,EAAAjD,EACA,CAAA,iBAAAkD,CAAA,EAAAlD,EAEA,CAAA,cAAAC,CAAA,EAAAD,EACA,CAAA,OAAAmD,CAAA,EAAAnD,EACA,CAAA,SAAAa,CAAA,EAAAb,EAgBS,MAAAM,EAAA,IAAAL,EAAc,aAAa,EAM3BM,EAAA,IAAAN,EAAc,aAAa,EAOhCmD,GAAAC,GAAMxC,EAAS,OAAQwC,EAAE,MAAM,EAQzBC,GAAA,IAAArD,EAAc,OAAO,EAQrBsD,GAAA,IAAAtD,EAAc,MAAM,EAQpBuD,GAAA,IAAAvD,EAAc,MAAM,isBAnDzCQ,EAAA,GAAGgD,EAAe7E,GAAYjE,CAAO,EAAI+D,GAAS/D,CAAO,EAAI,EAAA,qBAC7D8F,EAAA,GAAGiD,EAAYZ,GAAoBnI,GAAWiE,GAAYjE,CAAO,CAAA,iuEC4FnD,YAAAkE,KAAM,cACL,aAAAA,KAAM,cACR,CAAA,WAAAA,KAAM,UAAU,EACjB,CAAA,UAAAA,KAAM,SAAS,EACtBA,EAAK,CAAA,EAAC,kBAAoB,QAAS,CACtC,gBAAiBA,EAAM,CAAA,EAAA,qCAEJ,EAAI,cACZ,EAAK,yBAEJ,EAAK,+CAEC,EAAI,WACb,SAAQ8E,GAAY,KAAA9E,EAAI,CAAA,CAAA,IAf5B,IAAA+E,EAAA/E,KAAWA,EAAI,CAAA,CAAA,qIAER,YAAAA,KAAM,oBACL,aAAAA,KAAM,cACRG,EAAA,IAAA,CAAA,WAAAH,KAAM,UAAU,EACjBG,EAAA,IAAA,CAAA,UAAAH,KAAM,SAAS,WACtBA,EAAK,CAAA,EAAC,kBAAoB,QAAS,CACtC,gBAAiBA,EAAM,CAAA,EAAA,iEAQd,SAAQ8E,GAAY,KAAA9E,EAAI,CAAA,CAAA,2IAf5B,GAAAG,EAAA,GAAA4E,KAAAA,EAAA/E,KAAWA,EAAI,CAAA,CAAA,GAAA,8NAER,YAAAA,KAAM,oBACL,aAAAA,KAAM,cACRG,EAAA,IAAA,CAAA,WAAAH,KAAM,UAAU,EACjBG,EAAA,IAAA,CAAA,UAAAH,KAAM,SAAS,WACtBA,EAAK,CAAA,EAAC,kBAAoB,QAAS,CACtC,gBAAiBA,EAAM,CAAA,EAAA,iEAQd,SAAQ8E,GAAY,KAAA9E,EAAI,CAAA,CAAA,+IA1B5B,IAAA+E,EAAA/E,KAAWA,EAAI,CAAA,CAAA,sDAET,0CAEO,qBAET,SAAQgF,EAAA,8HANZ,GAAA7E,EAAA,GAAA4E,KAAAA,EAAA/E,KAAWA,EAAI,CAAA,CAAA,GAAA,yWAXf,IAAA+E,EAAA/E,KAAWA,EAAI,CAAA,CAAA,sDAET,8CAEUA,EAAoB,CAAA,qLAJpC,GAAAG,EAAA,GAAA4E,KAAAA,EAAA/E,KAAWA,EAAI,CAAA,CAAA,GAAA,6OAICA,EAAoB,CAAA,uNAnBpC,IAAA+E,EAAA/E,KAAWA,EAAI,CAAA,CAAA,gCACX,SAAAA,KAAM,eACTA,EAAK,CAAA,EAAC,OAASA,EAAK,CAAA,aACf,qBACO,2FAIGA,EAAoB,CAAA,qKARpC,GAAAG,EAAA,GAAA4E,KAAAA,EAAA/E,KAAWA,EAAI,CAAA,CAAA,GAAA,0LACXG,EAAA,KAAA8E,EAAA,SAAAjF,KAAM,wBACTA,EAAK,CAAA,EAAC,OAASA,EAAK,CAAA,sIAMLA,EAAoB,CAAA,gLAtBnC,IAAA+E,EAAA/E,KAAWA,EAAI,CAAA,CAAA,sDAET,qBACO,yCAGE,SAAUA,EAAK,CAAA,EAAC,QAAQ,uBACvBA,EAAoB,CAAA,wKAT5CvF,GAaKC,EAAAmI,EAAAjI,CAAA,+BAXG,GAAAuF,EAAA,GAAA4E,KAAAA,EAAA/E,KAAWA,EAAI,CAAA,CAAA,GAAA,sPAMA,SAAUA,EAAK,CAAA,EAAC,0CACfA,EAAoB,CAAA,iMAnBrC,IAAA+E,EAAA/E,KAAWA,EAAI,CAAA,CAAA,uEAIN,cAAAA,KAAM,6CAEA,+HANf,GAAAG,EAAA,GAAA4E,KAAAA,EAAA/E,KAAWA,EAAI,CAAA,CAAA,GAAA,6PAING,EAAA,KAAA8E,EAAA,cAAAjF,KAAM,sJAtBf,IAAA+E,EAAA/E,KAAWA,EAAI,CAAA,CAAA,sDAET,kCAGC,GACA,YAAAA,KAAM,iBACb,mBAEI,SAAQkF,GAAY,KAAAlF,EAAI,CAAA,CAAA,EACxB,SAAAA,KAAM,SACE,iBAAAA,KAAM,iBACb,UAAAA,KAAM,UACN,UAAAA,KAAM,sIAbX,GAAAG,EAAA,GAAA4E,KAAAA,EAAA/E,KAAWA,EAAI,CAAA,CAAA,GAAA,kOAMRG,EAAA,KAAA8E,EAAA,YAAAjF,KAAM,8BAGT,SAAQkF,GAAY,KAAAlF,EAAI,CAAA,CAAA,GACxBG,EAAA,KAAA8E,EAAA,SAAAjF,KAAM,UACEG,EAAA,KAAA8E,EAAA,iBAAAjF,KAAM,kBACbG,EAAA,KAAA8E,EAAA,UAAAjF,KAAM,WACNG,EAAA,KAAA8E,EAAA,UAAAjF,KAAM,kJA5BX,IAAA+E,EAAA/E,KAAWA,EAAI,CAAA,CAAA,mGAGT,gDAIG,eACF,+BAEC,8HAVR,GAAAG,EAAA,GAAA4E,KAAAA,EAAA/E,KAAWA,EAAI,CAAA,CAAA,GAAA,8eAsErBvF,GAAwBC,EAAAyK,EAAAvK,CAAA,0GAxErB,OAAAoF,OAAS,UAAS,EAebA,OAAS,YAAW,EAkBpBA,OAAS,OAAM,EAWfA,OAAS,QAAO,EAehBA,OAAS,QAAO,EAehBA,OAAS,QAAO,EAWhBA,OAAS,OAAM,EAWfA,OAAS,UAAS,0YArHhB,GAAA,CAAA,KAAAoF,CAAA,EAAAjE,EASA,CAAA,WAAApC,CAAA,EAAAoC,EACA,CAAA,MAAAgB,CAAA,EAAAhB,EACA,CAAA,OAAAzG,CAAA,EAAAyG,EACA,CAAA,WAAAkE,CAAA,EAAAlE,EACA,CAAA,MAAAmE,CAAA,EAAAnE,EACA,CAAA,KAAAD,CAAA,EAAAC,EACA,CAAA,OAAAoE,CAAA,EAAApE,EACA,CAAA,OAAAqE,CAAA,EAAArE,EACA,CAAA,qBAAAsE,CAAA,EAAAtE,GACA,uCAAAuE,EAAyC,EAAA,EAAAvE,uwEC4D9CnB,EAAO,EAAA,EAAC,QAAQ,OAAO,WACxBA,EAAQ,EAAA,EAAA,QAAQ,OAAO,KAAK,MAAM,GAAG,EAAE,IAAG,GAC1C,QAAM,gBAKPA,EAAO,EAAA,EAAC,QAAQ,OAAO,WACvBA,EAAO,EAAA,EAAC,QAAQ,OAAO,MACvB,IAEC,MAAM,GAAG,EACT,IAAG,EACH,YAAW,EAAA,mTAtBP4C,GAAA+C,EAAA,OAAAC,EAAA5F,EAAQ,EAAA,EAAA,QAAQ,MAAM,GAAG,0BAErB4C,GAAA+C,EAAA,WAAAE,EAAA,OAAO,aACd,KACA7F,EAAO,EAAA,EAAC,QAAQ,OAAO,WACxBA,EAAQ,EAAA,EAAA,QAAQ,OAAO,KAAK,MAAM,GAAG,EAAE,IAAG,GAC1C,MAAM,uIAdXvF,GAiCKC,EAAAoL,EAAAlL,CAAA,EAhCJC,GAEKiL,EAAAnF,CAAA,uBACL9F,GA4BKiL,EAAApF,CAAA,EA3BJ7F,GAgBG6F,EAAAiF,CAAA,EALF9K,GAIA8K,EAAAI,CAAA,kBAEDlL,GASA6F,EAAAsF,CAAA,8CAdIhG,EAAO,EAAA,EAAC,QAAQ,OAAO,WACxBA,EAAQ,EAAA,EAAA,QAAQ,OAAO,KAAK,MAAM,GAAG,EAAE,OACvC,QAAM,KAAAc,GAAAmF,EAAAC,CAAA,GAXF,CAAAnD,GAAA5C,EAAA,OAAAyF,KAAAA,EAAA5F,EAAQ,EAAA,EAAA,QAAQ,MAAM,uBAElB,CAAA+C,GAAA5C,EAAA,OAAA0F,KAAAA,EAAA,OAAO,aACd,KACA7F,EAAO,EAAA,EAAC,QAAQ,OAAO,WACxBA,EAAQ,EAAA,EAAA,QAAQ,OAAO,KAAK,MAAM,GAAG,EAAE,IAAG,GAC1C,oDAUDA,EAAO,EAAA,EAAC,QAAQ,OAAO,WACvBA,EAAO,EAAA,EAAC,QAAQ,OAAO,MACvB,IAEC,MAAM,GAAG,EACT,IAAG,EACH,YAAW,EAAA,KAAAc,GAAAqF,EAAAC,CAAA,6KA3CRpG,EAAO,EAAA,EAAC,QAAQ,WACjBA,EAAO,EAAA,EAAC,QAAQ,qBACVA,EAAW,CAAA,QAChBA,EAAO,EAAA,EAAC,QAAQ,6CACiBA,EAAa,EAAA,EAAG,GACvDA,EAAkC,EAAA,0NAL5BA,EAAO,EAAA,EAAC,QAAQ,wBACjBA,EAAO,EAAA,EAAC,QAAQ,gCACVA,EAAW,CAAA,qBAChBA,EAAO,EAAA,EAAC,QAAQ,0DACiBA,EAAa,EAAA,EAAG,GACvDA,EAAkC,EAAA,sPAnBzB,QAAAA,MAAQ,mJAKRqG,GAAArG,QAAAA,EAAM,EAAA,EAAA,MAAA,KAAA,SAAA,+EAPjBvF,GAWKC,EAAAmI,EAAAjI,CAAA,2CATMuF,EAAA,QAAAmG,EAAA,QAAAtG,MAAQ,sWAHfA,EAAO,EAAA,EAAC,OAAS,OAAM,EAalBA,EAAO,EAAA,EAAC,OAAS,aAAeA,EAAO,EAAA,EAAC,QAAQ,aAAaA,EAAW,CAAA,EAAA,EAgBxEA,EAAO,EAAA,EAAC,OAAS,aAAeA,EAAO,EAAA,EAAC,QAAQ,YAAc,OAAM,0WApDlE,GAAA,CAAA,iBAAAuG,CAAA,EAAApF,EAKA,CAAA,cAAAqF,CAAA,EAAArF,EACA,CAAA,OAAAqE,CAAA,EAAArE,EACA,CAAA,KAAAD,CAAA,EAAAC,EACA,CAAA,YAAAsF,CAAA,EAAAtF,EACA,CAAA,OAAAoE,CAAA,EAAApE,EACA,CAAA,OAAAzG,CAAA,EAAAyG,EACA,CAAA,WAAAkE,CAAA,EAAAlE,EACA,CAAA,YAAAvC,CAAA,EAAAuC,EACA,CAAA,gBAAAuF,CAAA,EAAAvF,EACA,CAAA,OAAAwF,CAAA,EAAAxF,EACA,CAAA,qBAAAsE,CAAA,EAAAtE,EACA,CAAA,mCAAAyF,CAAA,EAAAzF,EACA,CAAA,cAAA0F,CAAA,EAAA1F,GACA,WAAA2F,EAAiC,EAAA,EAAA3F,EAEjC,CAAA,QAAArF,CAAA,EAAAqF,cA6BKwF,koGCkDdlM,GAAoCC,EAAAqM,EAAAnM,CAAA,4CAI9BoF,EAAY,EAAA,EAAC,SAAS,KAAGgD,GAAAhD,CAAA,EAGzBgH,EAAAhH,EAAa,EAAA,EAAA,SAAS,WAAa,QAASmD,GAAAnD,CAAA,mGAJlDvF,GAaMC,EAAAqM,EAAAnM,CAAA,gDAZAoF,EAAY,EAAA,EAAC,SAAS,2DAGtBA,EAAa,EAAA,EAAA,SAAS,WAAa,yHAFtCA,EAAY,EAAA,EAAC,SAAS,IAAG,kEAAzBA,EAAY,EAAA,EAAC,SAAS,IAAG,KAAAc,GAAA,EAAAE,CAAA,qFAGpBiG,GAAA,OAAAA,EAAA,CAAA,CAAA,OAAO,UAAUjH,EAAa,EAAA,EAAA,SAAS,QAAQ,QAE9BA,EAAa,EAAA,EAAA,SAAS,UAAY,GAAGiD,2CAHZ,GAC/C,aAMS,GACX,+KAHcjD,EAAY,EAAA,EAAC,SAAS,SAAW,KAAM,QAClD,CAAA,EAAA,+BACC,IAAE,uDAFQA,EAAY,EAAA,EAAC,SAAS,SAAW,KAAM,QAClD,CAAA,EAAA,KAAAc,GAAAoG,EAAAC,CAAA,0CAH4D,IAAAA,EAAAnH,MAAa,SAAS,SAAS,QAC3F,CAAA,EAAA,+BACC,GAAC,sCAF0DG,EAAA,OAAAgH,KAAAA,EAAAnH,MAAa,SAAS,SAAS,QAC3F,CAAA,EAAA,KAAAc,GAAAoG,EAAAC,CAAA,0CAHsD,IAAAA,EAAAnH,EAAA,EAAA,EACrD,SACA,SAAQ,+BAAC,GAAC,sCAF2CG,EAAA,OAAAgH,KAAAA,EAAAnH,EAAA,EAAA,EACrD,SACA,SAAQ,KAAAc,GAAAoG,EAAAC,CAAA,qFAoBHnH,EAAY,EAAA,0RAkBjB,IAAA2D,EAAA3D,EAAa,EAAA,EAAA,UAAU,OAAS,GAACoH,GAAApH,CAAA,yGA1BvBA,EAAQ,EAAA,CAAA,EACCqH,GAAAxE,EAAA,kBAAA,CAAA7C,OACvBA,EAAY,EAAA,EAAC,UAAU,SAAW,MAAM,UAH1CvF,GAmDKC,EAAAmI,EAAAjI,CAAA,wEA9COoF,EAAY,EAAA,CAAA,+CAIbA,EAAY,EAAA,scAkBjBA,EAAa,EAAA,EAAA,UAAU,OAAS,yIA1BtBA,EAAQ,EAAA,CAAA,kBACCqH,GAAAxE,EAAA,kBAAA,CAAA7C,OACvBA,EAAY,EAAA,EAAC,UAAU,SAAW,MAAM,gQA0BhCsH,EAAAC,GAAAvH,MAAa,QAAQ,uBAA1B,OAAIS,GAAA,yKADPhG,GAqBKC,EAAAmI,EAAAjI,CAAA,4EApBG0M,EAAAC,GAAAvH,MAAa,QAAQ,oBAA1B,OAAIS,GAAA,EAAA,8GAAJ,OAAIA,EAAA+G,EAAA,OAAA/G,GAAA,0CAAJ,OAAIA,GAAA,qKAEKT,EAAK,EAAA,sGAOC,cAAAA,KAAgB,gPAPtBA,EAAK,EAAA,+JAOCG,EAAA,KAAAsH,EAAA,cAAAzH,KAAgB,uZArEjB0H,EAAqB,CAAA,CAAA,mBAG9B,QAAA1H,EAAa,EAAA,EAAA,UAAU,OAAS,qFAMrC,IAAA2H,EAAA3H,EAAa,EAAA,EAAA,UAAU,SAAW,WAAS4H,GAAA,KAG3C5H,EAAY,EAAA,GAAE,UAAU,KAAOA,EAAY,EAAA,GAAE,UAAU,WAAQoD,GAAApD,CAAA,IAkBhEA,EAAQ,EAAA,GAAAiB,GAAAjB,CAAA,mMAhCMA,EAAQ,EAAA,EAAG,iBAAmB,cAAc,0DAPnDA,EAAY,EAAA,EAAC,UAAY,IAAMA,EAAY,EAAA,EAAC,UAAY,IAAI,yHALzEvF,GAkGKC,EAAAgG,EAAA9F,CAAA,EAjGJC,GAyCK6F,EAAAC,CAAA,EAhCJ9F,GAKM8F,EAAAoG,CAAA,qIAXoB/G,EAAc,EAAA,CAAA,CAAA,oEAQtBA,EAAQ,EAAA,EAAG,iBAAmB,cAAc,aAKpDG,EAAA,QAAAmG,EAAA,QAAAtG,EAAa,EAAA,EAAA,UAAU,OAAS,qIAMrCA,EAAa,EAAA,EAAA,UAAU,SAAW,wDAGlCA,EAAY,EAAA,GAAE,UAAU,KAAOA,EAAY,EAAA,GAAE,UAAU,wFArBjDA,EAAY,EAAA,EAAC,UAAY,IAAMA,EAAY,EAAA,EAAC,UAAY,mEAuC/DA,EAAQ,EAAA,8SA/FJ6H,GAAgBjM,EAAA,OACjB,aAAcA,qBAvBX,GAAA,CAAA,QAAA+B,CAAA,EAAAwD,GACA,IAAA2G,EAAM,EAAA,EAAA3G,EACN,CAAA,cAAAqF,CAAA,EAAArF,EACA,CAAA,iBAAAoF,CAAA,EAAApF,EAKA,CAAA,gBAAAuF,CAAA,EAAAvF,EACA,CAAA,YAAAvC,CAAA,EAAAuC,EACA,CAAA,OAAAoE,CAAA,EAAApE,EACA,CAAA,cAAA0F,CAAA,EAAA1F,EACA,CAAA,OAAAzG,CAAA,EAAAyG,EACA,CAAA,WAAAkE,CAAA,EAAAlE,EACA,CAAA,OAAAqE,CAAA,EAAArE,EACA,CAAA,OAAAwF,CAAA,EAAAxF,EACA,CAAA,qBAAAsE,CAAA,EAAAtE,EACA,CAAA,mCAAAyF,CAAA,EAAAzF,EACA,CAAA,KAAAD,CAAA,EAAAC,EACA,CAAA,YAAAsF,CAAA,EAAAtF,GACA,WAAA2F,EAAiC,EAAA,EAAA3F,EAMxC4G,EACAC,EAAW,GACXC,EAAwB,GACxBC,EACAC,GAAoB,GAWf,SAAAC,IAAA,MACRJ,EAAY,CAAAA,CAAA,OACZC,EAAwB,EAAA,EAGhB,SAAAI,IAAA,CACJH,GAA4B,CAAAC,IAC/BvG,EAAA,GAAAsG,EAAwB,UAAYA,EAAwB,aAAAA,CAAA,EAIrD,SAAAI,IAAA,CACJJ,IAEFA,EAAwB,aACvBA,EAAwB,WACzBA,EAAwB,aAAe,KAEvCC,GAAoB,KAsBT,MAAAI,EAAA/D,GAAMA,EAAE,MAAQ,SAAW4D,gDAyC5BF,EAAuBM,4vBA1FjC5G,EAAA,GAAAmG,EAAA,CACC,GAAApK,EACH,SAAUkK,GAAgBlK,CAAO,EAAIA,EAAQ,SAAA,CAAA,yBAGtCsK,QACPD,EAAWD,GAAc,UAAU,SAAW,MAAA,sBA2B9CA,EAAa,SACbG,GACAH,EAAa,UAAU,SAAW,QAElC,WAAWM,GAAgB,CAAC,2gFC4DM,IAAArI,MAAY,QAAWA,EAAI,CAAA,EAAA,wGAD7DvF,GAEKC,EAAAmI,EAAAjI,CAAA,uCAD6BuF,EAAA,CAAA,EAAA,IAAAsI,EAAA,IAAAzI,MAAY,qBAAWA,EAAI,CAAA,EAAA,oMAqDnDA,EAAO,EAAA,GAAE,UAAU,MAAK,gMAnBhBA,EAAI,CAAA,CAAA,eAcZA,EAAG,EAAA,EAAG,MAAQ,KAAK,EACZ4C,GAAAC,EAAA,aAAA6F,EAAA1I,EACX,CAAA,EAAA,eACA2I,GAAuB3I,EAAO,EAAA,CAAA,CAAA,iCAhBjBqH,EAAAxE,EAAA,SAAA7C,EAAM,EAAA,IAAAA,EAAM,CAAA,EAAA,OAAS,CAAC,mCACFA,EAAe,CAAA,CAAA,mEAGnCA,EAAU,EAAA,EAAG,UAAY,MAAM,oBAC3BA,EAAG,EAAA,EAAG,QAAU,MAAM,UAPzCvF,GA4DKC,EAAAmI,EAAAjI,CAAA,6PA3DSoF,EAAI,CAAA,CAAA,yBAcZA,EAAG,EAAA,EAAG,MAAQ,wBACP,CAAA+C,GAAA5C,EAAA,CAAA,EAAA,IAAAuI,KAAAA,EAAA1I,EACX,CAAA,EAAA,eACA2I,GAAuB3I,EAAO,EAAA,CAAA,2EAhBjBqH,EAAAxE,EAAA,SAAA7C,EAAM,EAAA,IAAAA,EAAM,CAAA,EAAA,OAAS,CAAC,mDACFA,EAAe,CAAA,CAAA,oEAGnCA,EAAU,EAAA,EAAG,UAAY,MAAM,+BAC3BA,EAAG,EAAA,EAAG,QAAU,MAAM,kOAdpBY,GAAAgI,EAAA,QAAA,OAAA5I,MAAeA,EAAa,EAAA,CAAA,CAAA,YAAA,EAC3BY,GAAAgI,EAAA,aAAA,GAAA5I,MAAgBA,EAAa,EAAA,CAAA,CAAA,IAAA,UAHnDvF,GAMCC,EAAAkO,EAAAhO,CAAA,EADYiO,GAAAD,EAAA5I,KAAcA,EAAa,EAAA,CAAA,CAAA,4DAA3B6I,GAAAD,EAAA5I,KAAcA,EAAa,EAAA,CAAA,CAAA,kBAHnBY,GAAAgI,EAAA,QAAA,OAAA5I,MAAeA,EAAa,EAAA,CAAA,CAAA,YAAA,mBAC3BY,GAAAgI,EAAA,aAAA,GAAA5I,MAAgBA,EAAa,EAAA,CAAA,CAAA,IAAA,4lCA4BvCA,EAAO,EAAA,+XAAPA,EAAO,EAAA,ooBA4CfA,EAAkB,EAAA,iQAAlBA,EAAkB,EAAA,CAAA,2QA7ElB,OAAAA,EAAgB,EAAA,GAAAA,EAAQ,EAAA,EAAA,OAAS,OAAM,0BA2ExC,IAAAgH,EAAAhH,OAAW,SAAOoH,GAAApH,CAAA,iFAnFLA,EAAkC,EAAA,EAAU,GAAPA,EAAI,CAAA,GAAK,iBAAA,yBACvC,EAAI,mCACMA,EAAe,CAAA,CAAA,kBAChCA,EAAO,EAAA,EAAC,OAAS,WAAW,aACjChC,GAAqBgC,EAChC,EAAA,CAAA,GAAAA,MAAQ,QAAQ,YAAc,MAAM,EACtBqH,EAAAxE,EAAA,UAAA7C,MAAgB,CAAC,UAPjCvF,GAkFKC,EAAAmI,EAAAjI,CAAA,0OAjFaoF,EAAkC,EAAA,EAAU,GAAPA,EAAI,CAAA,GAAK,iFACvC,EAAI,wDACMA,EAAe,CAAA,CAAA,uCAChCA,EAAO,EAAA,EAAC,OAAS,WAAW,kCACjChC,GAAqBgC,EAChC,EAAA,CAAA,GAAAA,MAAQ,QAAQ,YAAc,MAAM,uBACtBqH,EAAAxE,EAAA,UAAA7C,MAAgB,CAAC,EA6E5BA,OAAW,sPAeFA,EAAkB,EAAA,EAAA,CAAA,KAAAA,EAAA,EAAA,CAAA,CAAA,gLAAlBA,EAAkB,EAAA,CAAA,gKAlH9B2H,EAAA3H,OAAe,MAAIkD,GAAAlD,CAAA,OAcfA,EAAQ,CAAA,CAAA,uBAAb,OAAIS,GAAA,4DAmGJ,IAAAuG,EAAAhH,OAAW,UAAQiB,GAAAjB,CAAA,+IArGdA,EAAkC,EAAA,EAAGA,EAAI,CAAA,EAAG,EAAE,EAAA,iBAAA,gBADtCA,EAAkC,EAAA,CAAA,4DAH5BqH,EAAA3G,EAAA,iBAAAV,EAAS,CAAA,EAAA,CAAC,EAAE,OAAS,WAAW,EAZnC4C,GAAAkD,EAAA,QAAAgD,EAAA,eAAA9I,SAASA,EAAI,CAAA,EAAA,qBAAA,EACdqH,EAAAvB,EAAA,cAAA9F,OAAe,IAAI,EACVqH,EAAAvB,EAAA,uBAAA9F,OAAwB,IAAI,UAHzDvF,GAoHKC,EAAAoL,EAAAlL,CAAA,yBA1GJC,GAyGKiL,EAAApF,CAAA,EApGJ7F,GAmGK6F,EAAAC,CAAA,mGA7GDX,OAAe,mIAcXA,EAAQ,CAAA,CAAA,oBAAb,OAAIS,GAAA,EAAA,4GAAJ,OAAIA,EAAA+G,EAAA,OAAA/G,GAAA,0CAFCT,EAAkC,EAAA,EAAGA,EAAI,CAAA,EAAG,EAAE,EAAA,wEADtCA,EAAkC,EAAA,CAAA,iDAH5BqH,EAAA3G,EAAA,iBAAAV,EAAS,CAAA,EAAA,CAAC,EAAE,OAAS,WAAW,GAZnC,CAAA+C,GAAA5C,EAAA,CAAA,EAAA,IAAA2I,KAAAA,EAAA,eAAA9I,SAASA,EAAI,CAAA,EAAA,wDACdqH,EAAAvB,EAAA,cAAA9F,OAAe,IAAI,iBACVqH,EAAAvB,EAAA,uBAAA9F,OAAwB,IAAI,EAmHpDA,OAAW,wJAnGX,OAAIS,GAAA,gKAlGJsI,GAAqB,YAqBhBJ,GAAuB7M,EAAA,CAC3B,OAAAA,EAAQ,OAAS,OACbA,EAAQ,QAEfA,EAAQ,OAAS,aACjBA,EAAQ,QAAQ,YAAc,OAE1B,MAAM,QAAQA,EAAQ,QAAQ,KAAK,EACJ,2BAAAA,EAAQ,QAAQ,MAAM,CAAC,EAAE,WAAW,MAAM,GAAG,EAAE,IAAA,CAAA,8BAGtDA,EAAQ,QAAQ,OAAO,WAAW,MAAM,GAAG,EAAE,IACvE,CAAA,IAAAA,EAAQ,QAAQ,OAAO,WAAa,2BAGTA,EAAQ,QAAQ,WAAa,SAAS,sBA9E1D,GAAA,CAAA,MAAAqG,CAAA,EAAAhB,EACA,CAAA,WAAA6H,CAAA,EAAA7H,GACA,oBAAA8H,EAAuC,IAAA,EAAA9H,GACvC,KAAApD,EAAO,MAAA,EAAAoD,EACP,CAAA,SAAA/D,EAAA,EAAA,EAAA+D,EACA,CAAA,OAAAmD,CAAA,EAAAnD,EACA,CAAA,gBAAAuF,CAAA,EAAAvF,EACA,CAAA,iBAAAoF,CAAA,EAAApF,EAKA,CAAA,cAAAqF,CAAA,EAAArF,EACA,CAAA,WAAA+H,CAAA,EAAA/H,EACA,CAAA,OAAAqE,CAAA,EAAArE,EACA,CAAA,IAAA2G,CAAA,EAAA3G,EACA,CAAA,SAAAa,CAAA,EAAAb,EACA,CAAA,KAAAD,CAAA,EAAAC,EACA,CAAA,YAAAsF,CAAA,EAAAtF,EACA,CAAA,OAAAoE,CAAA,EAAApE,EACA,CAAA,OAAAzG,CAAA,EAAAyG,EACA,CAAA,WAAAkE,CAAA,EAAAlE,EACA,CAAA,YAAAvC,CAAA,EAAAuC,EACA,CAAA,EAAAV,CAAA,EAAAU,EACA,CAAA,iBAAA8C,CAAA,EAAA9C,EACA,CAAA,WAAAiD,EAAA,EAAAjD,EACA,CAAA,iBAAAE,EAAA,EAAAF,EACA,CAAA,UAAAgI,EAAA,EAAAhI,EACA,CAAA,UAAA4C,EAAA,EAAA5C,EACA,CAAA,WAAA0C,CAAA,EAAA1C,EACA,CAAA,UAAA2C,EAAA,EAAA3C,EACA,CAAA,WAAA7C,CAAA,EAAA6C,EACA,CAAA,cAAAC,CAAA,EAAAD,EACA,CAAA,OAAAwF,EAAA,EAAAxF,EACA,CAAA,qBAAAsE,EAAA,EAAAtE,EACA,CAAA,aAAA6C,EAAA,EAAA7C,EACA,CAAA,cAAAiI,EAAA,EAAAjI,EACA,CAAA,mCAAAyF,EAAA,EAAAzF,GACA,iBAAAkD,GAAkC,IAAA,EAAAlD,GAClC,WAAA2F,GAAiC,EAAA,EAAA3F,GACjC,UAAAiB,GAA2B,IAAA,EAAAjB,EAClCkI,GAAA,CAAA,EAEAC,GAA2B,MAAMlM,EAAS,MAAM,EAAE,KAAK,GAAG,EAC1DmM,GAA4B,MAAMnM,EAAS,MAAM,EAAE,KAAK,CAAC,EAYpD,SAAAoM,GAAc/I,EAAW3E,EAAA,CACjCkG,EAAS,SAAA,CACR,MAAOlG,EAAQ,MACf,MAAOA,EAAQ,UAyCb,IAAA2N,kBAyDcL,GAAcvC,CAAa,EAAA,KAAA,2DAY5BwC,GAAgBxC,CAAa,EAAA2B,wBACxBgB,GAAc/I,EAAG3E,CAAO,QAC3B0I,IAAC,CACTA,EAAE,MAAQ,SACbgF,GAAc/I,EAAG3E,CAAO,GAyDjByI,GAAAC,GAAMxC,EAAS,OAAQwC,EAAE,MAAM,yhDAxLvCR,IAAiB,CAAA+E,GAAA,OACjBW,EAASL,GAAgB,OAASjM,EAAS,eACxCuM,EAAMD,EAAQC,EAAMN,GAAgB,OAAQM,IAChDA,GAAO,IACV/H,EAAA,GAAA0H,GAAeK,EAAMD,CAAM,EAAIL,GAAgBM,CAAG,GAAG,YAAAL,EAAA,EACrD1H,EAAA,GAAA2H,GAAgBI,EAAMD,CAAM,EAAIL,GAAgBM,CAAG,GAAG,aAAAJ,EAAA,+CAkDtD3H,EAAA,GAAA6H,GAAA,CACF,cAAArI,EACA,SAAU+H,GACV,iBAAA9H,GACA,WAAAwC,EACA,UAAAC,GACA,UAAAC,GACA,aAAAC,GACA,WAAAI,GACA,iBAAAH,EACA,QAAS3F,IAAe,SAAWlB,EAAS,CAAC,EAAIA,EACjD,SAAUW,IAAS,OAAS,QAAU,OACtC,OAAQiL,EACR,OAAA1E,EACA,SAAAtC,EACA,iBAAAqC,GACA,UAAAjC,42ICtHkCpC,EAAa,CAAA,EAAC,CAAC,EAAE,mHADnDvF,GAEKC,EAAAmI,EAAAjI,CAAA,mDAD6BoF,EAAa,CAAA,EAAC,CAAC,EAAE,0IAF/CA,EAAa,CAAA,EAAC,CAAC,IAAM,MAAIiB,GAAAjB,CAAA,6WAODA,EAAM,CAAA,EAAA,iBAAA,4GACfA,EAAa,CAAA,EAAC,CAAC,IAAM,IAAI,8BAChBA,EAAa,CAAA,EAAC,CAAC,IAAM,IAAI,mDAVvDvF,GAwBKC,EAAAkP,EAAAhP,CAAA,yBAjBJC,GAgBK+O,EAAAC,CAAA,EARJhP,GAOKgP,EAAAC,CAAA,iBArBD9J,EAAa,CAAA,EAAC,CAAC,IAAM,8IAOGA,EAAM,CAAA,EAAA,mEACfA,EAAa,CAAA,EAAC,CAAC,IAAM,IAAI,yCAChBA,EAAa,CAAA,EAAC,CAAC,IAAM,IAAI,0FAd3C,OAAAsE,EAAS,QAAA,EAAAnD,EACT,CAAA,cAAA4I,EAAA,CAAqD,KAAM,IAAI,CAAA,EAAA5I,+vBCApC,EAAA,OAAA,kNAgCjBnB,EAAW,CAAA,8GAD/BvF,EAEKC,EAAAmI,EAAAjI,CAAA,uDADeoF,EAAW,CAAA,+JAKvBA,EAAQ,CAAA,CAAA,uBAAb,OAAIS,GAAA,wLADPhG,EAoIKC,EAAAmI,EAAAjI,CAAA,6EAnIGoF,EAAQ,CAAA,CAAA,oBAAb,OAAIS,GAAA,EAAA,4GAAJ,OAAIA,EAAA+G,EAAA,OAAA/G,GAAA,0CAAJ,OAAIA,GAAA,0LAwBG,iDAAAT,EAAQ,CAAA,EAAA,MAAM,OAAS,EAAC,iBAkEnBA,EAAO,CAAA,EAAC,MAAM,CAAC,EAAE,WAAW,SAAS,OAAO,sBAQ5CA,EAAO,CAAA,EAAC,MAAM,CAAC,EAAE,WAAW,SAAS,OAAO,sBAQ5CA,EAAO,CAAA,EAAC,MAAM,CAAC,EAAE,WAAW,SAAS,OAAO,8eAtFtDvF,EAEKC,EAAAmI,EAAAjI,CAAA,4GAPEoF,EAAO,CAAA,EAAC,KAAK,yHAHpBvF,EAMKC,EAAAmI,EAAAjI,CAAA,mDAHEoF,EAAO,CAAA,EAAC,KAAK,iOAqGG4C,EAAAC,EAAA,aAAA6F,EAAA,SAAA1I,EAAQ,CAAA,EAAA,MAAM,CAAC,EAAE,SAAS,EAAA,UAFhDvF,EAKKC,EAAAmI,EAAAjI,CAAA,6BAHiB,CAAAmI,GAAA5C,EAAA,GAAAuI,KAAAA,EAAA,SAAA1I,EAAQ,CAAA,EAAA,MAAM,CAAC,EAAE,SAAS,4OAP1B4C,EAAAC,EAAA,aAAA6F,EAAA,SAAA1I,EAAQ,CAAA,EAAA,MAAM,CAAC,EAAE,SAAS,EAAA,UAFhDvF,EAKKC,EAAAmI,EAAAjI,CAAA,6BAHiB,CAAAmI,GAAA5C,EAAA,GAAAuI,KAAAA,EAAA,SAAA1I,EAAQ,CAAA,EAAA,MAAM,CAAC,EAAE,SAAS,0MAPzCgK,GAAAC,EAAA,IAAAC,EAAAlK,EAAQ,CAAA,EAAA,MAAM,CAAC,EAAE,GAAG,GAAA4C,EAAAqH,EAAA,MAAAC,CAAA,yFAH3BzP,EAMKC,EAAAmI,EAAAjI,CAAA,EALJC,EAICgI,EAAAoH,CAAA,UAFK9J,EAAA,GAAA,CAAA6J,GAAAC,EAAA,IAAAC,EAAAlK,EAAQ,CAAA,EAAA,MAAM,CAAC,EAAE,GAAG,iHARpB,IAAAA,EAAQ,CAAA,EAAA,MAAM,CAAC,EAAE,IACjB,IAAAA,KAAQ,MAAM,CAAC,EAAE,WAAa,kHAJrCvF,EAMKC,EAAAmI,EAAAjI,CAAA,uCAHEuF,EAAA,IAAAsI,EAAA,IAAAzI,EAAQ,CAAA,EAAA,MAAM,CAAC,EAAE,KACjBG,EAAA,IAAAsI,EAAA,IAAAzI,KAAQ,MAAM,CAAC,EAAE,WAAa,2IAjE7BmK,EAAA5C,GAAAvH,KAAQ,MAAM,MAAM,EAAG,CAAC,CAAA,uBAA7B,OAAIS,GAAA,4DAgDD,IAAAkD,EAAA3D,EAAQ,CAAA,EAAA,MAAM,OAAS,GAACiD,GAAAjD,CAAA,iMArD9BvF,EAgEKC,EAAAmI,EAAAjI,CAAA,8FA3DGuP,EAAA5C,GAAAvH,KAAQ,MAAM,MAAM,EAAG,CAAC,CAAA,oBAA7B,OAAIS,GAAA,EAAA,yGAAJ,OAAIA,EAAA+G,EAAA,OAAA/G,GAAA,YAgDDT,EAAQ,CAAA,EAAA,MAAM,OAAS,yFAhD1B,OAAIS,GAAA,6MAwCEwG,GAAA,OAAAA,EAAA,CAAA,CAAAjH,EAAK,CAAA,EAAA,WAAW,SAAS,OAAO,yGAFhB4C,EAAAC,EAAA,aAAA6F,EAAA,SAAA1I,KAAK,SAAS,EAAA,UAFpCvF,EASKC,EAAAmI,EAAAjI,CAAA,mJAPiB,CAAAmI,GAAA5C,EAAA,GAAAuI,KAAAA,EAAA,SAAA1I,KAAK,SAAS,qHAb9BA,EAAC,CAAA,IAAK,GAAKA,KAAQ,MAAM,OAAS,GAACoK,GAAApK,CAAA,kFAHlCgK,GAAAC,EAAA,IAAAC,EAAAlK,KAAK,GAAG,GAAA4C,EAAAqH,EAAA,MAAAC,CAAA,yFAHfzP,EAeKC,EAAAmI,EAAAjI,CAAA,EAdJC,EAICgI,EAAAoH,CAAA,gCAFK9J,EAAA,GAAA,CAAA6J,GAAAC,EAAA,IAAAC,EAAAlK,KAAK,GAAG,gBAGTA,EAAC,CAAA,IAAK,GAAKA,KAAQ,MAAM,OAAS,iKApBjC,IAAAA,KAAK,IACL,IAAAA,EAAK,CAAA,EAAA,WAA8B,iBAAAA,KAAI,CAAC,YAEzCA,EAAC,CAAA,IAAK,GAAKA,KAAQ,MAAM,OAAS,GAAC4H,GAAA5H,CAAA,uHANzCvF,EAeKC,EAAAmI,EAAAjI,CAAA,6DAZEuF,EAAA,IAAAsI,EAAA,IAAAzI,KAAK,KACLG,EAAA,IAAAsI,EAAA,IAAAzI,EAAK,CAAA,EAAA,WAA8B,iBAAAA,KAAI,CAAC,cAEzCA,EAAC,CAAA,IAAK,GAAKA,KAAQ,MAAM,OAAS,ugBAuBnCkG,EAAAlG,EAAQ,CAAA,EAAA,MAAM,OAAS,EAAC,kCAD3B,GACE,0EAFc4C,EAAAC,EAAA,aAAA6F,EAAA,GAAA1I,EAAQ,CAAA,EAAA,MAAM,OAAS,CAAC,aAAA,UAHxCvF,EAMKC,EAAAmI,EAAAjI,CAAA,wBADFuF,EAAA,GAAA+F,KAAAA,EAAAlG,EAAQ,CAAA,EAAA,MAAM,OAAS,EAAC,KAAAc,GAAAmF,EAAAC,CAAA,EAFX/F,EAAA,GAAAuI,KAAAA,EAAA,GAAA1I,EAAQ,CAAA,EAAA,MAAM,OAAS,CAAC,2EAfrCkG,EAAAlG,EAAQ,CAAA,EAAA,MAAM,OAAS,EAAC,kCAD3B,GACE,0EAFc4C,EAAAC,EAAA,aAAA6F,EAAA,GAAA1I,EAAQ,CAAA,EAAA,MAAM,OAAS,CAAC,aAAA,UAHxCvF,EAMKC,EAAAmI,EAAAjI,CAAA,wBADFuF,EAAA,GAAA+F,KAAAA,EAAAlG,EAAQ,CAAA,EAAA,MAAM,OAAS,EAAC,KAAAc,GAAAmF,EAAAC,CAAA,EAFX/F,EAAA,GAAAuI,KAAAA,EAAA,GAAA1I,EAAQ,CAAA,EAAA,MAAM,OAAS,CAAC,8JAXtCiH,GAAA,OAAAA,EAAA,CAAA,CAAAjH,EAAK,CAAA,EAAA,WAAW,SAAS,OAAO,QAiB3BqK,GAAA,OAAAA,EAAA,CAAA,CAAArK,EAAK,CAAA,EAAA,WAAW,SAAS,OAAO,6UAqCtCkG,EAAAlG,EAAQ,CAAA,EAAA,MAAM,OAAS,EAAC,6CAD3B,GACE,yEAFc4C,EAAAjC,EAAA,aAAA2J,EAAA,GAAAtK,EAAQ,CAAA,EAAA,MAAM,OAAS,CAAC,aAAA,oDAJzCvF,EAQKC,EAAAgG,EAAA9F,CAAA,EAPJC,EAMK6F,EAAAC,CAAA,wBADFR,EAAA,GAAA+F,KAAAA,EAAAlG,EAAQ,CAAA,EAAA,MAAM,OAAS,EAAC,KAAAc,GAAAmF,EAAAC,CAAA,EAFX/F,EAAA,GAAAmK,KAAAA,EAAA,GAAAtK,EAAQ,CAAA,EAAA,MAAM,OAAS,CAAC,qFA0CzCkG,GAAAlG,EAAQ,CAAA,EAAA,cAAgBA,KAAQ,MAAI,8DAlHnCA,EAAO,CAAA,GAAE,MAAM,IAAG,EAQbA,EAAS,CAAA,GAAA,MAAM,YAAc,OAAM,EAInCA,EAAO,CAAA,EAAC,QAAU,QAAaA,EAAO,CAAA,EAAC,MAAM,OAAS,EAAC,kXAfpCA,EAAC,CAAA,EAAG,CAAC,KAAKA,KAAQ,cAAgBA,EAAO,CAAA,EAAC,IAAI,EAAA,UAP7EvF,EAgIQC,EAAAmG,EAAAjG,CAAA,EAvHPC,EAsHKgG,EAAAH,CAAA,4BALJ7F,EAIK6F,EAAAC,CAAA,EAHJ9F,EAEA8F,EAAAoG,CAAA,qNADG,CAAAhE,GAAA5C,EAAA,IAAA+F,KAAAA,GAAAlG,EAAQ,CAAA,EAAA,cAAgBA,KAAQ,MAAI,KAAAc,GAAAmF,EAAAC,CAAA,qCArHXlG,EAAC,CAAA,EAAG,CAAC,KAAKA,KAAQ,cAAgBA,EAAO,CAAA,EAAC,IAAI,8HAf3E2H,EAAA3H,OAAgB,MAAIuK,GAAAvK,CAAA,EAKpBgH,EAAAhH,OAAa,MAAIiB,GAAAjB,CAAA,sIANvBvF,EA6IKC,EAAAmI,EAAAjI,CAAA,sDA5ICoF,OAAgB,mGAKhBA,OAAa,8MA/BP,SAAAwK,EAAoC,IAAA,EAAArJ,GACpC,YAAAsJ,EAA6B,IAAA,EAAAtJ,EAC7B,CAAA,iBAAAoF,CAAA,EAAApF,QAMLa,EAAWC,KAIR,SAAAyI,EACRjK,EACAkK,EAAA,CAEM,MAAAC,EAAA,OACED,GAAY,UAAa,KAAMA,CAAA,EAAYA,EACnD3I,EAAS,iBAAA,CACR,MAAOvB,EACP,MAAA,CAAS,KAAMmK,EAAY,KAAM,MAAOA,EAAY,SAiBjD,MAAAnJ,EAAA,CAAAhB,EAAAkK,IAAAD,EACCjK,EACO,OAAAkK,GAAY,SAAa,CAAA,KAAMA,CAAO,EAAKA,CAAA,ivBC/C9B,EAAA,OAAA,+EAqDpB3K,EAAM,CAAA,EAAG8B,GAAQC,SAEhB/B,EAAM,CAAA,EAAG,sBAAwB,qCAD9BA,EAAW,CAAA,CAAA,kFADfA,EAAM,CAAA,EAAG8B,GAAQC,kBAEhB/B,EAAM,CAAA,EAAG,sBAAwB,yIAlDpCkC,EAAS,GACF,CAAA,MAAAC,CAAA,EAAAhB,GACA,UAAAiB,EAA2B,IAAA,EAAAjB,EAElCkB,EAEK,SAAAC,GAAA,KACRJ,EAAS,EAAA,EACLG,GAAO,aAAaA,CAAK,EAC7BA,EAAQ,oBACPH,EAAS,EAAA,GACP,KAGE,MAAA2I,EAAA,IAAA,CACD,GAAA1I,EAAA,CACG,MAAA2I,EAAqB3I,EACzB,IAAKrG,GACDA,EAAQ,OAAS,UACVA,EAAQ,IAAI,KAAKA,EAAQ,OAAO,GAEjC,GAAAA,EAAQ,IAAI,KAAKA,EAAQ,QAAQ,MAAM,GAAG,EAEpD,EAAA,KAAK;AAAA;AAAA,CAAM,EAEP0G,EAAeJ,KACf0I,CAAkB;AAAA;AAAA,EAAO1I,CAAS,GACrC0I,EAEH,UAAU,UAAU,UAAUtI,CAAY,EAAE,MAAOuI,GAAA,CAClD,QAAQ,MAAM,gCAAiCA,CAAG,MAKtC,eAAAxI,GAAA,CACV,cAAe,YAClBsI,IACAvI,KAIF,OAAAK,GAAA,IAAA,CACKN,GAAO,aAAaA,CAAK,kxBChC7B,CAAA,sBAAAJ,GAAA,KAAA+I,aAKM,EAAA,OAAA,oJA4QW,MAAAC,EAAAC,EAAS,EAAA,EAAA,CAAC,EAAE,OAAS,OAAS,OAAS,cACjC,MAAAC,EAAAD,MAAcA,EAAI,EAAA,IAAK,OAAS,EAAI,CAAC,UAC5B,MAAAE,EAAAF,MAAcA,EAAI,EAAA,IAAK,OAAS,EAAI,CAAC,UAC1C,MAAAG,EAAAH,EAAA,EAAA,EACvB,MAAM,EAAGA,EAAC,EAAA,CAAA,EACV,OAAQpL,GAAMA,EAAE,CAAC,EAAE,OAAS,WAAW,EAAE,eAE1C,MAAAwL,EAAAJ,QAAS,OAASA,EAAkB,EAAA,GAAAA,MAAeA,EAAc,EAAA,CAAA,EAC9DA,MAAeA,EAAc,EAAA,CAAA,EAC7B,0XA9CEK,EAAS,CAAA,CAAA,sdAFZvL,EAAiB,EAAA,GAAA4H,GAAA5H,CAAA,wBAmBfwL,GAEC,MAAAxL,MAAK,eAAe,gCAEvBA,EAAoB,EAAA,GAAAgD,GAAAhD,CAAA,2JAvBpBA,EAAiB,EAAA,4HAqBdG,EAAA,CAAA,EAAA,UAAAC,EAAA,MAAAJ,MAAK,eAAe,aAEvBA,EAAoB,EAAA,mrBAejBA,EAAe,EAAA,CAAA,uBAApB,OAAIS,GAAA,kGAsEDT,EAAa,EAAA,IAAK,UAAYA,EAAe,CAAA,EAAA,EAExCA,EAAO,EAAA,EAAA,gKAzElBvF,GAyFKC,EAAA+Q,EAAA7Q,CAAA,+JAxFGoF,EAAe,EAAA,CAAA,oBAApB,OAAIS,GAAA,EAAA,yGAAJ,OAAIA,EAAA+G,EAAA,OAAA/G,GAAA,mMAAJ,OAAIA,GAAA,o/BAuCO,UAAAT,QAAS,OAASA,MAAYA,EAAiB,EAAA,EAAGA,EAAQ,CAAA,EACzD,WAAAA,EAAc,EAAA,GAAA/B,GAAoB+B,MAAUA,EAAK,CAAA,CAAA,EAClD,UAAAA,EAAa,EAAA,GAAA/B,GAAoB+B,MAAUA,EAAK,CAAA,CAAA,YAChDA,EAAQ,EAAA,IAAK,OACtBA,EAAQ,EAAA,GAAI,QACZA,EAAI,EAAA,IAAK,QACTA,EAAQ,EAAA,EAAC,OAAS,GAClBA,EAAQ,EAAA,EAACA,EAAQ,EAAA,EAAC,OAAS,CAAC,EAAE,MAAQ,OAC1B,aAAAA,QAAeA,EAAC,EAAA,yCAetB,OAAAA,MAAa,OAAMgF,mJAIvB,IAAArB,EAAA3D,EAAkB,EAAA,IAAA,UAAYA,EAAc,CAAA,GAAAA,EAAS,EAAA,EAAAA,EAAS,EAAA,EAAA,OAAS,CAAC,EAAE,OAAS,aAAeA,EAAS,EAAA,EAAAA,EAAS,EAAA,EAAA,OAAS,CAAC,EAAE,UAAU,SAAW,QAAMkD,GAAAlD,CAAA,g9BA3BpJG,EAAA,CAAA,EAAA,UAAAA,EAAA,CAAA,EAAA,MAAAuL,EAAA,UAAA1L,QAAS,OAASA,MAAYA,EAAiB,EAAA,EAAGA,EAAQ,CAAA,GACzDG,EAAA,CAAA,EAAA,UAAAA,EAAA,CAAA,EAAA,MAAAuL,EAAA,WAAA1L,EAAc,EAAA,GAAA/B,GAAoB+B,MAAUA,EAAK,CAAA,CAAA,GAClDG,EAAA,CAAA,EAAA,UAAAA,EAAA,CAAA,EAAA,MAAAuL,EAAA,UAAA1L,EAAa,EAAA,GAAA/B,GAAoB+B,MAAUA,EAAK,CAAA,CAAA,oCAChDA,EAAQ,EAAA,IAAK,OACtBA,EAAQ,EAAA,GAAI,QACZA,EAAI,EAAA,IAAK,QACTA,EAAQ,EAAA,EAAC,OAAS,GAClBA,EAAQ,EAAA,EAACA,EAAQ,EAAA,EAAC,OAAS,CAAC,EAAE,MAAQ,QAC1BG,EAAA,CAAA,EAAA,KAAAuL,EAAA,aAAA1L,QAAeA,EAAC,EAAA,+KAmB1BA,EAAkB,EAAA,IAAA,UAAYA,EAAc,CAAA,GAAAA,EAAS,EAAA,EAAAA,EAAS,EAAA,EAAA,OAAS,CAAC,EAAE,OAAS,aAAeA,EAAS,EAAA,EAAAA,EAAS,EAAA,EAAA,OAAS,CAAC,EAAE,UAAU,SAAW,6QAQlJA,EAAO,EAAA,CAAA,uBAAZ,OAAI,GAAA,+HADPvF,GAaKC,EAAA+Q,EAAA7Q,CAAA,8EAZGoF,EAAO,EAAA,CAAA,oBAAZ,OAAIS,GAAA,EAAA,mHAAJ,kXASC0G,GAAAnH,EAAO,EAAA,EAAA,OAASA,MAAO,OAAK,wIAR9BvF,GASQC,EAAAmG,EAAAjG,CAAA,0DADNuF,EAAA,CAAA,EAAA,KAAAgH,KAAAA,GAAAnH,EAAO,EAAA,EAAA,OAASA,MAAO,OAAK,KAAAc,GAAAoG,EAAAC,CAAA,kFAmB3BwE,qDAGI3L,EAAgB,EAAA,CAAA,wGAL5BvF,GAOKC,EAAA+Q,EAAA7Q,CAAA,oJAnJD+M,EAAA3H,OAAU,MAAQA,EAAM,CAAA,EAAA,OAAS,GAACiD,GAAAjD,CAAA,8CAsCjCA,EAAK,CAAA,IAAK,MAAQA,EAAK,CAAA,EAAC,OAAS,GAAKA,EAAe,EAAA,IAAK,KAAI,gCAqG/DA,EAAkB,EAAA,GAAAiB,GAAAjB,CAAA,sEA3Gf4C,GAAA6I,EAAA,QAAAG,EAAAC,GAAA7L,QAAW,SAAW,cAAgB,YAAY,EAAA,gBAAA,wHAD1DvF,GA0GKC,EAAA+Q,EAAA7Q,CAAA,uEAzIAoF,OAAU,MAAQA,EAAM,CAAA,EAAA,OAAS,oPAgC9B,CAAA+C,GAAA5C,EAAA,CAAA,EAAA,SAAAyL,KAAAA,EAAAC,GAAA7L,QAAW,SAAW,cAAgB,YAAY,EAAA,oCA2GrDA,EAAkB,EAAA,kSAhWX,CAAA,MAAAmC,EAAA,EAAA,EAAAhB,EACP2K,EAAwC,KAIjC,CAAA,OAAAtG,CAAA,EAAArE,EACA,CAAA,eAAAtC,CAAA,EAAAsC,EACA,CAAA,qBAAAsE,CAAA,EAAAtE,EACA,CAAA,mCAAAyF,CAAA,EAAAzF,EAEPvC,EAAA,CAAA,EAEE,MAAAmN,EAAA,OAAoB,OAAW,IAEtB,eAAAC,GAAA,CACdpK,EAAA,GAAAhD,EAAA,MAAoBF,GACnBa,GAA6B4C,CAAK,EAClCvD,EACAC,CAAA,CAAA,EAMS,GAAA,CAAA,iBAAA0H,CAAA,EAAApF,GAKA,gBAAA8K,EAAkB,EAAA,EAAA9K,GAClB,WAAAiD,EAAa,EAAA,EAAAjD,GACb,WAAA+H,EAAa,EAAA,EAAA/H,GACb,SAAAyC,EAAW,EAAA,EAAAzC,EACX,CAAA,iBAAAE,CAAA,EAAAF,GACA,eAAA+K,EAA2C,IAAA,EAAA/K,GAC3C,SAAAgL,EAAkC,IAAA,EAAAhL,GAClC,kBAAAiL,EAAoB,EAAA,EAAAjL,GACpB,qBAAAkL,EAAuB,EAAA,EAAAlL,GACvB,IAAA2G,GAAM,EAAA,EAAA3G,GACN,iBAAA8C,GAAmB,EAAA,EAAA9C,EACnB,CAAA,cAAA4I,GAAA,CAAqD,KAAM,IAAI,CAAA,EAAA5I,GAC/D,cAAAqF,GAAgB,EAAA,EAAArF,GAChB,gBAAAuF,EAAkB,EAAA,EAAAvF,GAClB,YAAAsF,GAAc,EAAA,EAAAtF,GACd,WAAAmL,EAAa,EAAA,EAAAnL,EACb,CAAA,WAAAkE,CAAA,EAAAlE,EACA,CAAA,KAAAD,EAAA,EAAAC,GACA,OAAAmD,GAA6B,QAAA,EAAAnD,GAC7B,YAAAsJ,GAA6B,IAAA,EAAAtJ,EAC7B,CAAA,OAAAoE,EAAA,EAAApE,GACA,WAAA7C,GAAoC,QAAA,EAAA6C,GACpC,SAAAqJ,GAAoC,IAAA,EAAArJ,GACpC,WAAAoL,GAAa,EAAA,EAAApL,GACb,UAAAqL,GAAY,EAAA,EAAArL,GACZ,kBAAAsL,GAAoB,EAAA,EAAAtL,GACpB,WAAA2F,GAAiC,EAAA,EAAA3F,GACjC,UAAAiB,GAA2B,IAAA,EAAAjB,GAC3B,cAAAuL,GAA+C,MAAA,EAAAvL,EAEtDzG,GAA6B,KAC7BiS,GAA4B,KAC5BvD,GAAA,CAAA,EAEJwD,GAAA,IAAA,MACClS,GAAS,SAAS,cAAc,sBAAsB,CAAA,IAGnD,IAAAmI,EAEAgK,GAAqB,SAEnB7K,GAAWC,KAeR,SAAA6K,GAAA,CACD,OAAAjK,GAAOA,EAAI,aAAeA,EAAI,UAAYA,EAAI,aAAe,IAG5D,SAAAkK,GAAA,CACHlK,IACLA,EAAI,SAAS,EAAGA,EAAI,YAAY,OAChCgK,GAAqB,EAAA,GAKP,eAAAG,IAAA,CACTV,GACDQ,EAAA,IAIG,MAAA9B,GAAA,YACI,QAASiC,GAAY,WAAWA,EAAS,GAAG,CAAA,EACtDF,KAGFH,GAAA,IAAA,CACKN,GACHS,IAEDC,OAMDJ,GAAA,IAAA,CACU,SAAAM,GAAA,CACJJ,EAAA,OACHD,GAAqB,EAAA,OAGrBA,GAAqB,EAAA,EAIvB,OAAAhK,GAAK,iBAAiB,SAAUqK,CAAa,OAE5CrK,GAAK,oBAAoB,SAAUqK,CAAa,KAazC,SAAA9L,GACRX,EACA3E,GACAwF,GAAA,CAEI,GAAAA,KAAa,QAAUA,KAAa,QAAA,OACjC6L,GAAOhL,EAGT,IAAA/D,GAAa+O,GAAK,OAAS,OACxBA,GAAK/O,EAAU,EAAE,OAAS,aAChCA,KAED4D,GAASV,GAAA,CACR,MAAO6L,GAAK/O,EAAU,EAAE,MACxB,MAAO+O,GAAK/O,EAAU,EAAE,kBAEfkD,IAAY,YACtBqL,GAAalM,CAAA,EACb2I,GAAc,KAAKtN,GAAQ,OAAiB,UAClCwF,IAAY,mBACtBqL,GAAa,IAAA,UACHrL,IAAY,mBACtBqL,GAAa,IAAA,EACb3K,GAAS,OAAA,CACR,MAAOlG,GAAQ,MACf,MAAOsN,GAAc3I,CAAC,EAAE,MAAA,EACxB,eAAgB3E,GAAQ,eAGrB,IAAAsR,GACH9L,KAAa,OACV,GACAA,KAAa,UACZ,GACAA,IAAY,MACbhD,KAAe,SAClB0D,GAAS,OAAA,CACR,MAAOlG,GAAQ,MACf,MAAOA,GAAQ,QACf,MAAOsR,UAGH,GAAA,CAAA7O,EAAA,OAEC,MAAA8O,GAAgB9O,EAAgBkC,CAAC,GAChC6M,GAAOC,EAAI,GACjBF,GAAc,CAAC,EACfA,GAAcA,GAAc,OAAS,CAAC,CAAA,EAGvCrL,GAAS,OAAA,CACR,MAAOsL,GAAM,MACb,MAAOD,GAAc,IAAKvN,IAAMA,GAAE,OAAO,EACzC,MAAOsN,OAMF,SAAAI,IAAA,KACHrL,GAAU,CAAA5D,GAAmBA,EAAgB,SAAW,EACrD,aACFkP,EAAalP,EAAgBA,EAAgB,OAAS,CAAC,KACzDkP,EAAW,CAAC,EAAE,OAAS,mBACpBA,EAAWA,EAAW,OAAS,CAAC,EAAE,qCAY/BjS,EAAS,MAASJ,GAAwB+G,CAAK,EACrDH,GAAS,QACR,CAAA,YAAaxG,CAAA,CAAA,QAENgJ,EAAC,CACT,QAAQ,MAAMA,CAAC,MACX1I,GAAU0I,aAAakJ,GAAalJ,EAAE,QAAU,gBACpDxC,GAAS,QAASlG,EAAO,IAOZ4F,GAAA,IAAAM,GAAS,OAAO,WAoEdV,KAAQ,CACnBA,IAAY,QACf8H,GAAc,OAAO,EAAGA,GAAc,MAAM,EAEzC9H,KAAa,QAAUA,KAAa,cACvClE,EAAS,QAAS,CAAAxB,GAAKkC,KAAK,CAC3BsD,GAAcE,KAAa,OAASb,GAAI3C,GAAOlC,GAAK0F,EAAQ,IAG7DF,GAAcX,GAAGrD,EAAS,CAAC,EAAGkE,EAAQ,gCAK9B,MAAAiD,GAAAC,GAAMxC,GAAS,OAAQwC,EAAE,MAAM,aActCxC,GAAS,gBACD,CAAA,MAAAlE,EACP,MAAO6D,GAAO,KAAA,CAAA,EAcAgM,EAAAnJ,GAAMxC,GAAS,iBAAkBwC,EAAE,MAAM,6CArGpD3B,EAAG2F,oiDAhOJwD,EAAA,qCA6FH7J,GAAS8J,GAAmBrN,IAClCoO,2CAoBKY,GAAOzL,EAAO2J,CAAS,SAC3BA,EAAY3J,CAAA,EACZH,GAAS,QAAQ,4BAGnBJ,EAAA,GAAGrD,EAAkB4D,GAAS9D,GAAe8D,CAAiB,CAAA,mBAC9DP,EAAA,GAAGiM,EAAU1L,GAASqL,GAAA,CAAA,q2ICpER,WAAAxN,MAAO,YACb,CAAA,KAAAA,MAAO,IAAI,EACbA,EAAc,EAAA,iBACHA,EAAc,EAAA,EAAC,gBAAkB,SAC7C,SACA,0NALS,WAAAA,MAAO,YACbG,EAAA,CAAA,EAAA,WAAA,CAAA,KAAAH,MAAO,IAAI,sBACbA,EAAc,EAAA,CAAA,kCACHA,EAAc,EAAA,EAAC,gBAAkB,SAC7C,SACA,iLAQI8N,SACC,GACA,MAAA9N,MAAS,4GAATG,EAAA,CAAA,EAAA,KAAA4N,EAAA,MAAA/N,MAAS,uIAjBdA,EAAc,EAAA,GAAAoH,GAAApH,CAAA,IAYbA,EAAU,CAAA,GAAAiB,GAAAjB,CAAA,0BASR,KAAAA,MAAO,gBACDA,EAAW,CAAA,qHAMhBA,EAAM,EAAA,4DAEuBA,EAA0B,EAAA,wEAI7CA,EAAc,EAAA,GAAE,SAAW,qBAChCA,EAAc,EAAA,GAAE,SAAW,oFAIxBA,EAAc,EAAA,GAAE,eAAiB,2LAsChC,eAAAA,MAAO,0BACXA,EAAI,EAAA,mdAnElBvF,GAwEKC,EAAAmI,EAAAjI,CAAA,mDAnFAoF,EAAc,EAAA,0HAYbA,EAAU,CAAA,mHASRG,EAAA,CAAA,EAAA,YAAA6N,EAAA,KAAAhO,MAAO,8BACDA,EAAW,CAAA,+MAMhBA,EAAM,EAAA,kGAEuBA,EAA0B,EAAA,yIAI7CA,EAAc,EAAA,GAAE,SAAW,0CAChCA,EAAc,EAAA,GAAE,SAAW,4JAIxBA,EAAc,EAAA,GAAE,eAAiB,2XAsChCG,EAAA,CAAA,EAAA,YAAA6N,EAAA,eAAAhO,MAAO,2CACXA,EAAI,EAAA,wWAzFT,2GAOO,QACV,0iBAlFK,QAAAiO,EAAU,EAAA,EAAA9M,EACV,CAAA,aAAA+M,EAAA,EAAA,EAAA/M,GACA,QAAAgN,EAAU,EAAA,EAAAhN,EACV,CAAA,MAAAgB,EAAA,EAAA,EAAAhB,GACA,MAAAiN,EAAuB,IAAA,EAAAjN,GACvB,UAAAkN,EAAgC,MAAA,EAAAlN,EAChC,CAAA,MAAAmN,CAAA,EAAAnN,GACA,WAAAoN,EAAa,EAAA,EAAApN,EACb,CAAA,KAAAvE,CAAA,EAAAuE,GACA,YAAAqN,EAAc,EAAA,EAAArN,GACd,SAAAyC,EAAW,EAAA,EAAAzC,EACX,CAAA,iBAAAE,EAAA,CAA8B,OAAQ,SAAS,CAAA,EAAAF,GAC/C,eAAA+K,EAA2C,IAAA,EAAA/K,GAC3C,kBAAAiL,EAAoB,EAAA,EAAAjL,GACpB,IAAA2G,EAAM,EAAA,EAAA3G,GACN,iBAAA8C,EAAmB,EAAA,EAAA9C,GACnB,qBAAAkL,EAAuB,EAAA,EAAAlL,GACvB,cAAAqF,EAAgB,EAAA,EAAArF,GAChB,OAAAmD,EAA6B,QAAA,EAAAnD,GAC7B,KAAAiE,EAA8B,QAAA,EAAAjE,GAC9B,gBAAAuF,EAAkB,EAAA,EAAAvF,GAClB,YAAAsF,GAAc,EAAA,EAAAtF,GACd,WAAAmL,GAAa,EAAA,EAAAnL,GACb,WAAAoL,GAAa,EAAA,EAAApL,GACb,UAAAqL,GAAY,EAAA,EAAArL,GACZ,2BAAAsN,EAA6B,EAAA,EAAAtN,GAC7B,WAAA2F,GAAiC,EAAA,EAAA3F,EACjC,CAAA,iBAAAoF,CAAA,EAAApF,EAKA,CAAA,OAAAuN,CAAA,EAAAvN,EAgBPwN,GAAA,CAAA,EAOO,CAAA,cAAA5E,GAAA,CAAqD,KAAM,IAAI,CAAA,EAAA5I,GAC/D,kBAAAsL,GAAoB,EAAA,EAAAtL,GACpB,eAAAyN,GAA4C,MAAA,EAAAzN,EAC5C,CAAA,OAAA0N,EAAA,EAAA1N,EACA,CAAA,UAAA2N,EAAA,EAAA3N,EACA,CAAA,WAAA4N,EAAA,EAAA5N,EACA,CAAA,WAAA6N,EAAA,EAAA7N,GACA,SAAAgL,GAAkC,IAAA,EAAAhL,GAClC,YAAAsJ,GAA6B,IAAA,EAAAtJ,GAC7B,SAAAqJ,GAAoC,IAAA,EAAArJ,EACpC,CAAA,WAAAkE,EAAA,EAAAlE,GACA,qBAAAsE,GAAuB,EAAA,EAAAtE,GACvB,UAAAiB,GAA2B,IAAA,EAAAjB,EA0Bb,MAAA8N,GAAA,IAAAP,EAAO,SAAS,eAAgBE,EAAc,EAmEzD1J,EAAA,IAAAgK,IAASR,EAAO,OAAO,UAAUQ,CAAI,EACrClK,GAAA,IAAAkK,IAASR,EAAO,OAAO,SAASQ,CAAI,EApC/BC,GAAA,IAAAT,EAAO,SAAS,SAAUvM,CAAK,IACpCqC,GAAMkK,EAAO,SAAS,SAAUlK,EAAE,MAAM,IAC1CA,GAAMkK,EAAO,SAAS,OAAQlK,EAAE,MAAM,KACrCA,GAAMkK,EAAO,SAAS,QAASlK,EAAE,MAAM,KACvCA,GAAMkK,EAAO,SAAS,QAASlK,EAAE,MAAM,KAC9BA,GAAMkK,EAAO,SAAS,iBAAkBlK,EAAE,MAAM,KACjDA,GAAMkK,EAAO,SAAS,gBAAiBlK,EAAE,MAAM,KACvDA,GAAMkK,EAAO,SAAS,QAASlK,EAAE,MAAM,KACxCA,GAAMkK,EAAO,SAAS,OAAQlK,EAAE,MAAM,cAE/CrC,EAAK,CAAA,CAAA,EACLuM,EAAO,SAAS,OAAO,MAEdlK,GAAMkK,EAAO,SAAS,OAAQlK,EAAE,MAAM,KACtCA,GAAC,CACNrC,IAAU,MAAQA,EAAM,SAAW,IACnCiD,IAAS,WAEZxD,EAAA,EAAAO,EAAMqC,EAAE,OAAO,KAAK,EAAE,QAAUA,EAAE,OAAO,MAAKrC,CAAA,MAG9CA,EAAMqC,EAAE,OAAO,MAAM,CAAC,CAAG,EAAAA,EAAE,OAAO,MAAM,CAAC,GAAKA,EAAE,OAAO,MAAKrC,CAAA,SAG7DuM,EAAO,SAAS,OAAQlK,EAAE,MAAM,4vDAnGnC5C,EAAA,GAAG+M,GACFvJ,IAAS,SACNxH,GAAiBuE,EAAsBvF,CAAI,EAC3CO,GAAmBgF,EAAoBvF,CAAI,CAAA"}