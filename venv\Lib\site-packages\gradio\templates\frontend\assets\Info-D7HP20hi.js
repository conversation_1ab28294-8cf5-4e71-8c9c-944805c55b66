import{M as a}from"./MarkdownCode-Cdb8e5t4.js";/* empty css                                                        */const{SvelteComponent:f,attr:c,create_component:m,destroy_component:u,detach:l,element:_,flush:d,init:$,insert:g,mount_component:p,safe_not_equal:h,transition_in:w,transition_out:v}=window.__gradio__svelte__internal;function k(r){let e,n,o;return n=new a({props:{message:r[0],sanitize_html:!0}}),{c(){e=_("div"),m(n.$$.fragment),c(e,"class","svelte-j9uq24")},m(t,i){g(t,e,i),p(n,e,null),o=!0},p(t,[i]){const s={};i&1&&(s.message=t[0]),n.$set(s)},i(t){o||(w(n.$$.fragment,t),o=!0)},o(t){v(n.$$.fragment,t),o=!1},d(t){t&&l(e),u(n)}}}function x(r,e,n){let{info:o}=e;return r.$$set=t=>{"info"in t&&n(0,o=t.info)},[o]}class I extends f{constructor(e){super(),$(this,e,x,k,h,{info:0})}get info(){return this.$$.ctx[0]}set info(e){this.$$set({info:e}),d()}}export{I};
//# sourceMappingURL=Info-D7HP20hi.js.map
