import{b as A,aT as L,aa as p,h as v,an as V,ao as k}from"./index-DIZHUVg4.js";import{A as R}from"./workerPool-CfMXSLnf.js";import{GLTFLoader as F,ArrayItem as D,LoadBoundingInfoFromPositionAccessor as N}from"./glTFLoader-DezBwpju.js";import"./index-DJ2rNx9E.js";import"./svelte/svelte.js";import"./bone-BH5HAZP0.js";import"./rawTexture-Dvmk_ChV.js";import"./assetContainer-DK02JDfv.js";import"./objectModelMapping-ErbZJar3.js";function I(c,t,n,i,f){const e=c;let r=null,o=null,s=null;try{r=new e.Decoder,o=new e.<PERSON>oder<PERSON>uffer,o.Init(t,t.byteLength);let a;const l=r.GetEncodedGeometryType(o);switch(l){case e.TRIANGULAR_MESH:{const d=new e.Mesh;if(a=r.DecodeBufferToMesh(o,d),!a.ok()||d.ptr===0)throw new Error(a.error_msg());const y=d.num_faces()*3,h=y*4,u=e._malloc(h);try{r.GetTrianglesUInt32Array(d,h,u);const P=new Uint32Array(y);P.set(new Uint32Array(e.HEAPF32.buffer,u,y)),i(P)}finally{e._free(u)}s=d;break}case e.POINT_CLOUD:{const d=new e.PointCloud;if(a=r.DecodeBufferToPointCloud(o,d),!a.ok()||!d.ptr)throw new Error(a.error_msg());s=d;break}default:throw new Error(`Invalid geometry type ${l}`)}const _=s.num_points(),w=(d,m,y,h)=>{const u=h.data_type(),P=h.num_components(),B=h.normalized(),C=h.byte_stride(),O=h.byte_offset(),T={[e.DT_FLOAT32]:{typedArrayConstructor:Float32Array,heap:e.HEAPF32},[e.DT_INT8]:{typedArrayConstructor:Int8Array,heap:e.HEAP8},[e.DT_INT16]:{typedArrayConstructor:Int16Array,heap:e.HEAP16},[e.DT_INT32]:{typedArrayConstructor:Int32Array,heap:e.HEAP32},[e.DT_UINT8]:{typedArrayConstructor:Uint8Array,heap:e.HEAPU8},[e.DT_UINT16]:{typedArrayConstructor:Uint16Array,heap:e.HEAPU16},[e.DT_UINT32]:{typedArrayConstructor:Uint32Array,heap:e.HEAPU32}}[u];if(!T)throw new Error(`Invalid data type ${u}`);const U=_*P,M=U*T.typedArrayConstructor.BYTES_PER_ELEMENT,g=e._malloc(M);try{d.GetAttributeDataArrayForAllPoints(m,h,u,M,g);const G=new T.typedArrayConstructor(T.heap.buffer,g,U);f(y,G.slice(),P,O,C,B)}finally{e._free(g)}};if(n)for(const d in n){const m=n[d],y=r.GetAttributeByUniqueId(s,m);w(r,s,d,y)}else{const d={position:e.POSITION,normal:e.NORMAL,color:e.COLOR,uv:e.TEX_COORD};for(const m in d){const y=r.GetAttributeId(s,d[m]);if(y!==-1){const h=r.GetAttribute(s,y);w(r,s,m,h)}}}return _}finally{s&&e.destroy(s),o&&e.destroy(o),r&&e.destroy(r)}}function S(){let c;onmessage=t=>{const n=t.data;switch(n.id){case"init":{n.url&&importScripts(n.url);const i=n.wasmBinary?{wasmBinary:n.wasmBinary}:{};c=DracoDecoderModule(i),postMessage({id:"initDone"});break}case"decodeMesh":{if(!c)throw new Error("Draco decoder module is not available");c.then(i=>{const f=I(i,n.dataView,n.attributes,e=>{postMessage({id:"indices",data:e},[e.buffer])},(e,r,o,s,a,l)=>{postMessage({id:"attribute",kind:e,data:r,size:o,byteOffset:s,byteStride:a,normalized:l},[r.buffer])});postMessage({id:"decodeMeshDone",totalVertices:f})});break}}}}function W(c,t,n){return new Promise((i,f)=>{const e=o=>{c.removeEventListener("error",e),c.removeEventListener("message",r),f(o)},r=o=>{o.data.id==="initDone"&&(c.removeEventListener("error",e),c.removeEventListener("message",r),i(c))};if(c.addEventListener("error",e),c.addEventListener("message",r),!t)c.postMessage({id:"init",url:n});else{const o=t.slice(0);c.postMessage({id:"init",url:n,wasmBinary:o},[o])}})}function K(){return typeof navigator!="object"||!navigator.hardwareConcurrency?1:Math.min(Math.floor(navigator.hardwareConcurrency*.5),4)}function $(c){return!!(c.wasmUrl&&(c.wasmBinary||c.wasmBinaryUrl)&&typeof WebAssembly=="object"||c.fallbackUrl)}class z{constructor(t){if(t.workerPool){this._workerPoolPromise=Promise.resolve(t.workerPool);return}const n=t.wasmBinary,i=t.numWorkers??K(),f=i&&typeof Worker=="function"&&typeof URL=="function",e=f||!t.jsModule,r=t.wasmUrl&&t.wasmBinaryUrl&&typeof WebAssembly=="object"?{url:e?A.GetBabylonScriptURL(t.wasmUrl,!0):"",wasmBinaryPromise:n?Promise.resolve(n):A.LoadFileAsync(A.GetBabylonScriptURL(t.wasmBinaryUrl,!0))}:{url:e?A.GetBabylonScriptURL(t.fallbackUrl):"",wasmBinaryPromise:Promise.resolve(void 0)};f?this._workerPoolPromise=r.wasmBinaryPromise.then(o=>{const s=this._getWorkerContent(),a=URL.createObjectURL(new Blob([s],{type:"application/javascript"}));return new R(i,()=>{const l=new Worker(a);return W(l,o,r.url)})}):this._modulePromise=r.wasmBinaryPromise.then(async o=>{if(!this._isModuleAvailable()&&!t.jsModule){if(!r.url)throw new Error("Draco codec module is not available");await A.LoadBabylonScriptAsync(r.url)}return this._createModuleAsync(o,t.jsModule)})}async whenReadyAsync(){if(this._workerPoolPromise){await this._workerPoolPromise;return}if(this._modulePromise){await this._modulePromise;return}}dispose(){this._workerPoolPromise&&this._workerPoolPromise.then(t=>{t.dispose()}),delete this._workerPoolPromise,delete this._modulePromise}}class b extends z{static get DefaultAvailable(){return $(b.DefaultConfiguration)}static get Default(){return b._Default??(b._Default=new b),b._Default}static ResetDefault(t){b._Default&&(t||b._Default.dispose(),b._Default=null)}_isModuleAvailable(){return typeof DracoDecoderModule<"u"}async _createModuleAsync(t,n){return{module:await(n||DracoDecoderModule)({wasmBinary:t})}}_getWorkerContent(){return`${I}(${S})()`}constructor(t=b.DefaultConfiguration){super(t)}decodeMeshToMeshDataAsync(t,n,i){const f=t instanceof ArrayBuffer?new Int8Array(t):new Int8Array(t.buffer,t.byteOffset,t.byteLength),e=(r,o)=>i&&i[r]!==void 0?(o!==i[r]&&v.Warn(`Normalized flag from Draco data (${o}) does not match normalized flag from glTF accessor (${i[r]}). Using flag from glTF accessor.`),i[r]):o;if(this._workerPoolPromise)return this._workerPoolPromise.then(r=>new Promise((o,s)=>{r.push((a,l)=>{let _=null;const w=[],d=h=>{a.removeEventListener("error",d),a.removeEventListener("message",m),s(h),l()},m=h=>{const u=h.data;switch(u.id){case"indices":{_=u.data;break}case"attribute":{w.push({kind:u.kind,data:u.data,size:u.size,byteOffset:u.byteOffset,byteStride:u.byteStride,normalized:e(u.kind,u.normalized)});break}case"decodeMeshDone":{a.removeEventListener("error",d),a.removeEventListener("message",m),o({indices:_,attributes:w,totalVertices:u.totalVertices}),l();break}}};a.addEventListener("error",d),a.addEventListener("message",m);const y=f.slice();a.postMessage({id:"decodeMesh",dataView:y,attributes:n},[y.buffer])})}));if(this._modulePromise)return this._modulePromise.then(r=>{let o=null;const s=[],a=I(r.module,f,n,l=>{o=l},(l,_,w,d,m,y)=>{s.push({kind:l,data:_,size:w,byteOffset:d,byteStride:m,normalized:y})});return{indices:o,attributes:s,totalVertices:a}});throw new Error("Draco decoder module is not available")}async decodeMeshToGeometryAsync(t,n,i,f){const e=await this.decodeMeshToMeshDataAsync(i,f),r=new L(t,n);e.indices&&r.setIndices(e.indices);for(const o of e.attributes)r.setVerticesBuffer(new p(n.getEngine(),o.data,o.kind,!1,void 0,o.byteStride,void 0,o.byteOffset,o.size,void 0,o.normalized,!0),e.totalVertices);return r}async _decodeMeshToGeometryForGltfAsync(t,n,i,f,e,r){const o=await this.decodeMeshToMeshDataAsync(i,f,e),s=new L(t,n);r&&(s._boundingInfo=r,s.useBoundingInfoFromGeometry=!0),o.indices&&s.setIndices(o.indices);for(const a of o.attributes)s.setVerticesBuffer(new p(n.getEngine(),a.data,a.kind,!1,void 0,a.byteStride,void 0,a.byteOffset,a.size,void 0,a.normalized,!0),o.totalVertices);return s}}b.DefaultConfiguration={wasmUrl:`${A._DefaultCdnUrl}/draco_wasm_wrapper_gltf.js`,wasmBinaryUrl:`${A._DefaultCdnUrl}/draco_decoder_gltf.wasm`,fallbackUrl:`${A._DefaultCdnUrl}/draco_decoder_gltf.js`};b._Default=null;const E="KHR_draco_mesh_compression";class H{constructor(t){this.name=E,this.useNormalizedFlagFromAccessor=!0,this._loader=t,this.enabled=b.DefaultAvailable&&this._loader.isExtensionUsed(E)}dispose(){delete this.dracoDecoder,this._loader=null}_loadVertexDataAsync(t,n,i){return F.LoadExtensionAsync(t,n,this.name,(f,e)=>{if(n.mode!=null&&n.mode!==4&&n.mode!==5)throw new Error(`${t}: Unsupported mode ${n.mode}`);const r={},o={},s=(l,_)=>{const w=e.attributes[l];if(w!=null&&(i._delayInfo=i._delayInfo||[],i._delayInfo.indexOf(_)===-1&&i._delayInfo.push(_),r[_]=w,this.useNormalizedFlagFromAccessor)){const d=D.TryGet(this._loader.gltf.accessors,n.attributes[l]);d&&(o[_]=d.normalized||!1)}};s("POSITION",p.PositionKind),s("NORMAL",p.NormalKind),s("TANGENT",p.TangentKind),s("TEXCOORD_0",p.UVKind),s("TEXCOORD_1",p.UV2Kind),s("TEXCOORD_2",p.UV3Kind),s("TEXCOORD_3",p.UV4Kind),s("TEXCOORD_4",p.UV5Kind),s("TEXCOORD_5",p.UV6Kind),s("JOINTS_0",p.MatricesIndicesKind),s("WEIGHTS_0",p.MatricesWeightsKind),s("COLOR_0",p.ColorKind);const a=D.Get(f,this._loader.gltf.bufferViews,e.bufferView);return a._dracoBabylonGeometry||(a._dracoBabylonGeometry=this._loader.loadBufferViewAsync(`/bufferViews/${a.index}`,a).then(l=>{const _=this.dracoDecoder||b.Default,w=D.TryGet(this._loader.gltf.accessors,n.attributes.POSITION),d=!this._loader.parent.alwaysComputeBoundingBox&&!i.skeleton&&w?N(w):null;return _._decodeMeshToGeometryForGltfAsync(i.name,this._loader.babylonScene,l,r,o,d).catch(m=>{throw new Error(`${t}: ${m.message}`)})})),a._dracoBabylonGeometry})}}V(E);k(E,!0,c=>new H(c));export{H as KHR_draco_mesh_compression};
//# sourceMappingURL=KHR_draco_mesh_compression-D9kjSflS.js.map
