{"version": 3, "file": "KHR_materials_anisotropy-9gSGbNdT.js", "sources": ["../../../../node_modules/.pnpm/@babylonjs+loaders@8.2.0_@babylonjs+core@8.2.0_babylonjs-gltf2interface@7.25.2/node_modules/@babylonjs/loaders/glTF/2.0/Extensions/KHR_materials_anisotropy.js"], "sourcesContent": ["import { PBRMaterial } from \"@babylonjs/core/Materials/PBR/pbrMaterial.js\";\nimport { GLTFLoader } from \"../glTFLoader.js\";\nimport { registerGLTFExtension, unregisterGLTFExtension } from \"../glTFLoaderExtensionRegistry.js\";\nconst NAME = \"KHR_materials_anisotropy\";\n/**\n * [Specification](https://github.com/KhronosGroup/glTF/tree/main/extensions/2.0/Khronos/KHR_materials_anisotropy)\n */\n// eslint-disable-next-line @typescript-eslint/naming-convention\nexport class KHR_materials_anisotropy {\n    /**\n     * @internal\n     */\n    constructor(loader) {\n        /**\n         * The name of this extension.\n         */\n        this.name = NAME;\n        /**\n         * Defines a number that determines the order the extensions are applied.\n         */\n        this.order = 195;\n        this._loader = loader;\n        this.enabled = this._loader.isExtensionUsed(NAME);\n    }\n    /** @internal */\n    dispose() {\n        this._loader = null;\n    }\n    /**\n     * @internal\n     */\n    loadMaterialPropertiesAsync(context, material, babylonMaterial) {\n        return GLTFLoader.LoadExtensionAsync(context, material, this.name, (extensionContext, extension) => {\n            const promises = new Array();\n            promises.push(this._loader.loadMaterialPropertiesAsync(context, material, babylonMaterial));\n            promises.push(this._loadIridescencePropertiesAsync(extensionContext, extension, babylonMaterial));\n            return Promise.all(promises).then(() => { });\n        });\n    }\n    _loadIridescencePropertiesAsync(context, properties, babylonMaterial) {\n        if (!(babylonMaterial instanceof PBRMaterial)) {\n            throw new Error(`${context}: Material type not supported`);\n        }\n        const promises = new Array();\n        babylonMaterial.anisotropy.isEnabled = true;\n        babylonMaterial.anisotropy.intensity = properties.anisotropyStrength ?? 0;\n        babylonMaterial.anisotropy.angle = properties.anisotropyRotation ?? 0;\n        if (properties.anisotropyTexture) {\n            properties.anisotropyTexture.nonColorData = true;\n            promises.push(this._loader.loadTextureInfoAsync(`${context}/anisotropyTexture`, properties.anisotropyTexture, (texture) => {\n                texture.name = `${babylonMaterial.name} (Anisotropy Intensity)`;\n                babylonMaterial.anisotropy.texture = texture;\n            }));\n        }\n        return Promise.all(promises).then(() => { });\n    }\n}\nunregisterGLTFExtension(NAME);\nregisterGLTFExtension(NAME, true, (loader) => new KHR_materials_anisotropy(loader));\n//# sourceMappingURL=KHR_materials_anisotropy.js.map"], "names": ["NAME", "KHR_materials_anisotropy", "loader", "context", "material", "babylonMaterial", "GLTFLoader", "extensionContext", "extension", "promises", "properties", "PBRMaterial", "texture", "unregisterGLTFExtension", "registerGLTFExtension"], "mappings": "gTAGA,MAAMA,EAAO,2BAKN,MAAMC,CAAyB,CAIlC,YAAYC,EAAQ,CAIhB,KAAK,KAAOF,EAIZ,KAAK,MAAQ,IACb,KAAK,QAAUE,EACf,KAAK,QAAU,KAAK,QAAQ,gBAAgBF,CAAI,CACnD,CAED,SAAU,CACN,KAAK,QAAU,IAClB,CAID,4BAA4BG,EAASC,EAAUC,EAAiB,CAC5D,OAAOC,EAAW,mBAAmBH,EAASC,EAAU,KAAK,KAAM,CAACG,EAAkBC,IAAc,CAChG,MAAMC,EAAW,IAAI,MACrB,OAAAA,EAAS,KAAK,KAAK,QAAQ,4BAA4BN,EAASC,EAAUC,CAAe,CAAC,EAC1FI,EAAS,KAAK,KAAK,gCAAgCF,EAAkBC,EAAWH,CAAe,CAAC,EACzF,QAAQ,IAAII,CAAQ,EAAE,KAAK,IAAM,CAAA,CAAG,CACvD,CAAS,CACJ,CACD,gCAAgCN,EAASO,EAAYL,EAAiB,CAClE,GAAI,EAAEA,aAA2BM,GAC7B,MAAM,IAAI,MAAM,GAAGR,CAAO,+BAA+B,EAE7D,MAAMM,EAAW,IAAI,MACrB,OAAAJ,EAAgB,WAAW,UAAY,GACvCA,EAAgB,WAAW,UAAYK,EAAW,oBAAsB,EACxEL,EAAgB,WAAW,MAAQK,EAAW,oBAAsB,EAChEA,EAAW,oBACXA,EAAW,kBAAkB,aAAe,GAC5CD,EAAS,KAAK,KAAK,QAAQ,qBAAqB,GAAGN,CAAO,qBAAsBO,EAAW,kBAAoBE,GAAY,CACvHA,EAAQ,KAAO,GAAGP,EAAgB,IAAI,0BACtCA,EAAgB,WAAW,QAAUO,CACxC,CAAA,CAAC,GAEC,QAAQ,IAAIH,CAAQ,EAAE,KAAK,IAAM,CAAA,CAAG,CAC9C,CACL,CACAI,EAAwBb,CAAI,EAC5Bc,EAAsBd,EAAM,GAAOE,GAAW,IAAID,EAAyBC,CAAM,CAAC", "x_google_ignoreList": [0]}