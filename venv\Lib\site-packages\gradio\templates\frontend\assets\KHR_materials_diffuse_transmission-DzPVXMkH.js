import{ar as c,C as f,an as l,ao as m}from"./index-DIZHUVg4.js";import{GLTFLoader as d}from"./glTFLoader-DezBwpju.js";import"./index-DJ2rNx9E.js";import"./svelte/svelte.js";import"./bone-BH5HAZP0.js";import"./rawTexture-Dvmk_ChV.js";import"./assetContainer-DK02JDfv.js";import"./objectModelMapping-ErbZJar3.js";const u="KHR_materials_diffuse_transmission";class T{constructor(e){this.name=u,this.order=174,this._loader=e,this.enabled=this._loader.isExtensionUsed(u),this.enabled&&(e.parent.transparencyAsCoverage=!0)}dispose(){this._loader=null}loadMaterialPropertiesAsync(e,o,n){return d.LoadExtensionAsync(e,o,this.name,(r,s)=>{const i=new Array;return i.push(this._loader.loadMaterialPropertiesAsync(e,o,n)),i.push(this._loadTranslucentPropertiesAsync(r,o,n,s)),Promise.all(i).then(()=>{})})}_loadTranslucentPropertiesAsync(e,o,n,r){if(!(n instanceof c))throw new Error(`${e}: Material type not supported`);const s=n;if(s.subSurface.isTranslucencyEnabled=!0,s.subSurface.volumeIndexOfRefraction=1,s.subSurface.minimumThickness=0,s.subSurface.maximumThickness=0,s.subSurface.useAlbedoToTintTranslucency=!1,r.diffuseTransmissionFactor!==void 0)s.subSurface.translucencyIntensity=r.diffuseTransmissionFactor;else return s.subSurface.translucencyIntensity=0,s.subSurface.isTranslucencyEnabled=!1,Promise.resolve();const i=new Array;return s.subSurface.useGltfStyleTextures=!0,r.diffuseTransmissionTexture&&(r.diffuseTransmissionTexture.nonColorData=!0,i.push(this._loader.loadTextureInfoAsync(`${e}/diffuseTransmissionTexture`,r.diffuseTransmissionTexture).then(a=>{a.name=`${n.name} (Diffuse Transmission)`,s.subSurface.translucencyIntensityTexture=a}))),r.diffuseTransmissionColorFactor!==void 0?s.subSurface.translucencyColor=f.FromArray(r.diffuseTransmissionColorFactor):s.subSurface.translucencyColor=f.White(),r.diffuseTransmissionColorTexture&&i.push(this._loader.loadTextureInfoAsync(`${e}/diffuseTransmissionColorTexture`,r.diffuseTransmissionColorTexture).then(a=>{a.name=`${n.name} (Diffuse Transmission Color)`,s.subSurface.translucencyColorTexture=a})),Promise.all(i).then(()=>{})}}l(u);m(u,!0,t=>new T(t));export{T as KHR_materials_diffuse_transmission};
//# sourceMappingURL=KHR_materials_diffuse_transmission-DzPVXMkH.js.map
