{"version": 3, "file": "KHR_materials_dispersion-CpvMJ7hh.js", "sources": ["../../../../node_modules/.pnpm/@babylonjs+loaders@8.2.0_@babylonjs+core@8.2.0_babylonjs-gltf2interface@7.25.2/node_modules/@babylonjs/loaders/glTF/2.0/Extensions/KHR_materials_dispersion.js"], "sourcesContent": ["import { PBRMaterial } from \"@babylonjs/core/Materials/PBR/pbrMaterial.js\";\nimport { GLTFLoader } from \"../glTFLoader.js\";\nimport { registerGLTFExtension, unregisterGLTFExtension } from \"../glTFLoaderExtensionRegistry.js\";\nconst NAME = \"KHR_materials_dispersion\";\n/**\n * [Specification](https://github.com/KhronosGroup/glTF/blob/87bd64a7f5e23c84b6aef2e6082069583ed0ddb4/extensions/2.0/Khronos/KHR_materials_dispersion/README.md)\n * @experimental\n */\n// eslint-disable-next-line @typescript-eslint/naming-convention\nexport class KHR_materials_dispersion {\n    /**\n     * @internal\n     */\n    constructor(loader) {\n        /**\n         * The name of this extension.\n         */\n        this.name = NAME;\n        /**\n         * Defines a number that determines the order the extensions are applied.\n         */\n        this.order = 174;\n        this._loader = loader;\n        this.enabled = this._loader.isExtensionUsed(NAME);\n    }\n    /** @internal */\n    dispose() {\n        this._loader = null;\n    }\n    /**\n     * @internal\n     */\n    loadMaterialPropertiesAsync(context, material, babylonMaterial) {\n        return GLTFLoader.LoadExtensionAsync(context, material, this.name, (extensionContext, extension) => {\n            const promises = new Array();\n            promises.push(this._loader.loadMaterialPropertiesAsync(context, material, babylonMaterial));\n            promises.push(this._loadDispersionPropertiesAsync(extensionContext, material, babylonMaterial, extension));\n            return Promise.all(promises).then(() => { });\n        });\n    }\n    _loadDispersionPropertiesAsync(context, material, babylonMaterial, extension) {\n        if (!(babylonMaterial instanceof PBRMaterial)) {\n            throw new Error(`${context}: Material type not supported`);\n        }\n        // If transparency isn't enabled already, this extension shouldn't do anything.\n        // i.e. it requires either the KHR_materials_transmission or KHR_materials_diffuse_transmission extensions.\n        if (!babylonMaterial.subSurface.isRefractionEnabled || !extension.dispersion) {\n            return Promise.resolve();\n        }\n        babylonMaterial.subSurface.isDispersionEnabled = true;\n        babylonMaterial.subSurface.dispersion = extension.dispersion;\n        return Promise.resolve();\n    }\n}\nunregisterGLTFExtension(NAME);\nregisterGLTFExtension(NAME, true, (loader) => new KHR_materials_dispersion(loader));\n//# sourceMappingURL=KHR_materials_dispersion.js.map"], "names": ["NAME", "KHR_materials_dispersion", "loader", "context", "material", "babylonMaterial", "GLTFLoader", "extensionContext", "extension", "promises", "PBRMaterial", "unregisterGLTFExtension", "registerGLTFExtension"], "mappings": "gTAGA,MAAMA,EAAO,2BAMN,MAAMC,CAAyB,CAIlC,YAAYC,EAAQ,CAIhB,KAAK,KAAOF,EAIZ,KAAK,MAAQ,IACb,KAAK,QAAUE,EACf,KAAK,QAAU,KAAK,QAAQ,gBAAgBF,CAAI,CACnD,CAED,SAAU,CACN,KAAK,QAAU,IAClB,CAID,4BAA4BG,EAASC,EAAUC,EAAiB,CAC5D,OAAOC,EAAW,mBAAmBH,EAASC,EAAU,KAAK,KAAM,CAACG,EAAkBC,IAAc,CAChG,MAAMC,EAAW,IAAI,MACrB,OAAAA,EAAS,KAAK,KAAK,QAAQ,4BAA4BN,EAASC,EAAUC,CAAe,CAAC,EAC1FI,EAAS,KAAK,KAAK,+BAA+BF,EAAkBH,EAAUC,EAAiBG,CAAS,CAAC,EAClG,QAAQ,IAAIC,CAAQ,EAAE,KAAK,IAAM,CAAA,CAAG,CACvD,CAAS,CACJ,CACD,+BAA+BN,EAASC,EAAUC,EAAiBG,EAAW,CAC1E,GAAI,EAAEH,aAA2BK,GAC7B,MAAM,IAAI,MAAM,GAAGP,CAAO,+BAA+B,EAI7D,MAAI,CAACE,EAAgB,WAAW,qBAAuB,CAACG,EAAU,aAGlEH,EAAgB,WAAW,oBAAsB,GACjDA,EAAgB,WAAW,WAAaG,EAAU,YAC3C,QAAQ,SAClB,CACL,CACAG,EAAwBX,CAAI,EAC5BY,EAAsBZ,EAAM,GAAOE,GAAW,IAAID,EAAyBC,CAAM,CAAC", "x_google_ignoreList": [0]}