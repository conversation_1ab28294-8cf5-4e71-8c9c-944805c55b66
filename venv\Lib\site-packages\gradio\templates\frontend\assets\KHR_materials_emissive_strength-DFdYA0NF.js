import{ar as a,an as m,ao as d}from"./index-DIZHUVg4.js";import{GLTFLoader as h}from"./glTFLoader-DezBwpju.js";import"./index-DJ2rNx9E.js";import"./svelte/svelte.js";import"./bone-BH5HAZP0.js";import"./rawTexture-Dvmk_ChV.js";import"./assetContainer-DK02JDfv.js";import"./objectModelMapping-ErbZJar3.js";const t="KHR_materials_emissive_strength";class p{constructor(e){this.name=t,this.order=170,this._loader=e,this.enabled=this._loader.isExtensionUsed(t)}dispose(){this._loader=null}loadMaterialPropertiesAsync(e,s,i){return h.LoadExtensionAsync(e,s,this.name,(o,n)=>this._loader.loadMaterialPropertiesAsync(e,s,i).then(()=>{this._loadEmissiveProperties(o,n,i)}))}_loadEmissiveProperties(e,s,i){if(!(i instanceof a))throw new Error(`${e}: Material type not supported`);s.emissiveStrength!==void 0&&(i.emissiveIntensity=s.emissiveStrength)}}m(t);d(t,!0,r=>new p(r));export{p as KHR_materials_emissive_strength};
//# sourceMappingURL=KHR_materials_emissive_strength-DFdYA0NF.js.map
