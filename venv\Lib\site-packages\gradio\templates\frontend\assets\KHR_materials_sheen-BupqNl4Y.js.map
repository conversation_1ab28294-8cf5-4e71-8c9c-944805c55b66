{"version": 3, "file": "KHR_materials_sheen-BupqNl4Y.js", "sources": ["../../../../node_modules/.pnpm/@babylonjs+loaders@8.2.0_@babylonjs+core@8.2.0_babylonjs-gltf2interface@7.25.2/node_modules/@babylonjs/loaders/glTF/2.0/Extensions/KHR_materials_sheen.js"], "sourcesContent": ["import { PBRMaterial } from \"@babylonjs/core/Materials/PBR/pbrMaterial.js\";\nimport { GLTFLoader } from \"../glTFLoader.js\";\nimport { Color3 } from \"@babylonjs/core/Maths/math.color.js\";\nimport { registerGLTFExtension, unregisterGLTFExtension } from \"../glTFLoaderExtensionRegistry.js\";\nconst NAME = \"KHR_materials_sheen\";\n/**\n * [Specification](https://github.com/KhronosGroup/glTF/blob/main/extensions/2.0/Khronos/KHR_materials_sheen/README.md)\n * [Playground Sample](https://www.babylonjs-playground.com/frame.html#BNIZX6#4)\n */\n// eslint-disable-next-line @typescript-eslint/naming-convention\nexport class KHR_materials_sheen {\n    /**\n     * @internal\n     */\n    constructor(loader) {\n        /**\n         * The name of this extension.\n         */\n        this.name = NAME;\n        /**\n         * Defines a number that determines the order the extensions are applied.\n         */\n        this.order = 190;\n        this._loader = loader;\n        this.enabled = this._loader.isExtensionUsed(NAME);\n    }\n    /** @internal */\n    dispose() {\n        this._loader = null;\n    }\n    /**\n     * @internal\n     */\n    loadMaterialPropertiesAsync(context, material, babylonMaterial) {\n        return GLTFLoader.LoadExtensionAsync(context, material, this.name, (extensionContext, extension) => {\n            const promises = new Array();\n            promises.push(this._loader.loadMaterialPropertiesAsync(context, material, babylonMaterial));\n            promises.push(this._loadSheenPropertiesAsync(extensionContext, extension, babylonMaterial));\n            return Promise.all(promises).then(() => { });\n        });\n    }\n    _loadSheenPropertiesAsync(context, properties, babylonMaterial) {\n        if (!(babylonMaterial instanceof PBRMaterial)) {\n            throw new Error(`${context}: Material type not supported`);\n        }\n        const promises = new Array();\n        babylonMaterial.sheen.isEnabled = true;\n        babylonMaterial.sheen.intensity = 1;\n        if (properties.sheenColorFactor != undefined) {\n            babylonMaterial.sheen.color = Color3.FromArray(properties.sheenColorFactor);\n        }\n        else {\n            babylonMaterial.sheen.color = Color3.Black();\n        }\n        if (properties.sheenColorTexture) {\n            promises.push(this._loader.loadTextureInfoAsync(`${context}/sheenColorTexture`, properties.sheenColorTexture, (texture) => {\n                texture.name = `${babylonMaterial.name} (Sheen Color)`;\n                babylonMaterial.sheen.texture = texture;\n            }));\n        }\n        if (properties.sheenRoughnessFactor !== undefined) {\n            babylonMaterial.sheen.roughness = properties.sheenRoughnessFactor;\n        }\n        else {\n            babylonMaterial.sheen.roughness = 0;\n        }\n        if (properties.sheenRoughnessTexture) {\n            properties.sheenRoughnessTexture.nonColorData = true;\n            promises.push(this._loader.loadTextureInfoAsync(`${context}/sheenRoughnessTexture`, properties.sheenRoughnessTexture, (texture) => {\n                texture.name = `${babylonMaterial.name} (Sheen Roughness)`;\n                babylonMaterial.sheen.textureRoughness = texture;\n            }));\n        }\n        babylonMaterial.sheen.albedoScaling = true;\n        babylonMaterial.sheen.useRoughnessFromMainTexture = false;\n        return Promise.all(promises).then(() => { });\n    }\n}\nunregisterGLTFExtension(NAME);\nregisterGLTFExtension(NAME, true, (loader) => new KHR_materials_sheen(loader));\n//# sourceMappingURL=KHR_materials_sheen.js.map"], "names": ["NAME", "KHR_materials_sheen", "loader", "context", "material", "babylonMaterial", "GLTFLoader", "extensionContext", "extension", "promises", "properties", "PBRMaterial", "Color3", "texture", "unregisterGLTFExtension", "registerGLTFExtension"], "mappings": "uTAIA,MAAMA,EAAO,sBAMN,MAAMC,CAAoB,CAI7B,YAAYC,EAAQ,CAIhB,KAAK,KAAOF,EAIZ,KAAK,MAAQ,IACb,KAAK,QAAUE,EACf,KAAK,QAAU,KAAK,QAAQ,gBAAgBF,CAAI,CACnD,CAED,SAAU,CACN,KAAK,QAAU,IAClB,CAID,4BAA4BG,EAASC,EAAUC,EAAiB,CAC5D,OAAOC,EAAW,mBAAmBH,EAASC,EAAU,KAAK,KAAM,CAACG,EAAkBC,IAAc,CAChG,MAAMC,EAAW,IAAI,MACrB,OAAAA,EAAS,KAAK,KAAK,QAAQ,4BAA4BN,EAASC,EAAUC,CAAe,CAAC,EAC1FI,EAAS,KAAK,KAAK,0BAA0BF,EAAkBC,EAAWH,CAAe,CAAC,EACnF,QAAQ,IAAII,CAAQ,EAAE,KAAK,IAAM,CAAA,CAAG,CACvD,CAAS,CACJ,CACD,0BAA0BN,EAASO,EAAYL,EAAiB,CAC5D,GAAI,EAAEA,aAA2BM,GAC7B,MAAM,IAAI,MAAM,GAAGR,CAAO,+BAA+B,EAE7D,MAAMM,EAAW,IAAI,MACrB,OAAAJ,EAAgB,MAAM,UAAY,GAClCA,EAAgB,MAAM,UAAY,EAC9BK,EAAW,kBAAoB,KAC/BL,EAAgB,MAAM,MAAQO,EAAO,UAAUF,EAAW,gBAAgB,EAG1EL,EAAgB,MAAM,MAAQO,EAAO,MAAK,EAE1CF,EAAW,mBACXD,EAAS,KAAK,KAAK,QAAQ,qBAAqB,GAAGN,CAAO,qBAAsBO,EAAW,kBAAoBG,GAAY,CACvHA,EAAQ,KAAO,GAAGR,EAAgB,IAAI,iBACtCA,EAAgB,MAAM,QAAUQ,CACnC,CAAA,CAAC,EAEFH,EAAW,uBAAyB,OACpCL,EAAgB,MAAM,UAAYK,EAAW,qBAG7CL,EAAgB,MAAM,UAAY,EAElCK,EAAW,wBACXA,EAAW,sBAAsB,aAAe,GAChDD,EAAS,KAAK,KAAK,QAAQ,qBAAqB,GAAGN,CAAO,yBAA0BO,EAAW,sBAAwBG,GAAY,CAC/HA,EAAQ,KAAO,GAAGR,EAAgB,IAAI,qBACtCA,EAAgB,MAAM,iBAAmBQ,CAC5C,CAAA,CAAC,GAENR,EAAgB,MAAM,cAAgB,GACtCA,EAAgB,MAAM,4BAA8B,GAC7C,QAAQ,IAAII,CAAQ,EAAE,KAAK,IAAM,CAAA,CAAG,CAC9C,CACL,CACAK,EAAwBd,CAAI,EAC5Be,EAAsBf,EAAM,GAAOE,GAAW,IAAID,EAAoBC,CAAM,CAAC", "x_google_ignoreList": [0]}