import{ar as a,an as c,ao as h}from"./index-DIZHUVg4.js";import{GLTFLoader as d}from"./glTFLoader-DezBwpju.js";import"./index-DJ2rNx9E.js";import"./svelte/svelte.js";import"./bone-BH5HAZP0.js";import"./rawTexture-Dvmk_ChV.js";import"./assetContainer-DK02JDfv.js";import"./objectModelMapping-ErbZJar3.js";const i="KHR_materials_volume";class m{constructor(t){this.name=i,this.order=173,this._loader=t,this.enabled=this._loader.isExtensionUsed(i),this.enabled&&this._loader._disableInstancedMesh++}dispose(){this.enabled&&this._loader._disableInstancedMesh--,this._loader=null}loadMaterialPropertiesAsync(t,o,e){return d.LoadExtensionAsync(t,o,this.name,(s,u)=>{const r=new Array;return r.push(this._loader.loadMaterialPropertiesAsync(t,o,e)),r.push(this._loadVolumePropertiesAsync(s,o,e,u)),Promise.all(r).then(()=>{})})}_loadVolumePropertiesAsync(t,o,e,s){if(!(e instanceof a))throw new Error(`${t}: Material type not supported`);if(!e.subSurface.isRefractionEnabled&&!e.subSurface.isTranslucencyEnabled||!s.thicknessFactor)return Promise.resolve();e.subSurface.volumeIndexOfRefraction=e.indexOfRefraction;const u=s.attenuationDistance!==void 0?s.attenuationDistance:Number.MAX_VALUE;return e.subSurface.tintColorAtDistance=u,s.attenuationColor!==void 0&&s.attenuationColor.length==3&&e.subSurface.tintColor.copyFromFloats(s.attenuationColor[0],s.attenuationColor[1],s.attenuationColor[2]),e.subSurface.minimumThickness=0,e.subSurface.maximumThickness=s.thicknessFactor,e.subSurface.useThicknessAsDepth=!0,s.thicknessTexture?(s.thicknessTexture.nonColorData=!0,this._loader.loadTextureInfoAsync(`${t}/thicknessTexture`,s.thicknessTexture).then(r=>{r.name=`${e.name} (Thickness)`,e.subSurface.thicknessTexture=r,e.subSurface.useGltfStyleTextures=!0})):Promise.resolve()}}c(i);h(i,!0,n=>new m(n));export{m as KHR_materials_volume};
//# sourceMappingURL=KHR_materials_volume-DVVdXHuU.js.map
