import{an as d,ao as u}from"./index-DIZHUVg4.js";import{a as p}from"./declarationMapper-BQSrvbt0.js";import{A as b}from"./objectModelMapping-ErbZJar3.js";import"./index-DJ2rNx9E.js";import"./svelte/svelte.js";const t="KHR_node_selectability";p("event/onSelect",t,{blocks:["FlowGraphMeshPickEventBlock","FlowGraphGetVariableBlock","FlowGraphIndexOfBlock","KHR_interactivity/FlowGraphGLTFDataProvider"],configuration:{stopPropagation:{name:"stopPropagation"},nodeIndex:{name:"variable",toBlock:"FlowGraphGetVariableBlock",dataTransformer(o){return["pickedMesh_"+o[0]]}}},outputs:{values:{selectedNodeIndex:{name:"index",toBlock:"FlowGraphIndexOfBlock"},controllerIndex:{name:"pointerId"},selectionPoint:{name:"pickedPoint"},selectionRayOrigin:{name:"pickOrigin"}},flows:{out:{name:"done"}}},interBlockConnectors:[{input:"asset",output:"value",inputBlockIndex:0,outputBlockIndex:1,isVariable:!0},{input:"array",output:"nodes",inputBlockIndex:2,outputBlockIndex:3,isVariable:!0},{input:"object",output:"pickedMesh",inputBlockIndex:2,outputBlockIndex:0,isVariable:!0}],extraProcessor(o,e,i,m,a,c,r){const s=a[a.length-1];s.config=s.config||{},s.config.glTF=r;const n=o.configuration?.nodeIndex?.value[0];if(n===void 0||typeof n!="number")throw new Error("nodeIndex not found in configuration");const l="pickedMesh_"+n;return a[1].config.variable=l,c._userVariables[l]={className:"Mesh",id:r?.nodes?.[n]._babylonTransformNode?.id,uniqueId:r?.nodes?.[n]._babylonTransformNode?.uniqueId},a}});b("/nodes/{}/extensions/KHR_node_selectability/selectable",{get:o=>{const e=o._babylonTransformNode;return e&&e.isPickable!==void 0?e.isPickable:!0},set:(o,e)=>{e._primitiveBabylonMeshes?.forEach(i=>{i.isPickable=o})},getTarget:o=>o._babylonTransformNode,getPropertyName:[()=>"isPickable"],type:"boolean"});class f{constructor(e){this.name=t,this._loader=e,this.enabled=e.isExtensionUsed(t)}async onReady(){this._loader.gltf.nodes?.forEach(e=>{e.extensions?.KHR_node_selectability&&e.extensions?.KHR_node_selectability.selectable===!1&&e._babylonTransformNode?.getChildMeshes().forEach(i=>{i.isPickable=!1})})}dispose(){this._loader=null}}d(t);u(t,!0,o=>new f(o));export{f as KHR_node_selectability};
//# sourceMappingURL=KHR_node_selectability-CtJoI6-o.js.map
