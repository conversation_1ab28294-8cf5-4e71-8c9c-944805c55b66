import{ar as d,an as a,ao as u}from"./index-DIZHUVg4.js";import{GLTFLoader as f}from"./glTFLoader-DezBwpju.js";import"./index-DJ2rNx9E.js";import"./svelte/svelte.js";import"./bone-BH5HAZP0.js";import"./rawTexture-Dvmk_ChV.js";import"./assetContainer-DK02JDfv.js";import"./objectModelMapping-ErbZJar3.js";const o="MSFT_sRGBFactors";class m{constructor(r){this.name=o,this._loader=r,this.enabled=this._loader.isExtensionUsed(o)}dispose(){this._loader=null}loadMaterialPropertiesAsync(r,s,e){return f.LoadExtraAsync(r,s,this.name,(n,c)=>{if(c){if(!(e instanceof d))throw new Error(`${n}: Material type not supported`);const p=this._loader.loadMaterialPropertiesAsync(r,s,e),i=e.getScene().getEngine().useExactSrgbConversions;return e.albedoTexture||e.albedoColor.toLinearSpaceToRef(e.albedoColor,i),e.reflectivityTexture||e.reflectivityColor.toLinearSpaceToRef(e.reflectivityColor,i),p}return null})}}a(o);u(o,!0,t=>new m(t));export{m as MSFT_sRGBFactors};
//# sourceMappingURL=MSFT_sRGBFactors-D6fNrr2P.js.map
