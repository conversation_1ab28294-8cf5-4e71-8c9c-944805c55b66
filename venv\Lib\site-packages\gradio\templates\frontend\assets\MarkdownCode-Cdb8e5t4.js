const __vite__fileDeps=["./katex-CIJ2fXeA.css","./auto-render-8xbOkcXl.js","./katex-rPiVaalG.js","./mermaid.core-iWqruL5K.js","./index-DJ2rNx9E.js","./index-rsZ55Oi2.css","./dispatch-kxCwF96_.js","./step-Ce-xBr2D.js","./select-BigU4G0v.js"],__vite__mapDeps=i=>i.map(i=>__vite__fileDeps[i]);
import{_}from"./index-DJ2rNx9E.js";import{A as H,c as I}from"./MarkdownCode.svelte_svelte_type_style_lang-Bl8p72Ow.js";const V=(a,e=location.href)=>{try{return!!a&&new URL(a).origin!==new URL(e).origin}catch{return!1}};function A(a){const e=new H,r=new DOMParser().parseFromString(a,"text/html");return O(r.body,"A",n=>{n instanceof HTMLElement&&"target"in n&&V(n.getAttribute("href"),location.href)&&(n.setAttribute("target","_blank"),n.setAttribute("rel","noopener noreferrer"))}),e.sanitize(r).body.innerHTML}function O(a,e,r){a&&(a.nodeName===e||typeof e=="function")&&r(a);const n=a?.childNodes||[];for(let l=0;l<n.length;l++)O(n[l],e,r)}const E=["!--","!doctype","a","abbr","acronym","address","applet","area","article","aside","audio","b","base","basefont","bdi","bdo","big","blockquote","body","br","button","canvas","caption","center","cite","code","col","colgroup","data","datalist","dd","del","details","dfn","dialog","dir","div","dl","dt","em","embed","fieldset","figcaption","figure","font","footer","form","frame","frameset","h1","h2","h3","h4","h5","h6","head","header","hgroup","hr","html","i","iframe","img","input","ins","kbd","label","legend","li","link","main","map","mark","menu","meta","meter","nav","noframes","noscript","object","ol","optgroup","option","output","p","param","picture","pre","progress","q","rp","rt","ruby","s","samp","script","search","section","select","small","source","span","strike","strong","style","sub","summary","sup","svg","table","tbody","td","template","textarea","tfoot","th","thead","time","title","tr","track","tt","u","ul","var","video","wbr"],q=["g","defs","use","symbol","rect","circle","ellipse","line","polyline","polygon","path","image","text","tspan","textPath","linearGradient","radialGradient","stop","pattern","clipPath","mask","filter","feBlend","feColorMatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feGaussianBlur","feMerge","feMorphology","feOffset","feSpecularLighting","feTurbulence","feMergeNode","feFuncR","feFuncG","feFuncB","feFuncA","feDistantLight","fePointLight","feSpotLight","feFlood","feTile","animate","animateTransform","animateMotion","mpath","set","view","cursor","foreignObject","desc","title","metadata","switch"],G=[...E,...q.filter(a=>!E.includes(a))],{SvelteComponent:N,attr:U,binding_callbacks:j,detach:K,element:X,flush:d,init:Z,insert:J,noop:M,safe_not_equal:Q,toggle_class:g}=window.__gradio__svelte__internal,{afterUpdate:W,tick:Y,onMount:ae}=window.__gradio__svelte__internal;function $(a){let e;return{c(){e=X("span"),U(e,"class","md svelte-7ddecg"),g(e,"chatbot",a[0]),g(e,"prose",a[1])},m(r,n){J(r,e,n),e.innerHTML=a[3],a[11](e)},p(r,[n]){n&8&&(e.innerHTML=r[3]),n&1&&g(e,"chatbot",r[0]),n&2&&g(e,"prose",r[1])},i:M,o:M,d(r){r&&K(e),a[11](null)}}}function T(a){return a.replace(/[.*+?^${}()|[\]\\]/g,"\\$&")}function ee(a,e,r){let{chatbot:n=!0}=e,{message:l}=e,{sanitize_html:b=!0}=e,{latex_delimiters:o=[]}=e,{render_markdown:p=!0}=e,{line_breaks:w=!0}=e,{header_links:k=!1}=e,{allow_tags:h=!1}=e,{theme_mode:x="system"}=e,f,y,L=!1;const D=I({header_links:k,line_breaks:w,latex_delimiters:o||[]});function R(t){return!o||o.length===0?!1:o.some(i=>t.includes(i.left)&&t.includes(i.right))}function z(t,i){if(i===!0){const c=/<\/?([a-zA-Z][a-zA-Z0-9-]*)([\s>])/g;return t.replace(c,(s,m,u)=>G.includes(m.toLowerCase())?s:s.replace(/</g,"&lt;").replace(/>/g,"&gt;"))}if(Array.isArray(i)){const c=i.map(m=>({open:new RegExp(`<(${m})(\\s+[^>]*)?>`,"gi"),close:new RegExp(`</(${m})>`,"gi")}));let s=t;return c.forEach(m=>{s=s.replace(m.open,u=>u.replace(/</g,"&lt;").replace(/>/g,"&gt;")),s=s.replace(m.close,u=>u.replace(/</g,"&lt;").replace(/>/g,"&gt;"))}),s}return t}function P(t){let i=t;if(p){const c=[];o.forEach((s,m)=>{const u=T(s.left),S=T(s.right),B=new RegExp(`${u}([\\s\\S]+?)${S}`,"g");i=i.replace(B,(F,te)=>(c.push(F),`%%%LATEX_BLOCK_${c.length-1}%%%`))}),i=D.parse(i),i=i.replace(/%%%LATEX_BLOCK_(\d+)%%%/g,(s,m)=>c[parseInt(m,10)])}return h&&(i=z(i,h)),b&&A&&(i=A(i)),i}async function C(t){if(o.length>0&&t&&R(t))if(!L)await Promise.all([_(()=>Promise.resolve({}),__vite__mapDeps([0]),import.meta.url),_(()=>import("./auto-render-8xbOkcXl.js"),__vite__mapDeps([1,2]),import.meta.url)]).then(([,{default:i}])=>{L=!0,i(f,{delimiters:o,throwOnError:!1})});else{const{default:i}=await _(()=>import("./auto-render-8xbOkcXl.js"),__vite__mapDeps([1,2]),import.meta.url);i(f,{delimiters:o,throwOnError:!1})}if(f){const i=f.querySelectorAll(".mermaid");if(i.length>0){await Y();const{default:c}=await _(()=>import("./mermaid.core-iWqruL5K.js").then(s=>s.b3),__vite__mapDeps([3,4,5,6,7,8]),import.meta.url);c.initialize({startOnLoad:!1,theme:x==="dark"?"dark":"default",securityLevel:"antiscript"}),await c.run({nodes:Array.from(i).map(s=>s)})}}}W(async()=>{f&&document.body.contains(f)?await C(l):console.error("Element is not in the DOM")});function v(t){j[t?"unshift":"push"](()=>{f=t,r(2,f)})}return a.$$set=t=>{"chatbot"in t&&r(0,n=t.chatbot),"message"in t&&r(4,l=t.message),"sanitize_html"in t&&r(5,b=t.sanitize_html),"latex_delimiters"in t&&r(6,o=t.latex_delimiters),"render_markdown"in t&&r(1,p=t.render_markdown),"line_breaks"in t&&r(7,w=t.line_breaks),"header_links"in t&&r(8,k=t.header_links),"allow_tags"in t&&r(9,h=t.allow_tags),"theme_mode"in t&&r(10,x=t.theme_mode)},a.$$.update=()=>{a.$$.dirty&16&&(l&&l.trim()?r(3,y=P(l)):r(3,y=""))},[n,p,f,y,l,b,o,w,k,h,x,v]}class ne extends N{constructor(e){super(),Z(this,e,ee,$,Q,{chatbot:0,message:4,sanitize_html:5,latex_delimiters:6,render_markdown:1,line_breaks:7,header_links:8,allow_tags:9,theme_mode:10})}get chatbot(){return this.$$.ctx[0]}set chatbot(e){this.$$set({chatbot:e}),d()}get message(){return this.$$.ctx[4]}set message(e){this.$$set({message:e}),d()}get sanitize_html(){return this.$$.ctx[5]}set sanitize_html(e){this.$$set({sanitize_html:e}),d()}get latex_delimiters(){return this.$$.ctx[6]}set latex_delimiters(e){this.$$set({latex_delimiters:e}),d()}get render_markdown(){return this.$$.ctx[1]}set render_markdown(e){this.$$set({render_markdown:e}),d()}get line_breaks(){return this.$$.ctx[7]}set line_breaks(e){this.$$set({line_breaks:e}),d()}get header_links(){return this.$$.ctx[8]}set header_links(e){this.$$set({header_links:e}),d()}get allow_tags(){return this.$$.ctx[9]}set allow_tags(e){this.$$set({allow_tags:e}),d()}get theme_mode(){return this.$$.ctx[10]}set theme_mode(e){this.$$set({theme_mode:e}),d()}}export{ne as M};
//# sourceMappingURL=MarkdownCode-Cdb8e5t4.js.map
