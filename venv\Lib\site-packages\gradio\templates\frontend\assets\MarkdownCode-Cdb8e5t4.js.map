{"version": 3, "mappings": ";uHAEA,MAAMA,EAAkB,CACvBC,EACAC,EAAO,SAAS,OACH,CACT,IACI,OAAC,CAACD,GAAQ,IAAI,IAAIA,CAAI,EAAE,SAAW,IAAI,IAAIC,CAAI,EAAE,YAC7C,CACJ,QACR,CACD,EAEO,SAASC,EAASC,EAAwB,CAC1C,MAAAC,EAAW,IAAIC,EACfC,EAAO,IAAI,YAAY,gBAAgBH,EAAQ,WAAW,EAChE,OAAAI,EAAWD,EAAK,KAAM,IAAMA,GAAS,CAChCA,aAAgB,aAAe,WAAYA,GAC1CP,EAAgBO,EAAK,aAAa,MAAM,EAAG,SAAS,IAAI,IAC3DA,EAAK,aAAa,SAAU,QAAQ,EACpCA,EAAK,aAAa,MAAO,qBAAqB,EAEhD,CACA,EAEMF,EAAS,SAASE,CAAI,EAAE,KAAK,SACrC,CAEA,SAASC,EACRD,EACAE,EACAC,EACO,CAENH,IAC8BA,EAAK,WAAaE,GAC9C,OAAOA,GAAS,aAElBC,EAASH,CAAI,EAER,MAAAI,EAAWJ,GAAM,YAAc,GACrC,QAASK,EAAI,EAAGA,EAAID,EAAS,OAAQC,IAEpCJ,EAAWG,EAASC,CAAC,EAAGH,EAAMC,CAAQ,CAExC,CC5CO,MAAMG,EAAmB,CAC/B,MACA,WACA,IACA,OACA,UACA,UACA,SACA,OACA,UACA,QACA,QACA,IACA,OACA,WACA,MACA,MACA,MACA,aACA,OACA,KACA,SACA,SACA,UACA,SACA,OACA,OACA,MACA,WACA,OACA,WACA,KACA,MACA,UACA,MACA,SACA,MACA,MACA,KACA,KACA,KACA,QACA,WACA,aACA,SACA,OACA,SACA,OACA,QACA,WACA,KACA,KACA,KACA,KACA,KACA,KACA,OACA,SACA,SACA,KACA,OACA,IACA,SACA,MACA,QACA,MACA,MACA,QACA,SACA,KACA,OACA,OACA,MACA,OACA,OACA,OACA,QACA,MACA,WACA,WACA,SACA,KACA,WACA,SACA,SACA,IACA,QACA,UACA,MACA,WACA,IACA,KACA,KACA,OACA,IACA,OACA,SACA,SACA,UACA,SACA,QACA,SACA,OACA,SACA,SACA,QACA,MACA,UACA,MACA,MACA,QACA,QACA,KACA,WACA,WACA,QACA,KACA,QACA,OACA,QACA,KACA,QACA,KACA,IACA,KACA,MACA,QACA,KACD,EAGaC,EAAU,CAEtB,IACA,OACA,MACA,SAGA,OACA,SACA,UACA,OACA,WACA,UACA,OACA,QAGA,OACA,QACA,WAGA,iBACA,iBACA,OACA,UACA,WACA,OACA,SAGA,UACA,gBACA,sBACA,cACA,mBACA,oBACA,oBACA,iBACA,UACA,eACA,WACA,qBACA,eACA,cACA,UACA,UACA,UACA,UACA,iBACA,eACA,cACA,UACA,SAGA,UACA,mBACA,gBACA,QACA,MAGA,OACA,SACA,gBACA,OACA,QACA,WACA,QACD,EAEaC,EAAyB,CACrC,GAAGF,EACH,GAAGC,EAAQ,OAAQE,GAAQ,CAACH,EAAiB,SAASG,CAAG,CAAC,CAC3D,qKC/MU,aAAAC,EAAA,KAAAC,YAAkC,+IA6KeC,EAAe,YAA1EC,EAEMC,EAAAC,EAAAC,CAAA,cADEJ,EAAI,wCAAJA,EAAI,8CAD+CA,EAAe,iDApIhEK,EAAaC,EAAA,QACdA,EAAO,QAAQ,sBAAuB,MAAM,yBAnCzC,QAAAC,EAAU,IAAAC,EACV,SAAAC,CAAA,EAAAD,GACA,cAAAE,EAAgB,IAAAF,EAChB,kBAAAG,EAAA,IAAAH,GAKA,gBAAAI,EAAkB,IAAAJ,GAClB,YAAAK,EAAc,IAAAL,GACd,aAAAM,EAAe,IAAAN,GACf,WAAAO,EAAiC,IAAAP,GACjC,WAAAQ,EAAwB,UAAAR,EAC/BS,EACAC,EACAC,EAAe,SAEbC,EAASC,EAAA,CACd,aAAAP,EACA,YAAAD,EACA,iBAAkBF,GAAA,cAGVW,EAAgBC,EAAA,CACnB,OAAAZ,GAAoBA,EAAiB,SAAW,EAC7C,GAGDA,EAAiB,KACtBa,GACAD,EAAK,SAASC,EAAU,IAAI,GAAKD,EAAK,SAASC,EAAU,KAAK,GAQxD,SAAAC,EACRC,EACAC,EAAA,IAEIA,IAAiB,UAEdC,EAAW,6CACVF,EAAQ,QAAQE,EAAW,CAAAC,EAAOC,EAASC,IAC5CnC,EAAuB,SAASkC,EAAQ,eAGtCD,EAFCA,EAAM,QAAQ,KAAM,MAAM,EAAE,QAAQ,KAAM,MAAM,GAMtD,SAAM,QAAQF,CAAY,GACvB,MAAAK,EAAaL,EAAa,IAAK9B,IAAA,CACpC,KAAU,gBAAYA,CAAG,iBAAkB,IAAI,EAC/C,MAAW,iBAAaA,CAAG,KAAM,IAAI,SAGlCoC,EAASP,EAEb,OAAAM,EAAW,QAASE,GAAA,CACnBD,EAASA,EAAO,QAAQC,EAAQ,KAAOL,GACtCA,EAAM,QAAQ,KAAM,MAAM,EAAE,QAAQ,KAAM,MAAM,GAEjDI,EAASA,EAAO,QAAQC,EAAQ,MAAQL,GACvCA,EAAM,QAAQ,KAAM,MAAM,EAAE,QAAQ,KAAM,MAAM,KAG3CI,EAED,OAAAP,WAGCS,EAAgBC,EAAA,KACpBC,EAAcD,EACd,GAAAxB,EAAA,CACG,MAAA0B,EAAA,GACN3B,EAAiB,SAASa,EAAWe,IAAA,OAC9BC,EAAgBnC,EAAamB,EAAU,IAAI,EAC3CiB,EAAiBpC,EAAamB,EAAU,KAAK,EAC7CkB,EAAA,IAAY,OACd,GAAAF,CAAa,eAAeC,CAAc,GAC7C,KAEDJ,EAAcA,EAAY,QAAQK,EAAA,CAAQb,EAAOc,MAChDL,EAAY,KAAKT,CAAK,EACG,kBAAAS,EAAY,OAAS,CAAC,UAIjDD,EAAcjB,EAAO,MAAMiB,CAAW,EAEtCA,EAAcA,EAAY,QACzB,2BACC,CAAAR,EAAOc,IAAOL,EAAY,SAASK,EAAI,EAAE,IAIxC,OAAA5B,IACHsB,EAAcZ,EAAWY,EAAatB,CAAU,GAG7CL,GAAiB1B,IACpBqD,EAAcrD,EAASqD,CAAW,GAE5BA,iBASOO,EAAYR,EAAA,CACtB,GAAAzB,EAAiB,OAAS,GAAKyB,GAASd,EAAgBc,CAAK,EAC3D,IAAAjB,EACE,cAAQ,8BACoB,uCAAA0B,EAAA,WAC1B,2BAA2B,4CAChC,SAAW,QAASC,CAAA,MACtB3B,EAAe,GACf2B,EAAuB7B,EAAA,CACtB,WAAYN,EACZ,aAAc,YAIR,cAASmC,sBAChB,2BACD,0CACAA,EAAuB7B,EAAA,CACtB,WAAYN,EACZ,aAAc,KAKb,GAAAM,EAAA,OACG8B,EAAc9B,EAAG,iBAAiB,UAAU,EAC9C,GAAA8B,EAAY,OAAS,GAClB,MAAAhD,EAAA,EACE,cAASiD,sBAAyB,4BAAS,OAAAC,KAAA,oDAEnDD,EAAQ,YACP,YAAa,GACb,MAAOhC,IAAe,OAAS,OAAS,UACxC,cAAe,qBAEVgC,EAAQ,KACb,MAAO,MAAM,KAAKD,CAAW,EAAE,IAAK3D,GAASA,CAAmB,MAMpEU,EAAA,UACKmB,GAAM,SAAS,KAAK,SAASA,CAAE,EAC5B,MAAA2B,EAAYnC,CAAO,EAEzB,QAAQ,MAAM,2BAA2B,6CAKbQ,EAAEiC,6aAzDzBzC,GAAWA,EAAQ,OACzB0C,EAAA,EAAAjC,EAAOiB,EAAgB1B,CAAO,OAE9BS,EAAO", "names": ["is_external_url", "link", "root", "sanitize", "source", "<PERSON><PERSON>na", "<PERSON><PERSON><PERSON>", "node", "walk_nodes", "test", "callback", "children", "i", "standardHtmlTags", "svgTags", "standardHtmlAndSvgTags", "tag", "afterUpdate", "tick", "ctx", "insert", "target", "span", "anchor", "escapeRegExp", "string", "chatbot", "$$props", "message", "sanitize_html", "latex_delimiters", "render_markdown", "line_breaks", "header_links", "allow_tags", "theme_mode", "el", "html", "katex_loaded", "marked", "create_marked", "has_math_syntax", "text", "delimiter", "escapeTags", "content", "tagsToEscape", "tagRegex", "match", "tagName", "endChar", "tagPattern", "result", "pattern", "process_message", "value", "parsedValue", "latexBlocks", "index", "leftDelimiter", "rightDelimiter", "regex", "p1", "render_html", "__vitePreload", "render_math_in_element", "mermaidDivs", "mermaid", "n", "$$value", "$$invalidate"], "ignoreList": [], "sources": ["../../../../js/sanitize/browser.ts", "../../../../js/markdown-code/html-tags.ts", "../../../../js/markdown-code/MarkdownCode.svelte"], "sourcesContent": ["import Amuchina from \"amuchina\";\n\nconst is_external_url = (\n\tlink: string | null,\n\troot = location.href\n): boolean => {\n\ttry {\n\t\treturn !!link && new URL(link).origin !== new URL(root).origin;\n\t} catch (e) {\n\t\treturn false;\n\t}\n};\n\nexport function sanitize(source: string): string {\n\tconst amuchina = new Amuchina();\n\tconst node = new DOMParser().parseFromString(source, \"text/html\");\n\twalk_nodes(node.body, \"A\", (node) => {\n\t\tif (node instanceof HTMLElement && \"target\" in node) {\n\t\t\tif (is_external_url(node.getAttribute(\"href\"), location.href)) {\n\t\t\t\tnode.setAttribute(\"target\", \"_blank\");\n\t\t\t\tnode.setAttribute(\"rel\", \"noopener noreferrer\");\n\t\t\t}\n\t\t}\n\t});\n\n\treturn amuchina.sanitize(node).body.innerHTML;\n}\n\nfunction walk_nodes(\n\tnode: Node | null | HTMLElement,\n\ttest: string | ((node: Node | HTMLElement) => boolean),\n\tcallback: (node: Node | HTMLElement) => void\n): void {\n\tif (\n\t\tnode &&\n\t\t((typeof test === \"string\" && node.nodeName === test) ||\n\t\t\t(typeof test === \"function\" && test(node)))\n\t) {\n\t\tcallback(node);\n\t}\n\tconst children = node?.childNodes || [];\n\tfor (let i = 0; i < children.length; i++) {\n\t\t// @ts-ignore\n\t\twalk_nodes(children[i], test, callback);\n\t}\n}\n", "// https://www.w3schools.com/tags/\nexport const standardHtmlTags = [\n\t\"!--\",\n\t\"!doctype\",\n\t\"a\",\n\t\"abbr\",\n\t\"acronym\",\n\t\"address\",\n\t\"applet\",\n\t\"area\",\n\t\"article\",\n\t\"aside\",\n\t\"audio\",\n\t\"b\",\n\t\"base\",\n\t\"basefont\",\n\t\"bdi\",\n\t\"bdo\",\n\t\"big\",\n\t\"blockquote\",\n\t\"body\",\n\t\"br\",\n\t\"button\",\n\t\"canvas\",\n\t\"caption\",\n\t\"center\",\n\t\"cite\",\n\t\"code\",\n\t\"col\",\n\t\"colgroup\",\n\t\"data\",\n\t\"datalist\",\n\t\"dd\",\n\t\"del\",\n\t\"details\",\n\t\"dfn\",\n\t\"dialog\",\n\t\"dir\",\n\t\"div\",\n\t\"dl\",\n\t\"dt\",\n\t\"em\",\n\t\"embed\",\n\t\"fieldset\",\n\t\"figcaption\",\n\t\"figure\",\n\t\"font\",\n\t\"footer\",\n\t\"form\",\n\t\"frame\",\n\t\"frameset\",\n\t\"h1\",\n\t\"h2\",\n\t\"h3\",\n\t\"h4\",\n\t\"h5\",\n\t\"h6\",\n\t\"head\",\n\t\"header\",\n\t\"hgroup\",\n\t\"hr\",\n\t\"html\",\n\t\"i\",\n\t\"iframe\",\n\t\"img\",\n\t\"input\",\n\t\"ins\",\n\t\"kbd\",\n\t\"label\",\n\t\"legend\",\n\t\"li\",\n\t\"link\",\n\t\"main\",\n\t\"map\",\n\t\"mark\",\n\t\"menu\",\n\t\"meta\",\n\t\"meter\",\n\t\"nav\",\n\t\"noframes\",\n\t\"noscript\",\n\t\"object\",\n\t\"ol\",\n\t\"optgroup\",\n\t\"option\",\n\t\"output\",\n\t\"p\",\n\t\"param\",\n\t\"picture\",\n\t\"pre\",\n\t\"progress\",\n\t\"q\",\n\t\"rp\",\n\t\"rt\",\n\t\"ruby\",\n\t\"s\",\n\t\"samp\",\n\t\"script\",\n\t\"search\",\n\t\"section\",\n\t\"select\",\n\t\"small\",\n\t\"source\",\n\t\"span\",\n\t\"strike\",\n\t\"strong\",\n\t\"style\",\n\t\"sub\",\n\t\"summary\",\n\t\"sup\",\n\t\"svg\",\n\t\"table\",\n\t\"tbody\",\n\t\"td\",\n\t\"template\",\n\t\"textarea\",\n\t\"tfoot\",\n\t\"th\",\n\t\"thead\",\n\t\"time\",\n\t\"title\",\n\t\"tr\",\n\t\"track\",\n\t\"tt\",\n\t\"u\",\n\t\"ul\",\n\t\"var\",\n\t\"video\",\n\t\"wbr\"\n];\n\n// SVG tags\nexport const svgTags = [\n\t// Base structural elements\n\t\"g\",\n\t\"defs\",\n\t\"use\",\n\t\"symbol\",\n\n\t// Shape elements\n\t\"rect\",\n\t\"circle\",\n\t\"ellipse\",\n\t\"line\",\n\t\"polyline\",\n\t\"polygon\",\n\t\"path\",\n\t\"image\",\n\n\t// Text elements\n\t\"text\",\n\t\"tspan\",\n\t\"textPath\",\n\n\t// Gradient and effects\n\t\"linearGradient\",\n\t\"radialGradient\",\n\t\"stop\",\n\t\"pattern\",\n\t\"clipPath\",\n\t\"mask\",\n\t\"filter\",\n\n\t// Filter effects\n\t\"feBlend\",\n\t\"feColorMatrix\",\n\t\"feComponentTransfer\",\n\t\"feComposite\",\n\t\"feConvolveMatrix\",\n\t\"feDiffuseLighting\",\n\t\"feDisplacementMap\",\n\t\"feGaussianBlur\",\n\t\"feMerge\",\n\t\"feMorphology\",\n\t\"feOffset\",\n\t\"feSpecularLighting\",\n\t\"feTurbulence\",\n\t\"feMergeNode\",\n\t\"feFuncR\",\n\t\"feFuncG\",\n\t\"feFuncB\",\n\t\"feFuncA\",\n\t\"feDistantLight\",\n\t\"fePointLight\",\n\t\"feSpotLight\",\n\t\"feFlood\",\n\t\"feTile\",\n\n\t// Animation elements\n\t\"animate\",\n\t\"animateTransform\",\n\t\"animateMotion\",\n\t\"mpath\",\n\t\"set\",\n\n\t// Interactive and other elements\n\t\"view\",\n\t\"cursor\",\n\t\"foreignObject\",\n\t\"desc\",\n\t\"title\",\n\t\"metadata\",\n\t\"switch\"\n];\n\nexport const standardHtmlAndSvgTags = [\n\t...standardHtmlTags,\n\t...svgTags.filter((tag) => !standardHtmlTags.includes(tag))\n];\n", "<script lang=\"ts\">\n\timport { afterUpdate, tick, onMount } from \"svelte\";\n\timport { create_marked } from \"./utils\";\n\timport { sanitize } from \"@gradio/sanitize\";\n\timport \"./prism.css\";\n\timport { standardHtmlAndSvgTags } from \"./html-tags\";\n\timport type { ThemeMode } from \"@gradio/core\";\n\n\texport let chatbot = true;\n\texport let message: string;\n\texport let sanitize_html = true;\n\texport let latex_delimiters: {\n\t\tleft: string;\n\t\tright: string;\n\t\tdisplay: boolean;\n\t}[] = [];\n\texport let render_markdown = true;\n\texport let line_breaks = true;\n\texport let header_links = false;\n\texport let allow_tags: string[] | boolean = false;\n\texport let theme_mode: ThemeMode = \"system\";\n\tlet el: HTMLSpanElement;\n\tlet html: string;\n\tlet katex_loaded = false;\n\n\tconst marked = create_marked({\n\t\theader_links,\n\t\tline_breaks,\n\t\tlatex_delimiters: latex_delimiters || []\n\t});\n\n\tfunction has_math_syntax(text: string): boolean {\n\t\tif (!latex_delimiters || latex_delimiters.length === 0) {\n\t\t\treturn false;\n\t\t}\n\n\t\treturn latex_delimiters.some(\n\t\t\t(delimiter) =>\n\t\t\t\ttext.includes(delimiter.left) && text.includes(delimiter.right)\n\t\t);\n\t}\n\n\tfunction escapeRegExp(string: string): string {\n\t\treturn string.replace(/[.*+?^${}()|[\\]\\\\]/g, \"\\\\$&\");\n\t}\n\n\tfunction escapeTags(\n\t\tcontent: string,\n\t\ttagsToEscape: string[] | boolean\n\t): string {\n\t\tif (tagsToEscape === true) {\n\t\t\t// https://www.w3schools.com/tags/\n\t\t\tconst tagRegex = /<\\/?([a-zA-Z][a-zA-Z0-9-]*)([\\s>])/g;\n\t\t\treturn content.replace(tagRegex, (match, tagName, endChar) => {\n\t\t\t\tif (!standardHtmlAndSvgTags.includes(tagName.toLowerCase())) {\n\t\t\t\t\treturn match.replace(/</g, \"&lt;\").replace(/>/g, \"&gt;\");\n\t\t\t\t}\n\t\t\t\treturn match;\n\t\t\t});\n\t\t}\n\n\t\tif (Array.isArray(tagsToEscape)) {\n\t\t\tconst tagPattern = tagsToEscape.map((tag) => ({\n\t\t\t\topen: new RegExp(`<(${tag})(\\\\s+[^>]*)?>`, \"gi\"),\n\t\t\t\tclose: new RegExp(`</(${tag})>`, \"gi\")\n\t\t\t}));\n\n\t\t\tlet result = content;\n\n\t\t\ttagPattern.forEach((pattern) => {\n\t\t\t\tresult = result.replace(pattern.open, (match) =>\n\t\t\t\t\tmatch.replace(/</g, \"&lt;\").replace(/>/g, \"&gt;\")\n\t\t\t\t);\n\t\t\t\tresult = result.replace(pattern.close, (match) =>\n\t\t\t\t\tmatch.replace(/</g, \"&lt;\").replace(/>/g, \"&gt;\")\n\t\t\t\t);\n\t\t\t});\n\t\t\treturn result;\n\t\t}\n\t\treturn content;\n\t}\n\n\tfunction process_message(value: string): string {\n\t\tlet parsedValue = value;\n\t\tif (render_markdown) {\n\t\t\tconst latexBlocks: string[] = [];\n\t\t\tlatex_delimiters.forEach((delimiter, index) => {\n\t\t\t\tconst leftDelimiter = escapeRegExp(delimiter.left);\n\t\t\t\tconst rightDelimiter = escapeRegExp(delimiter.right);\n\t\t\t\tconst regex = new RegExp(\n\t\t\t\t\t`${leftDelimiter}([\\\\s\\\\S]+?)${rightDelimiter}`,\n\t\t\t\t\t\"g\"\n\t\t\t\t);\n\t\t\t\tparsedValue = parsedValue.replace(regex, (match, p1) => {\n\t\t\t\t\tlatexBlocks.push(match);\n\t\t\t\t\treturn `%%%LATEX_BLOCK_${latexBlocks.length - 1}%%%`;\n\t\t\t\t});\n\t\t\t});\n\n\t\t\tparsedValue = marked.parse(parsedValue) as string;\n\n\t\t\tparsedValue = parsedValue.replace(\n\t\t\t\t/%%%LATEX_BLOCK_(\\d+)%%%/g,\n\t\t\t\t(match, p1) => latexBlocks[parseInt(p1, 10)]\n\t\t\t);\n\t\t}\n\n\t\tif (allow_tags) {\n\t\t\tparsedValue = escapeTags(parsedValue, allow_tags);\n\t\t}\n\n\t\tif (sanitize_html && sanitize) {\n\t\t\tparsedValue = sanitize(parsedValue);\n\t\t}\n\t\treturn parsedValue;\n\t}\n\n\t$: if (message && message.trim()) {\n\t\thtml = process_message(message);\n\t} else {\n\t\thtml = \"\";\n\t}\n\n\tasync function render_html(value: string): Promise<void> {\n\t\tif (latex_delimiters.length > 0 && value && has_math_syntax(value)) {\n\t\t\tif (!katex_loaded) {\n\t\t\t\tawait Promise.all([\n\t\t\t\t\timport(\"katex/dist/katex.min.css\"),\n\t\t\t\t\timport(\"katex/contrib/auto-render\")\n\t\t\t\t]).then(([, { default: render_math_in_element }]) => {\n\t\t\t\t\tkatex_loaded = true;\n\t\t\t\t\trender_math_in_element(el, {\n\t\t\t\t\t\tdelimiters: latex_delimiters,\n\t\t\t\t\t\tthrowOnError: false\n\t\t\t\t\t});\n\t\t\t\t});\n\t\t\t} else {\n\t\t\t\tconst { default: render_math_in_element } = await import(\n\t\t\t\t\t\"katex/contrib/auto-render\"\n\t\t\t\t);\n\t\t\t\trender_math_in_element(el, {\n\t\t\t\t\tdelimiters: latex_delimiters,\n\t\t\t\t\tthrowOnError: false\n\t\t\t\t});\n\t\t\t}\n\t\t}\n\n\t\tif (el) {\n\t\t\tconst mermaidDivs = el.querySelectorAll(\".mermaid\");\n\t\t\tif (mermaidDivs.length > 0) {\n\t\t\t\tawait tick();\n\t\t\t\tconst { default: mermaid } = await import(\"mermaid\");\n\n\t\t\t\tmermaid.initialize({\n\t\t\t\t\tstartOnLoad: false,\n\t\t\t\t\ttheme: theme_mode === \"dark\" ? \"dark\" : \"default\",\n\t\t\t\t\tsecurityLevel: \"antiscript\"\n\t\t\t\t});\n\t\t\t\tawait mermaid.run({\n\t\t\t\t\tnodes: Array.from(mermaidDivs).map((node) => node as HTMLElement)\n\t\t\t\t});\n\t\t\t}\n\t\t}\n\t}\n\n\tafterUpdate(async () => {\n\t\tif (el && document.body.contains(el)) {\n\t\t\tawait render_html(message);\n\t\t} else {\n\t\t\tconsole.error(\"Element is not in the DOM\");\n\t\t}\n\t});\n</script>\n\n<span class:chatbot bind:this={el} class=\"md\" class:prose={render_markdown}>\n\t{@html html}\n</span>\n\n<style>\n\tspan :global(div[class*=\"code_wrap\"]) {\n\t\tposition: relative;\n\t}\n\n\t/* KaTeX */\n\tspan :global(span.katex) {\n\t\tfont-size: var(--text-lg);\n\t\tdirection: ltr;\n\t}\n\n\tspan :global(div[class*=\"code_wrap\"] > button) {\n\t\tz-index: 1;\n\t\tcursor: pointer;\n\t\tborder-bottom-left-radius: var(--radius-sm);\n\t\tpadding: var(--spacing-md);\n\t\twidth: 25px;\n\t\theight: 25px;\n\t\tposition: absolute;\n\t\tright: 0;\n\t}\n\n\tspan :global(.check) {\n\t\topacity: 0;\n\t\tz-index: var(--layer-top);\n\t\ttransition: opacity 0.2s;\n\t\tbackground: var(--code-background-fill);\n\t\tcolor: var(--body-text-color);\n\t\tposition: absolute;\n\t\ttop: var(--size-1-5);\n\t\tleft: var(--size-1-5);\n\t}\n\n\tspan :global(p:not(:first-child)) {\n\t\tmargin-top: var(--spacing-xxl);\n\t}\n\n\tspan :global(.md-header-anchor) {\n\t\t/* position: absolute; */\n\t\tmargin-left: -25px;\n\t\tpadding-right: 8px;\n\t\tline-height: 1;\n\t\tcolor: var(--body-text-color-subdued);\n\t\topacity: 0;\n\t}\n\n\tspan :global(h1:hover .md-header-anchor),\n\tspan :global(h2:hover .md-header-anchor),\n\tspan :global(h3:hover .md-header-anchor),\n\tspan :global(h4:hover .md-header-anchor),\n\tspan :global(h5:hover .md-header-anchor),\n\tspan :global(h6:hover .md-header-anchor) {\n\t\topacity: 1;\n\t}\n\n\tspan.md :global(.md-header-anchor > svg) {\n\t\tcolor: var(--body-text-color-subdued);\n\t}\n\n\tspan :global(table) {\n\t\tword-break: break-word;\n\t}\n</style>\n"], "file": "assets/MarkdownCode-Cdb8e5t4.js"}