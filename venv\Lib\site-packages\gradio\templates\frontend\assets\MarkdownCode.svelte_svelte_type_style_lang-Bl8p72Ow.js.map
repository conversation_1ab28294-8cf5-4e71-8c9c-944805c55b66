{"version": 3, "file": "MarkdownCode.svelte_svelte_type_style_lang-Bl8p72Ow.js", "sources": ["../../../../node_modules/.pnpm/marked@12.0.0/node_modules/marked/lib/marked.esm.js", "../../../../node_modules/.pnpm/marked-highlight@2.2.0_marked@12.0.0/node_modules/marked-highlight/src/index.js", "../../../../node_modules/.pnpm/github-slugger@2.0.0/node_modules/github-slugger/regex.js", "../../../../node_modules/.pnpm/github-slugger@2.0.0/node_modules/github-slugger/index.js", "../../../../node_modules/.pnpm/marked-gfm-heading-id@3.2.0_marked@12.0.0/node_modules/marked-gfm-heading-id/src/index.js", "../../../../node_modules/.pnpm/prismjs@1.29.0/node_modules/prismjs/components/prism-latex.js", "../../../../node_modules/.pnpm/prismjs@1.29.0/node_modules/prismjs/components/prism-bash.js", "../../../../js/markdown-code/utils.ts", "../../../../node_modules/.pnpm/amuchina@1.0.12/node_modules/amuchina/dist/utils.js", "../../../../node_modules/.pnpm/amuchina@1.0.12/node_modules/amuchina/dist/constants.js", "../../../../node_modules/.pnpm/amuchina@1.0.12/node_modules/amuchina/dist/index.js"], "sourcesContent": ["/**\n * marked v12.0.0 - a markdown parser\n * Copyright (c) 2011-2024, <PERSON>. (MIT Licensed)\n * https://github.com/markedjs/marked\n */\n\n/**\n * DO NOT EDIT THIS FILE\n * The code in this file is generated from files in ./src/\n */\n\n/**\n * Gets the original marked default options.\n */\nfunction _getDefaults() {\n    return {\n        async: false,\n        breaks: false,\n        extensions: null,\n        gfm: true,\n        hooks: null,\n        pedantic: false,\n        renderer: null,\n        silent: false,\n        tokenizer: null,\n        walkTokens: null\n    };\n}\nlet _defaults = _getDefaults();\nfunction changeDefaults(newDefaults) {\n    _defaults = newDefaults;\n}\n\n/**\n * Helpers\n */\nconst escapeTest = /[&<>\"']/;\nconst escapeReplace = new RegExp(escapeTest.source, 'g');\nconst escapeTestNoEncode = /[<>\"']|&(?!(#\\d{1,7}|#[Xx][a-fA-F0-9]{1,6}|\\w+);)/;\nconst escapeReplaceNoEncode = new RegExp(escapeTestNoEncode.source, 'g');\nconst escapeReplacements = {\n    '&': '&amp;',\n    '<': '&lt;',\n    '>': '&gt;',\n    '\"': '&quot;',\n    \"'\": '&#39;'\n};\nconst getEscapeReplacement = (ch) => escapeReplacements[ch];\nfunction escape$1(html, encode) {\n    if (encode) {\n        if (escapeTest.test(html)) {\n            return html.replace(escapeReplace, getEscapeReplacement);\n        }\n    }\n    else {\n        if (escapeTestNoEncode.test(html)) {\n            return html.replace(escapeReplaceNoEncode, getEscapeReplacement);\n        }\n    }\n    return html;\n}\nconst unescapeTest = /&(#(?:\\d+)|(?:#x[0-9A-Fa-f]+)|(?:\\w+));?/ig;\nfunction unescape(html) {\n    // explicitly match decimal, hex, and named HTML entities\n    return html.replace(unescapeTest, (_, n) => {\n        n = n.toLowerCase();\n        if (n === 'colon')\n            return ':';\n        if (n.charAt(0) === '#') {\n            return n.charAt(1) === 'x'\n                ? String.fromCharCode(parseInt(n.substring(2), 16))\n                : String.fromCharCode(+n.substring(1));\n        }\n        return '';\n    });\n}\nconst caret = /(^|[^\\[])\\^/g;\nfunction edit(regex, opt) {\n    let source = typeof regex === 'string' ? regex : regex.source;\n    opt = opt || '';\n    const obj = {\n        replace: (name, val) => {\n            let valSource = typeof val === 'string' ? val : val.source;\n            valSource = valSource.replace(caret, '$1');\n            source = source.replace(name, valSource);\n            return obj;\n        },\n        getRegex: () => {\n            return new RegExp(source, opt);\n        }\n    };\n    return obj;\n}\nfunction cleanUrl(href) {\n    try {\n        href = encodeURI(href).replace(/%25/g, '%');\n    }\n    catch (e) {\n        return null;\n    }\n    return href;\n}\nconst noopTest = { exec: () => null };\nfunction splitCells(tableRow, count) {\n    // ensure that every cell-delimiting pipe has a space\n    // before it to distinguish it from an escaped pipe\n    const row = tableRow.replace(/\\|/g, (match, offset, str) => {\n        let escaped = false;\n        let curr = offset;\n        while (--curr >= 0 && str[curr] === '\\\\')\n            escaped = !escaped;\n        if (escaped) {\n            // odd number of slashes means | is escaped\n            // so we leave it alone\n            return '|';\n        }\n        else {\n            // add space before unescaped |\n            return ' |';\n        }\n    }), cells = row.split(/ \\|/);\n    let i = 0;\n    // First/last cell in a row cannot be empty if it has no leading/trailing pipe\n    if (!cells[0].trim()) {\n        cells.shift();\n    }\n    if (cells.length > 0 && !cells[cells.length - 1].trim()) {\n        cells.pop();\n    }\n    if (count) {\n        if (cells.length > count) {\n            cells.splice(count);\n        }\n        else {\n            while (cells.length < count)\n                cells.push('');\n        }\n    }\n    for (; i < cells.length; i++) {\n        // leading or trailing whitespace is ignored per the gfm spec\n        cells[i] = cells[i].trim().replace(/\\\\\\|/g, '|');\n    }\n    return cells;\n}\n/**\n * Remove trailing 'c's. Equivalent to str.replace(/c*$/, '').\n * /c*$/ is vulnerable to REDOS.\n *\n * @param str\n * @param c\n * @param invert Remove suffix of non-c chars instead. Default falsey.\n */\nfunction rtrim(str, c, invert) {\n    const l = str.length;\n    if (l === 0) {\n        return '';\n    }\n    // Length of suffix matching the invert condition.\n    let suffLen = 0;\n    // Step left until we fail to match the invert condition.\n    while (suffLen < l) {\n        const currChar = str.charAt(l - suffLen - 1);\n        if (currChar === c && !invert) {\n            suffLen++;\n        }\n        else if (currChar !== c && invert) {\n            suffLen++;\n        }\n        else {\n            break;\n        }\n    }\n    return str.slice(0, l - suffLen);\n}\nfunction findClosingBracket(str, b) {\n    if (str.indexOf(b[1]) === -1) {\n        return -1;\n    }\n    let level = 0;\n    for (let i = 0; i < str.length; i++) {\n        if (str[i] === '\\\\') {\n            i++;\n        }\n        else if (str[i] === b[0]) {\n            level++;\n        }\n        else if (str[i] === b[1]) {\n            level--;\n            if (level < 0) {\n                return i;\n            }\n        }\n    }\n    return -1;\n}\n\nfunction outputLink(cap, link, raw, lexer) {\n    const href = link.href;\n    const title = link.title ? escape$1(link.title) : null;\n    const text = cap[1].replace(/\\\\([\\[\\]])/g, '$1');\n    if (cap[0].charAt(0) !== '!') {\n        lexer.state.inLink = true;\n        const token = {\n            type: 'link',\n            raw,\n            href,\n            title,\n            text,\n            tokens: lexer.inlineTokens(text)\n        };\n        lexer.state.inLink = false;\n        return token;\n    }\n    return {\n        type: 'image',\n        raw,\n        href,\n        title,\n        text: escape$1(text)\n    };\n}\nfunction indentCodeCompensation(raw, text) {\n    const matchIndentToCode = raw.match(/^(\\s+)(?:```)/);\n    if (matchIndentToCode === null) {\n        return text;\n    }\n    const indentToCode = matchIndentToCode[1];\n    return text\n        .split('\\n')\n        .map(node => {\n        const matchIndentInNode = node.match(/^\\s+/);\n        if (matchIndentInNode === null) {\n            return node;\n        }\n        const [indentInNode] = matchIndentInNode;\n        if (indentInNode.length >= indentToCode.length) {\n            return node.slice(indentToCode.length);\n        }\n        return node;\n    })\n        .join('\\n');\n}\n/**\n * Tokenizer\n */\nclass _Tokenizer {\n    options;\n    rules; // set by the lexer\n    lexer; // set by the lexer\n    constructor(options) {\n        this.options = options || _defaults;\n    }\n    space(src) {\n        const cap = this.rules.block.newline.exec(src);\n        if (cap && cap[0].length > 0) {\n            return {\n                type: 'space',\n                raw: cap[0]\n            };\n        }\n    }\n    code(src) {\n        const cap = this.rules.block.code.exec(src);\n        if (cap) {\n            const text = cap[0].replace(/^ {1,4}/gm, '');\n            return {\n                type: 'code',\n                raw: cap[0],\n                codeBlockStyle: 'indented',\n                text: !this.options.pedantic\n                    ? rtrim(text, '\\n')\n                    : text\n            };\n        }\n    }\n    fences(src) {\n        const cap = this.rules.block.fences.exec(src);\n        if (cap) {\n            const raw = cap[0];\n            const text = indentCodeCompensation(raw, cap[3] || '');\n            return {\n                type: 'code',\n                raw,\n                lang: cap[2] ? cap[2].trim().replace(this.rules.inline.anyPunctuation, '$1') : cap[2],\n                text\n            };\n        }\n    }\n    heading(src) {\n        const cap = this.rules.block.heading.exec(src);\n        if (cap) {\n            let text = cap[2].trim();\n            // remove trailing #s\n            if (/#$/.test(text)) {\n                const trimmed = rtrim(text, '#');\n                if (this.options.pedantic) {\n                    text = trimmed.trim();\n                }\n                else if (!trimmed || / $/.test(trimmed)) {\n                    // CommonMark requires space before trailing #s\n                    text = trimmed.trim();\n                }\n            }\n            return {\n                type: 'heading',\n                raw: cap[0],\n                depth: cap[1].length,\n                text,\n                tokens: this.lexer.inline(text)\n            };\n        }\n    }\n    hr(src) {\n        const cap = this.rules.block.hr.exec(src);\n        if (cap) {\n            return {\n                type: 'hr',\n                raw: cap[0]\n            };\n        }\n    }\n    blockquote(src) {\n        const cap = this.rules.block.blockquote.exec(src);\n        if (cap) {\n            const text = rtrim(cap[0].replace(/^ *>[ \\t]?/gm, ''), '\\n');\n            const top = this.lexer.state.top;\n            this.lexer.state.top = true;\n            const tokens = this.lexer.blockTokens(text);\n            this.lexer.state.top = top;\n            return {\n                type: 'blockquote',\n                raw: cap[0],\n                tokens,\n                text\n            };\n        }\n    }\n    list(src) {\n        let cap = this.rules.block.list.exec(src);\n        if (cap) {\n            let bull = cap[1].trim();\n            const isordered = bull.length > 1;\n            const list = {\n                type: 'list',\n                raw: '',\n                ordered: isordered,\n                start: isordered ? +bull.slice(0, -1) : '',\n                loose: false,\n                items: []\n            };\n            bull = isordered ? `\\\\d{1,9}\\\\${bull.slice(-1)}` : `\\\\${bull}`;\n            if (this.options.pedantic) {\n                bull = isordered ? bull : '[*+-]';\n            }\n            // Get next list item\n            const itemRegex = new RegExp(`^( {0,3}${bull})((?:[\\t ][^\\\\n]*)?(?:\\\\n|$))`);\n            let raw = '';\n            let itemContents = '';\n            let endsWithBlankLine = false;\n            // Check if current bullet point can start a new List Item\n            while (src) {\n                let endEarly = false;\n                if (!(cap = itemRegex.exec(src))) {\n                    break;\n                }\n                if (this.rules.block.hr.test(src)) { // End list if bullet was actually HR (possibly move into itemRegex?)\n                    break;\n                }\n                raw = cap[0];\n                src = src.substring(raw.length);\n                let line = cap[2].split('\\n', 1)[0].replace(/^\\t+/, (t) => ' '.repeat(3 * t.length));\n                let nextLine = src.split('\\n', 1)[0];\n                let indent = 0;\n                if (this.options.pedantic) {\n                    indent = 2;\n                    itemContents = line.trimStart();\n                }\n                else {\n                    indent = cap[2].search(/[^ ]/); // Find first non-space char\n                    indent = indent > 4 ? 1 : indent; // Treat indented code blocks (> 4 spaces) as having only 1 indent\n                    itemContents = line.slice(indent);\n                    indent += cap[1].length;\n                }\n                let blankLine = false;\n                if (!line && /^ *$/.test(nextLine)) { // Items begin with at most one blank line\n                    raw += nextLine + '\\n';\n                    src = src.substring(nextLine.length + 1);\n                    endEarly = true;\n                }\n                if (!endEarly) {\n                    const nextBulletRegex = new RegExp(`^ {0,${Math.min(3, indent - 1)}}(?:[*+-]|\\\\d{1,9}[.)])((?:[ \\t][^\\\\n]*)?(?:\\\\n|$))`);\n                    const hrRegex = new RegExp(`^ {0,${Math.min(3, indent - 1)}}((?:- *){3,}|(?:_ *){3,}|(?:\\\\* *){3,})(?:\\\\n+|$)`);\n                    const fencesBeginRegex = new RegExp(`^ {0,${Math.min(3, indent - 1)}}(?:\\`\\`\\`|~~~)`);\n                    const headingBeginRegex = new RegExp(`^ {0,${Math.min(3, indent - 1)}}#`);\n                    // Check if following lines should be included in List Item\n                    while (src) {\n                        const rawLine = src.split('\\n', 1)[0];\n                        nextLine = rawLine;\n                        // Re-align to follow commonmark nesting rules\n                        if (this.options.pedantic) {\n                            nextLine = nextLine.replace(/^ {1,4}(?=( {4})*[^ ])/g, '  ');\n                        }\n                        // End list item if found code fences\n                        if (fencesBeginRegex.test(nextLine)) {\n                            break;\n                        }\n                        // End list item if found start of new heading\n                        if (headingBeginRegex.test(nextLine)) {\n                            break;\n                        }\n                        // End list item if found start of new bullet\n                        if (nextBulletRegex.test(nextLine)) {\n                            break;\n                        }\n                        // Horizontal rule found\n                        if (hrRegex.test(src)) {\n                            break;\n                        }\n                        if (nextLine.search(/[^ ]/) >= indent || !nextLine.trim()) { // Dedent if possible\n                            itemContents += '\\n' + nextLine.slice(indent);\n                        }\n                        else {\n                            // not enough indentation\n                            if (blankLine) {\n                                break;\n                            }\n                            // paragraph continuation unless last line was a different block level element\n                            if (line.search(/[^ ]/) >= 4) { // indented code block\n                                break;\n                            }\n                            if (fencesBeginRegex.test(line)) {\n                                break;\n                            }\n                            if (headingBeginRegex.test(line)) {\n                                break;\n                            }\n                            if (hrRegex.test(line)) {\n                                break;\n                            }\n                            itemContents += '\\n' + nextLine;\n                        }\n                        if (!blankLine && !nextLine.trim()) { // Check if current line is blank\n                            blankLine = true;\n                        }\n                        raw += rawLine + '\\n';\n                        src = src.substring(rawLine.length + 1);\n                        line = nextLine.slice(indent);\n                    }\n                }\n                if (!list.loose) {\n                    // If the previous item ended with a blank line, the list is loose\n                    if (endsWithBlankLine) {\n                        list.loose = true;\n                    }\n                    else if (/\\n *\\n *$/.test(raw)) {\n                        endsWithBlankLine = true;\n                    }\n                }\n                let istask = null;\n                let ischecked;\n                // Check for task list items\n                if (this.options.gfm) {\n                    istask = /^\\[[ xX]\\] /.exec(itemContents);\n                    if (istask) {\n                        ischecked = istask[0] !== '[ ] ';\n                        itemContents = itemContents.replace(/^\\[[ xX]\\] +/, '');\n                    }\n                }\n                list.items.push({\n                    type: 'list_item',\n                    raw,\n                    task: !!istask,\n                    checked: ischecked,\n                    loose: false,\n                    text: itemContents,\n                    tokens: []\n                });\n                list.raw += raw;\n            }\n            // Do not consume newlines at end of final item. Alternatively, make itemRegex *start* with any newlines to simplify/speed up endsWithBlankLine logic\n            list.items[list.items.length - 1].raw = raw.trimEnd();\n            (list.items[list.items.length - 1]).text = itemContents.trimEnd();\n            list.raw = list.raw.trimEnd();\n            // Item child tokens handled here at end because we needed to have the final item to trim it first\n            for (let i = 0; i < list.items.length; i++) {\n                this.lexer.state.top = false;\n                list.items[i].tokens = this.lexer.blockTokens(list.items[i].text, []);\n                if (!list.loose) {\n                    // Check if list should be loose\n                    const spacers = list.items[i].tokens.filter(t => t.type === 'space');\n                    const hasMultipleLineBreaks = spacers.length > 0 && spacers.some(t => /\\n.*\\n/.test(t.raw));\n                    list.loose = hasMultipleLineBreaks;\n                }\n            }\n            // Set all items to loose if list is loose\n            if (list.loose) {\n                for (let i = 0; i < list.items.length; i++) {\n                    list.items[i].loose = true;\n                }\n            }\n            return list;\n        }\n    }\n    html(src) {\n        const cap = this.rules.block.html.exec(src);\n        if (cap) {\n            const token = {\n                type: 'html',\n                block: true,\n                raw: cap[0],\n                pre: cap[1] === 'pre' || cap[1] === 'script' || cap[1] === 'style',\n                text: cap[0]\n            };\n            return token;\n        }\n    }\n    def(src) {\n        const cap = this.rules.block.def.exec(src);\n        if (cap) {\n            const tag = cap[1].toLowerCase().replace(/\\s+/g, ' ');\n            const href = cap[2] ? cap[2].replace(/^<(.*)>$/, '$1').replace(this.rules.inline.anyPunctuation, '$1') : '';\n            const title = cap[3] ? cap[3].substring(1, cap[3].length - 1).replace(this.rules.inline.anyPunctuation, '$1') : cap[3];\n            return {\n                type: 'def',\n                tag,\n                raw: cap[0],\n                href,\n                title\n            };\n        }\n    }\n    table(src) {\n        const cap = this.rules.block.table.exec(src);\n        if (!cap) {\n            return;\n        }\n        if (!/[:|]/.test(cap[2])) {\n            // delimiter row must have a pipe (|) or colon (:) otherwise it is a setext heading\n            return;\n        }\n        const headers = splitCells(cap[1]);\n        const aligns = cap[2].replace(/^\\||\\| *$/g, '').split('|');\n        const rows = cap[3] && cap[3].trim() ? cap[3].replace(/\\n[ \\t]*$/, '').split('\\n') : [];\n        const item = {\n            type: 'table',\n            raw: cap[0],\n            header: [],\n            align: [],\n            rows: []\n        };\n        if (headers.length !== aligns.length) {\n            // header and align columns must be equal, rows can be different.\n            return;\n        }\n        for (const align of aligns) {\n            if (/^ *-+: *$/.test(align)) {\n                item.align.push('right');\n            }\n            else if (/^ *:-+: *$/.test(align)) {\n                item.align.push('center');\n            }\n            else if (/^ *:-+ *$/.test(align)) {\n                item.align.push('left');\n            }\n            else {\n                item.align.push(null);\n            }\n        }\n        for (const header of headers) {\n            item.header.push({\n                text: header,\n                tokens: this.lexer.inline(header)\n            });\n        }\n        for (const row of rows) {\n            item.rows.push(splitCells(row, item.header.length).map(cell => {\n                return {\n                    text: cell,\n                    tokens: this.lexer.inline(cell)\n                };\n            }));\n        }\n        return item;\n    }\n    lheading(src) {\n        const cap = this.rules.block.lheading.exec(src);\n        if (cap) {\n            return {\n                type: 'heading',\n                raw: cap[0],\n                depth: cap[2].charAt(0) === '=' ? 1 : 2,\n                text: cap[1],\n                tokens: this.lexer.inline(cap[1])\n            };\n        }\n    }\n    paragraph(src) {\n        const cap = this.rules.block.paragraph.exec(src);\n        if (cap) {\n            const text = cap[1].charAt(cap[1].length - 1) === '\\n'\n                ? cap[1].slice(0, -1)\n                : cap[1];\n            return {\n                type: 'paragraph',\n                raw: cap[0],\n                text,\n                tokens: this.lexer.inline(text)\n            };\n        }\n    }\n    text(src) {\n        const cap = this.rules.block.text.exec(src);\n        if (cap) {\n            return {\n                type: 'text',\n                raw: cap[0],\n                text: cap[0],\n                tokens: this.lexer.inline(cap[0])\n            };\n        }\n    }\n    escape(src) {\n        const cap = this.rules.inline.escape.exec(src);\n        if (cap) {\n            return {\n                type: 'escape',\n                raw: cap[0],\n                text: escape$1(cap[1])\n            };\n        }\n    }\n    tag(src) {\n        const cap = this.rules.inline.tag.exec(src);\n        if (cap) {\n            if (!this.lexer.state.inLink && /^<a /i.test(cap[0])) {\n                this.lexer.state.inLink = true;\n            }\n            else if (this.lexer.state.inLink && /^<\\/a>/i.test(cap[0])) {\n                this.lexer.state.inLink = false;\n            }\n            if (!this.lexer.state.inRawBlock && /^<(pre|code|kbd|script)(\\s|>)/i.test(cap[0])) {\n                this.lexer.state.inRawBlock = true;\n            }\n            else if (this.lexer.state.inRawBlock && /^<\\/(pre|code|kbd|script)(\\s|>)/i.test(cap[0])) {\n                this.lexer.state.inRawBlock = false;\n            }\n            return {\n                type: 'html',\n                raw: cap[0],\n                inLink: this.lexer.state.inLink,\n                inRawBlock: this.lexer.state.inRawBlock,\n                block: false,\n                text: cap[0]\n            };\n        }\n    }\n    link(src) {\n        const cap = this.rules.inline.link.exec(src);\n        if (cap) {\n            const trimmedUrl = cap[2].trim();\n            if (!this.options.pedantic && /^</.test(trimmedUrl)) {\n                // commonmark requires matching angle brackets\n                if (!(/>$/.test(trimmedUrl))) {\n                    return;\n                }\n                // ending angle bracket cannot be escaped\n                const rtrimSlash = rtrim(trimmedUrl.slice(0, -1), '\\\\');\n                if ((trimmedUrl.length - rtrimSlash.length) % 2 === 0) {\n                    return;\n                }\n            }\n            else {\n                // find closing parenthesis\n                const lastParenIndex = findClosingBracket(cap[2], '()');\n                if (lastParenIndex > -1) {\n                    const start = cap[0].indexOf('!') === 0 ? 5 : 4;\n                    const linkLen = start + cap[1].length + lastParenIndex;\n                    cap[2] = cap[2].substring(0, lastParenIndex);\n                    cap[0] = cap[0].substring(0, linkLen).trim();\n                    cap[3] = '';\n                }\n            }\n            let href = cap[2];\n            let title = '';\n            if (this.options.pedantic) {\n                // split pedantic href and title\n                const link = /^([^'\"]*[^\\s])\\s+(['\"])(.*)\\2/.exec(href);\n                if (link) {\n                    href = link[1];\n                    title = link[3];\n                }\n            }\n            else {\n                title = cap[3] ? cap[3].slice(1, -1) : '';\n            }\n            href = href.trim();\n            if (/^</.test(href)) {\n                if (this.options.pedantic && !(/>$/.test(trimmedUrl))) {\n                    // pedantic allows starting angle bracket without ending angle bracket\n                    href = href.slice(1);\n                }\n                else {\n                    href = href.slice(1, -1);\n                }\n            }\n            return outputLink(cap, {\n                href: href ? href.replace(this.rules.inline.anyPunctuation, '$1') : href,\n                title: title ? title.replace(this.rules.inline.anyPunctuation, '$1') : title\n            }, cap[0], this.lexer);\n        }\n    }\n    reflink(src, links) {\n        let cap;\n        if ((cap = this.rules.inline.reflink.exec(src))\n            || (cap = this.rules.inline.nolink.exec(src))) {\n            const linkString = (cap[2] || cap[1]).replace(/\\s+/g, ' ');\n            const link = links[linkString.toLowerCase()];\n            if (!link) {\n                const text = cap[0].charAt(0);\n                return {\n                    type: 'text',\n                    raw: text,\n                    text\n                };\n            }\n            return outputLink(cap, link, cap[0], this.lexer);\n        }\n    }\n    emStrong(src, maskedSrc, prevChar = '') {\n        let match = this.rules.inline.emStrongLDelim.exec(src);\n        if (!match)\n            return;\n        // _ can't be between two alphanumerics. \\p{L}\\p{N} includes non-english alphabet/numbers as well\n        if (match[3] && prevChar.match(/[\\p{L}\\p{N}]/u))\n            return;\n        const nextChar = match[1] || match[2] || '';\n        if (!nextChar || !prevChar || this.rules.inline.punctuation.exec(prevChar)) {\n            // unicode Regex counts emoji as 1 char; spread into array for proper count (used multiple times below)\n            const lLength = [...match[0]].length - 1;\n            let rDelim, rLength, delimTotal = lLength, midDelimTotal = 0;\n            const endReg = match[0][0] === '*' ? this.rules.inline.emStrongRDelimAst : this.rules.inline.emStrongRDelimUnd;\n            endReg.lastIndex = 0;\n            // Clip maskedSrc to same section of string as src (move to lexer?)\n            maskedSrc = maskedSrc.slice(-1 * src.length + lLength);\n            while ((match = endReg.exec(maskedSrc)) != null) {\n                rDelim = match[1] || match[2] || match[3] || match[4] || match[5] || match[6];\n                if (!rDelim)\n                    continue; // skip single * in __abc*abc__\n                rLength = [...rDelim].length;\n                if (match[3] || match[4]) { // found another Left Delim\n                    delimTotal += rLength;\n                    continue;\n                }\n                else if (match[5] || match[6]) { // either Left or Right Delim\n                    if (lLength % 3 && !((lLength + rLength) % 3)) {\n                        midDelimTotal += rLength;\n                        continue; // CommonMark Emphasis Rules 9-10\n                    }\n                }\n                delimTotal -= rLength;\n                if (delimTotal > 0)\n                    continue; // Haven't found enough closing delimiters\n                // Remove extra characters. *a*** -> *a*\n                rLength = Math.min(rLength, rLength + delimTotal + midDelimTotal);\n                // char length can be >1 for unicode characters;\n                const lastCharLength = [...match[0]][0].length;\n                const raw = src.slice(0, lLength + match.index + lastCharLength + rLength);\n                // Create `em` if smallest delimiter has odd char count. *a***\n                if (Math.min(lLength, rLength) % 2) {\n                    const text = raw.slice(1, -1);\n                    return {\n                        type: 'em',\n                        raw,\n                        text,\n                        tokens: this.lexer.inlineTokens(text)\n                    };\n                }\n                // Create 'strong' if smallest delimiter has even char count. **a***\n                const text = raw.slice(2, -2);\n                return {\n                    type: 'strong',\n                    raw,\n                    text,\n                    tokens: this.lexer.inlineTokens(text)\n                };\n            }\n        }\n    }\n    codespan(src) {\n        const cap = this.rules.inline.code.exec(src);\n        if (cap) {\n            let text = cap[2].replace(/\\n/g, ' ');\n            const hasNonSpaceChars = /[^ ]/.test(text);\n            const hasSpaceCharsOnBothEnds = /^ /.test(text) && / $/.test(text);\n            if (hasNonSpaceChars && hasSpaceCharsOnBothEnds) {\n                text = text.substring(1, text.length - 1);\n            }\n            text = escape$1(text, true);\n            return {\n                type: 'codespan',\n                raw: cap[0],\n                text\n            };\n        }\n    }\n    br(src) {\n        const cap = this.rules.inline.br.exec(src);\n        if (cap) {\n            return {\n                type: 'br',\n                raw: cap[0]\n            };\n        }\n    }\n    del(src) {\n        const cap = this.rules.inline.del.exec(src);\n        if (cap) {\n            return {\n                type: 'del',\n                raw: cap[0],\n                text: cap[2],\n                tokens: this.lexer.inlineTokens(cap[2])\n            };\n        }\n    }\n    autolink(src) {\n        const cap = this.rules.inline.autolink.exec(src);\n        if (cap) {\n            let text, href;\n            if (cap[2] === '@') {\n                text = escape$1(cap[1]);\n                href = 'mailto:' + text;\n            }\n            else {\n                text = escape$1(cap[1]);\n                href = text;\n            }\n            return {\n                type: 'link',\n                raw: cap[0],\n                text,\n                href,\n                tokens: [\n                    {\n                        type: 'text',\n                        raw: text,\n                        text\n                    }\n                ]\n            };\n        }\n    }\n    url(src) {\n        let cap;\n        if (cap = this.rules.inline.url.exec(src)) {\n            let text, href;\n            if (cap[2] === '@') {\n                text = escape$1(cap[0]);\n                href = 'mailto:' + text;\n            }\n            else {\n                // do extended autolink path validation\n                let prevCapZero;\n                do {\n                    prevCapZero = cap[0];\n                    cap[0] = this.rules.inline._backpedal.exec(cap[0])?.[0] ?? '';\n                } while (prevCapZero !== cap[0]);\n                text = escape$1(cap[0]);\n                if (cap[1] === 'www.') {\n                    href = 'http://' + cap[0];\n                }\n                else {\n                    href = cap[0];\n                }\n            }\n            return {\n                type: 'link',\n                raw: cap[0],\n                text,\n                href,\n                tokens: [\n                    {\n                        type: 'text',\n                        raw: text,\n                        text\n                    }\n                ]\n            };\n        }\n    }\n    inlineText(src) {\n        const cap = this.rules.inline.text.exec(src);\n        if (cap) {\n            let text;\n            if (this.lexer.state.inRawBlock) {\n                text = cap[0];\n            }\n            else {\n                text = escape$1(cap[0]);\n            }\n            return {\n                type: 'text',\n                raw: cap[0],\n                text\n            };\n        }\n    }\n}\n\n/**\n * Block-Level Grammar\n */\nconst newline = /^(?: *(?:\\n|$))+/;\nconst blockCode = /^( {4}[^\\n]+(?:\\n(?: *(?:\\n|$))*)?)+/;\nconst fences = /^ {0,3}(`{3,}(?=[^`\\n]*(?:\\n|$))|~{3,})([^\\n]*)(?:\\n|$)(?:|([\\s\\S]*?)(?:\\n|$))(?: {0,3}\\1[~`]* *(?=\\n|$)|$)/;\nconst hr = /^ {0,3}((?:-[\\t ]*){3,}|(?:_[ \\t]*){3,}|(?:\\*[ \\t]*){3,})(?:\\n+|$)/;\nconst heading = /^ {0,3}(#{1,6})(?=\\s|$)(.*)(?:\\n+|$)/;\nconst bullet = /(?:[*+-]|\\d{1,9}[.)])/;\nconst lheading = edit(/^(?!bull )((?:.|\\n(?!\\s*?\\n|bull ))+?)\\n {0,3}(=+|-+) *(?:\\n+|$)/)\n    .replace(/bull/g, bullet) // lists can interrupt\n    .getRegex();\nconst _paragraph = /^([^\\n]+(?:\\n(?!hr|heading|lheading|blockquote|fences|list|html|table| +\\n)[^\\n]+)*)/;\nconst blockText = /^[^\\n]+/;\nconst _blockLabel = /(?!\\s*\\])(?:\\\\.|[^\\[\\]\\\\])+/;\nconst def = edit(/^ {0,3}\\[(label)\\]: *(?:\\n *)?([^<\\s][^\\s]*|<.*?>)(?:(?: +(?:\\n *)?| *\\n *)(title))? *(?:\\n+|$)/)\n    .replace('label', _blockLabel)\n    .replace('title', /(?:\"(?:\\\\\"?|[^\"\\\\])*\"|'[^'\\n]*(?:\\n[^'\\n]+)*\\n?'|\\([^()]*\\))/)\n    .getRegex();\nconst list = edit(/^( {0,3}bull)([ \\t][^\\n]+?)?(?:\\n|$)/)\n    .replace(/bull/g, bullet)\n    .getRegex();\nconst _tag = 'address|article|aside|base|basefont|blockquote|body|caption'\n    + '|center|col|colgroup|dd|details|dialog|dir|div|dl|dt|fieldset|figcaption'\n    + '|figure|footer|form|frame|frameset|h[1-6]|head|header|hr|html|iframe'\n    + '|legend|li|link|main|menu|menuitem|meta|nav|noframes|ol|optgroup|option'\n    + '|p|param|search|section|summary|table|tbody|td|tfoot|th|thead|title'\n    + '|tr|track|ul';\nconst _comment = /<!--(?:-?>|[\\s\\S]*?(?:-->|$))/;\nconst html = edit('^ {0,3}(?:' // optional indentation\n    + '<(script|pre|style|textarea)[\\\\s>][\\\\s\\\\S]*?(?:</\\\\1>[^\\\\n]*\\\\n+|$)' // (1)\n    + '|comment[^\\\\n]*(\\\\n+|$)' // (2)\n    + '|<\\\\?[\\\\s\\\\S]*?(?:\\\\?>\\\\n*|$)' // (3)\n    + '|<![A-Z][\\\\s\\\\S]*?(?:>\\\\n*|$)' // (4)\n    + '|<!\\\\[CDATA\\\\[[\\\\s\\\\S]*?(?:\\\\]\\\\]>\\\\n*|$)' // (5)\n    + '|</?(tag)(?: +|\\\\n|/?>)[\\\\s\\\\S]*?(?:(?:\\\\n *)+\\\\n|$)' // (6)\n    + '|<(?!script|pre|style|textarea)([a-z][\\\\w-]*)(?:attribute)*? */?>(?=[ \\\\t]*(?:\\\\n|$))[\\\\s\\\\S]*?(?:(?:\\\\n *)+\\\\n|$)' // (7) open tag\n    + '|</(?!script|pre|style|textarea)[a-z][\\\\w-]*\\\\s*>(?=[ \\\\t]*(?:\\\\n|$))[\\\\s\\\\S]*?(?:(?:\\\\n *)+\\\\n|$)' // (7) closing tag\n    + ')', 'i')\n    .replace('comment', _comment)\n    .replace('tag', _tag)\n    .replace('attribute', / +[a-zA-Z:_][\\w.:-]*(?: *= *\"[^\"\\n]*\"| *= *'[^'\\n]*'| *= *[^\\s\"'=<>`]+)?/)\n    .getRegex();\nconst paragraph = edit(_paragraph)\n    .replace('hr', hr)\n    .replace('heading', ' {0,3}#{1,6}(?:\\\\s|$)')\n    .replace('|lheading', '') // setex headings don't interrupt commonmark paragraphs\n    .replace('|table', '')\n    .replace('blockquote', ' {0,3}>')\n    .replace('fences', ' {0,3}(?:`{3,}(?=[^`\\\\n]*\\\\n)|~{3,})[^\\\\n]*\\\\n')\n    .replace('list', ' {0,3}(?:[*+-]|1[.)]) ') // only lists starting from 1 can interrupt\n    .replace('html', '</?(?:tag)(?: +|\\\\n|/?>)|<(?:script|pre|style|textarea|!--)')\n    .replace('tag', _tag) // pars can be interrupted by type (6) html blocks\n    .getRegex();\nconst blockquote = edit(/^( {0,3}> ?(paragraph|[^\\n]*)(?:\\n|$))+/)\n    .replace('paragraph', paragraph)\n    .getRegex();\n/**\n * Normal Block Grammar\n */\nconst blockNormal = {\n    blockquote,\n    code: blockCode,\n    def,\n    fences,\n    heading,\n    hr,\n    html,\n    lheading,\n    list,\n    newline,\n    paragraph,\n    table: noopTest,\n    text: blockText\n};\n/**\n * GFM Block Grammar\n */\nconst gfmTable = edit('^ *([^\\\\n ].*)\\\\n' // Header\n    + ' {0,3}((?:\\\\| *)?:?-+:? *(?:\\\\| *:?-+:? *)*(?:\\\\| *)?)' // Align\n    + '(?:\\\\n((?:(?! *\\\\n|hr|heading|blockquote|code|fences|list|html).*(?:\\\\n|$))*)\\\\n*|$)') // Cells\n    .replace('hr', hr)\n    .replace('heading', ' {0,3}#{1,6}(?:\\\\s|$)')\n    .replace('blockquote', ' {0,3}>')\n    .replace('code', ' {4}[^\\\\n]')\n    .replace('fences', ' {0,3}(?:`{3,}(?=[^`\\\\n]*\\\\n)|~{3,})[^\\\\n]*\\\\n')\n    .replace('list', ' {0,3}(?:[*+-]|1[.)]) ') // only lists starting from 1 can interrupt\n    .replace('html', '</?(?:tag)(?: +|\\\\n|/?>)|<(?:script|pre|style|textarea|!--)')\n    .replace('tag', _tag) // tables can be interrupted by type (6) html blocks\n    .getRegex();\nconst blockGfm = {\n    ...blockNormal,\n    table: gfmTable,\n    paragraph: edit(_paragraph)\n        .replace('hr', hr)\n        .replace('heading', ' {0,3}#{1,6}(?:\\\\s|$)')\n        .replace('|lheading', '') // setex headings don't interrupt commonmark paragraphs\n        .replace('table', gfmTable) // interrupt paragraphs with table\n        .replace('blockquote', ' {0,3}>')\n        .replace('fences', ' {0,3}(?:`{3,}(?=[^`\\\\n]*\\\\n)|~{3,})[^\\\\n]*\\\\n')\n        .replace('list', ' {0,3}(?:[*+-]|1[.)]) ') // only lists starting from 1 can interrupt\n        .replace('html', '</?(?:tag)(?: +|\\\\n|/?>)|<(?:script|pre|style|textarea|!--)')\n        .replace('tag', _tag) // pars can be interrupted by type (6) html blocks\n        .getRegex()\n};\n/**\n * Pedantic grammar (original John Gruber's loose markdown specification)\n */\nconst blockPedantic = {\n    ...blockNormal,\n    html: edit('^ *(?:comment *(?:\\\\n|\\\\s*$)'\n        + '|<(tag)[\\\\s\\\\S]+?</\\\\1> *(?:\\\\n{2,}|\\\\s*$)' // closed tag\n        + '|<tag(?:\"[^\"]*\"|\\'[^\\']*\\'|\\\\s[^\\'\"/>\\\\s]*)*?/?> *(?:\\\\n{2,}|\\\\s*$))')\n        .replace('comment', _comment)\n        .replace(/tag/g, '(?!(?:'\n        + 'a|em|strong|small|s|cite|q|dfn|abbr|data|time|code|var|samp|kbd|sub'\n        + '|sup|i|b|u|mark|ruby|rt|rp|bdi|bdo|span|br|wbr|ins|del|img)'\n        + '\\\\b)\\\\w+(?!:|[^\\\\w\\\\s@]*@)\\\\b')\n        .getRegex(),\n    def: /^ *\\[([^\\]]+)\\]: *<?([^\\s>]+)>?(?: +([\"(][^\\n]+[\")]))? *(?:\\n+|$)/,\n    heading: /^(#{1,6})(.*)(?:\\n+|$)/,\n    fences: noopTest, // fences not supported\n    lheading: /^(.+?)\\n {0,3}(=+|-+) *(?:\\n+|$)/,\n    paragraph: edit(_paragraph)\n        .replace('hr', hr)\n        .replace('heading', ' *#{1,6} *[^\\n]')\n        .replace('lheading', lheading)\n        .replace('|table', '')\n        .replace('blockquote', ' {0,3}>')\n        .replace('|fences', '')\n        .replace('|list', '')\n        .replace('|html', '')\n        .replace('|tag', '')\n        .getRegex()\n};\n/**\n * Inline-Level Grammar\n */\nconst escape = /^\\\\([!\"#$%&'()*+,\\-./:;<=>?@\\[\\]\\\\^_`{|}~])/;\nconst inlineCode = /^(`+)([^`]|[^`][\\s\\S]*?[^`])\\1(?!`)/;\nconst br = /^( {2,}|\\\\)\\n(?!\\s*$)/;\nconst inlineText = /^(`+|[^`])(?:(?= {2,}\\n)|[\\s\\S]*?(?:(?=[\\\\<!\\[`*_]|\\b_|$)|[^ ](?= {2,}\\n)))/;\n// list of unicode punctuation marks, plus any missing characters from CommonMark spec\nconst _punctuation = '\\\\p{P}\\\\p{S}';\nconst punctuation = edit(/^((?![*_])[\\spunctuation])/, 'u')\n    .replace(/punctuation/g, _punctuation).getRegex();\n// sequences em should skip over [title](link), `code`, <html>\nconst blockSkip = /\\[[^[\\]]*?\\]\\([^\\(\\)]*?\\)|`[^`]*?`|<[^<>]*?>/g;\nconst emStrongLDelim = edit(/^(?:\\*+(?:((?!\\*)[punct])|[^\\s*]))|^_+(?:((?!_)[punct])|([^\\s_]))/, 'u')\n    .replace(/punct/g, _punctuation)\n    .getRegex();\nconst emStrongRDelimAst = edit('^[^_*]*?__[^_*]*?\\\\*[^_*]*?(?=__)' // Skip orphan inside strong\n    + '|[^*]+(?=[^*])' // Consume to delim\n    + '|(?!\\\\*)[punct](\\\\*+)(?=[\\\\s]|$)' // (1) #*** can only be a Right Delimiter\n    + '|[^punct\\\\s](\\\\*+)(?!\\\\*)(?=[punct\\\\s]|$)' // (2) a***#, a*** can only be a Right Delimiter\n    + '|(?!\\\\*)[punct\\\\s](\\\\*+)(?=[^punct\\\\s])' // (3) #***a, ***a can only be Left Delimiter\n    + '|[\\\\s](\\\\*+)(?!\\\\*)(?=[punct])' // (4) ***# can only be Left Delimiter\n    + '|(?!\\\\*)[punct](\\\\*+)(?!\\\\*)(?=[punct])' // (5) #***# can be either Left or Right Delimiter\n    + '|[^punct\\\\s](\\\\*+)(?=[^punct\\\\s])', 'gu') // (6) a***a can be either Left or Right Delimiter\n    .replace(/punct/g, _punctuation)\n    .getRegex();\n// (6) Not allowed for _\nconst emStrongRDelimUnd = edit('^[^_*]*?\\\\*\\\\*[^_*]*?_[^_*]*?(?=\\\\*\\\\*)' // Skip orphan inside strong\n    + '|[^_]+(?=[^_])' // Consume to delim\n    + '|(?!_)[punct](_+)(?=[\\\\s]|$)' // (1) #___ can only be a Right Delimiter\n    + '|[^punct\\\\s](_+)(?!_)(?=[punct\\\\s]|$)' // (2) a___#, a___ can only be a Right Delimiter\n    + '|(?!_)[punct\\\\s](_+)(?=[^punct\\\\s])' // (3) #___a, ___a can only be Left Delimiter\n    + '|[\\\\s](_+)(?!_)(?=[punct])' // (4) ___# can only be Left Delimiter\n    + '|(?!_)[punct](_+)(?!_)(?=[punct])', 'gu') // (5) #___# can be either Left or Right Delimiter\n    .replace(/punct/g, _punctuation)\n    .getRegex();\nconst anyPunctuation = edit(/\\\\([punct])/, 'gu')\n    .replace(/punct/g, _punctuation)\n    .getRegex();\nconst autolink = edit(/^<(scheme:[^\\s\\x00-\\x1f<>]*|email)>/)\n    .replace('scheme', /[a-zA-Z][a-zA-Z0-9+.-]{1,31}/)\n    .replace('email', /[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+(@)[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)+(?![-_])/)\n    .getRegex();\nconst _inlineComment = edit(_comment).replace('(?:-->|$)', '-->').getRegex();\nconst tag = edit('^comment'\n    + '|^</[a-zA-Z][\\\\w:-]*\\\\s*>' // self-closing tag\n    + '|^<[a-zA-Z][\\\\w-]*(?:attribute)*?\\\\s*/?>' // open tag\n    + '|^<\\\\?[\\\\s\\\\S]*?\\\\?>' // processing instruction, e.g. <?php ?>\n    + '|^<![a-zA-Z]+\\\\s[\\\\s\\\\S]*?>' // declaration, e.g. <!DOCTYPE html>\n    + '|^<!\\\\[CDATA\\\\[[\\\\s\\\\S]*?\\\\]\\\\]>') // CDATA section\n    .replace('comment', _inlineComment)\n    .replace('attribute', /\\s+[a-zA-Z:_][\\w.:-]*(?:\\s*=\\s*\"[^\"]*\"|\\s*=\\s*'[^']*'|\\s*=\\s*[^\\s\"'=<>`]+)?/)\n    .getRegex();\nconst _inlineLabel = /(?:\\[(?:\\\\.|[^\\[\\]\\\\])*\\]|\\\\.|`[^`]*`|[^\\[\\]\\\\`])*?/;\nconst link = edit(/^!?\\[(label)\\]\\(\\s*(href)(?:\\s+(title))?\\s*\\)/)\n    .replace('label', _inlineLabel)\n    .replace('href', /<(?:\\\\.|[^\\n<>\\\\])+>|[^\\s\\x00-\\x1f]*/)\n    .replace('title', /\"(?:\\\\\"?|[^\"\\\\])*\"|'(?:\\\\'?|[^'\\\\])*'|\\((?:\\\\\\)?|[^)\\\\])*\\)/)\n    .getRegex();\nconst reflink = edit(/^!?\\[(label)\\]\\[(ref)\\]/)\n    .replace('label', _inlineLabel)\n    .replace('ref', _blockLabel)\n    .getRegex();\nconst nolink = edit(/^!?\\[(ref)\\](?:\\[\\])?/)\n    .replace('ref', _blockLabel)\n    .getRegex();\nconst reflinkSearch = edit('reflink|nolink(?!\\\\()', 'g')\n    .replace('reflink', reflink)\n    .replace('nolink', nolink)\n    .getRegex();\n/**\n * Normal Inline Grammar\n */\nconst inlineNormal = {\n    _backpedal: noopTest, // only used for GFM url\n    anyPunctuation,\n    autolink,\n    blockSkip,\n    br,\n    code: inlineCode,\n    del: noopTest,\n    emStrongLDelim,\n    emStrongRDelimAst,\n    emStrongRDelimUnd,\n    escape,\n    link,\n    nolink,\n    punctuation,\n    reflink,\n    reflinkSearch,\n    tag,\n    text: inlineText,\n    url: noopTest\n};\n/**\n * Pedantic Inline Grammar\n */\nconst inlinePedantic = {\n    ...inlineNormal,\n    link: edit(/^!?\\[(label)\\]\\((.*?)\\)/)\n        .replace('label', _inlineLabel)\n        .getRegex(),\n    reflink: edit(/^!?\\[(label)\\]\\s*\\[([^\\]]*)\\]/)\n        .replace('label', _inlineLabel)\n        .getRegex()\n};\n/**\n * GFM Inline Grammar\n */\nconst inlineGfm = {\n    ...inlineNormal,\n    escape: edit(escape).replace('])', '~|])').getRegex(),\n    url: edit(/^((?:ftp|https?):\\/\\/|www\\.)(?:[a-zA-Z0-9\\-]+\\.?)+[^\\s<]*|^email/, 'i')\n        .replace('email', /[A-Za-z0-9._+-]+(@)[a-zA-Z0-9-_]+(?:\\.[a-zA-Z0-9-_]*[a-zA-Z0-9])+(?![-_])/)\n        .getRegex(),\n    _backpedal: /(?:[^?!.,:;*_'\"~()&]+|\\([^)]*\\)|&(?![a-zA-Z0-9]+;$)|[?!.,:;*_'\"~)]+(?!$))+/,\n    del: /^(~~?)(?=[^\\s~])([\\s\\S]*?[^\\s~])\\1(?=[^~]|$)/,\n    text: /^([`~]+|[^`~])(?:(?= {2,}\\n)|(?=[a-zA-Z0-9.!#$%&'*+\\/=?_`{\\|}~-]+@)|[\\s\\S]*?(?:(?=[\\\\<!\\[`*~_]|\\b_|https?:\\/\\/|ftp:\\/\\/|www\\.|$)|[^ ](?= {2,}\\n)|[^a-zA-Z0-9.!#$%&'*+\\/=?_`{\\|}~-](?=[a-zA-Z0-9.!#$%&'*+\\/=?_`{\\|}~-]+@)))/\n};\n/**\n * GFM + Line Breaks Inline Grammar\n */\nconst inlineBreaks = {\n    ...inlineGfm,\n    br: edit(br).replace('{2,}', '*').getRegex(),\n    text: edit(inlineGfm.text)\n        .replace('\\\\b_', '\\\\b_| {2,}\\\\n')\n        .replace(/\\{2,\\}/g, '*')\n        .getRegex()\n};\n/**\n * exports\n */\nconst block = {\n    normal: blockNormal,\n    gfm: blockGfm,\n    pedantic: blockPedantic\n};\nconst inline = {\n    normal: inlineNormal,\n    gfm: inlineGfm,\n    breaks: inlineBreaks,\n    pedantic: inlinePedantic\n};\n\n/**\n * Block Lexer\n */\nclass _Lexer {\n    tokens;\n    options;\n    state;\n    tokenizer;\n    inlineQueue;\n    constructor(options) {\n        // TokenList cannot be created in one go\n        this.tokens = [];\n        this.tokens.links = Object.create(null);\n        this.options = options || _defaults;\n        this.options.tokenizer = this.options.tokenizer || new _Tokenizer();\n        this.tokenizer = this.options.tokenizer;\n        this.tokenizer.options = this.options;\n        this.tokenizer.lexer = this;\n        this.inlineQueue = [];\n        this.state = {\n            inLink: false,\n            inRawBlock: false,\n            top: true\n        };\n        const rules = {\n            block: block.normal,\n            inline: inline.normal\n        };\n        if (this.options.pedantic) {\n            rules.block = block.pedantic;\n            rules.inline = inline.pedantic;\n        }\n        else if (this.options.gfm) {\n            rules.block = block.gfm;\n            if (this.options.breaks) {\n                rules.inline = inline.breaks;\n            }\n            else {\n                rules.inline = inline.gfm;\n            }\n        }\n        this.tokenizer.rules = rules;\n    }\n    /**\n     * Expose Rules\n     */\n    static get rules() {\n        return {\n            block,\n            inline\n        };\n    }\n    /**\n     * Static Lex Method\n     */\n    static lex(src, options) {\n        const lexer = new _Lexer(options);\n        return lexer.lex(src);\n    }\n    /**\n     * Static Lex Inline Method\n     */\n    static lexInline(src, options) {\n        const lexer = new _Lexer(options);\n        return lexer.inlineTokens(src);\n    }\n    /**\n     * Preprocessing\n     */\n    lex(src) {\n        src = src\n            .replace(/\\r\\n|\\r/g, '\\n');\n        this.blockTokens(src, this.tokens);\n        for (let i = 0; i < this.inlineQueue.length; i++) {\n            const next = this.inlineQueue[i];\n            this.inlineTokens(next.src, next.tokens);\n        }\n        this.inlineQueue = [];\n        return this.tokens;\n    }\n    blockTokens(src, tokens = []) {\n        if (this.options.pedantic) {\n            src = src.replace(/\\t/g, '    ').replace(/^ +$/gm, '');\n        }\n        else {\n            src = src.replace(/^( *)(\\t+)/gm, (_, leading, tabs) => {\n                return leading + '    '.repeat(tabs.length);\n            });\n        }\n        let token;\n        let lastToken;\n        let cutSrc;\n        let lastParagraphClipped;\n        while (src) {\n            if (this.options.extensions\n                && this.options.extensions.block\n                && this.options.extensions.block.some((extTokenizer) => {\n                    if (token = extTokenizer.call({ lexer: this }, src, tokens)) {\n                        src = src.substring(token.raw.length);\n                        tokens.push(token);\n                        return true;\n                    }\n                    return false;\n                })) {\n                continue;\n            }\n            // newline\n            if (token = this.tokenizer.space(src)) {\n                src = src.substring(token.raw.length);\n                if (token.raw.length === 1 && tokens.length > 0) {\n                    // if there's a single \\n as a spacer, it's terminating the last line,\n                    // so move it there so that we don't get unnecessary paragraph tags\n                    tokens[tokens.length - 1].raw += '\\n';\n                }\n                else {\n                    tokens.push(token);\n                }\n                continue;\n            }\n            // code\n            if (token = this.tokenizer.code(src)) {\n                src = src.substring(token.raw.length);\n                lastToken = tokens[tokens.length - 1];\n                // An indented code block cannot interrupt a paragraph.\n                if (lastToken && (lastToken.type === 'paragraph' || lastToken.type === 'text')) {\n                    lastToken.raw += '\\n' + token.raw;\n                    lastToken.text += '\\n' + token.text;\n                    this.inlineQueue[this.inlineQueue.length - 1].src = lastToken.text;\n                }\n                else {\n                    tokens.push(token);\n                }\n                continue;\n            }\n            // fences\n            if (token = this.tokenizer.fences(src)) {\n                src = src.substring(token.raw.length);\n                tokens.push(token);\n                continue;\n            }\n            // heading\n            if (token = this.tokenizer.heading(src)) {\n                src = src.substring(token.raw.length);\n                tokens.push(token);\n                continue;\n            }\n            // hr\n            if (token = this.tokenizer.hr(src)) {\n                src = src.substring(token.raw.length);\n                tokens.push(token);\n                continue;\n            }\n            // blockquote\n            if (token = this.tokenizer.blockquote(src)) {\n                src = src.substring(token.raw.length);\n                tokens.push(token);\n                continue;\n            }\n            // list\n            if (token = this.tokenizer.list(src)) {\n                src = src.substring(token.raw.length);\n                tokens.push(token);\n                continue;\n            }\n            // html\n            if (token = this.tokenizer.html(src)) {\n                src = src.substring(token.raw.length);\n                tokens.push(token);\n                continue;\n            }\n            // def\n            if (token = this.tokenizer.def(src)) {\n                src = src.substring(token.raw.length);\n                lastToken = tokens[tokens.length - 1];\n                if (lastToken && (lastToken.type === 'paragraph' || lastToken.type === 'text')) {\n                    lastToken.raw += '\\n' + token.raw;\n                    lastToken.text += '\\n' + token.raw;\n                    this.inlineQueue[this.inlineQueue.length - 1].src = lastToken.text;\n                }\n                else if (!this.tokens.links[token.tag]) {\n                    this.tokens.links[token.tag] = {\n                        href: token.href,\n                        title: token.title\n                    };\n                }\n                continue;\n            }\n            // table (gfm)\n            if (token = this.tokenizer.table(src)) {\n                src = src.substring(token.raw.length);\n                tokens.push(token);\n                continue;\n            }\n            // lheading\n            if (token = this.tokenizer.lheading(src)) {\n                src = src.substring(token.raw.length);\n                tokens.push(token);\n                continue;\n            }\n            // top-level paragraph\n            // prevent paragraph consuming extensions by clipping 'src' to extension start\n            cutSrc = src;\n            if (this.options.extensions && this.options.extensions.startBlock) {\n                let startIndex = Infinity;\n                const tempSrc = src.slice(1);\n                let tempStart;\n                this.options.extensions.startBlock.forEach((getStartIndex) => {\n                    tempStart = getStartIndex.call({ lexer: this }, tempSrc);\n                    if (typeof tempStart === 'number' && tempStart >= 0) {\n                        startIndex = Math.min(startIndex, tempStart);\n                    }\n                });\n                if (startIndex < Infinity && startIndex >= 0) {\n                    cutSrc = src.substring(0, startIndex + 1);\n                }\n            }\n            if (this.state.top && (token = this.tokenizer.paragraph(cutSrc))) {\n                lastToken = tokens[tokens.length - 1];\n                if (lastParagraphClipped && lastToken.type === 'paragraph') {\n                    lastToken.raw += '\\n' + token.raw;\n                    lastToken.text += '\\n' + token.text;\n                    this.inlineQueue.pop();\n                    this.inlineQueue[this.inlineQueue.length - 1].src = lastToken.text;\n                }\n                else {\n                    tokens.push(token);\n                }\n                lastParagraphClipped = (cutSrc.length !== src.length);\n                src = src.substring(token.raw.length);\n                continue;\n            }\n            // text\n            if (token = this.tokenizer.text(src)) {\n                src = src.substring(token.raw.length);\n                lastToken = tokens[tokens.length - 1];\n                if (lastToken && lastToken.type === 'text') {\n                    lastToken.raw += '\\n' + token.raw;\n                    lastToken.text += '\\n' + token.text;\n                    this.inlineQueue.pop();\n                    this.inlineQueue[this.inlineQueue.length - 1].src = lastToken.text;\n                }\n                else {\n                    tokens.push(token);\n                }\n                continue;\n            }\n            if (src) {\n                const errMsg = 'Infinite loop on byte: ' + src.charCodeAt(0);\n                if (this.options.silent) {\n                    console.error(errMsg);\n                    break;\n                }\n                else {\n                    throw new Error(errMsg);\n                }\n            }\n        }\n        this.state.top = true;\n        return tokens;\n    }\n    inline(src, tokens = []) {\n        this.inlineQueue.push({ src, tokens });\n        return tokens;\n    }\n    /**\n     * Lexing/Compiling\n     */\n    inlineTokens(src, tokens = []) {\n        let token, lastToken, cutSrc;\n        // String with links masked to avoid interference with em and strong\n        let maskedSrc = src;\n        let match;\n        let keepPrevChar, prevChar;\n        // Mask out reflinks\n        if (this.tokens.links) {\n            const links = Object.keys(this.tokens.links);\n            if (links.length > 0) {\n                while ((match = this.tokenizer.rules.inline.reflinkSearch.exec(maskedSrc)) != null) {\n                    if (links.includes(match[0].slice(match[0].lastIndexOf('[') + 1, -1))) {\n                        maskedSrc = maskedSrc.slice(0, match.index) + '[' + 'a'.repeat(match[0].length - 2) + ']' + maskedSrc.slice(this.tokenizer.rules.inline.reflinkSearch.lastIndex);\n                    }\n                }\n            }\n        }\n        // Mask out other blocks\n        while ((match = this.tokenizer.rules.inline.blockSkip.exec(maskedSrc)) != null) {\n            maskedSrc = maskedSrc.slice(0, match.index) + '[' + 'a'.repeat(match[0].length - 2) + ']' + maskedSrc.slice(this.tokenizer.rules.inline.blockSkip.lastIndex);\n        }\n        // Mask out escaped characters\n        while ((match = this.tokenizer.rules.inline.anyPunctuation.exec(maskedSrc)) != null) {\n            maskedSrc = maskedSrc.slice(0, match.index) + '++' + maskedSrc.slice(this.tokenizer.rules.inline.anyPunctuation.lastIndex);\n        }\n        while (src) {\n            if (!keepPrevChar) {\n                prevChar = '';\n            }\n            keepPrevChar = false;\n            // extensions\n            if (this.options.extensions\n                && this.options.extensions.inline\n                && this.options.extensions.inline.some((extTokenizer) => {\n                    if (token = extTokenizer.call({ lexer: this }, src, tokens)) {\n                        src = src.substring(token.raw.length);\n                        tokens.push(token);\n                        return true;\n                    }\n                    return false;\n                })) {\n                continue;\n            }\n            // escape\n            if (token = this.tokenizer.escape(src)) {\n                src = src.substring(token.raw.length);\n                tokens.push(token);\n                continue;\n            }\n            // tag\n            if (token = this.tokenizer.tag(src)) {\n                src = src.substring(token.raw.length);\n                lastToken = tokens[tokens.length - 1];\n                if (lastToken && token.type === 'text' && lastToken.type === 'text') {\n                    lastToken.raw += token.raw;\n                    lastToken.text += token.text;\n                }\n                else {\n                    tokens.push(token);\n                }\n                continue;\n            }\n            // link\n            if (token = this.tokenizer.link(src)) {\n                src = src.substring(token.raw.length);\n                tokens.push(token);\n                continue;\n            }\n            // reflink, nolink\n            if (token = this.tokenizer.reflink(src, this.tokens.links)) {\n                src = src.substring(token.raw.length);\n                lastToken = tokens[tokens.length - 1];\n                if (lastToken && token.type === 'text' && lastToken.type === 'text') {\n                    lastToken.raw += token.raw;\n                    lastToken.text += token.text;\n                }\n                else {\n                    tokens.push(token);\n                }\n                continue;\n            }\n            // em & strong\n            if (token = this.tokenizer.emStrong(src, maskedSrc, prevChar)) {\n                src = src.substring(token.raw.length);\n                tokens.push(token);\n                continue;\n            }\n            // code\n            if (token = this.tokenizer.codespan(src)) {\n                src = src.substring(token.raw.length);\n                tokens.push(token);\n                continue;\n            }\n            // br\n            if (token = this.tokenizer.br(src)) {\n                src = src.substring(token.raw.length);\n                tokens.push(token);\n                continue;\n            }\n            // del (gfm)\n            if (token = this.tokenizer.del(src)) {\n                src = src.substring(token.raw.length);\n                tokens.push(token);\n                continue;\n            }\n            // autolink\n            if (token = this.tokenizer.autolink(src)) {\n                src = src.substring(token.raw.length);\n                tokens.push(token);\n                continue;\n            }\n            // url (gfm)\n            if (!this.state.inLink && (token = this.tokenizer.url(src))) {\n                src = src.substring(token.raw.length);\n                tokens.push(token);\n                continue;\n            }\n            // text\n            // prevent inlineText consuming extensions by clipping 'src' to extension start\n            cutSrc = src;\n            if (this.options.extensions && this.options.extensions.startInline) {\n                let startIndex = Infinity;\n                const tempSrc = src.slice(1);\n                let tempStart;\n                this.options.extensions.startInline.forEach((getStartIndex) => {\n                    tempStart = getStartIndex.call({ lexer: this }, tempSrc);\n                    if (typeof tempStart === 'number' && tempStart >= 0) {\n                        startIndex = Math.min(startIndex, tempStart);\n                    }\n                });\n                if (startIndex < Infinity && startIndex >= 0) {\n                    cutSrc = src.substring(0, startIndex + 1);\n                }\n            }\n            if (token = this.tokenizer.inlineText(cutSrc)) {\n                src = src.substring(token.raw.length);\n                if (token.raw.slice(-1) !== '_') { // Track prevChar before string of ____ started\n                    prevChar = token.raw.slice(-1);\n                }\n                keepPrevChar = true;\n                lastToken = tokens[tokens.length - 1];\n                if (lastToken && lastToken.type === 'text') {\n                    lastToken.raw += token.raw;\n                    lastToken.text += token.text;\n                }\n                else {\n                    tokens.push(token);\n                }\n                continue;\n            }\n            if (src) {\n                const errMsg = 'Infinite loop on byte: ' + src.charCodeAt(0);\n                if (this.options.silent) {\n                    console.error(errMsg);\n                    break;\n                }\n                else {\n                    throw new Error(errMsg);\n                }\n            }\n        }\n        return tokens;\n    }\n}\n\n/**\n * Renderer\n */\nclass _Renderer {\n    options;\n    constructor(options) {\n        this.options = options || _defaults;\n    }\n    code(code, infostring, escaped) {\n        const lang = (infostring || '').match(/^\\S*/)?.[0];\n        code = code.replace(/\\n$/, '') + '\\n';\n        if (!lang) {\n            return '<pre><code>'\n                + (escaped ? code : escape$1(code, true))\n                + '</code></pre>\\n';\n        }\n        return '<pre><code class=\"language-'\n            + escape$1(lang)\n            + '\">'\n            + (escaped ? code : escape$1(code, true))\n            + '</code></pre>\\n';\n    }\n    blockquote(quote) {\n        return `<blockquote>\\n${quote}</blockquote>\\n`;\n    }\n    html(html, block) {\n        return html;\n    }\n    heading(text, level, raw) {\n        // ignore IDs\n        return `<h${level}>${text}</h${level}>\\n`;\n    }\n    hr() {\n        return '<hr>\\n';\n    }\n    list(body, ordered, start) {\n        const type = ordered ? 'ol' : 'ul';\n        const startatt = (ordered && start !== 1) ? (' start=\"' + start + '\"') : '';\n        return '<' + type + startatt + '>\\n' + body + '</' + type + '>\\n';\n    }\n    listitem(text, task, checked) {\n        return `<li>${text}</li>\\n`;\n    }\n    checkbox(checked) {\n        return '<input '\n            + (checked ? 'checked=\"\" ' : '')\n            + 'disabled=\"\" type=\"checkbox\">';\n    }\n    paragraph(text) {\n        return `<p>${text}</p>\\n`;\n    }\n    table(header, body) {\n        if (body)\n            body = `<tbody>${body}</tbody>`;\n        return '<table>\\n'\n            + '<thead>\\n'\n            + header\n            + '</thead>\\n'\n            + body\n            + '</table>\\n';\n    }\n    tablerow(content) {\n        return `<tr>\\n${content}</tr>\\n`;\n    }\n    tablecell(content, flags) {\n        const type = flags.header ? 'th' : 'td';\n        const tag = flags.align\n            ? `<${type} align=\"${flags.align}\">`\n            : `<${type}>`;\n        return tag + content + `</${type}>\\n`;\n    }\n    /**\n     * span level renderer\n     */\n    strong(text) {\n        return `<strong>${text}</strong>`;\n    }\n    em(text) {\n        return `<em>${text}</em>`;\n    }\n    codespan(text) {\n        return `<code>${text}</code>`;\n    }\n    br() {\n        return '<br>';\n    }\n    del(text) {\n        return `<del>${text}</del>`;\n    }\n    link(href, title, text) {\n        const cleanHref = cleanUrl(href);\n        if (cleanHref === null) {\n            return text;\n        }\n        href = cleanHref;\n        let out = '<a href=\"' + href + '\"';\n        if (title) {\n            out += ' title=\"' + title + '\"';\n        }\n        out += '>' + text + '</a>';\n        return out;\n    }\n    image(href, title, text) {\n        const cleanHref = cleanUrl(href);\n        if (cleanHref === null) {\n            return text;\n        }\n        href = cleanHref;\n        let out = `<img src=\"${href}\" alt=\"${text}\"`;\n        if (title) {\n            out += ` title=\"${title}\"`;\n        }\n        out += '>';\n        return out;\n    }\n    text(text) {\n        return text;\n    }\n}\n\n/**\n * TextRenderer\n * returns only the textual part of the token\n */\nclass _TextRenderer {\n    // no need for block level renderers\n    strong(text) {\n        return text;\n    }\n    em(text) {\n        return text;\n    }\n    codespan(text) {\n        return text;\n    }\n    del(text) {\n        return text;\n    }\n    html(text) {\n        return text;\n    }\n    text(text) {\n        return text;\n    }\n    link(href, title, text) {\n        return '' + text;\n    }\n    image(href, title, text) {\n        return '' + text;\n    }\n    br() {\n        return '';\n    }\n}\n\n/**\n * Parsing & Compiling\n */\nclass _Parser {\n    options;\n    renderer;\n    textRenderer;\n    constructor(options) {\n        this.options = options || _defaults;\n        this.options.renderer = this.options.renderer || new _Renderer();\n        this.renderer = this.options.renderer;\n        this.renderer.options = this.options;\n        this.textRenderer = new _TextRenderer();\n    }\n    /**\n     * Static Parse Method\n     */\n    static parse(tokens, options) {\n        const parser = new _Parser(options);\n        return parser.parse(tokens);\n    }\n    /**\n     * Static Parse Inline Method\n     */\n    static parseInline(tokens, options) {\n        const parser = new _Parser(options);\n        return parser.parseInline(tokens);\n    }\n    /**\n     * Parse Loop\n     */\n    parse(tokens, top = true) {\n        let out = '';\n        for (let i = 0; i < tokens.length; i++) {\n            const token = tokens[i];\n            // Run any renderer extensions\n            if (this.options.extensions && this.options.extensions.renderers && this.options.extensions.renderers[token.type]) {\n                const genericToken = token;\n                const ret = this.options.extensions.renderers[genericToken.type].call({ parser: this }, genericToken);\n                if (ret !== false || !['space', 'hr', 'heading', 'code', 'table', 'blockquote', 'list', 'html', 'paragraph', 'text'].includes(genericToken.type)) {\n                    out += ret || '';\n                    continue;\n                }\n            }\n            switch (token.type) {\n                case 'space': {\n                    continue;\n                }\n                case 'hr': {\n                    out += this.renderer.hr();\n                    continue;\n                }\n                case 'heading': {\n                    const headingToken = token;\n                    out += this.renderer.heading(this.parseInline(headingToken.tokens), headingToken.depth, unescape(this.parseInline(headingToken.tokens, this.textRenderer)));\n                    continue;\n                }\n                case 'code': {\n                    const codeToken = token;\n                    out += this.renderer.code(codeToken.text, codeToken.lang, !!codeToken.escaped);\n                    continue;\n                }\n                case 'table': {\n                    const tableToken = token;\n                    let header = '';\n                    // header\n                    let cell = '';\n                    for (let j = 0; j < tableToken.header.length; j++) {\n                        cell += this.renderer.tablecell(this.parseInline(tableToken.header[j].tokens), { header: true, align: tableToken.align[j] });\n                    }\n                    header += this.renderer.tablerow(cell);\n                    let body = '';\n                    for (let j = 0; j < tableToken.rows.length; j++) {\n                        const row = tableToken.rows[j];\n                        cell = '';\n                        for (let k = 0; k < row.length; k++) {\n                            cell += this.renderer.tablecell(this.parseInline(row[k].tokens), { header: false, align: tableToken.align[k] });\n                        }\n                        body += this.renderer.tablerow(cell);\n                    }\n                    out += this.renderer.table(header, body);\n                    continue;\n                }\n                case 'blockquote': {\n                    const blockquoteToken = token;\n                    const body = this.parse(blockquoteToken.tokens);\n                    out += this.renderer.blockquote(body);\n                    continue;\n                }\n                case 'list': {\n                    const listToken = token;\n                    const ordered = listToken.ordered;\n                    const start = listToken.start;\n                    const loose = listToken.loose;\n                    let body = '';\n                    for (let j = 0; j < listToken.items.length; j++) {\n                        const item = listToken.items[j];\n                        const checked = item.checked;\n                        const task = item.task;\n                        let itemBody = '';\n                        if (item.task) {\n                            const checkbox = this.renderer.checkbox(!!checked);\n                            if (loose) {\n                                if (item.tokens.length > 0 && item.tokens[0].type === 'paragraph') {\n                                    item.tokens[0].text = checkbox + ' ' + item.tokens[0].text;\n                                    if (item.tokens[0].tokens && item.tokens[0].tokens.length > 0 && item.tokens[0].tokens[0].type === 'text') {\n                                        item.tokens[0].tokens[0].text = checkbox + ' ' + item.tokens[0].tokens[0].text;\n                                    }\n                                }\n                                else {\n                                    item.tokens.unshift({\n                                        type: 'text',\n                                        text: checkbox + ' '\n                                    });\n                                }\n                            }\n                            else {\n                                itemBody += checkbox + ' ';\n                            }\n                        }\n                        itemBody += this.parse(item.tokens, loose);\n                        body += this.renderer.listitem(itemBody, task, !!checked);\n                    }\n                    out += this.renderer.list(body, ordered, start);\n                    continue;\n                }\n                case 'html': {\n                    const htmlToken = token;\n                    out += this.renderer.html(htmlToken.text, htmlToken.block);\n                    continue;\n                }\n                case 'paragraph': {\n                    const paragraphToken = token;\n                    out += this.renderer.paragraph(this.parseInline(paragraphToken.tokens));\n                    continue;\n                }\n                case 'text': {\n                    let textToken = token;\n                    let body = textToken.tokens ? this.parseInline(textToken.tokens) : textToken.text;\n                    while (i + 1 < tokens.length && tokens[i + 1].type === 'text') {\n                        textToken = tokens[++i];\n                        body += '\\n' + (textToken.tokens ? this.parseInline(textToken.tokens) : textToken.text);\n                    }\n                    out += top ? this.renderer.paragraph(body) : body;\n                    continue;\n                }\n                default: {\n                    const errMsg = 'Token with \"' + token.type + '\" type was not found.';\n                    if (this.options.silent) {\n                        console.error(errMsg);\n                        return '';\n                    }\n                    else {\n                        throw new Error(errMsg);\n                    }\n                }\n            }\n        }\n        return out;\n    }\n    /**\n     * Parse Inline Tokens\n     */\n    parseInline(tokens, renderer) {\n        renderer = renderer || this.renderer;\n        let out = '';\n        for (let i = 0; i < tokens.length; i++) {\n            const token = tokens[i];\n            // Run any renderer extensions\n            if (this.options.extensions && this.options.extensions.renderers && this.options.extensions.renderers[token.type]) {\n                const ret = this.options.extensions.renderers[token.type].call({ parser: this }, token);\n                if (ret !== false || !['escape', 'html', 'link', 'image', 'strong', 'em', 'codespan', 'br', 'del', 'text'].includes(token.type)) {\n                    out += ret || '';\n                    continue;\n                }\n            }\n            switch (token.type) {\n                case 'escape': {\n                    const escapeToken = token;\n                    out += renderer.text(escapeToken.text);\n                    break;\n                }\n                case 'html': {\n                    const tagToken = token;\n                    out += renderer.html(tagToken.text);\n                    break;\n                }\n                case 'link': {\n                    const linkToken = token;\n                    out += renderer.link(linkToken.href, linkToken.title, this.parseInline(linkToken.tokens, renderer));\n                    break;\n                }\n                case 'image': {\n                    const imageToken = token;\n                    out += renderer.image(imageToken.href, imageToken.title, imageToken.text);\n                    break;\n                }\n                case 'strong': {\n                    const strongToken = token;\n                    out += renderer.strong(this.parseInline(strongToken.tokens, renderer));\n                    break;\n                }\n                case 'em': {\n                    const emToken = token;\n                    out += renderer.em(this.parseInline(emToken.tokens, renderer));\n                    break;\n                }\n                case 'codespan': {\n                    const codespanToken = token;\n                    out += renderer.codespan(codespanToken.text);\n                    break;\n                }\n                case 'br': {\n                    out += renderer.br();\n                    break;\n                }\n                case 'del': {\n                    const delToken = token;\n                    out += renderer.del(this.parseInline(delToken.tokens, renderer));\n                    break;\n                }\n                case 'text': {\n                    const textToken = token;\n                    out += renderer.text(textToken.text);\n                    break;\n                }\n                default: {\n                    const errMsg = 'Token with \"' + token.type + '\" type was not found.';\n                    if (this.options.silent) {\n                        console.error(errMsg);\n                        return '';\n                    }\n                    else {\n                        throw new Error(errMsg);\n                    }\n                }\n            }\n        }\n        return out;\n    }\n}\n\nclass _Hooks {\n    options;\n    constructor(options) {\n        this.options = options || _defaults;\n    }\n    static passThroughHooks = new Set([\n        'preprocess',\n        'postprocess',\n        'processAllTokens'\n    ]);\n    /**\n     * Process markdown before marked\n     */\n    preprocess(markdown) {\n        return markdown;\n    }\n    /**\n     * Process HTML after marked is finished\n     */\n    postprocess(html) {\n        return html;\n    }\n    /**\n     * Process all tokens before walk tokens\n     */\n    processAllTokens(tokens) {\n        return tokens;\n    }\n}\n\nclass Marked {\n    defaults = _getDefaults();\n    options = this.setOptions;\n    parse = this.#parseMarkdown(_Lexer.lex, _Parser.parse);\n    parseInline = this.#parseMarkdown(_Lexer.lexInline, _Parser.parseInline);\n    Parser = _Parser;\n    Renderer = _Renderer;\n    TextRenderer = _TextRenderer;\n    Lexer = _Lexer;\n    Tokenizer = _Tokenizer;\n    Hooks = _Hooks;\n    constructor(...args) {\n        this.use(...args);\n    }\n    /**\n     * Run callback for every token\n     */\n    walkTokens(tokens, callback) {\n        let values = [];\n        for (const token of tokens) {\n            values = values.concat(callback.call(this, token));\n            switch (token.type) {\n                case 'table': {\n                    const tableToken = token;\n                    for (const cell of tableToken.header) {\n                        values = values.concat(this.walkTokens(cell.tokens, callback));\n                    }\n                    for (const row of tableToken.rows) {\n                        for (const cell of row) {\n                            values = values.concat(this.walkTokens(cell.tokens, callback));\n                        }\n                    }\n                    break;\n                }\n                case 'list': {\n                    const listToken = token;\n                    values = values.concat(this.walkTokens(listToken.items, callback));\n                    break;\n                }\n                default: {\n                    const genericToken = token;\n                    if (this.defaults.extensions?.childTokens?.[genericToken.type]) {\n                        this.defaults.extensions.childTokens[genericToken.type].forEach((childTokens) => {\n                            const tokens = genericToken[childTokens].flat(Infinity);\n                            values = values.concat(this.walkTokens(tokens, callback));\n                        });\n                    }\n                    else if (genericToken.tokens) {\n                        values = values.concat(this.walkTokens(genericToken.tokens, callback));\n                    }\n                }\n            }\n        }\n        return values;\n    }\n    use(...args) {\n        const extensions = this.defaults.extensions || { renderers: {}, childTokens: {} };\n        args.forEach((pack) => {\n            // copy options to new object\n            const opts = { ...pack };\n            // set async to true if it was set to true before\n            opts.async = this.defaults.async || opts.async || false;\n            // ==-- Parse \"addon\" extensions --== //\n            if (pack.extensions) {\n                pack.extensions.forEach((ext) => {\n                    if (!ext.name) {\n                        throw new Error('extension name required');\n                    }\n                    if ('renderer' in ext) { // Renderer extensions\n                        const prevRenderer = extensions.renderers[ext.name];\n                        if (prevRenderer) {\n                            // Replace extension with func to run new extension but fall back if false\n                            extensions.renderers[ext.name] = function (...args) {\n                                let ret = ext.renderer.apply(this, args);\n                                if (ret === false) {\n                                    ret = prevRenderer.apply(this, args);\n                                }\n                                return ret;\n                            };\n                        }\n                        else {\n                            extensions.renderers[ext.name] = ext.renderer;\n                        }\n                    }\n                    if ('tokenizer' in ext) { // Tokenizer Extensions\n                        if (!ext.level || (ext.level !== 'block' && ext.level !== 'inline')) {\n                            throw new Error(\"extension level must be 'block' or 'inline'\");\n                        }\n                        const extLevel = extensions[ext.level];\n                        if (extLevel) {\n                            extLevel.unshift(ext.tokenizer);\n                        }\n                        else {\n                            extensions[ext.level] = [ext.tokenizer];\n                        }\n                        if (ext.start) { // Function to check for start of token\n                            if (ext.level === 'block') {\n                                if (extensions.startBlock) {\n                                    extensions.startBlock.push(ext.start);\n                                }\n                                else {\n                                    extensions.startBlock = [ext.start];\n                                }\n                            }\n                            else if (ext.level === 'inline') {\n                                if (extensions.startInline) {\n                                    extensions.startInline.push(ext.start);\n                                }\n                                else {\n                                    extensions.startInline = [ext.start];\n                                }\n                            }\n                        }\n                    }\n                    if ('childTokens' in ext && ext.childTokens) { // Child tokens to be visited by walkTokens\n                        extensions.childTokens[ext.name] = ext.childTokens;\n                    }\n                });\n                opts.extensions = extensions;\n            }\n            // ==-- Parse \"overwrite\" extensions --== //\n            if (pack.renderer) {\n                const renderer = this.defaults.renderer || new _Renderer(this.defaults);\n                for (const prop in pack.renderer) {\n                    if (!(prop in renderer)) {\n                        throw new Error(`renderer '${prop}' does not exist`);\n                    }\n                    if (prop === 'options') {\n                        // ignore options property\n                        continue;\n                    }\n                    const rendererProp = prop;\n                    const rendererFunc = pack.renderer[rendererProp];\n                    const prevRenderer = renderer[rendererProp];\n                    // Replace renderer with func to run extension, but fall back if false\n                    renderer[rendererProp] = (...args) => {\n                        let ret = rendererFunc.apply(renderer, args);\n                        if (ret === false) {\n                            ret = prevRenderer.apply(renderer, args);\n                        }\n                        return ret || '';\n                    };\n                }\n                opts.renderer = renderer;\n            }\n            if (pack.tokenizer) {\n                const tokenizer = this.defaults.tokenizer || new _Tokenizer(this.defaults);\n                for (const prop in pack.tokenizer) {\n                    if (!(prop in tokenizer)) {\n                        throw new Error(`tokenizer '${prop}' does not exist`);\n                    }\n                    if (['options', 'rules', 'lexer'].includes(prop)) {\n                        // ignore options, rules, and lexer properties\n                        continue;\n                    }\n                    const tokenizerProp = prop;\n                    const tokenizerFunc = pack.tokenizer[tokenizerProp];\n                    const prevTokenizer = tokenizer[tokenizerProp];\n                    // Replace tokenizer with func to run extension, but fall back if false\n                    // @ts-expect-error cannot type tokenizer function dynamically\n                    tokenizer[tokenizerProp] = (...args) => {\n                        let ret = tokenizerFunc.apply(tokenizer, args);\n                        if (ret === false) {\n                            ret = prevTokenizer.apply(tokenizer, args);\n                        }\n                        return ret;\n                    };\n                }\n                opts.tokenizer = tokenizer;\n            }\n            // ==-- Parse Hooks extensions --== //\n            if (pack.hooks) {\n                const hooks = this.defaults.hooks || new _Hooks();\n                for (const prop in pack.hooks) {\n                    if (!(prop in hooks)) {\n                        throw new Error(`hook '${prop}' does not exist`);\n                    }\n                    if (prop === 'options') {\n                        // ignore options property\n                        continue;\n                    }\n                    const hooksProp = prop;\n                    const hooksFunc = pack.hooks[hooksProp];\n                    const prevHook = hooks[hooksProp];\n                    if (_Hooks.passThroughHooks.has(prop)) {\n                        // @ts-expect-error cannot type hook function dynamically\n                        hooks[hooksProp] = (arg) => {\n                            if (this.defaults.async) {\n                                return Promise.resolve(hooksFunc.call(hooks, arg)).then(ret => {\n                                    return prevHook.call(hooks, ret);\n                                });\n                            }\n                            const ret = hooksFunc.call(hooks, arg);\n                            return prevHook.call(hooks, ret);\n                        };\n                    }\n                    else {\n                        // @ts-expect-error cannot type hook function dynamically\n                        hooks[hooksProp] = (...args) => {\n                            let ret = hooksFunc.apply(hooks, args);\n                            if (ret === false) {\n                                ret = prevHook.apply(hooks, args);\n                            }\n                            return ret;\n                        };\n                    }\n                }\n                opts.hooks = hooks;\n            }\n            // ==-- Parse WalkTokens extensions --== //\n            if (pack.walkTokens) {\n                const walkTokens = this.defaults.walkTokens;\n                const packWalktokens = pack.walkTokens;\n                opts.walkTokens = function (token) {\n                    let values = [];\n                    values.push(packWalktokens.call(this, token));\n                    if (walkTokens) {\n                        values = values.concat(walkTokens.call(this, token));\n                    }\n                    return values;\n                };\n            }\n            this.defaults = { ...this.defaults, ...opts };\n        });\n        return this;\n    }\n    setOptions(opt) {\n        this.defaults = { ...this.defaults, ...opt };\n        return this;\n    }\n    lexer(src, options) {\n        return _Lexer.lex(src, options ?? this.defaults);\n    }\n    parser(tokens, options) {\n        return _Parser.parse(tokens, options ?? this.defaults);\n    }\n    #parseMarkdown(lexer, parser) {\n        return (src, options) => {\n            const origOpt = { ...options };\n            const opt = { ...this.defaults, ...origOpt };\n            // Show warning if an extension set async to true but the parse was called with async: false\n            if (this.defaults.async === true && origOpt.async === false) {\n                if (!opt.silent) {\n                    console.warn('marked(): The async option was set to true by an extension. The async: false option sent to parse will be ignored.');\n                }\n                opt.async = true;\n            }\n            const throwError = this.#onError(!!opt.silent, !!opt.async);\n            // throw error in case of non string input\n            if (typeof src === 'undefined' || src === null) {\n                return throwError(new Error('marked(): input parameter is undefined or null'));\n            }\n            if (typeof src !== 'string') {\n                return throwError(new Error('marked(): input parameter is of type '\n                    + Object.prototype.toString.call(src) + ', string expected'));\n            }\n            if (opt.hooks) {\n                opt.hooks.options = opt;\n            }\n            if (opt.async) {\n                return Promise.resolve(opt.hooks ? opt.hooks.preprocess(src) : src)\n                    .then(src => lexer(src, opt))\n                    .then(tokens => opt.hooks ? opt.hooks.processAllTokens(tokens) : tokens)\n                    .then(tokens => opt.walkTokens ? Promise.all(this.walkTokens(tokens, opt.walkTokens)).then(() => tokens) : tokens)\n                    .then(tokens => parser(tokens, opt))\n                    .then(html => opt.hooks ? opt.hooks.postprocess(html) : html)\n                    .catch(throwError);\n            }\n            try {\n                if (opt.hooks) {\n                    src = opt.hooks.preprocess(src);\n                }\n                let tokens = lexer(src, opt);\n                if (opt.hooks) {\n                    tokens = opt.hooks.processAllTokens(tokens);\n                }\n                if (opt.walkTokens) {\n                    this.walkTokens(tokens, opt.walkTokens);\n                }\n                let html = parser(tokens, opt);\n                if (opt.hooks) {\n                    html = opt.hooks.postprocess(html);\n                }\n                return html;\n            }\n            catch (e) {\n                return throwError(e);\n            }\n        };\n    }\n    #onError(silent, async) {\n        return (e) => {\n            e.message += '\\nPlease report this to https://github.com/markedjs/marked.';\n            if (silent) {\n                const msg = '<p>An error occurred:</p><pre>'\n                    + escape$1(e.message + '', true)\n                    + '</pre>';\n                if (async) {\n                    return Promise.resolve(msg);\n                }\n                return msg;\n            }\n            if (async) {\n                return Promise.reject(e);\n            }\n            throw e;\n        };\n    }\n}\n\nconst markedInstance = new Marked();\nfunction marked(src, opt) {\n    return markedInstance.parse(src, opt);\n}\n/**\n * Sets the default options.\n *\n * @param options Hash of options\n */\nmarked.options =\n    marked.setOptions = function (options) {\n        markedInstance.setOptions(options);\n        marked.defaults = markedInstance.defaults;\n        changeDefaults(marked.defaults);\n        return marked;\n    };\n/**\n * Gets the original marked default options.\n */\nmarked.getDefaults = _getDefaults;\nmarked.defaults = _defaults;\n/**\n * Use Extension\n */\nmarked.use = function (...args) {\n    markedInstance.use(...args);\n    marked.defaults = markedInstance.defaults;\n    changeDefaults(marked.defaults);\n    return marked;\n};\n/**\n * Run callback for every token\n */\nmarked.walkTokens = function (tokens, callback) {\n    return markedInstance.walkTokens(tokens, callback);\n};\n/**\n * Compiles markdown to HTML without enclosing `p` tag.\n *\n * @param src String of markdown source to be compiled\n * @param options Hash of options\n * @return String of compiled HTML\n */\nmarked.parseInline = markedInstance.parseInline;\n/**\n * Expose\n */\nmarked.Parser = _Parser;\nmarked.parser = _Parser.parse;\nmarked.Renderer = _Renderer;\nmarked.TextRenderer = _TextRenderer;\nmarked.Lexer = _Lexer;\nmarked.lexer = _Lexer.lex;\nmarked.Tokenizer = _Tokenizer;\nmarked.Hooks = _Hooks;\nmarked.parse = marked;\nconst options = marked.options;\nconst setOptions = marked.setOptions;\nconst use = marked.use;\nconst walkTokens = marked.walkTokens;\nconst parseInline = marked.parseInline;\nconst parse = marked;\nconst parser = _Parser.parse;\nconst lexer = _Lexer.lex;\n\nexport { _Hooks as Hooks, _Lexer as Lexer, Marked, _Parser as Parser, _Renderer as Renderer, _TextRenderer as TextRenderer, _Tokenizer as Tokenizer, _defaults as defaults, _getDefaults as getDefaults, lexer, marked, options, parse, parseInline, parser, setOptions, use, walkTokens };\n//# sourceMappingURL=marked.esm.js.map\n", "export function markedHighlight(options) {\n  if (typeof options === 'function') {\n    options = {\n      highlight: options,\n    };\n  }\n\n  if (!options || typeof options.highlight !== 'function') {\n    throw new Error('Must provide highlight function');\n  }\n\n  if (typeof options.langPrefix !== 'string') {\n    options.langPrefix = 'language-';\n  }\n\n  if (typeof options.emptyLangClass !== 'string') {\n    options.emptyLangClass = '';\n  }\n\n  return {\n    async: !!options.async,\n    walkTokens(token) {\n      if (token.type !== 'code') {\n        return;\n      }\n\n      const lang = getLang(token.lang);\n\n      if (options.async) {\n        return Promise.resolve(options.highlight(token.text, lang, token.lang || '')).then(updateToken(token));\n      }\n\n      const code = options.highlight(token.text, lang, token.lang || '');\n      if (code instanceof Promise) {\n        throw new Error('markedHighlight is not set to async but the highlight function is async. Set the async option to true on markedHighlight to await the async highlight function.');\n      }\n      updateToken(token)(code);\n    },\n    useNewRenderer: true,\n    renderer: {\n      code(code, infoString, escaped) {\n        // istanbul ignore next\n        if (typeof code === 'object') {\n          escaped = code.escaped;\n          infoString = code.lang;\n          code = code.text;\n        }\n        const lang = getLang(infoString);\n        const classValue = lang ? options.langPrefix + escape(lang) : options.emptyLangClass;\n        const classAttr = classValue\n          ? ` class=\"${classValue}\"`\n          : '';\n        code = code.replace(/\\n$/, '');\n        return `<pre><code${classAttr}>${escaped ? code : escape(code, true)}\\n</code></pre>`;\n      },\n    },\n  };\n}\n\nfunction getLang(lang) {\n  return (lang || '').match(/\\S*/)[0];\n}\n\nfunction updateToken(token) {\n  return (code) => {\n    if (typeof code === 'string' && code !== token.text) {\n      token.escaped = true;\n      token.text = code;\n    }\n  };\n}\n\n// copied from marked helpers\nconst escapeTest = /[&<>\"']/;\nconst escapeReplace = new RegExp(escapeTest.source, 'g');\nconst escapeTestNoEncode = /[<>\"']|&(?!(#\\d{1,7}|#[Xx][a-fA-F0-9]{1,6}|\\w+);)/;\nconst escapeReplaceNoEncode = new RegExp(escapeTestNoEncode.source, 'g');\nconst escapeReplacements = {\n  '&': '&amp;',\n  '<': '&lt;',\n  '>': '&gt;',\n  '\"': '&quot;',\n  \"'\": '&#39;',\n};\nconst getEscapeReplacement = (ch) => escapeReplacements[ch];\nfunction escape(html, encode) {\n  if (encode) {\n    if (escapeTest.test(html)) {\n      return html.replace(escapeReplace, getEscapeReplacement);\n    }\n  } else {\n    if (escapeTestNoEncode.test(html)) {\n      return html.replace(escapeReplaceNoEncode, getEscapeReplacement);\n    }\n  }\n\n  return html;\n}\n", "// This module is generated by `script/`.\n/* eslint-disable no-control-regex, no-misleading-character-class, no-useless-escape */\nexport const regex = /[\\0-\\x1F!-,\\.\\/:-@\\[-\\^`\\{-\\xA9\\xAB-\\xB4\\xB6-\\xB9\\xBB-\\xBF\\xD7\\xF7\\u02C2-\\u02C5\\u02D2-\\u02DF\\u02E5-\\u02EB\\u02ED\\u02EF-\\u02FF\\u0375\\u0378\\u0379\\u037E\\u0380-\\u0385\\u0387\\u038B\\u038D\\u03A2\\u03F6\\u0482\\u0530\\u0557\\u0558\\u055A-\\u055F\\u0589-\\u0590\\u05BE\\u05C0\\u05C3\\u05C6\\u05C8-\\u05CF\\u05EB-\\u05EE\\u05F3-\\u060F\\u061B-\\u061F\\u066A-\\u066D\\u06D4\\u06DD\\u06DE\\u06E9\\u06FD\\u06FE\\u0700-\\u070F\\u074B\\u074C\\u07B2-\\u07BF\\u07F6-\\u07F9\\u07FB\\u07FC\\u07FE\\u07FF\\u082E-\\u083F\\u085C-\\u085F\\u086B-\\u089F\\u08B5\\u08C8-\\u08D2\\u08E2\\u0964\\u0965\\u0970\\u0984\\u098D\\u098E\\u0991\\u0992\\u09A9\\u09B1\\u09B3-\\u09B5\\u09BA\\u09BB\\u09C5\\u09C6\\u09C9\\u09CA\\u09CF-\\u09D6\\u09D8-\\u09DB\\u09DE\\u09E4\\u09E5\\u09F2-\\u09FB\\u09FD\\u09FF\\u0A00\\u0A04\\u0A0B-\\u0A0E\\u0A11\\u0A12\\u0A29\\u0A31\\u0A34\\u0A37\\u0A3A\\u0A3B\\u0A3D\\u0A43-\\u0A46\\u0A49\\u0A4A\\u0A4E-\\u0A50\\u0A52-\\u0A58\\u0A5D\\u0A5F-\\u0A65\\u0A76-\\u0A80\\u0A84\\u0A8E\\u0A92\\u0AA9\\u0AB1\\u0AB4\\u0ABA\\u0ABB\\u0AC6\\u0ACA\\u0ACE\\u0ACF\\u0AD1-\\u0ADF\\u0AE4\\u0AE5\\u0AF0-\\u0AF8\\u0B00\\u0B04\\u0B0D\\u0B0E\\u0B11\\u0B12\\u0B29\\u0B31\\u0B34\\u0B3A\\u0B3B\\u0B45\\u0B46\\u0B49\\u0B4A\\u0B4E-\\u0B54\\u0B58-\\u0B5B\\u0B5E\\u0B64\\u0B65\\u0B70\\u0B72-\\u0B81\\u0B84\\u0B8B-\\u0B8D\\u0B91\\u0B96-\\u0B98\\u0B9B\\u0B9D\\u0BA0-\\u0BA2\\u0BA5-\\u0BA7\\u0BAB-\\u0BAD\\u0BBA-\\u0BBD\\u0BC3-\\u0BC5\\u0BC9\\u0BCE\\u0BCF\\u0BD1-\\u0BD6\\u0BD8-\\u0BE5\\u0BF0-\\u0BFF\\u0C0D\\u0C11\\u0C29\\u0C3A-\\u0C3C\\u0C45\\u0C49\\u0C4E-\\u0C54\\u0C57\\u0C5B-\\u0C5F\\u0C64\\u0C65\\u0C70-\\u0C7F\\u0C84\\u0C8D\\u0C91\\u0CA9\\u0CB4\\u0CBA\\u0CBB\\u0CC5\\u0CC9\\u0CCE-\\u0CD4\\u0CD7-\\u0CDD\\u0CDF\\u0CE4\\u0CE5\\u0CF0\\u0CF3-\\u0CFF\\u0D0D\\u0D11\\u0D45\\u0D49\\u0D4F-\\u0D53\\u0D58-\\u0D5E\\u0D64\\u0D65\\u0D70-\\u0D79\\u0D80\\u0D84\\u0D97-\\u0D99\\u0DB2\\u0DBC\\u0DBE\\u0DBF\\u0DC7-\\u0DC9\\u0DCB-\\u0DCE\\u0DD5\\u0DD7\\u0DE0-\\u0DE5\\u0DF0\\u0DF1\\u0DF4-\\u0E00\\u0E3B-\\u0E3F\\u0E4F\\u0E5A-\\u0E80\\u0E83\\u0E85\\u0E8B\\u0EA4\\u0EA6\\u0EBE\\u0EBF\\u0EC5\\u0EC7\\u0ECE\\u0ECF\\u0EDA\\u0EDB\\u0EE0-\\u0EFF\\u0F01-\\u0F17\\u0F1A-\\u0F1F\\u0F2A-\\u0F34\\u0F36\\u0F38\\u0F3A-\\u0F3D\\u0F48\\u0F6D-\\u0F70\\u0F85\\u0F98\\u0FBD-\\u0FC5\\u0FC7-\\u0FFF\\u104A-\\u104F\\u109E\\u109F\\u10C6\\u10C8-\\u10CC\\u10CE\\u10CF\\u10FB\\u1249\\u124E\\u124F\\u1257\\u1259\\u125E\\u125F\\u1289\\u128E\\u128F\\u12B1\\u12B6\\u12B7\\u12BF\\u12C1\\u12C6\\u12C7\\u12D7\\u1311\\u1316\\u1317\\u135B\\u135C\\u1360-\\u137F\\u1390-\\u139F\\u13F6\\u13F7\\u13FE-\\u1400\\u166D\\u166E\\u1680\\u169B-\\u169F\\u16EB-\\u16ED\\u16F9-\\u16FF\\u170D\\u1715-\\u171F\\u1735-\\u173F\\u1754-\\u175F\\u176D\\u1771\\u1774-\\u177F\\u17D4-\\u17D6\\u17D8-\\u17DB\\u17DE\\u17DF\\u17EA-\\u180A\\u180E\\u180F\\u181A-\\u181F\\u1879-\\u187F\\u18AB-\\u18AF\\u18F6-\\u18FF\\u191F\\u192C-\\u192F\\u193C-\\u1945\\u196E\\u196F\\u1975-\\u197F\\u19AC-\\u19AF\\u19CA-\\u19CF\\u19DA-\\u19FF\\u1A1C-\\u1A1F\\u1A5F\\u1A7D\\u1A7E\\u1A8A-\\u1A8F\\u1A9A-\\u1AA6\\u1AA8-\\u1AAF\\u1AC1-\\u1AFF\\u1B4C-\\u1B4F\\u1B5A-\\u1B6A\\u1B74-\\u1B7F\\u1BF4-\\u1BFF\\u1C38-\\u1C3F\\u1C4A-\\u1C4C\\u1C7E\\u1C7F\\u1C89-\\u1C8F\\u1CBB\\u1CBC\\u1CC0-\\u1CCF\\u1CD3\\u1CFB-\\u1CFF\\u1DFA\\u1F16\\u1F17\\u1F1E\\u1F1F\\u1F46\\u1F47\\u1F4E\\u1F4F\\u1F58\\u1F5A\\u1F5C\\u1F5E\\u1F7E\\u1F7F\\u1FB5\\u1FBD\\u1FBF-\\u1FC1\\u1FC5\\u1FCD-\\u1FCF\\u1FD4\\u1FD5\\u1FDC-\\u1FDF\\u1FED-\\u1FF1\\u1FF5\\u1FFD-\\u203E\\u2041-\\u2053\\u2055-\\u2070\\u2072-\\u207E\\u2080-\\u208F\\u209D-\\u20CF\\u20F1-\\u2101\\u2103-\\u2106\\u2108\\u2109\\u2114\\u2116-\\u2118\\u211E-\\u2123\\u2125\\u2127\\u2129\\u212E\\u213A\\u213B\\u2140-\\u2144\\u214A-\\u214D\\u214F-\\u215F\\u2189-\\u24B5\\u24EA-\\u2BFF\\u2C2F\\u2C5F\\u2CE5-\\u2CEA\\u2CF4-\\u2CFF\\u2D26\\u2D28-\\u2D2C\\u2D2E\\u2D2F\\u2D68-\\u2D6E\\u2D70-\\u2D7E\\u2D97-\\u2D9F\\u2DA7\\u2DAF\\u2DB7\\u2DBF\\u2DC7\\u2DCF\\u2DD7\\u2DDF\\u2E00-\\u2E2E\\u2E30-\\u3004\\u3008-\\u3020\\u3030\\u3036\\u3037\\u303D-\\u3040\\u3097\\u3098\\u309B\\u309C\\u30A0\\u30FB\\u3100-\\u3104\\u3130\\u318F-\\u319F\\u31C0-\\u31EF\\u3200-\\u33FF\\u4DC0-\\u4DFF\\u9FFD-\\u9FFF\\uA48D-\\uA4CF\\uA4FE\\uA4FF\\uA60D-\\uA60F\\uA62C-\\uA63F\\uA673\\uA67E\\uA6F2-\\uA716\\uA720\\uA721\\uA789\\uA78A\\uA7C0\\uA7C1\\uA7CB-\\uA7F4\\uA828-\\uA82B\\uA82D-\\uA83F\\uA874-\\uA87F\\uA8C6-\\uA8CF\\uA8DA-\\uA8DF\\uA8F8-\\uA8FA\\uA8FC\\uA92E\\uA92F\\uA954-\\uA95F\\uA97D-\\uA97F\\uA9C1-\\uA9CE\\uA9DA-\\uA9DF\\uA9FF\\uAA37-\\uAA3F\\uAA4E\\uAA4F\\uAA5A-\\uAA5F\\uAA77-\\uAA79\\uAAC3-\\uAADA\\uAADE\\uAADF\\uAAF0\\uAAF1\\uAAF7-\\uAB00\\uAB07\\uAB08\\uAB0F\\uAB10\\uAB17-\\uAB1F\\uAB27\\uAB2F\\uAB5B\\uAB6A-\\uAB6F\\uABEB\\uABEE\\uABEF\\uABFA-\\uABFF\\uD7A4-\\uD7AF\\uD7C7-\\uD7CA\\uD7FC-\\uD7FF\\uE000-\\uF8FF\\uFA6E\\uFA6F\\uFADA-\\uFAFF\\uFB07-\\uFB12\\uFB18-\\uFB1C\\uFB29\\uFB37\\uFB3D\\uFB3F\\uFB42\\uFB45\\uFBB2-\\uFBD2\\uFD3E-\\uFD4F\\uFD90\\uFD91\\uFDC8-\\uFDEF\\uFDFC-\\uFDFF\\uFE10-\\uFE1F\\uFE30-\\uFE32\\uFE35-\\uFE4C\\uFE50-\\uFE6F\\uFE75\\uFEFD-\\uFF0F\\uFF1A-\\uFF20\\uFF3B-\\uFF3E\\uFF40\\uFF5B-\\uFF65\\uFFBF-\\uFFC1\\uFFC8\\uFFC9\\uFFD0\\uFFD1\\uFFD8\\uFFD9\\uFFDD-\\uFFFF]|\\uD800[\\uDC0C\\uDC27\\uDC3B\\uDC3E\\uDC4E\\uDC4F\\uDC5E-\\uDC7F\\uDCFB-\\uDD3F\\uDD75-\\uDDFC\\uDDFE-\\uDE7F\\uDE9D-\\uDE9F\\uDED1-\\uDEDF\\uDEE1-\\uDEFF\\uDF20-\\uDF2C\\uDF4B-\\uDF4F\\uDF7B-\\uDF7F\\uDF9E\\uDF9F\\uDFC4-\\uDFC7\\uDFD0\\uDFD6-\\uDFFF]|\\uD801[\\uDC9E\\uDC9F\\uDCAA-\\uDCAF\\uDCD4-\\uDCD7\\uDCFC-\\uDCFF\\uDD28-\\uDD2F\\uDD64-\\uDDFF\\uDF37-\\uDF3F\\uDF56-\\uDF5F\\uDF68-\\uDFFF]|\\uD802[\\uDC06\\uDC07\\uDC09\\uDC36\\uDC39-\\uDC3B\\uDC3D\\uDC3E\\uDC56-\\uDC5F\\uDC77-\\uDC7F\\uDC9F-\\uDCDF\\uDCF3\\uDCF6-\\uDCFF\\uDD16-\\uDD1F\\uDD3A-\\uDD7F\\uDDB8-\\uDDBD\\uDDC0-\\uDDFF\\uDE04\\uDE07-\\uDE0B\\uDE14\\uDE18\\uDE36\\uDE37\\uDE3B-\\uDE3E\\uDE40-\\uDE5F\\uDE7D-\\uDE7F\\uDE9D-\\uDEBF\\uDEC8\\uDEE7-\\uDEFF\\uDF36-\\uDF3F\\uDF56-\\uDF5F\\uDF73-\\uDF7F\\uDF92-\\uDFFF]|\\uD803[\\uDC49-\\uDC7F\\uDCB3-\\uDCBF\\uDCF3-\\uDCFF\\uDD28-\\uDD2F\\uDD3A-\\uDE7F\\uDEAA\\uDEAD-\\uDEAF\\uDEB2-\\uDEFF\\uDF1D-\\uDF26\\uDF28-\\uDF2F\\uDF51-\\uDFAF\\uDFC5-\\uDFDF\\uDFF7-\\uDFFF]|\\uD804[\\uDC47-\\uDC65\\uDC70-\\uDC7E\\uDCBB-\\uDCCF\\uDCE9-\\uDCEF\\uDCFA-\\uDCFF\\uDD35\\uDD40-\\uDD43\\uDD48-\\uDD4F\\uDD74\\uDD75\\uDD77-\\uDD7F\\uDDC5-\\uDDC8\\uDDCD\\uDDDB\\uDDDD-\\uDDFF\\uDE12\\uDE38-\\uDE3D\\uDE3F-\\uDE7F\\uDE87\\uDE89\\uDE8E\\uDE9E\\uDEA9-\\uDEAF\\uDEEB-\\uDEEF\\uDEFA-\\uDEFF\\uDF04\\uDF0D\\uDF0E\\uDF11\\uDF12\\uDF29\\uDF31\\uDF34\\uDF3A\\uDF45\\uDF46\\uDF49\\uDF4A\\uDF4E\\uDF4F\\uDF51-\\uDF56\\uDF58-\\uDF5C\\uDF64\\uDF65\\uDF6D-\\uDF6F\\uDF75-\\uDFFF]|\\uD805[\\uDC4B-\\uDC4F\\uDC5A-\\uDC5D\\uDC62-\\uDC7F\\uDCC6\\uDCC8-\\uDCCF\\uDCDA-\\uDD7F\\uDDB6\\uDDB7\\uDDC1-\\uDDD7\\uDDDE-\\uDDFF\\uDE41-\\uDE43\\uDE45-\\uDE4F\\uDE5A-\\uDE7F\\uDEB9-\\uDEBF\\uDECA-\\uDEFF\\uDF1B\\uDF1C\\uDF2C-\\uDF2F\\uDF3A-\\uDFFF]|\\uD806[\\uDC3B-\\uDC9F\\uDCEA-\\uDCFE\\uDD07\\uDD08\\uDD0A\\uDD0B\\uDD14\\uDD17\\uDD36\\uDD39\\uDD3A\\uDD44-\\uDD4F\\uDD5A-\\uDD9F\\uDDA8\\uDDA9\\uDDD8\\uDDD9\\uDDE2\\uDDE5-\\uDDFF\\uDE3F-\\uDE46\\uDE48-\\uDE4F\\uDE9A-\\uDE9C\\uDE9E-\\uDEBF\\uDEF9-\\uDFFF]|\\uD807[\\uDC09\\uDC37\\uDC41-\\uDC4F\\uDC5A-\\uDC71\\uDC90\\uDC91\\uDCA8\\uDCB7-\\uDCFF\\uDD07\\uDD0A\\uDD37-\\uDD39\\uDD3B\\uDD3E\\uDD48-\\uDD4F\\uDD5A-\\uDD5F\\uDD66\\uDD69\\uDD8F\\uDD92\\uDD99-\\uDD9F\\uDDAA-\\uDEDF\\uDEF7-\\uDFAF\\uDFB1-\\uDFFF]|\\uD808[\\uDF9A-\\uDFFF]|\\uD809[\\uDC6F-\\uDC7F\\uDD44-\\uDFFF]|[\\uD80A\\uD80B\\uD80E-\\uD810\\uD812-\\uD819\\uD824-\\uD82B\\uD82D\\uD82E\\uD830-\\uD833\\uD837\\uD839\\uD83D\\uD83F\\uD87B-\\uD87D\\uD87F\\uD885-\\uDB3F\\uDB41-\\uDBFF][\\uDC00-\\uDFFF]|\\uD80D[\\uDC2F-\\uDFFF]|\\uD811[\\uDE47-\\uDFFF]|\\uD81A[\\uDE39-\\uDE3F\\uDE5F\\uDE6A-\\uDECF\\uDEEE\\uDEEF\\uDEF5-\\uDEFF\\uDF37-\\uDF3F\\uDF44-\\uDF4F\\uDF5A-\\uDF62\\uDF78-\\uDF7C\\uDF90-\\uDFFF]|\\uD81B[\\uDC00-\\uDE3F\\uDE80-\\uDEFF\\uDF4B-\\uDF4E\\uDF88-\\uDF8E\\uDFA0-\\uDFDF\\uDFE2\\uDFE5-\\uDFEF\\uDFF2-\\uDFFF]|\\uD821[\\uDFF8-\\uDFFF]|\\uD823[\\uDCD6-\\uDCFF\\uDD09-\\uDFFF]|\\uD82C[\\uDD1F-\\uDD4F\\uDD53-\\uDD63\\uDD68-\\uDD6F\\uDEFC-\\uDFFF]|\\uD82F[\\uDC6B-\\uDC6F\\uDC7D-\\uDC7F\\uDC89-\\uDC8F\\uDC9A-\\uDC9C\\uDC9F-\\uDFFF]|\\uD834[\\uDC00-\\uDD64\\uDD6A-\\uDD6C\\uDD73-\\uDD7A\\uDD83\\uDD84\\uDD8C-\\uDDA9\\uDDAE-\\uDE41\\uDE45-\\uDFFF]|\\uD835[\\uDC55\\uDC9D\\uDCA0\\uDCA1\\uDCA3\\uDCA4\\uDCA7\\uDCA8\\uDCAD\\uDCBA\\uDCBC\\uDCC4\\uDD06\\uDD0B\\uDD0C\\uDD15\\uDD1D\\uDD3A\\uDD3F\\uDD45\\uDD47-\\uDD49\\uDD51\\uDEA6\\uDEA7\\uDEC1\\uDEDB\\uDEFB\\uDF15\\uDF35\\uDF4F\\uDF6F\\uDF89\\uDFA9\\uDFC3\\uDFCC\\uDFCD]|\\uD836[\\uDC00-\\uDDFF\\uDE37-\\uDE3A\\uDE6D-\\uDE74\\uDE76-\\uDE83\\uDE85-\\uDE9A\\uDEA0\\uDEB0-\\uDFFF]|\\uD838[\\uDC07\\uDC19\\uDC1A\\uDC22\\uDC25\\uDC2B-\\uDCFF\\uDD2D-\\uDD2F\\uDD3E\\uDD3F\\uDD4A-\\uDD4D\\uDD4F-\\uDEBF\\uDEFA-\\uDFFF]|\\uD83A[\\uDCC5-\\uDCCF\\uDCD7-\\uDCFF\\uDD4C-\\uDD4F\\uDD5A-\\uDFFF]|\\uD83B[\\uDC00-\\uDDFF\\uDE04\\uDE20\\uDE23\\uDE25\\uDE26\\uDE28\\uDE33\\uDE38\\uDE3A\\uDE3C-\\uDE41\\uDE43-\\uDE46\\uDE48\\uDE4A\\uDE4C\\uDE50\\uDE53\\uDE55\\uDE56\\uDE58\\uDE5A\\uDE5C\\uDE5E\\uDE60\\uDE63\\uDE65\\uDE66\\uDE6B\\uDE73\\uDE78\\uDE7D\\uDE7F\\uDE8A\\uDE9C-\\uDEA0\\uDEA4\\uDEAA\\uDEBC-\\uDFFF]|\\uD83C[\\uDC00-\\uDD2F\\uDD4A-\\uDD4F\\uDD6A-\\uDD6F\\uDD8A-\\uDFFF]|\\uD83E[\\uDC00-\\uDFEF\\uDFFA-\\uDFFF]|\\uD869[\\uDEDE-\\uDEFF]|\\uD86D[\\uDF35-\\uDF3F]|\\uD86E[\\uDC1E\\uDC1F]|\\uD873[\\uDEA2-\\uDEAF]|\\uD87A[\\uDFE1-\\uDFFF]|\\uD87E[\\uDE1E-\\uDFFF]|\\uD884[\\uDF4B-\\uDFFF]|\\uDB40[\\uDC00-\\uDCFF\\uDDF0-\\uDFFF]/g\n", "import { regex } from './regex.js'\n\nconst own = Object.hasOwnProperty\n\n/**\n * Slugger.\n */\nexport default class BananaSlug {\n  /**\n   * Create a new slug class.\n   */\n  constructor () {\n    /** @type {Record<string, number>} */\n    // eslint-disable-next-line no-unused-expressions\n    this.occurrences\n\n    this.reset()\n  }\n\n  /**\n   * Generate a unique slug.\n  *\n  * Tracks previously generated slugs: repeated calls with the same value\n  * will result in different slugs.\n  * Use the `slug` function to get same slugs.\n   *\n   * @param  {string} value\n   *   String of text to slugify\n   * @param  {boolean} [maintainCase=false]\n   *   Keep the current case, otherwise make all lowercase\n   * @return {string}\n   *   A unique slug string\n   */\n  slug (value, maintainCase) {\n    const self = this\n    let result = slug(value, maintainCase === true)\n    const originalSlug = result\n\n    while (own.call(self.occurrences, result)) {\n      self.occurrences[originalSlug]++\n      result = originalSlug + '-' + self.occurrences[originalSlug]\n    }\n\n    self.occurrences[result] = 0\n\n    return result\n  }\n\n  /**\n   * Reset - Forget all previous slugs\n   *\n   * @return void\n   */\n  reset () {\n    this.occurrences = Object.create(null)\n  }\n}\n\n/**\n * Generate a slug.\n *\n * Does not track previously generated slugs: repeated calls with the same value\n * will result in the exact same slug.\n * Use the `GithubSlugger` class to get unique slugs.\n *\n * @param  {string} value\n *   String of text to slugify\n * @param  {boolean} [maintainCase=false]\n *   Keep the current case, otherwise make all lowercase\n * @return {string}\n *   A unique slug string\n */\nexport function slug (value, maintainCase) {\n  if (typeof value !== 'string') return ''\n  if (!maintainCase) value = value.toLowerCase()\n  return value.replace(regex, '').replace(/ /g, '-')\n}\n", "import GithubSlugger from 'github-slugger';\nlet slugger = new GithubSlugger();\n\nlet headings = [];\n\nexport function gfmHeadingId({ prefix = '', globalSlugs = false } = {}) {\n  return {\n    headerIds: false, // prevent deprecation warning; remove this once headerIds option is removed\n    hooks: {\n      preprocess(src) {\n        if (!globalSlugs) {\n          resetHeadings();\n        }\n        return src;\n      }\n    },\n    renderer: {\n      heading(text, level, raw) {\n        raw = raw\n          .toLowerCase()\n          .trim()\n          .replace(/<[!\\/a-z].*?>/gi, '');\n        const id = `${prefix}${slugger.slug(raw)}`;\n        const heading = { level, text, id };\n        headings.push(heading);\n\n        return `<h${level} id=\"${id}\">${text}</h${level}>\\n`;\n      }\n    }\n  };\n}\n\nexport function getHeadingList() {\n  return headings;\n}\n\nexport function resetHeadings() {\n  headings = [];\n  slugger = new GithubSlugger();\n}\n", "(function (Prism) {\n\tvar funcPattern = /\\\\(?:[^a-z()[\\]]|[a-z*]+)/i;\n\tvar insideEqu = {\n\t\t'equation-command': {\n\t\t\tpattern: funcPattern,\n\t\t\talias: 'regex'\n\t\t}\n\t};\n\n\tPrism.languages.latex = {\n\t\t'comment': /%.*/,\n\t\t// the verbatim environment prints whitespace to the document\n\t\t'cdata': {\n\t\t\tpattern: /(\\\\begin\\{((?:lstlisting|verbatim)\\*?)\\})[\\s\\S]*?(?=\\\\end\\{\\2\\})/,\n\t\t\tlookbehind: true\n\t\t},\n\t\t/*\n\t\t * equations can be between $$ $$ or $ $ or \\( \\) or \\[ \\]\n\t\t * (all are multiline)\n\t\t */\n\t\t'equation': [\n\t\t\t{\n\t\t\t\tpattern: /\\$\\$(?:\\\\[\\s\\S]|[^\\\\$])+\\$\\$|\\$(?:\\\\[\\s\\S]|[^\\\\$])+\\$|\\\\\\([\\s\\S]*?\\\\\\)|\\\\\\[[\\s\\S]*?\\\\\\]/,\n\t\t\t\tinside: insideEqu,\n\t\t\t\talias: 'string'\n\t\t\t},\n\t\t\t{\n\t\t\t\tpattern: /(\\\\begin\\{((?:align|eqnarray|equation|gather|math|multline)\\*?)\\})[\\s\\S]*?(?=\\\\end\\{\\2\\})/,\n\t\t\t\tlookbehind: true,\n\t\t\t\tinside: insideEqu,\n\t\t\t\talias: 'string'\n\t\t\t}\n\t\t],\n\t\t/*\n\t\t * arguments which are keywords or references are highlighted\n\t\t * as keywords\n\t\t */\n\t\t'keyword': {\n\t\t\tpattern: /(\\\\(?:begin|cite|documentclass|end|label|ref|usepackage)(?:\\[[^\\]]+\\])?\\{)[^}]+(?=\\})/,\n\t\t\tlookbehind: true\n\t\t},\n\t\t'url': {\n\t\t\tpattern: /(\\\\url\\{)[^}]+(?=\\})/,\n\t\t\tlookbehind: true\n\t\t},\n\t\t/*\n\t\t * section or chapter headlines are highlighted as bold so that\n\t\t * they stand out more\n\t\t */\n\t\t'headline': {\n\t\t\tpattern: /(\\\\(?:chapter|frametitle|paragraph|part|section|subparagraph|subsection|subsubparagraph|subsubsection|subsubsubparagraph)\\*?(?:\\[[^\\]]+\\])?\\{)[^}]+(?=\\})/,\n\t\t\tlookbehind: true,\n\t\t\talias: 'class-name'\n\t\t},\n\t\t'function': {\n\t\t\tpattern: funcPattern,\n\t\t\talias: 'selector'\n\t\t},\n\t\t'punctuation': /[[\\]{}&]/\n\t};\n\n\tPrism.languages.tex = Prism.languages.latex;\n\tPrism.languages.context = Prism.languages.latex;\n}(Prism));\n", "(function (Prism) {\n\t// $ set | grep '^[A-Z][^[:space:]]*=' | cut -d= -f1 | tr '\\n' '|'\n\t// + LC_ALL, RANDOM, REPLY, SECONDS.\n\t// + make sure PS1..4 are here as they are not always set,\n\t// - some useless things.\n\tvar envVars = '\\\\b(?:BASH|BASHOPTS|BASH_ALIASES|BASH_ARGC|BASH_ARGV|BASH_CMDS|BASH_COMPLETION_COMPAT_DIR|BASH_LINENO|BASH_REMATCH|BASH_SOURCE|BASH_VERSINFO|BASH_VERSION|COLORTERM|COLUMNS|COMP_WORDBREAKS|DBUS_SESSION_BUS_ADDRESS|DEFAULTS_PATH|DESKTOP_SESSION|DIRSTACK|DISPLAY|EUID|GDMSESSION|GDM_LANG|GNOME_KEYRING_CONTROL|GNOME_KEYRING_PID|GPG_AGENT_INFO|GROUPS|HISTCONTROL|HISTFILE|HISTFILESIZE|HISTSIZE|HOME|HOSTNAME|HOSTTYPE|IFS|INSTANCE|JOB|LANG|LANGUAGE|LC_ADDRESS|LC_ALL|LC_IDENTIFICATION|LC_MEASUREMENT|LC_MONETARY|LC_NAME|LC_NUMERIC|LC_PAPER|LC_TELEPHONE|LC_TIME|LESSCLOSE|LESSOPEN|LINES|LOGNAME|LS_COLORS|MACHTYPE|MAILCHECK|MANDATORY_PATH|NO_AT_BRIDGE|OLDPWD|OPTERR|OPTIND|ORBIT_SOCKETDIR|OSTYPE|PAPERSIZE|PATH|PIPESTATUS|PPID|PS1|PS2|PS3|PS4|PWD|RANDOM|REPLY|SECONDS|SELINUX_INIT|SESSION|SESSIONTYPE|SESSION_MANAGER|SHELL|SHELLOPTS|SHLVL|SSH_AUTH_SOCK|TERM|UID|UPSTART_EVENTS|UPSTART_INSTANCE|UPSTART_JOB|UPSTART_SESSION|USER|WINDOWID|XAUTHORITY|XDG_CONFIG_DIRS|XDG_CURRENT_DESKTOP|XDG_DATA_DIRS|XDG_GREETER_DATA_DIR|XDG_MENU_PREFIX|XDG_RUNTIME_DIR|XDG_SEAT|XDG_SEAT_PATH|XDG_SESSION_DESKTOP|XDG_SESSION_ID|XDG_SESSION_PATH|XDG_SESSION_TYPE|XDG_VTNR|XMODIFIERS)\\\\b';\n\n\tvar commandAfterHeredoc = {\n\t\tpattern: /(^([\"']?)\\w+\\2)[ \\t]+\\S.*/,\n\t\tlookbehind: true,\n\t\talias: 'punctuation', // this looks reasonably well in all themes\n\t\tinside: null // see below\n\t};\n\n\tvar insideString = {\n\t\t'bash': commandAfterHeredoc,\n\t\t'environment': {\n\t\t\tpattern: RegExp('\\\\$' + envVars),\n\t\t\talias: 'constant'\n\t\t},\n\t\t'variable': [\n\t\t\t// [0]: Arithmetic Environment\n\t\t\t{\n\t\t\t\tpattern: /\\$?\\(\\([\\s\\S]+?\\)\\)/,\n\t\t\t\tgreedy: true,\n\t\t\t\tinside: {\n\t\t\t\t\t// If there is a $ sign at the beginning highlight $(( and )) as variable\n\t\t\t\t\t'variable': [\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\tpattern: /(^\\$\\(\\([\\s\\S]+)\\)\\)/,\n\t\t\t\t\t\t\tlookbehind: true\n\t\t\t\t\t\t},\n\t\t\t\t\t\t/^\\$\\(\\(/\n\t\t\t\t\t],\n\t\t\t\t\t'number': /\\b0x[\\dA-Fa-f]+\\b|(?:\\b\\d+(?:\\.\\d*)?|\\B\\.\\d+)(?:[Ee]-?\\d+)?/,\n\t\t\t\t\t// Operators according to https://www.gnu.org/software/bash/manual/bashref.html#Shell-Arithmetic\n\t\t\t\t\t'operator': /--|\\+\\+|\\*\\*=?|<<=?|>>=?|&&|\\|\\||[=!+\\-*/%<>^&|]=?|[?~:]/,\n\t\t\t\t\t// If there is no $ sign at the beginning highlight (( and )) as punctuation\n\t\t\t\t\t'punctuation': /\\(\\(?|\\)\\)?|,|;/\n\t\t\t\t}\n\t\t\t},\n\t\t\t// [1]: Command Substitution\n\t\t\t{\n\t\t\t\tpattern: /\\$\\((?:\\([^)]+\\)|[^()])+\\)|`[^`]+`/,\n\t\t\t\tgreedy: true,\n\t\t\t\tinside: {\n\t\t\t\t\t'variable': /^\\$\\(|^`|\\)$|`$/\n\t\t\t\t}\n\t\t\t},\n\t\t\t// [2]: Brace expansion\n\t\t\t{\n\t\t\t\tpattern: /\\$\\{[^}]+\\}/,\n\t\t\t\tgreedy: true,\n\t\t\t\tinside: {\n\t\t\t\t\t'operator': /:[-=?+]?|[!\\/]|##?|%%?|\\^\\^?|,,?/,\n\t\t\t\t\t'punctuation': /[\\[\\]]/,\n\t\t\t\t\t'environment': {\n\t\t\t\t\t\tpattern: RegExp('(\\\\{)' + envVars),\n\t\t\t\t\t\tlookbehind: true,\n\t\t\t\t\t\talias: 'constant'\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t},\n\t\t\t/\\$(?:\\w+|[#?*!@$])/\n\t\t],\n\t\t// Escape sequences from echo and printf's manuals, and escaped quotes.\n\t\t'entity': /\\\\(?:[abceEfnrtv\\\\\"]|O?[0-7]{1,3}|U[0-9a-fA-F]{8}|u[0-9a-fA-F]{4}|x[0-9a-fA-F]{1,2})/\n\t};\n\n\tPrism.languages.bash = {\n\t\t'shebang': {\n\t\t\tpattern: /^#!\\s*\\/.*/,\n\t\t\talias: 'important'\n\t\t},\n\t\t'comment': {\n\t\t\tpattern: /(^|[^\"{\\\\$])#.*/,\n\t\t\tlookbehind: true\n\t\t},\n\t\t'function-name': [\n\t\t\t// a) function foo {\n\t\t\t// b) foo() {\n\t\t\t// c) function foo() {\n\t\t\t// but not “foo {”\n\t\t\t{\n\t\t\t\t// a) and c)\n\t\t\t\tpattern: /(\\bfunction\\s+)[\\w-]+(?=(?:\\s*\\(?:\\s*\\))?\\s*\\{)/,\n\t\t\t\tlookbehind: true,\n\t\t\t\talias: 'function'\n\t\t\t},\n\t\t\t{\n\t\t\t\t// b)\n\t\t\t\tpattern: /\\b[\\w-]+(?=\\s*\\(\\s*\\)\\s*\\{)/,\n\t\t\t\talias: 'function'\n\t\t\t}\n\t\t],\n\t\t// Highlight variable names as variables in for and select beginnings.\n\t\t'for-or-select': {\n\t\t\tpattern: /(\\b(?:for|select)\\s+)\\w+(?=\\s+in\\s)/,\n\t\t\talias: 'variable',\n\t\t\tlookbehind: true\n\t\t},\n\t\t// Highlight variable names as variables in the left-hand part\n\t\t// of assignments (“=” and “+=”).\n\t\t'assign-left': {\n\t\t\tpattern: /(^|[\\s;|&]|[<>]\\()\\w+(?:\\.\\w+)*(?=\\+?=)/,\n\t\t\tinside: {\n\t\t\t\t'environment': {\n\t\t\t\t\tpattern: RegExp('(^|[\\\\s;|&]|[<>]\\\\()' + envVars),\n\t\t\t\t\tlookbehind: true,\n\t\t\t\t\talias: 'constant'\n\t\t\t\t}\n\t\t\t},\n\t\t\talias: 'variable',\n\t\t\tlookbehind: true\n\t\t},\n\t\t// Highlight parameter names as variables\n\t\t'parameter': {\n\t\t\tpattern: /(^|\\s)-{1,2}(?:\\w+:[+-]?)?\\w+(?:\\.\\w+)*(?=[=\\s]|$)/,\n\t\t\talias: 'variable',\n\t\t\tlookbehind: true\n\t\t},\n\t\t'string': [\n\t\t\t// Support for Here-documents https://en.wikipedia.org/wiki/Here_document\n\t\t\t{\n\t\t\t\tpattern: /((?:^|[^<])<<-?\\s*)(\\w+)\\s[\\s\\S]*?(?:\\r?\\n|\\r)\\2/,\n\t\t\t\tlookbehind: true,\n\t\t\t\tgreedy: true,\n\t\t\t\tinside: insideString\n\t\t\t},\n\t\t\t// Here-document with quotes around the tag\n\t\t\t// → No expansion (so no “inside”).\n\t\t\t{\n\t\t\t\tpattern: /((?:^|[^<])<<-?\\s*)([\"'])(\\w+)\\2\\s[\\s\\S]*?(?:\\r?\\n|\\r)\\3/,\n\t\t\t\tlookbehind: true,\n\t\t\t\tgreedy: true,\n\t\t\t\tinside: {\n\t\t\t\t\t'bash': commandAfterHeredoc\n\t\t\t\t}\n\t\t\t},\n\t\t\t// “Normal” string\n\t\t\t{\n\t\t\t\t// https://www.gnu.org/software/bash/manual/html_node/Double-Quotes.html\n\t\t\t\tpattern: /(^|[^\\\\](?:\\\\\\\\)*)\"(?:\\\\[\\s\\S]|\\$\\([^)]+\\)|\\$(?!\\()|`[^`]+`|[^\"\\\\`$])*\"/,\n\t\t\t\tlookbehind: true,\n\t\t\t\tgreedy: true,\n\t\t\t\tinside: insideString\n\t\t\t},\n\t\t\t{\n\t\t\t\t// https://www.gnu.org/software/bash/manual/html_node/Single-Quotes.html\n\t\t\t\tpattern: /(^|[^$\\\\])'[^']*'/,\n\t\t\t\tlookbehind: true,\n\t\t\t\tgreedy: true\n\t\t\t},\n\t\t\t{\n\t\t\t\t// https://www.gnu.org/software/bash/manual/html_node/ANSI_002dC-Quoting.html\n\t\t\t\tpattern: /\\$'(?:[^'\\\\]|\\\\[\\s\\S])*'/,\n\t\t\t\tgreedy: true,\n\t\t\t\tinside: {\n\t\t\t\t\t'entity': insideString.entity\n\t\t\t\t}\n\t\t\t}\n\t\t],\n\t\t'environment': {\n\t\t\tpattern: RegExp('\\\\$?' + envVars),\n\t\t\talias: 'constant'\n\t\t},\n\t\t'variable': insideString.variable,\n\t\t'function': {\n\t\t\tpattern: /(^|[\\s;|&]|[<>]\\()(?:add|apropos|apt|apt-cache|apt-get|aptitude|aspell|automysqlbackup|awk|basename|bash|bc|bconsole|bg|bzip2|cal|cargo|cat|cfdisk|chgrp|chkconfig|chmod|chown|chroot|cksum|clear|cmp|column|comm|composer|cp|cron|crontab|csplit|curl|cut|date|dc|dd|ddrescue|debootstrap|df|diff|diff3|dig|dir|dircolors|dirname|dirs|dmesg|docker|docker-compose|du|egrep|eject|env|ethtool|expand|expect|expr|fdformat|fdisk|fg|fgrep|file|find|fmt|fold|format|free|fsck|ftp|fuser|gawk|git|gparted|grep|groupadd|groupdel|groupmod|groups|grub-mkconfig|gzip|halt|head|hg|history|host|hostname|htop|iconv|id|ifconfig|ifdown|ifup|import|install|ip|java|jobs|join|kill|killall|less|link|ln|locate|logname|logrotate|look|lpc|lpr|lprint|lprintd|lprintq|lprm|ls|lsof|lynx|make|man|mc|mdadm|mkconfig|mkdir|mke2fs|mkfifo|mkfs|mkisofs|mknod|mkswap|mmv|more|most|mount|mtools|mtr|mutt|mv|nano|nc|netstat|nice|nl|node|nohup|notify-send|npm|nslookup|op|open|parted|passwd|paste|pathchk|ping|pkill|pnpm|podman|podman-compose|popd|pr|printcap|printenv|ps|pushd|pv|quota|quotacheck|quotactl|ram|rar|rcp|reboot|remsync|rename|renice|rev|rm|rmdir|rpm|rsync|scp|screen|sdiff|sed|sendmail|seq|service|sftp|sh|shellcheck|shuf|shutdown|sleep|slocate|sort|split|ssh|stat|strace|su|sudo|sum|suspend|swapon|sync|sysctl|tac|tail|tar|tee|time|timeout|top|touch|tr|traceroute|tsort|tty|umount|uname|unexpand|uniq|units|unrar|unshar|unzip|update-grub|uptime|useradd|userdel|usermod|users|uudecode|uuencode|v|vcpkg|vdir|vi|vim|virsh|vmstat|wait|watch|wc|wget|whereis|which|who|whoami|write|xargs|xdg-open|yarn|yes|zenity|zip|zsh|zypper)(?=$|[)\\s;|&])/,\n\t\t\tlookbehind: true\n\t\t},\n\t\t'keyword': {\n\t\t\tpattern: /(^|[\\s;|&]|[<>]\\()(?:case|do|done|elif|else|esac|fi|for|function|if|in|select|then|until|while)(?=$|[)\\s;|&])/,\n\t\t\tlookbehind: true\n\t\t},\n\t\t// https://www.gnu.org/software/bash/manual/html_node/Shell-Builtin-Commands.html\n\t\t'builtin': {\n\t\t\tpattern: /(^|[\\s;|&]|[<>]\\()(?:\\.|:|alias|bind|break|builtin|caller|cd|command|continue|declare|echo|enable|eval|exec|exit|export|getopts|hash|help|let|local|logout|mapfile|printf|pwd|read|readarray|readonly|return|set|shift|shopt|source|test|times|trap|type|typeset|ulimit|umask|unalias|unset)(?=$|[)\\s;|&])/,\n\t\t\tlookbehind: true,\n\t\t\t// Alias added to make those easier to distinguish from strings.\n\t\t\talias: 'class-name'\n\t\t},\n\t\t'boolean': {\n\t\t\tpattern: /(^|[\\s;|&]|[<>]\\()(?:false|true)(?=$|[)\\s;|&])/,\n\t\t\tlookbehind: true\n\t\t},\n\t\t'file-descriptor': {\n\t\t\tpattern: /\\B&\\d\\b/,\n\t\t\talias: 'important'\n\t\t},\n\t\t'operator': {\n\t\t\t// Lots of redirections here, but not just that.\n\t\t\tpattern: /\\d?<>|>\\||\\+=|=[=~]?|!=?|<<[<-]?|[&\\d]?>>|\\d[<>]&?|[<>][&=]?|&[>&]?|\\|[&|]?/,\n\t\t\tinside: {\n\t\t\t\t'file-descriptor': {\n\t\t\t\t\tpattern: /^\\d/,\n\t\t\t\t\talias: 'important'\n\t\t\t\t}\n\t\t\t}\n\t\t},\n\t\t'punctuation': /\\$?\\(\\(?|\\)\\)?|\\.\\.|[{}[\\];\\\\]/,\n\t\t'number': {\n\t\t\tpattern: /(^|\\s)(?:[1-9]\\d*|0)(?:[.,]\\d+)?\\b/,\n\t\t\tlookbehind: true\n\t\t}\n\t};\n\n\tcommandAfterHeredoc.inside = Prism.languages.bash;\n\n\t/* Patterns in command substitution. */\n\tvar toBeCopied = [\n\t\t'comment',\n\t\t'function-name',\n\t\t'for-or-select',\n\t\t'assign-left',\n\t\t'parameter',\n\t\t'string',\n\t\t'environment',\n\t\t'function',\n\t\t'keyword',\n\t\t'builtin',\n\t\t'boolean',\n\t\t'file-descriptor',\n\t\t'operator',\n\t\t'punctuation',\n\t\t'number'\n\t];\n\tvar inside = insideString.variable[1].inside;\n\tfor (var i = 0; i < toBeCopied.length; i++) {\n\t\tinside[toBeCopied[i]] = Prism.languages.bash[toBeCopied[i]];\n\t}\n\n\tPrism.languages.sh = Prism.languages.bash;\n\tPrism.languages.shell = Prism.languages.bash;\n}(Prism));\n", "import { type Ren<PERSON><PERSON>, <PERSON><PERSON> } from \"marked\";\nimport { markedHighlight } from \"marked-highlight\";\nimport { gfmHeadingId } from \"marked-gfm-heading-id\";\nimport * as Prism from \"prismjs\";\nimport \"prismjs/components/prism-python\";\nimport \"prismjs/components/prism-latex\";\nimport \"prismjs/components/prism-bash\";\nimport GithubSlugger from \"github-slugger\";\n\n// import loadLanguages from \"prismjs/components/\";\n\n// loadLanguages([\"python\", \"latex\"]);\n\nconst LINK_ICON_CODE = `<svg class=\"md-link-icon\" viewBox=\"0 0 16 16\" version=\"1.1\" width=\"16\" height=\"16\" aria-hidden=\"true\" fill=\"currentColor\"><path d=\"m7.775 3.275 1.25-1.25a3.5 3.5 0 1 1 4.95 4.95l-2.5 2.5a3.5 3.5 0 0 1-4.95 0 .751.751 0 0 1 .018-1.042.751.751 0 0 1 1.042-.018 1.998 1.998 0 0 0 2.83 0l2.5-2.5a2.002 2.002 0 0 0-2.83-2.83l-1.25 1.25a.751.751 0 0 1-1.042-.018.751.751 0 0 1-.018-1.042Zm-4.69 9.64a1.998 1.998 0 0 0 2.83 0l1.25-1.25a.751.751 0 0 1 1.042.018.751.751 0 0 1 .018 1.042l-1.25 1.25a3.5 3.5 0 1 1-4.95-4.95l2.5-2.5a3.5 3.5 0 0 1 4.95 0 .751.751 0 0 1-.018 1.042.751.751 0 0 1-1.042.018 1.998 1.998 0 0 0-2.83 0l-2.5 2.5a1.998 1.998 0 0 0 0 2.83Z\"></path></svg>`;\n\nconst COPY_ICON_CODE = `\n<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 15 15\" color=\"currentColor\" aria-hidden=\"true\" aria-label=\"Copy\" stroke-width=\"1.3\" width=\"15\" height=\"15\">\n  <path fill=\"currentColor\" d=\"M12.728 4.545v8.182H4.545V4.545zm0 -0.909H4.545a0.909 0.909 0 0 0 -0.909 0.909v8.182a0.909 0.909 0 0 0 0.909 0.909h8.182a0.909 0.909 0 0 0 0.909 -0.909V4.545a0.909 0.909 0 0 0 -0.909 -0.909\"/>\n  <path fill=\"currentColor\" d=\"M1.818 8.182H0.909V1.818a0.909 0.909 0 0 1 0.909 -0.909h6.364v0.909H1.818Z\"/>\n</svg>\n\n`;\n\nconst CHECK_ICON_CODE = `<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"17\" height=\"17\" viewBox=\"0 0 17 17\" aria-hidden=\"true\" aria-label=\"Copied\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"1.3\">\n  <path d=\"m13.813 4.781 -7.438 7.438 -3.188 -3.188\"/>\n</svg>\n`;\n\nconst COPY_BUTTON_CODE = `<button title=\"copy\" class=\"copy_code_button\">\n  <span class=\"copy-text\">${COPY_ICON_CODE}</span>\n  <span class=\"check\">${CHECK_ICON_CODE}</span>\n</button>`;\n\nconst escape_test = /[&<>\"']/;\nconst escape_replace = new RegExp(escape_test.source, \"g\");\nconst escape_test_no_encode =\n\t/[<>\"']|&(?!(#\\d{1,7}|#[Xx][a-fA-F0-9]{1,6}|\\w+);)/;\nconst escape_replace_no_encode = new RegExp(escape_test_no_encode.source, \"g\");\nconst escape_replacements: Record<string, any> = {\n\t\"&\": \"&amp;\",\n\t\"<\": \"&lt;\",\n\t\">\": \"&gt;\",\n\t'\"': \"&quot;\",\n\t\"'\": \"&#39;\"\n};\n\nconst get_escape_replacement = (ch: string): string =>\n\tescape_replacements[ch] || \"\";\n\nfunction escape(html: string, encode?: boolean): string {\n\tif (encode) {\n\t\tif (escape_test.test(html)) {\n\t\t\treturn html.replace(escape_replace, get_escape_replacement);\n\t\t}\n\t} else {\n\t\tif (escape_test_no_encode.test(html)) {\n\t\t\treturn html.replace(escape_replace_no_encode, get_escape_replacement);\n\t\t}\n\t}\n\n\treturn html;\n}\ninterface Tokenizer {\n\tname: string;\n\tlevel: string;\n\tstart: (src: string) => number | undefined;\n\ttokenizer: (src: string, tokens: any) => any;\n\trenderer: (token: any) => string;\n}\n\nfunction createLatexTokenizer(\n\tdelimiters: { left: string; right: string; display: boolean }[]\n): Tokenizer {\n\tconst delimiterPatterns = delimiters.map((delimiter) => ({\n\t\tstart: new RegExp(delimiter.left.replace(/[-\\/\\\\^$*+?.()|[\\]{}]/g, \"\\\\$&\")),\n\t\tend: new RegExp(delimiter.right.replace(/[-\\/\\\\^$*+?.()|[\\]{}]/g, \"\\\\$&\"))\n\t}));\n\n\treturn {\n\t\tname: \"latex\",\n\t\tlevel: \"block\",\n\t\tstart(src: string) {\n\t\t\tfor (const pattern of delimiterPatterns) {\n\t\t\t\tconst match = src.match(pattern.start);\n\t\t\t\tif (match) {\n\t\t\t\t\treturn match.index;\n\t\t\t\t}\n\t\t\t}\n\t\t\treturn -1;\n\t\t},\n\t\ttokenizer(src: string, tokens: any) {\n\t\t\tfor (const pattern of delimiterPatterns) {\n\t\t\t\tconst match = new RegExp(\n\t\t\t\t\t`${pattern.start.source}([\\\\s\\\\S]+?)${pattern.end.source}`\n\t\t\t\t).exec(src);\n\t\t\t\tif (match) {\n\t\t\t\t\treturn {\n\t\t\t\t\t\ttype: \"latex\",\n\t\t\t\t\t\traw: match[0],\n\t\t\t\t\t\ttext: match[1].trim()\n\t\t\t\t\t};\n\t\t\t\t}\n\t\t\t}\n\t\t},\n\t\trenderer(token: any) {\n\t\t\treturn `<div class=\"latex-block\">${token.text}</div>`;\n\t\t}\n\t};\n}\n\nfunction createMermaidTokenizer(): Tokenizer {\n\treturn {\n\t\tname: \"mermaid\",\n\t\tlevel: \"block\",\n\t\tstart(src) {\n\t\t\treturn src.match(/^```mermaid\\s*\\n/)?.index;\n\t\t},\n\t\ttokenizer(src) {\n\t\t\tconst match = /^```mermaid\\s*\\n([\\s\\S]*?)```\\s*(?:\\n|$)/.exec(src);\n\t\t\tif (match) {\n\t\t\t\treturn {\n\t\t\t\t\ttype: \"mermaid\",\n\t\t\t\t\traw: match[0],\n\t\t\t\t\ttext: match[1].trim()\n\t\t\t\t};\n\t\t\t}\n\t\t\treturn undefined;\n\t\t},\n\t\trenderer(token) {\n\t\t\treturn `<div class=\"mermaid\">${token.text}</div>\\n`;\n\t\t}\n\t};\n}\n\nconst renderer: Partial<Omit<Renderer, \"constructor\" | \"options\">> = {\n\tcode(\n\t\tthis: Renderer,\n\t\tcode: string,\n\t\tinfostring: string | undefined,\n\t\tescaped: boolean\n\t) {\n\t\tconst lang = (infostring ?? \"\").match(/\\S*/)?.[0] ?? \"\";\n\t\tcode = code.replace(/\\n$/, \"\") + \"\\n\";\n\n\t\tif (!lang || lang === \"mermaid\") {\n\t\t\t// We include lang === \"mermaid\" to handle mermaid blocks that don't match our custom tokenizer\n\t\t\t// (i.e., those without closing ```). This handles mermaid blocks that have started streaming\n\t\t\t// but haven't finished yet.\n\t\t\treturn (\n\t\t\t\t'<div class=\"code_wrap\">' +\n\t\t\t\tCOPY_BUTTON_CODE +\n\t\t\t\t\"<pre><code>\" +\n\t\t\t\t(escaped ? code : escape(code, true)) +\n\t\t\t\t\"</code></pre></div>\\n\"\n\t\t\t);\n\t\t}\n\t\treturn (\n\t\t\t'<div class=\"code_wrap\">' +\n\t\t\tCOPY_BUTTON_CODE +\n\t\t\t'<pre><code class=\"' +\n\t\t\t\"language-\" +\n\t\t\tescape(lang) +\n\t\t\t'\">' +\n\t\t\t(escaped ? code : escape(code, true)) +\n\t\t\t\"</code></pre></div>\\n\"\n\t\t);\n\t}\n};\n\nconst slugger = new GithubSlugger();\n\nexport function create_marked({\n\theader_links,\n\tline_breaks,\n\tlatex_delimiters\n}: {\n\theader_links: boolean;\n\tline_breaks: boolean;\n\tlatex_delimiters: { left: string; right: string; display: boolean }[];\n}): typeof marked {\n\tconst marked = new Marked();\n\n\tmarked.use(\n\t\t{\n\t\t\tgfm: true,\n\t\t\tpedantic: false,\n\t\t\tbreaks: line_breaks\n\t\t},\n\t\tmarkedHighlight({\n\t\t\thighlight: (code: string, lang: string) => {\n\t\t\t\tif (Prism?.languages?.[lang]) {\n\t\t\t\t\treturn Prism.highlight(code, Prism.languages[lang], lang);\n\t\t\t\t}\n\t\t\t\treturn code;\n\t\t\t}\n\t\t}),\n\t\t{ renderer }\n\t);\n\n\tif (header_links) {\n\t\tmarked.use(gfmHeadingId());\n\t\tmarked.use({\n\t\t\textensions: [\n\t\t\t\t{\n\t\t\t\t\tname: \"heading\",\n\t\t\t\t\tlevel: \"block\",\n\t\t\t\t\trenderer(token) {\n\t\t\t\t\t\tconst raw = token.raw\n\t\t\t\t\t\t\t.toLowerCase()\n\t\t\t\t\t\t\t.trim()\n\t\t\t\t\t\t\t.replace(/<[!\\/a-z].*?>/gi, \"\");\n\t\t\t\t\t\tconst id = \"h\" + slugger.slug(raw);\n\t\t\t\t\t\tconst level = token.depth;\n\t\t\t\t\t\tconst text = this.parser.parseInline(token.tokens!);\n\n\t\t\t\t\t\treturn `<h${level} id=\"${id}\"><a class=\"md-header-anchor\" href=\"#${id}\">${LINK_ICON_CODE}</a>${text}</h${level}>\\n`;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t]\n\t\t});\n\t}\n\n\tconst mermaidTokenizer = createMermaidTokenizer();\n\tconst latexTokenizer = createLatexTokenizer(latex_delimiters);\n\n\tmarked.use({\n\t\textensions: [mermaidTokenizer, latexTokenizer]\n\t});\n\n\treturn marked;\n}\n\nexport function copy(node: HTMLDivElement): any {\n\tnode.addEventListener(\"click\", handle_copy);\n\n\tasync function handle_copy(event: MouseEvent): Promise<void> {\n\t\tconst path = event.composedPath() as HTMLButtonElement[];\n\n\t\tconst [copy_button] = path.filter(\n\t\t\t(e) => e?.tagName === \"BUTTON\" && e.classList.contains(\"copy_code_button\")\n\t\t);\n\n\t\tif (copy_button) {\n\t\t\tevent.stopImmediatePropagation();\n\n\t\t\tconst copy_text = copy_button.parentElement!.innerText.trim();\n\t\t\tconst copy_sucess_button = Array.from(\n\t\t\t\tcopy_button.children\n\t\t\t)[1] as HTMLDivElement;\n\n\t\t\tconst copied = await copy_to_clipboard(copy_text);\n\n\t\t\tif (copied) copy_feedback(copy_sucess_button);\n\n\t\t\tfunction copy_feedback(_copy_sucess_button: HTMLDivElement): void {\n\t\t\t\t_copy_sucess_button.style.opacity = \"1\";\n\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t_copy_sucess_button.style.opacity = \"0\";\n\t\t\t\t}, 2000);\n\t\t\t}\n\t\t}\n\t}\n\n\treturn {\n\t\tdestroy(): void {\n\t\t\tnode.removeEventListener(\"click\", handle_copy);\n\t\t}\n\t};\n}\n\nasync function copy_to_clipboard(value: string): Promise<boolean> {\n\tlet copied = false;\n\tif (\"clipboard\" in navigator) {\n\t\tawait navigator.clipboard.writeText(value);\n\t\tcopied = true;\n\t} else {\n\t\tconst textArea = document.createElement(\"textarea\");\n\t\ttextArea.value = value;\n\n\t\ttextArea.style.position = \"absolute\";\n\t\ttextArea.style.left = \"-999999px\";\n\n\t\tdocument.body.prepend(textArea);\n\t\ttextArea.select();\n\n\t\ttry {\n\t\t\tdocument.execCommand(\"copy\");\n\t\t\tcopied = true;\n\t\t} catch (error) {\n\t\t\tconsole.error(error);\n\t\t\tcopied = false;\n\t\t} finally {\n\t\t\ttextArea.remove();\n\t\t}\n\t}\n\n\treturn copied;\n}\n", "/* IMPORT */\nimport { FUNKY_TAG_NAMES } from './constants.js';\n/* MAIN */\nconst cloneDeep = (value) => {\n    return JSON.parse(JSON.stringify(value));\n};\nconst isElement = (value) => {\n    return (value.nodeType === 1);\n};\nconst isElementFunky = (value) => {\n    return FUNKY_TAG_NAMES.has(value.tagName);\n};\nconst isElementAction = (value) => {\n    return ('action' in value);\n};\nconst isElementIframe = (value) => {\n    return (value.tagName === 'IFRAME');\n};\nconst isElementFormAction = (value) => {\n    return ('formAction' in value);\n};\nconst isElementHyperlink = (value) => {\n    return ('protocol' in value);\n};\nconst isScriptOrDataUrl = (() => {\n    const re = /^(?:\\w+script|data):/i;\n    return (url) => {\n        return re.test(url);\n    };\n})();\nconst isScriptOrDataUrlLoose = (() => {\n    const re = /(?:script|data):/i;\n    return (url) => {\n        return re.test(url);\n    };\n})();\nconst mergeMaps = (maps) => {\n    const merged = {};\n    for (let i = 0, l = maps.length; i < l; i++) {\n        const map = maps[i];\n        for (const key in map) {\n            if (!merged[key]) {\n                merged[key] = map[key];\n            }\n            else {\n                merged[key] = merged[key].concat(map[key]);\n            }\n        }\n    }\n    return merged;\n};\nconst traverseElementsBasic = (parent, callback) => {\n    let current = parent.firstChild;\n    while (current) {\n        const next = current.nextSibling;\n        if (isElement(current)) {\n            callback(current, parent);\n            if (current.parentNode) { // Still connected, so recurse\n                traverseElementsBasic(current, callback);\n            }\n        }\n        current = next;\n    }\n};\nconst traverseElementsIterator = (parent, callback) => {\n    const iterator = document.createNodeIterator(parent, NodeFilter.SHOW_ELEMENT);\n    let current;\n    while (current = iterator.nextNode()) {\n        const parent = current.parentNode;\n        if (!parent)\n            continue;\n        callback(current, parent); //TSC\n    }\n};\nconst traverseElements = (parent, callback) => {\n    const hasIterator = !!globalThis.document && !!globalThis.document.createNodeIterator; // For better WebWorker support\n    if (hasIterator) {\n        return traverseElementsIterator(parent, callback);\n    }\n    else {\n        return traverseElementsBasic(parent, callback);\n    }\n};\n/* EXPORT */\nexport { cloneDeep, isElement, isElementFunky, isElementAction, isElementIframe, isElementFormAction, isElementHyperlink, isScriptOrDataUrl, isScriptOrDataUrlLoose, mergeMaps, traverseElements };\n", "/* IMPORT */\nimport { mergeMaps } from './utils.js';\n/* ELEMENTS */\nconst HTML_ELEMENTS_ALLOW = [\n    'a',\n    'abbr',\n    'acronym',\n    'address',\n    'area',\n    'article',\n    'aside',\n    'audio',\n    'b',\n    'bdi',\n    'bdo',\n    'bgsound',\n    'big',\n    'blockquote',\n    'body',\n    'br',\n    'button',\n    'canvas',\n    'caption',\n    'center',\n    'cite',\n    'code',\n    'col',\n    'colgroup',\n    'datalist',\n    'dd',\n    'del',\n    'details',\n    'dfn',\n    'dialog',\n    'dir',\n    'div',\n    'dl',\n    'dt',\n    'em',\n    'fieldset',\n    'figcaption',\n    'figure',\n    'font',\n    'footer',\n    'form',\n    'h1',\n    'h2',\n    'h3',\n    'h4',\n    'h5',\n    'h6',\n    'head',\n    'header',\n    'hgroup',\n    'hr',\n    'html',\n    'i',\n    'img',\n    'input',\n    'ins',\n    'kbd',\n    'keygen',\n    'label',\n    'layer',\n    'legend',\n    'li',\n    'link',\n    'listing',\n    'main',\n    'map',\n    'mark',\n    'marquee',\n    'menu',\n    'meta',\n    'meter',\n    'nav',\n    'nobr',\n    'ol',\n    'optgroup',\n    'option',\n    'output',\n    'p',\n    'picture',\n    'popup',\n    'pre',\n    'progress',\n    'q',\n    'rb',\n    'rp',\n    'rt',\n    'rtc',\n    'ruby',\n    's',\n    'samp',\n    'section',\n    'select',\n    'selectmenu',\n    'small',\n    'source',\n    'span',\n    'strike',\n    'strong',\n    'style',\n    'sub',\n    'summary',\n    'sup',\n    'table',\n    'tbody',\n    'td',\n    'tfoot',\n    'th',\n    'thead',\n    'time',\n    'tr',\n    'track',\n    'tt',\n    'u',\n    'ul',\n    'var',\n    'video',\n    'wbr'\n];\nconst HTML_ELEMENTS_DISALLOW = [\n    'basefont',\n    'command',\n    'data',\n    'iframe',\n    'image',\n    'plaintext',\n    'portal',\n    'slot',\n    // 'template', //TODO: Not exactly correct to never allow this, too strict\n    'textarea',\n    'title',\n    'xmp'\n];\nconst HTML_ELEMENTS = new Set([\n    ...HTML_ELEMENTS_ALLOW,\n    ...HTML_ELEMENTS_DISALLOW\n]);\nconst SVG_ELEMENTS_ALLOW = [\n    'svg',\n    'a',\n    'altglyph',\n    'altglyphdef',\n    'altglyphitem',\n    'animatecolor',\n    'animatemotion',\n    'animatetransform',\n    'circle',\n    'clippath',\n    'defs',\n    'desc',\n    'ellipse',\n    'filter',\n    'font',\n    'g',\n    'glyph',\n    'glyphref',\n    'hkern',\n    'image',\n    'line',\n    'lineargradient',\n    'marker',\n    'mask',\n    'metadata',\n    'mpath',\n    'path',\n    'pattern',\n    'polygon',\n    'polyline',\n    'radialgradient',\n    'rect',\n    'stop',\n    'style',\n    'switch',\n    'symbol',\n    'text',\n    'textpath',\n    'title',\n    'tref',\n    'tspan',\n    'view',\n    'vkern',\n    /* FILTERS */\n    'feBlend',\n    'feColorMatrix',\n    'feComponentTransfer',\n    'feComposite',\n    'feConvolveMatrix',\n    'feDiffuseLighting',\n    'feDisplacementMap',\n    'feDistantLight',\n    'feFlood',\n    'feFuncA',\n    'feFuncB',\n    'feFuncG',\n    'feFuncR',\n    'feGaussianBlur',\n    'feImage',\n    'feMerge',\n    'feMergeNode',\n    'feMorphology',\n    'feOffset',\n    'fePointLight',\n    'feSpecularLighting',\n    'feSpotLight',\n    'feTile',\n    'feTurbulence'\n];\nconst SVG_ELEMENTS_DISALLOW = [\n    'animate',\n    'color-profile',\n    'cursor',\n    'discard',\n    'fedropshadow',\n    'font-face',\n    'font-face-format',\n    'font-face-name',\n    'font-face-src',\n    'font-face-uri',\n    'foreignobject',\n    'hatch',\n    'hatchpath',\n    'mesh',\n    'meshgradient',\n    'meshpatch',\n    'meshrow',\n    'missing-glyph',\n    'script',\n    'set',\n    'solidcolor',\n    'unknown',\n    'use'\n];\nconst SVG_ELEMENTS = new Set([\n    ...SVG_ELEMENTS_ALLOW,\n    ...SVG_ELEMENTS_DISALLOW\n]);\nconst MATH_ELEMENTS_ALLOW = [\n    'math',\n    'menclose',\n    'merror',\n    'mfenced',\n    'mfrac',\n    'mglyph',\n    'mi',\n    'mlabeledtr',\n    'mmultiscripts',\n    'mn',\n    'mo',\n    'mover',\n    'mpadded',\n    'mphantom',\n    'mroot',\n    'mrow',\n    'ms',\n    'mspace',\n    'msqrt',\n    'mstyle',\n    'msub',\n    'msup',\n    'msubsup',\n    'mtable',\n    'mtd',\n    'mtext',\n    'mtr',\n    'munder',\n    'munderover'\n];\nconst MATH_ELEMENTS_DISALLOW = [\n    'maction',\n    'maligngroup',\n    'malignmark',\n    'mlongdiv',\n    'mscarries',\n    'mscarry',\n    'msgroup',\n    'mstack',\n    'msline',\n    'msrow',\n    'semantics',\n    'annotation',\n    'annotation-xml',\n    'mprescripts',\n    'none'\n];\nconst MATH_ELEMENTS = new Set([\n    ...MATH_ELEMENTS_ALLOW,\n    ...MATH_ELEMENTS_DISALLOW\n]);\n/* ATTRIBUTES */\nconst HTML_ATTRIBUTES_ALLOW = [\n    'abbr',\n    'accept',\n    'accept-charset',\n    'accesskey',\n    'action',\n    'align',\n    'alink',\n    'allow',\n    'allowfullscreen',\n    'alt',\n    'anchor',\n    'archive',\n    'as',\n    'async',\n    'autocapitalize',\n    'autocomplete',\n    'autocorrect',\n    'autofocus',\n    'autopictureinpicture',\n    'autoplay',\n    'axis',\n    'background',\n    'behavior',\n    'bgcolor',\n    'border',\n    'bordercolor',\n    'capture',\n    'cellpadding',\n    'cellspacing',\n    'challenge',\n    'char',\n    'charoff',\n    'charset',\n    'checked',\n    'cite',\n    'class',\n    'classid',\n    'clear',\n    'code',\n    'codebase',\n    'codetype',\n    'color',\n    'cols',\n    'colspan',\n    'compact',\n    'content',\n    'contenteditable',\n    'controls',\n    'controlslist',\n    'conversiondestination',\n    'coords',\n    'crossorigin',\n    'csp',\n    'data',\n    'datetime',\n    'declare',\n    'decoding',\n    'default',\n    'defer',\n    'dir',\n    'direction',\n    'dirname',\n    'disabled',\n    'disablepictureinpicture',\n    'disableremoteplayback',\n    'disallowdocumentaccess',\n    'download',\n    'draggable',\n    'elementtiming',\n    'enctype',\n    'end',\n    'enterkeyhint',\n    'event',\n    'exportparts',\n    'face',\n    'for',\n    'form',\n    'formaction',\n    'formenctype',\n    'formmethod',\n    'formnovalidate',\n    'formtarget',\n    'frame',\n    'frameborder',\n    'headers',\n    'height',\n    'hidden',\n    'high',\n    'href',\n    'hreflang',\n    'hreftranslate',\n    'hspace',\n    'http-equiv',\n    'id',\n    'imagesizes',\n    'imagesrcset',\n    'importance',\n    'impressiondata',\n    'impressionexpiry',\n    'incremental',\n    'inert',\n    'inputmode',\n    'integrity',\n    'invisible',\n    'ismap',\n    'keytype',\n    'kind',\n    'label',\n    'lang',\n    'language',\n    'latencyhint',\n    'leftmargin',\n    'link',\n    'list',\n    'loading',\n    'longdesc',\n    'loop',\n    'low',\n    'lowsrc',\n    'manifest',\n    'marginheight',\n    'marginwidth',\n    'max',\n    'maxlength',\n    'mayscript',\n    'media',\n    'method',\n    'min',\n    'minlength',\n    'multiple',\n    'muted',\n    'name',\n    'nohref',\n    'nomodule',\n    'nonce',\n    'noresize',\n    'noshade',\n    'novalidate',\n    'nowrap',\n    'object',\n    'open',\n    'optimum',\n    'part',\n    'pattern',\n    'ping',\n    'placeholder',\n    'playsinline',\n    'policy',\n    'poster',\n    'preload',\n    'pseudo',\n    'readonly',\n    'referrerpolicy',\n    'rel',\n    'reportingorigin',\n    'required',\n    'resources',\n    'rev',\n    'reversed',\n    'role',\n    'rows',\n    'rowspan',\n    'rules',\n    'sandbox',\n    'scheme',\n    'scope',\n    'scopes',\n    'scrollamount',\n    'scrolldelay',\n    'scrolling',\n    'select',\n    'selected',\n    'shadowroot',\n    'shadowrootdelegatesfocus',\n    'shape',\n    'size',\n    'sizes',\n    'slot',\n    'span',\n    'spellcheck',\n    'src',\n    'srclang',\n    'srcset',\n    'standby',\n    'start',\n    'step',\n    'style',\n    'summary',\n    'tabindex',\n    'target',\n    'text',\n    'title',\n    'topmargin',\n    'translate',\n    'truespeed',\n    'trusttoken',\n    'type',\n    'usemap',\n    'valign',\n    'value',\n    'valuetype',\n    'version',\n    'virtualkeyboardpolicy',\n    'vlink',\n    'vspace',\n    'webkitdirectory',\n    'width',\n    'wrap'\n];\nconst HTML_ATTRIBUTES_DISALLOW = [\n    'allowpaymentrequest',\n    'is'\n];\nconst HTML_ATTRIBUTES = new Set([\n    ...HTML_ATTRIBUTES_ALLOW,\n    ...HTML_ATTRIBUTES_DISALLOW\n]);\nconst SVG_ATTRIBUTES_ALLOW = [\n    'accent-height',\n    'accumulate',\n    'additive',\n    'alignment-baseline',\n    'ascent',\n    'attributename',\n    'attributetype',\n    'azimuth',\n    'basefrequency',\n    'baseline-shift',\n    'begin',\n    'bias',\n    'by',\n    'class',\n    'clip',\n    'clippathunits',\n    'clip-path',\n    'clip-rule',\n    'color',\n    'color-interpolation',\n    'color-interpolation-filters',\n    'color-profile',\n    'color-rendering',\n    'cx',\n    'cy',\n    'd',\n    'dx',\n    'dy',\n    'diffuseconstant',\n    'direction',\n    'display',\n    'divisor',\n    'dominant-baseline',\n    'dur',\n    'edgemode',\n    'elevation',\n    'end',\n    'fill',\n    'fill-opacity',\n    'fill-rule',\n    'filter',\n    'filterunits',\n    'flood-color',\n    'flood-opacity',\n    'font-family',\n    'font-size',\n    'font-size-adjust',\n    'font-stretch',\n    'font-style',\n    'font-variant',\n    'font-weight',\n    'fx',\n    'fy',\n    'g1',\n    'g2',\n    'glyph-name',\n    'glyphref',\n    'gradientunits',\n    'gradienttransform',\n    'height',\n    'href',\n    'id',\n    'image-rendering',\n    'in',\n    'in2',\n    'k',\n    'k1',\n    'k2',\n    'k3',\n    'k4',\n    'kerning',\n    'keypoints',\n    'keysplines',\n    'keytimes',\n    'lang',\n    'lengthadjust',\n    'letter-spacing',\n    'kernelmatrix',\n    'kernelunitlength',\n    'lighting-color',\n    'local',\n    'marker-end',\n    'marker-mid',\n    'marker-start',\n    'markerheight',\n    'markerunits',\n    'markerwidth',\n    'maskcontentunits',\n    'maskunits',\n    'max',\n    'mask',\n    'media',\n    'method',\n    'mode',\n    'min',\n    'name',\n    'numoctaves',\n    'offset',\n    'operator',\n    'opacity',\n    'order',\n    'orient',\n    'orientation',\n    'origin',\n    'overflow',\n    'paint-order',\n    'path',\n    'pathlength',\n    'patterncontentunits',\n    'patterntransform',\n    'patternunits',\n    'points',\n    'preservealpha',\n    'preserveaspectratio',\n    'primitiveunits',\n    'r',\n    'rx',\n    'ry',\n    'radius',\n    'refx',\n    'refy',\n    'repeatcount',\n    'repeatdur',\n    'restart',\n    'result',\n    'rotate',\n    'scale',\n    'seed',\n    'shape-rendering',\n    'specularconstant',\n    'specularexponent',\n    'spreadmethod',\n    'startoffset',\n    'stddeviation',\n    'stitchtiles',\n    'stop-color',\n    'stop-opacity',\n    'stroke-dasharray',\n    'stroke-dashoffset',\n    'stroke-linecap',\n    'stroke-linejoin',\n    'stroke-miterlimit',\n    'stroke-opacity',\n    'stroke',\n    'stroke-width',\n    'style',\n    'surfacescale',\n    'systemlanguage',\n    'tabindex',\n    'targetx',\n    'targety',\n    'transform',\n    'transform-origin',\n    'text-anchor',\n    'text-decoration',\n    'text-rendering',\n    'textlength',\n    'type',\n    'u1',\n    'u2',\n    'unicode',\n    'values',\n    'viewbox',\n    'visibility',\n    'version',\n    'vert-adv-y',\n    'vert-origin-x',\n    'vert-origin-y',\n    'width',\n    'word-spacing',\n    'wrap',\n    'writing-mode',\n    'xchannelselector',\n    'ychannelselector',\n    'x',\n    'x1',\n    'x2',\n    'xmlns',\n    'y',\n    'y1',\n    'y2',\n    'z',\n    'zoomandpan'\n];\nconst SVG_ATTRIBUTES_DISALLOW = [];\nconst SVG_ATTRIBUTES = new Set([\n    ...SVG_ATTRIBUTES_ALLOW,\n    ...SVG_ATTRIBUTES_DISALLOW\n]);\nconst MATH_ATTRIBUTES_ALLOW = [\n    'accent',\n    'accentunder',\n    'align',\n    'bevelled',\n    'close',\n    'columnsalign',\n    'columnlines',\n    'columnspan',\n    'denomalign',\n    'depth',\n    'dir',\n    'display',\n    'displaystyle',\n    'encoding',\n    'fence',\n    'frame',\n    'height',\n    'href',\n    'id',\n    'largeop',\n    'length',\n    'linethickness',\n    'lspace',\n    'lquote',\n    'mathbackground',\n    'mathcolor',\n    'mathsize',\n    'mathvariant',\n    'maxsize',\n    'minsize',\n    'movablelimits',\n    'notation',\n    'numalign',\n    'open',\n    'rowalign',\n    'rowlines',\n    'rowspacing',\n    'rowspan',\n    'rspace',\n    'rquote',\n    'scriptlevel',\n    'scriptminsize',\n    'scriptsizemultiplier',\n    'selection',\n    'separator',\n    'separators',\n    'stretchy',\n    'subscriptshift',\n    'supscriptshift',\n    'symmetric',\n    'voffset',\n    'width',\n    'xmlns'\n];\nconst MATH_ATTRIBUTES_DISALLOW = [];\nconst MATH_ATTRIBUTES = new Set([\n    ...MATH_ATTRIBUTES_ALLOW,\n    ...MATH_ATTRIBUTES_DISALLOW\n]);\n/* NAMESPACES */\nconst NAMESPACES = {\n    HTML: 'http://www.w3.org/1999/xhtml',\n    SVG: 'http://www.w3.org/2000/svg',\n    MATH: 'http://www.w3.org/1998/Math/MathML'\n};\nconst NAMESPACES_ELEMENTS = {\n    [NAMESPACES.HTML]: HTML_ELEMENTS,\n    [NAMESPACES.SVG]: SVG_ELEMENTS,\n    [NAMESPACES.MATH]: MATH_ELEMENTS\n};\nconst NAMESPACES_ATTRIBUTES = {\n    [NAMESPACES.HTML]: HTML_ATTRIBUTES,\n    [NAMESPACES.SVG]: SVG_ATTRIBUTES,\n    [NAMESPACES.MATH]: MATH_ATTRIBUTES\n};\nconst NAMESPACES_ROOTS = {\n    [NAMESPACES.HTML]: 'html',\n    [NAMESPACES.SVG]: 'svg',\n    [NAMESPACES.MATH]: 'math'\n};\nconst NAMESPACES_PREFIXES = {\n    [NAMESPACES.HTML]: '',\n    [NAMESPACES.SVG]: 'svg:',\n    [NAMESPACES.MATH]: 'math:'\n};\n/* TAG NAMES */\nconst FUNKY_TAG_NAMES = new Set([\n    'A',\n    'AREA',\n    'BUTTON',\n    'FORM',\n    'IFRAME',\n    'INPUT'\n]);\n/* OTHERS */\nconst DEFAULTS = {\n    allowComments: true,\n    allowCustomElements: false,\n    allowUnknownMarkup: false,\n    allowElements: [\n        ...HTML_ELEMENTS_ALLOW,\n        ...SVG_ELEMENTS_ALLOW.map(name => `svg:${name}`),\n        ...MATH_ELEMENTS_ALLOW.map(name => `math:${name}`)\n    ],\n    allowAttributes: mergeMaps([\n        Object.fromEntries(HTML_ATTRIBUTES_ALLOW.map(name => [name, ['*']])),\n        Object.fromEntries(SVG_ATTRIBUTES_ALLOW.map(name => [name, ['svg:*']])),\n        Object.fromEntries(MATH_ATTRIBUTES_ALLOW.map(name => [name, ['math:*']]))\n    ])\n};\n/* EXPORT */\nexport { HTML_ELEMENTS, SVG_ELEMENTS, MATH_ELEMENTS };\nexport { HTML_ATTRIBUTES, SVG_ATTRIBUTES, MATH_ATTRIBUTES };\nexport { NAMESPACES, NAMESPACES_ELEMENTS, NAMESPACES_ATTRIBUTES, NAMESPACES_ROOTS, NAMESPACES_PREFIXES };\nexport { FUNKY_TAG_NAMES };\nexport { DEFAULTS };\n", "/* IMPORT */\nvar __classPrivateFieldSet = (this && this.__classPrivateFieldSet) || function (receiver, state, value, kind, f) {\n    if (kind === \"m\") throw new TypeError(\"Private method is not writable\");\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a setter\");\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot write private member to an object whose class did not declare it\");\n    return (kind === \"a\" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value)), value;\n};\nvar __classPrivateFieldGet = (this && this.__classPrivateFieldGet) || function (receiver, state, kind, f) {\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a getter\");\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot read private member from an object whose class did not declare it\");\n    return kind === \"m\" ? f : kind === \"a\" ? f.call(receiver) : f ? f.value : state.get(receiver);\n};\nvar _Amuchina_configuration, _Amuchina_allowElements, _Amuchina_allowAttributes;\nimport { NAMESPACES, NAMESPACES_ELEMENTS, NAMESPACES_ROOTS, NAMESPACES_PREFIXES } from './constants.js';\nimport { DEFAULTS } from './constants.js';\nimport { cloneDeep, isElementFunky, isElementAction, isElementIframe, isElementFormAction, isElementHyperlink, isScriptOrDataUrl, isScriptOrDataUrlLoose, traverseElements } from './utils.js';\n/* MAIN */\n//TODO: Add a decent test suite, possibly one from an existing trusted library\nclass Amuchina {\n    /* CONSTRUCTOR */\n    constructor(configuration = {}) {\n        /* VARIABLES */\n        _Amuchina_configuration.set(this, void 0);\n        _Amuchina_allowElements.set(this, void 0);\n        _Amuchina_allowAttributes.set(this, void 0);\n        /* API */\n        this.getConfiguration = () => {\n            return cloneDeep(__classPrivateFieldGet(this, _Amuchina_configuration, \"f\"));\n        };\n        this.sanitize = (input) => {\n            //TODO: Support integration points (foreignObject and friends)\n            //TODO: Support xlink:href, xml:id, xlink:title, xml:space, xmlns:xlink\n            const allowElements = __classPrivateFieldGet(this, _Amuchina_allowElements, \"f\");\n            const allowAttributes = __classPrivateFieldGet(this, _Amuchina_allowAttributes, \"f\");\n            traverseElements(input, (node, parent) => {\n                const namespace = node.namespaceURI || NAMESPACES.HTML;\n                const namespaceParent = parent['namespaceURI'] || NAMESPACES.HTML;\n                const elements = NAMESPACES_ELEMENTS[namespace];\n                const root = NAMESPACES_ROOTS[namespace];\n                const prefix = NAMESPACES_PREFIXES[namespace];\n                const tag = node.tagName.toLowerCase();\n                const tagPrefixed = `${prefix}${tag}`;\n                const all = '*';\n                const allPrefixed = `${prefix}${all}`;\n                if (!elements.has(tag) || !allowElements.has(tagPrefixed) || (namespace !== namespaceParent && tag !== root)) {\n                    parent.removeChild(node);\n                }\n                else {\n                    const attributes = node.getAttributeNames();\n                    const attributesLength = attributes.length;\n                    if (attributesLength) {\n                        for (let i = 0; i < attributesLength; i++) {\n                            const attribute = attributes[i];\n                            const allowedValues = allowAttributes[attribute];\n                            if (!allowedValues || (!allowedValues.has(allPrefixed) && !allowedValues.has(tagPrefixed))) {\n                                node.removeAttribute(attribute);\n                            }\n                        }\n                        if (isElementFunky(node)) {\n                            if (isElementHyperlink(node)) {\n                                const href = node.getAttribute('href');\n                                if (href && isScriptOrDataUrlLoose(href) && isScriptOrDataUrl(node.protocol)) {\n                                    node.removeAttribute('href');\n                                }\n                            }\n                            else if (isElementAction(node)) {\n                                if (isScriptOrDataUrl(node.action)) {\n                                    node.removeAttribute('action');\n                                }\n                            }\n                            else if (isElementFormAction(node)) {\n                                if (isScriptOrDataUrl(node.formAction)) {\n                                    node.removeAttribute('formaction');\n                                }\n                            }\n                            else if (isElementIframe(node)) {\n                                if (isScriptOrDataUrl(node.src)) {\n                                    node.removeAttribute('formaction');\n                                }\n                                node.setAttribute('sandbox', 'allow-scripts'); //TODO: This is kinda arbitrary, it should be customizable and more flexible\n                            }\n                        }\n                    }\n                }\n            });\n            return input;\n        };\n        this.sanitizeFor = (element, input) => {\n            throw new Error('\"sanitizeFor\" is not implemented yet');\n        };\n        const { allowComments, allowCustomElements, allowUnknownMarkup, blockElements, dropElements, dropAttributes } = configuration;\n        if (allowComments === false)\n            throw new Error('A false \"allowComments\" is not supported yet');\n        if (allowCustomElements)\n            throw new Error('A true \"allowCustomElements\" is not supported yet');\n        if (allowUnknownMarkup)\n            throw new Error('A true \"allowUnknownMarkup\" is not supported yet');\n        if (blockElements)\n            throw new Error('\"blockElements\" is not supported yet, use \"allowElements\" instead');\n        if (dropElements)\n            throw new Error('\"dropElements\" is not supported yet, use \"allowElements\" instead');\n        if (dropAttributes)\n            throw new Error('\"dropAttributes\" is not supported yet, use \"allowAttributes\" instead');\n        __classPrivateFieldSet(this, _Amuchina_configuration, cloneDeep(DEFAULTS), \"f\");\n        const { allowElements, allowAttributes } = configuration;\n        if (allowElements)\n            __classPrivateFieldGet(this, _Amuchina_configuration, \"f\").allowElements = configuration.allowElements;\n        if (allowAttributes)\n            __classPrivateFieldGet(this, _Amuchina_configuration, \"f\").allowAttributes = configuration.allowAttributes;\n        __classPrivateFieldSet(this, _Amuchina_allowElements, new Set(__classPrivateFieldGet(this, _Amuchina_configuration, \"f\").allowElements), \"f\");\n        __classPrivateFieldSet(this, _Amuchina_allowAttributes, Object.fromEntries(Object.entries(__classPrivateFieldGet(this, _Amuchina_configuration, \"f\").allowAttributes || {}).map(([element, attributes]) => [element, new Set(attributes)])), \"f\");\n    }\n}\n_Amuchina_configuration = new WeakMap(), _Amuchina_allowElements = new WeakMap(), _Amuchina_allowAttributes = new WeakMap();\n/* STATIC API */\nAmuchina.getDefaultConfiguration = () => {\n    return cloneDeep(DEFAULTS);\n};\n/* EXPORT */\nexport default Amuchina;\n"], "names": ["_getDefaults", "_defaults", "changeDefaults", "newDefaults", "escapeTest", "escapeReplace", "escapeTestNoEncode", "escapeReplaceNoEncode", "escapeReplacements", "getEscapeReplacement", "ch", "escape$1", "html", "encode", "unescapeTest", "unescape", "_", "n", "caret", "edit", "regex", "opt", "source", "obj", "name", "val", "valSource", "cleanUrl", "href", "noopTest", "splitCells", "tableRow", "count", "row", "match", "offset", "str", "escaped", "curr", "cells", "i", "rtrim", "c", "invert", "l", "suffLen", "curr<PERSON>har", "findClosingBracket", "b", "level", "outputLink", "cap", "link", "raw", "lexer", "title", "text", "token", "indentCodeCompensation", "matchIndentToCode", "indentToCode", "node", "matchIndentInNode", "indentInNode", "_Tokenizer", "options", "src", "trimmed", "top", "tokens", "bull", "isordered", "list", "itemRegex", "itemContents", "endsWithBlankLine", "endEarly", "line", "t", "nextLine", "indent", "blankLine", "nextBulletRegex", "hrRegex", "fencesBeginRegex", "headingBeginRegex", "rawLine", "istask", "ischecked", "spacers", "hasMultipleLineBreaks", "tag", "headers", "aligns", "rows", "item", "align", "header", "cell", "trimmedUrl", "rtrimSlash", "lastParenIndex", "linkLen", "links", "linkString", "maskedSrc", "prevChar", "l<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "r<PERSON><PERSON><PERSON>", "delimTotal", "midDelimTotal", "endReg", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hasNonSpaceChars", "hasSpaceCharsOnBothEnds", "prevCapZero", "newline", "blockCode", "fences", "hr", "heading", "bullet", "lheading", "_paragraph", "blockText", "_blockLabel", "def", "_tag", "_comment", "paragraph", "blockquote", "blockNormal", "gfmTable", "blockGfm", "blockPedantic", "escape", "inlineCode", "br", "inlineText", "_punctuation", "punctuation", "blockSkip", "em<PERSON><PERSON>g<PERSON><PERSON><PERSON>", "emStrongRDelim<PERSON>t", "emStrongRDelimUnd", "anyPunctuation", "autolink", "_inlineComment", "_inlineLabel", "reflink", "nolink", "reflinkSearch", "inlineNormal", "inlinePedantic", "inlineGfm", "inlineBreaks", "block", "inline", "_<PERSON>er", "rules", "next", "leading", "tabs", "lastToken", "cutSrc", "lastParagraphClipped", "extTokenizer", "startIndex", "tempSrc", "tempStart", "getStartIndex", "errMsg", "keepPrevChar", "_Renderer", "code", "infostring", "lang", "quote", "body", "ordered", "start", "type", "startatt", "task", "checked", "content", "flags", "cleanHref", "out", "_<PERSON><PERSON><PERSON><PERSON>", "_<PERSON><PERSON>r", "genericToken", "ret", "headingToken", "codeToken", "tableToken", "j", "k", "blockquoteToken", "listToken", "loose", "itemBody", "checkbox", "htmlToken", "paragraphToken", "textToken", "renderer", "escapeToken", "tagToken", "linkToken", "imageToken", "strongToken", "emToken", "codespanToken", "delToken", "_Hooks", "markdown", "Marked", "#parseMarkdown", "args", "callback", "values", "childTokens", "extensions", "pack", "opts", "ext", "prev<PERSON><PERSON><PERSON>", "extLevel", "prop", "rendererProp", "rendererFunc", "tokenizer", "tokenizerProp", "tokenizerFunc", "prevTokenizer", "hooks", "hooksProp", "hooksFunc", "prevHook", "arg", "walkTokens", "packWalktokens", "parser", "origOpt", "throwError", "#onError", "e", "silent", "async", "msg", "markedInstance", "marked", "<PERSON><PERSON><PERSON><PERSON>", "getLang", "updateToken", "infoString", "classValue", "classAttr", "own", "<PERSON><PERSON><PERSON><PERSON>", "value", "maintainCase", "self", "result", "slug", "originalSlug", "slugger", "GithubSlugger", "headings", "gfmHeadingId", "prefix", "globalSlugs", "resetHeadings", "id", "Prism", "funcPattern", "insideEqu", "envVars", "commandAfterHeredoc", "insideString", "toBeCopied", "inside", "LINK_ICON_CODE", "COPY_ICON_CODE", "CHECK_ICON_CODE", "COPY_BUTTON_CODE", "escape_test", "escape_replace", "escape_test_no_encode", "escape_replace_no_encode", "escape_replacements", "get_escape_replacement", "createLatexTokenizer", "delimiters", "delimiterPatterns", "delimiter", "pattern", "createMermaidTokenizer", "create_marked", "header_links", "line_breaks", "latex_delimiters", "Prism?.languages", "Prism.highlight", "Prism.languages", "mermaidTokenizer", "latexTokenizer", "cloneDeep", "isElement", "isElementFunky", "FUNKY_TAG_NAMES", "isElementAction", "isElementIframe", "isElementFormAction", "isElementHyperlink", "isScriptOrDataUrl", "re", "url", "isScriptOrDataUrlLoose", "mergeMaps", "maps", "merged", "map", "key", "traverseElementsBasic", "parent", "current", "traverseElementsIterator", "iterator", "traverseElements", "HTML_ELEMENTS_ALLOW", "HTML_ELEMENTS_DISALLOW", "HTML_ELEMENTS", "SVG_ELEMENTS_ALLOW", "SVG_ELEMENTS_DISALLOW", "SVG_ELEMENTS", "MATH_ELEMENTS_ALLOW", "MATH_ELEMENTS_DISALLOW", "MATH_ELEMENTS", "HTML_ATTRIBUTES_ALLOW", "SVG_ATTRIBUTES_ALLOW", "MATH_ATTRIBUTES_ALLOW", "NAMESPACES", "NAMESPACES_ELEMENTS", "NAMESPACES_ROOTS", "NAMESPACES_PREFIXES", "DEFAULTS", "__classPrivateFieldSet", "receiver", "state", "kind", "f", "__classPrivateFieldGet", "_Amuchina_configuration", "_Amuchina_allowElements", "_Amuchina_allowAttributes", "<PERSON><PERSON><PERSON>", "configuration", "input", "allowElements", "allowAttributes", "namespace", "namespaceParent", "elements", "root", "tagPrefixed", "allPrefixed", "attributes", "<PERSON><PERSON><PERSON><PERSON>", "attribute", "<PERSON><PERSON><PERSON><PERSON>", "element", "allowComments", "allowCustomElements", "allowUnknownMarkup", "blockElements", "dropElements", "dropAttributes"], "mappings": "+CAcA,SAASA,GAAe,CACpB,MAAO,CACH,MAAO,GACP,OAAQ,GACR,WAAY,KACZ,IAAK,GACL,MAAO,KACP,SAAU,GACV,SAAU,KACV,OAAQ,GACR,UAAW,KACX,WAAY,IACpB,CACA,CACA,IAAIC,EAAYD,EAAY,EAC5B,SAASE,GAAeC,EAAa,CACjCF,EAAYE,CAChB,CAKA,MAAMC,GAAa,UACbC,GAAgB,IAAI,OAAOD,GAAW,OAAQ,GAAG,EACjDE,GAAqB,oDACrBC,GAAwB,IAAI,OAAOD,GAAmB,OAAQ,GAAG,EACjEE,GAAqB,CACvB,IAAK,QACL,IAAK,OACL,IAAK,OACL,IAAK,SACL,IAAK,OACT,EACMC,GAAwBC,GAAOF,GAAmBE,CAAE,EAC1D,SAASC,EAASC,EAAMC,EAAQ,CAC5B,GAAIA,GACA,GAAIT,GAAW,KAAKQ,CAAI,EACpB,OAAOA,EAAK,QAAQP,GAAeI,EAAoB,UAIvDH,GAAmB,KAAKM,CAAI,EAC5B,OAAOA,EAAK,QAAQL,GAAuBE,EAAoB,EAGvE,OAAOG,CACX,CACA,MAAME,GAAe,6CACrB,SAASC,GAASH,EAAM,CAEpB,OAAOA,EAAK,QAAQE,GAAc,CAACE,EAAGC,KAClCA,EAAIA,EAAE,cACFA,IAAM,QACC,IACPA,EAAE,OAAO,CAAC,IAAM,IACTA,EAAE,OAAO,CAAC,IAAM,IACjB,OAAO,aAAa,SAASA,EAAE,UAAU,CAAC,EAAG,EAAE,CAAC,EAChD,OAAO,aAAa,CAACA,EAAE,UAAU,CAAC,CAAC,EAEtC,GACV,CACL,CACA,MAAMC,GAAQ,eACd,SAASC,EAAKC,EAAOC,EAAK,CACtB,IAAIC,EAAS,OAAOF,GAAU,SAAWA,EAAQA,EAAM,OACvDC,EAAMA,GAAO,GACb,MAAME,EAAM,CACR,QAAS,CAACC,EAAMC,IAAQ,CACpB,IAAIC,EAAY,OAAOD,GAAQ,SAAWA,EAAMA,EAAI,OACpD,OAAAC,EAAYA,EAAU,QAAQR,GAAO,IAAI,EACzCI,EAASA,EAAO,QAAQE,EAAME,CAAS,EAChCH,CACV,EACD,SAAU,IACC,IAAI,OAAOD,EAAQD,CAAG,CAEzC,EACI,OAAOE,CACX,CACA,SAASI,GAASC,EAAM,CACpB,GAAI,CACAA,EAAO,UAAUA,CAAI,EAAE,QAAQ,OAAQ,GAAG,CAC7C,MACS,CACN,OAAO,IACV,CACD,OAAOA,CACX,CACA,MAAMC,EAAW,CAAE,KAAM,IAAM,MAC/B,SAASC,GAAWC,EAAUC,EAAO,CAGjC,MAAMC,EAAMF,EAAS,QAAQ,MAAO,CAACG,EAAOC,EAAQC,IAAQ,CACxD,IAAIC,EAAU,GACVC,EAAOH,EACX,KAAO,EAAEG,GAAQ,GAAKF,EAAIE,CAAI,IAAM,MAChCD,EAAU,CAACA,EACf,OAAIA,EAGO,IAIA,IAEd,CAAA,EAAGE,EAAQN,EAAI,MAAM,KAAK,EAC3B,IAAIO,EAAI,EAQR,GANKD,EAAM,CAAC,EAAE,KAAI,GACdA,EAAM,MAAK,EAEXA,EAAM,OAAS,GAAK,CAACA,EAAMA,EAAM,OAAS,CAAC,EAAE,QAC7CA,EAAM,IAAG,EAETP,EACA,GAAIO,EAAM,OAASP,EACfO,EAAM,OAAOP,CAAK,MAGlB,MAAOO,EAAM,OAASP,GAClBO,EAAM,KAAK,EAAE,EAGzB,KAAOC,EAAID,EAAM,OAAQC,IAErBD,EAAMC,CAAC,EAAID,EAAMC,CAAC,EAAE,KAAI,EAAG,QAAQ,QAAS,GAAG,EAEnD,OAAOD,CACX,CASA,SAASE,EAAML,EAAKM,EAAGC,EAAQ,CAC3B,MAAMC,EAAIR,EAAI,OACd,GAAIQ,IAAM,EACN,MAAO,GAGX,IAAIC,EAAU,EAEd,KAAOA,EAAUD,GAAG,CAChB,MAAME,EAAWV,EAAI,OAAOQ,EAAIC,EAAU,CAAC,EAC3C,GAAIC,IAAaJ,GAAK,CAACC,EACnBE,YAEKC,IAAaJ,GAAKC,EACvBE,QAGA,MAEP,CACD,OAAOT,EAAI,MAAM,EAAGQ,EAAIC,CAAO,CACnC,CACA,SAASE,GAAmBX,EAAKY,EAAG,CAChC,GAAIZ,EAAI,QAAQY,EAAE,CAAC,CAAC,IAAM,GACtB,MAAO,GAEX,IAAIC,EAAQ,EACZ,QAAST,EAAI,EAAGA,EAAIJ,EAAI,OAAQI,IAC5B,GAAIJ,EAAII,CAAC,IAAM,KACXA,YAEKJ,EAAII,CAAC,IAAMQ,EAAE,CAAC,EACnBC,YAEKb,EAAII,CAAC,IAAMQ,EAAE,CAAC,IACnBC,IACIA,EAAQ,GACR,OAAOT,EAInB,MAAO,EACX,CAEA,SAASU,GAAWC,EAAKC,EAAMC,EAAKC,EAAO,CACvC,MAAM1B,EAAOwB,EAAK,KACZG,EAAQH,EAAK,MAAQzC,EAASyC,EAAK,KAAK,EAAI,KAC5CI,EAAOL,EAAI,CAAC,EAAE,QAAQ,cAAe,IAAI,EAC/C,GAAIA,EAAI,CAAC,EAAE,OAAO,CAAC,IAAM,IAAK,CAC1BG,EAAM,MAAM,OAAS,GACrB,MAAMG,EAAQ,CACV,KAAM,OACN,IAAAJ,EACA,KAAAzB,EACA,MAAA2B,EACA,KAAAC,EACA,OAAQF,EAAM,aAAaE,CAAI,CAC3C,EACQ,OAAAF,EAAM,MAAM,OAAS,GACdG,CACV,CACD,MAAO,CACH,KAAM,QACN,IAAAJ,EACA,KAAAzB,EACA,MAAA2B,EACA,KAAM5C,EAAS6C,CAAI,CAC3B,CACA,CACA,SAASE,GAAuBL,EAAKG,EAAM,CACvC,MAAMG,EAAoBN,EAAI,MAAM,eAAe,EACnD,GAAIM,IAAsB,KACtB,OAAOH,EAEX,MAAMI,EAAeD,EAAkB,CAAC,EACxC,OAAOH,EACF,MAAM;AAAA,CAAI,EACV,IAAIK,GAAQ,CACb,MAAMC,EAAoBD,EAAK,MAAM,MAAM,EAC3C,GAAIC,IAAsB,KACtB,OAAOD,EAEX,KAAM,CAACE,CAAY,EAAID,EACvB,OAAIC,EAAa,QAAUH,EAAa,OAC7BC,EAAK,MAAMD,EAAa,MAAM,EAElCC,CACf,CAAK,EACI,KAAK;AAAA,CAAI,CAClB,CAIA,MAAMG,CAAW,CACb,QACA,MACA,MACA,YAAYC,EAAS,CACjB,KAAK,QAAUA,GAAWhE,CAC7B,CACD,MAAMiE,EAAK,CACP,MAAMf,EAAM,KAAK,MAAM,MAAM,QAAQ,KAAKe,CAAG,EAC7C,GAAIf,GAAOA,EAAI,CAAC,EAAE,OAAS,EACvB,MAAO,CACH,KAAM,QACN,IAAKA,EAAI,CAAC,CAC1B,CAEK,CACD,KAAKe,EAAK,CACN,MAAMf,EAAM,KAAK,MAAM,MAAM,KAAK,KAAKe,CAAG,EAC1C,GAAIf,EAAK,CACL,MAAMK,EAAOL,EAAI,CAAC,EAAE,QAAQ,YAAa,EAAE,EAC3C,MAAO,CACH,KAAM,OACN,IAAKA,EAAI,CAAC,EACV,eAAgB,WAChB,KAAO,KAAK,QAAQ,SAEdK,EADAf,EAAMe,EAAM;AAAA,CAAI,CAEtC,CACS,CACJ,CACD,OAAOU,EAAK,CACR,MAAMf,EAAM,KAAK,MAAM,MAAM,OAAO,KAAKe,CAAG,EAC5C,GAAIf,EAAK,CACL,MAAME,EAAMF,EAAI,CAAC,EACXK,EAAOE,GAAuBL,EAAKF,EAAI,CAAC,GAAK,EAAE,EACrD,MAAO,CACH,KAAM,OACN,IAAAE,EACA,KAAMF,EAAI,CAAC,EAAIA,EAAI,CAAC,EAAE,KAAM,EAAC,QAAQ,KAAK,MAAM,OAAO,eAAgB,IAAI,EAAIA,EAAI,CAAC,EACpF,KAAAK,CAChB,CACS,CACJ,CACD,QAAQU,EAAK,CACT,MAAMf,EAAM,KAAK,MAAM,MAAM,QAAQ,KAAKe,CAAG,EAC7C,GAAIf,EAAK,CACL,IAAIK,EAAOL,EAAI,CAAC,EAAE,KAAI,EAEtB,GAAI,KAAK,KAAKK,CAAI,EAAG,CACjB,MAAMW,EAAU1B,EAAMe,EAAM,GAAG,GAC3B,KAAK,QAAQ,UAGR,CAACW,GAAW,KAAK,KAAKA,CAAO,KAElCX,EAAOW,EAAQ,OAEtB,CACD,MAAO,CACH,KAAM,UACN,IAAKhB,EAAI,CAAC,EACV,MAAOA,EAAI,CAAC,EAAE,OACd,KAAAK,EACA,OAAQ,KAAK,MAAM,OAAOA,CAAI,CAC9C,CACS,CACJ,CACD,GAAGU,EAAK,CACJ,MAAMf,EAAM,KAAK,MAAM,MAAM,GAAG,KAAKe,CAAG,EACxC,GAAIf,EACA,MAAO,CACH,KAAM,KACN,IAAKA,EAAI,CAAC,CAC1B,CAEK,CACD,WAAWe,EAAK,CACZ,MAAMf,EAAM,KAAK,MAAM,MAAM,WAAW,KAAKe,CAAG,EAChD,GAAIf,EAAK,CACL,MAAMK,EAAOf,EAAMU,EAAI,CAAC,EAAE,QAAQ,eAAgB,EAAE,EAAG;AAAA,CAAI,EACrDiB,EAAM,KAAK,MAAM,MAAM,IAC7B,KAAK,MAAM,MAAM,IAAM,GACvB,MAAMC,EAAS,KAAK,MAAM,YAAYb,CAAI,EAC1C,YAAK,MAAM,MAAM,IAAMY,EAChB,CACH,KAAM,aACN,IAAKjB,EAAI,CAAC,EACV,OAAAkB,EACA,KAAAb,CAChB,CACS,CACJ,CACD,KAAKU,EAAK,CACN,IAAIf,EAAM,KAAK,MAAM,MAAM,KAAK,KAAKe,CAAG,EACxC,GAAIf,EAAK,CACL,IAAImB,EAAOnB,EAAI,CAAC,EAAE,KAAI,EACtB,MAAMoB,EAAYD,EAAK,OAAS,EAC1BE,EAAO,CACT,KAAM,OACN,IAAK,GACL,QAASD,EACT,MAAOA,EAAY,CAACD,EAAK,MAAM,EAAG,EAAE,EAAI,GACxC,MAAO,GACP,MAAO,CAAE,CACzB,EACYA,EAAOC,EAAY,aAAaD,EAAK,MAAM,EAAE,CAAC,GAAK,KAAKA,CAAI,GACxD,KAAK,QAAQ,WACbA,EAAOC,EAAYD,EAAO,SAG9B,MAAMG,EAAY,IAAI,OAAO,WAAWH,CAAI,8BAA+B,EAC3E,IAAIjB,EAAM,GACNqB,EAAe,GACfC,EAAoB,GAExB,KAAOT,GAAK,CACR,IAAIU,EAAW,GAIf,GAHI,EAAEzB,EAAMsB,EAAU,KAAKP,CAAG,IAG1B,KAAK,MAAM,MAAM,GAAG,KAAKA,CAAG,EAC5B,MAEJb,EAAMF,EAAI,CAAC,EACXe,EAAMA,EAAI,UAAUb,EAAI,MAAM,EAC9B,IAAIwB,EAAO1B,EAAI,CAAC,EAAE,MAAM;AAAA,EAAM,CAAC,EAAE,CAAC,EAAE,QAAQ,OAAS2B,GAAM,IAAI,OAAO,EAAIA,EAAE,MAAM,CAAC,EAC/EC,EAAWb,EAAI,MAAM;AAAA,EAAM,CAAC,EAAE,CAAC,EAC/Bc,EAAS,EACT,KAAK,QAAQ,UACbA,EAAS,EACTN,EAAeG,EAAK,cAGpBG,EAAS7B,EAAI,CAAC,EAAE,OAAO,MAAM,EAC7B6B,EAASA,EAAS,EAAI,EAAIA,EAC1BN,EAAeG,EAAK,MAAMG,CAAM,EAChCA,GAAU7B,EAAI,CAAC,EAAE,QAErB,IAAI8B,EAAY,GAMhB,GALI,CAACJ,GAAQ,OAAO,KAAKE,CAAQ,IAC7B1B,GAAO0B,EAAW;AAAA,EAClBb,EAAMA,EAAI,UAAUa,EAAS,OAAS,CAAC,EACvCH,EAAW,IAEX,CAACA,EAAU,CACX,MAAMM,EAAkB,IAAI,OAAO,QAAQ,KAAK,IAAI,EAAGF,EAAS,CAAC,CAAC,oDAAqD,EACjHG,EAAU,IAAI,OAAO,QAAQ,KAAK,IAAI,EAAGH,EAAS,CAAC,CAAC,oDAAoD,EACxGI,EAAmB,IAAI,OAAO,QAAQ,KAAK,IAAI,EAAGJ,EAAS,CAAC,CAAC,iBAAiB,EAC9EK,EAAoB,IAAI,OAAO,QAAQ,KAAK,IAAI,EAAGL,EAAS,CAAC,CAAC,IAAI,EAExE,KAAOd,GAAK,CACR,MAAMoB,EAAUpB,EAAI,MAAM;AAAA,EAAM,CAAC,EAAE,CAAC,EAmBpC,GAlBAa,EAAWO,EAEP,KAAK,QAAQ,WACbP,EAAWA,EAAS,QAAQ,0BAA2B,IAAI,GAG3DK,EAAiB,KAAKL,CAAQ,GAI9BM,EAAkB,KAAKN,CAAQ,GAI/BG,EAAgB,KAAKH,CAAQ,GAI7BI,EAAQ,KAAKjB,CAAG,EAChB,MAEJ,GAAIa,EAAS,OAAO,MAAM,GAAKC,GAAU,CAACD,EAAS,OAC/CL,GAAgB;AAAA,EAAOK,EAAS,MAAMC,CAAM,MAE3C,CAeD,GAbIC,GAIAJ,EAAK,OAAO,MAAM,GAAK,GAGvBO,EAAiB,KAAKP,CAAI,GAG1BQ,EAAkB,KAAKR,CAAI,GAG3BM,EAAQ,KAAKN,CAAI,EACjB,MAEJH,GAAgB;AAAA,EAAOK,CAC1B,CACG,CAACE,GAAa,CAACF,EAAS,KAAI,IAC5BE,EAAY,IAEhB5B,GAAOiC,EAAU;AAAA,EACjBpB,EAAMA,EAAI,UAAUoB,EAAQ,OAAS,CAAC,EACtCT,EAAOE,EAAS,MAAMC,CAAM,CAC/B,CACJ,CACIR,EAAK,QAEFG,EACAH,EAAK,MAAQ,GAER,YAAY,KAAKnB,CAAG,IACzBsB,EAAoB,KAG5B,IAAIY,EAAS,KACTC,EAEA,KAAK,QAAQ,MACbD,EAAS,cAAc,KAAKb,CAAY,EACpCa,IACAC,EAAYD,EAAO,CAAC,IAAM,OAC1Bb,EAAeA,EAAa,QAAQ,eAAgB,EAAE,IAG9DF,EAAK,MAAM,KAAK,CACZ,KAAM,YACN,IAAAnB,EACA,KAAM,CAAC,CAACkC,EACR,QAASC,EACT,MAAO,GACP,KAAMd,EACN,OAAQ,CAAE,CAC9B,CAAiB,EACDF,EAAK,KAAOnB,CACf,CAEDmB,EAAK,MAAMA,EAAK,MAAM,OAAS,CAAC,EAAE,IAAMnB,EAAI,UAC3CmB,EAAK,MAAMA,EAAK,MAAM,OAAS,CAAC,EAAG,KAAOE,EAAa,UACxDF,EAAK,IAAMA,EAAK,IAAI,QAAO,EAE3B,QAAShC,EAAI,EAAGA,EAAIgC,EAAK,MAAM,OAAQhC,IAGnC,GAFA,KAAK,MAAM,MAAM,IAAM,GACvBgC,EAAK,MAAMhC,CAAC,EAAE,OAAS,KAAK,MAAM,YAAYgC,EAAK,MAAMhC,CAAC,EAAE,KAAM,CAAE,CAAA,EAChE,CAACgC,EAAK,MAAO,CAEb,MAAMiB,EAAUjB,EAAK,MAAMhC,CAAC,EAAE,OAAO,OAAOsC,GAAKA,EAAE,OAAS,OAAO,EAC7DY,EAAwBD,EAAQ,OAAS,GAAKA,EAAQ,KAAKX,GAAK,SAAS,KAAKA,EAAE,GAAG,CAAC,EAC1FN,EAAK,MAAQkB,CAChB,CAGL,GAAIlB,EAAK,MACL,QAAShC,EAAI,EAAGA,EAAIgC,EAAK,MAAM,OAAQhC,IACnCgC,EAAK,MAAMhC,CAAC,EAAE,MAAQ,GAG9B,OAAOgC,CACV,CACJ,CACD,KAAKN,EAAK,CACN,MAAMf,EAAM,KAAK,MAAM,MAAM,KAAK,KAAKe,CAAG,EAC1C,GAAIf,EAQA,MAPc,CACV,KAAM,OACN,MAAO,GACP,IAAKA,EAAI,CAAC,EACV,IAAKA,EAAI,CAAC,IAAM,OAASA,EAAI,CAAC,IAAM,UAAYA,EAAI,CAAC,IAAM,QAC3D,KAAMA,EAAI,CAAC,CAC3B,CAGK,CACD,IAAIe,EAAK,CACL,MAAMf,EAAM,KAAK,MAAM,MAAM,IAAI,KAAKe,CAAG,EACzC,GAAIf,EAAK,CACL,MAAMwC,EAAMxC,EAAI,CAAC,EAAE,YAAW,EAAG,QAAQ,OAAQ,GAAG,EAC9CvB,EAAOuB,EAAI,CAAC,EAAIA,EAAI,CAAC,EAAE,QAAQ,WAAY,IAAI,EAAE,QAAQ,KAAK,MAAM,OAAO,eAAgB,IAAI,EAAI,GACnGI,EAAQJ,EAAI,CAAC,EAAIA,EAAI,CAAC,EAAE,UAAU,EAAGA,EAAI,CAAC,EAAE,OAAS,CAAC,EAAE,QAAQ,KAAK,MAAM,OAAO,eAAgB,IAAI,EAAIA,EAAI,CAAC,EACrH,MAAO,CACH,KAAM,MACN,IAAAwC,EACA,IAAKxC,EAAI,CAAC,EACV,KAAAvB,EACA,MAAA2B,CAChB,CACS,CACJ,CACD,MAAMW,EAAK,CACP,MAAMf,EAAM,KAAK,MAAM,MAAM,MAAM,KAAKe,CAAG,EAI3C,GAHI,CAACf,GAGD,CAAC,OAAO,KAAKA,EAAI,CAAC,CAAC,EAEnB,OAEJ,MAAMyC,EAAU9D,GAAWqB,EAAI,CAAC,CAAC,EAC3B0C,EAAS1C,EAAI,CAAC,EAAE,QAAQ,aAAc,EAAE,EAAE,MAAM,GAAG,EACnD2C,EAAO3C,EAAI,CAAC,GAAKA,EAAI,CAAC,EAAE,OAASA,EAAI,CAAC,EAAE,QAAQ,YAAa,EAAE,EAAE,MAAM;AAAA,CAAI,EAAI,GAC/E4C,EAAO,CACT,KAAM,QACN,IAAK5C,EAAI,CAAC,EACV,OAAQ,CAAE,EACV,MAAO,CAAE,EACT,KAAM,CAAE,CACpB,EACQ,GAAIyC,EAAQ,SAAWC,EAAO,OAI9B,WAAWG,KAASH,EACZ,YAAY,KAAKG,CAAK,EACtBD,EAAK,MAAM,KAAK,OAAO,EAElB,aAAa,KAAKC,CAAK,EAC5BD,EAAK,MAAM,KAAK,QAAQ,EAEnB,YAAY,KAAKC,CAAK,EAC3BD,EAAK,MAAM,KAAK,MAAM,EAGtBA,EAAK,MAAM,KAAK,IAAI,EAG5B,UAAWE,KAAUL,EACjBG,EAAK,OAAO,KAAK,CACb,KAAME,EACN,OAAQ,KAAK,MAAM,OAAOA,CAAM,CAChD,CAAa,EAEL,UAAWhE,KAAO6D,EACdC,EAAK,KAAK,KAAKjE,GAAWG,EAAK8D,EAAK,OAAO,MAAM,EAAE,IAAIG,IAC5C,CACH,KAAMA,EACN,OAAQ,KAAK,MAAM,OAAOA,CAAI,CAClD,EACa,CAAC,EAEN,OAAOH,EACV,CACD,SAAS7B,EAAK,CACV,MAAMf,EAAM,KAAK,MAAM,MAAM,SAAS,KAAKe,CAAG,EAC9C,GAAIf,EACA,MAAO,CACH,KAAM,UACN,IAAKA,EAAI,CAAC,EACV,MAAOA,EAAI,CAAC,EAAE,OAAO,CAAC,IAAM,IAAM,EAAI,EACtC,KAAMA,EAAI,CAAC,EACX,OAAQ,KAAK,MAAM,OAAOA,EAAI,CAAC,CAAC,CAChD,CAEK,CACD,UAAUe,EAAK,CACX,MAAMf,EAAM,KAAK,MAAM,MAAM,UAAU,KAAKe,CAAG,EAC/C,GAAIf,EAAK,CACL,MAAMK,EAAOL,EAAI,CAAC,EAAE,OAAOA,EAAI,CAAC,EAAE,OAAS,CAAC,IAAM;AAAA,EAC5CA,EAAI,CAAC,EAAE,MAAM,EAAG,EAAE,EAClBA,EAAI,CAAC,EACX,MAAO,CACH,KAAM,YACN,IAAKA,EAAI,CAAC,EACV,KAAAK,EACA,OAAQ,KAAK,MAAM,OAAOA,CAAI,CAC9C,CACS,CACJ,CACD,KAAKU,EAAK,CACN,MAAMf,EAAM,KAAK,MAAM,MAAM,KAAK,KAAKe,CAAG,EAC1C,GAAIf,EACA,MAAO,CACH,KAAM,OACN,IAAKA,EAAI,CAAC,EACV,KAAMA,EAAI,CAAC,EACX,OAAQ,KAAK,MAAM,OAAOA,EAAI,CAAC,CAAC,CAChD,CAEK,CACD,OAAOe,EAAK,CACR,MAAMf,EAAM,KAAK,MAAM,OAAO,OAAO,KAAKe,CAAG,EAC7C,GAAIf,EACA,MAAO,CACH,KAAM,SACN,IAAKA,EAAI,CAAC,EACV,KAAMxC,EAASwC,EAAI,CAAC,CAAC,CACrC,CAEK,CACD,IAAIe,EAAK,CACL,MAAMf,EAAM,KAAK,MAAM,OAAO,IAAI,KAAKe,CAAG,EAC1C,GAAIf,EACA,MAAI,CAAC,KAAK,MAAM,MAAM,QAAU,QAAQ,KAAKA,EAAI,CAAC,CAAC,EAC/C,KAAK,MAAM,MAAM,OAAS,GAErB,KAAK,MAAM,MAAM,QAAU,UAAU,KAAKA,EAAI,CAAC,CAAC,IACrD,KAAK,MAAM,MAAM,OAAS,IAE1B,CAAC,KAAK,MAAM,MAAM,YAAc,iCAAiC,KAAKA,EAAI,CAAC,CAAC,EAC5E,KAAK,MAAM,MAAM,WAAa,GAEzB,KAAK,MAAM,MAAM,YAAc,mCAAmC,KAAKA,EAAI,CAAC,CAAC,IAClF,KAAK,MAAM,MAAM,WAAa,IAE3B,CACH,KAAM,OACN,IAAKA,EAAI,CAAC,EACV,OAAQ,KAAK,MAAM,MAAM,OACzB,WAAY,KAAK,MAAM,MAAM,WAC7B,MAAO,GACP,KAAMA,EAAI,CAAC,CAC3B,CAEK,CACD,KAAKe,EAAK,CACN,MAAMf,EAAM,KAAK,MAAM,OAAO,KAAK,KAAKe,CAAG,EAC3C,GAAIf,EAAK,CACL,MAAMgD,EAAahD,EAAI,CAAC,EAAE,KAAI,EAC9B,GAAI,CAAC,KAAK,QAAQ,UAAY,KAAK,KAAKgD,CAAU,EAAG,CAEjD,GAAI,CAAE,KAAK,KAAKA,CAAU,EACtB,OAGJ,MAAMC,EAAa3D,EAAM0D,EAAW,MAAM,EAAG,EAAE,EAAG,IAAI,EACtD,IAAKA,EAAW,OAASC,EAAW,QAAU,IAAM,EAChD,MAEP,KACI,CAED,MAAMC,EAAiBtD,GAAmBI,EAAI,CAAC,EAAG,IAAI,EACtD,GAAIkD,EAAiB,GAAI,CAErB,MAAMC,GADQnD,EAAI,CAAC,EAAE,QAAQ,GAAG,IAAM,EAAI,EAAI,GACtBA,EAAI,CAAC,EAAE,OAASkD,EACxClD,EAAI,CAAC,EAAIA,EAAI,CAAC,EAAE,UAAU,EAAGkD,CAAc,EAC3ClD,EAAI,CAAC,EAAIA,EAAI,CAAC,EAAE,UAAU,EAAGmD,CAAO,EAAE,OACtCnD,EAAI,CAAC,EAAI,EACZ,CACJ,CACD,IAAIvB,EAAOuB,EAAI,CAAC,EACZI,EAAQ,GACZ,GAAI,KAAK,QAAQ,SAAU,CAEvB,MAAMH,EAAO,gCAAgC,KAAKxB,CAAI,EAClDwB,IACAxB,EAAOwB,EAAK,CAAC,EACbG,EAAQH,EAAK,CAAC,EAErB,MAEGG,EAAQJ,EAAI,CAAC,EAAIA,EAAI,CAAC,EAAE,MAAM,EAAG,EAAE,EAAI,GAE3C,OAAAvB,EAAOA,EAAK,OACR,KAAK,KAAKA,CAAI,IACV,KAAK,QAAQ,UAAY,CAAE,KAAK,KAAKuE,CAAU,EAE/CvE,EAAOA,EAAK,MAAM,CAAC,EAGnBA,EAAOA,EAAK,MAAM,EAAG,EAAE,GAGxBsB,GAAWC,EAAK,CACnB,KAAMvB,GAAOA,EAAK,QAAQ,KAAK,MAAM,OAAO,eAAgB,IAAI,EAChE,MAAO2B,GAAQA,EAAM,QAAQ,KAAK,MAAM,OAAO,eAAgB,IAAI,CACtE,EAAEJ,EAAI,CAAC,EAAG,KAAK,KAAK,CACxB,CACJ,CACD,QAAQe,EAAKqC,EAAO,CAChB,IAAIpD,EACJ,IAAKA,EAAM,KAAK,MAAM,OAAO,QAAQ,KAAKe,CAAG,KACrCf,EAAM,KAAK,MAAM,OAAO,OAAO,KAAKe,CAAG,GAAI,CAC/C,MAAMsC,GAAcrD,EAAI,CAAC,GAAKA,EAAI,CAAC,GAAG,QAAQ,OAAQ,GAAG,EACnDC,EAAOmD,EAAMC,EAAW,YAAa,CAAA,EAC3C,GAAI,CAACpD,EAAM,CACP,MAAMI,EAAOL,EAAI,CAAC,EAAE,OAAO,CAAC,EAC5B,MAAO,CACH,KAAM,OACN,IAAKK,EACL,KAAAA,CACpB,CACa,CACD,OAAON,GAAWC,EAAKC,EAAMD,EAAI,CAAC,EAAG,KAAK,KAAK,CAClD,CACJ,CACD,SAASe,EAAKuC,EAAWC,EAAW,GAAI,CACpC,IAAIxE,EAAQ,KAAK,MAAM,OAAO,eAAe,KAAKgC,CAAG,EAIrD,GAHI,CAAChC,GAGDA,EAAM,CAAC,GAAKwE,EAAS,MAAM,eAAe,EAC1C,OAEJ,GAAI,EADaxE,EAAM,CAAC,GAAKA,EAAM,CAAC,GAAK,KACxB,CAACwE,GAAY,KAAK,MAAM,OAAO,YAAY,KAAKA,CAAQ,EAAG,CAExE,MAAMC,EAAU,CAAC,GAAGzE,EAAM,CAAC,CAAC,EAAE,OAAS,EACvC,IAAI0E,EAAQC,EAASC,EAAaH,EAASI,EAAgB,EAC3D,MAAMC,EAAS9E,EAAM,CAAC,EAAE,CAAC,IAAM,IAAM,KAAK,MAAM,OAAO,kBAAoB,KAAK,MAAM,OAAO,kBAI7F,IAHA8E,EAAO,UAAY,EAEnBP,EAAYA,EAAU,MAAM,GAAKvC,EAAI,OAASyC,CAAO,GAC7CzE,EAAQ8E,EAAO,KAAKP,CAAS,IAAM,MAAM,CAE7C,GADAG,EAAS1E,EAAM,CAAC,GAAKA,EAAM,CAAC,GAAKA,EAAM,CAAC,GAAKA,EAAM,CAAC,GAAKA,EAAM,CAAC,GAAKA,EAAM,CAAC,EACxE,CAAC0E,EACD,SAEJ,GADAC,EAAU,CAAC,GAAGD,CAAM,EAAE,OAClB1E,EAAM,CAAC,GAAKA,EAAM,CAAC,EAAG,CACtB4E,GAAcD,EACd,QACH,UACQ3E,EAAM,CAAC,GAAKA,EAAM,CAAC,IACpByE,EAAU,GAAK,GAAGA,EAAUE,GAAW,GAAI,CAC3CE,GAAiBF,EACjB,QACH,CAGL,GADAC,GAAcD,EACVC,EAAa,EACb,SAEJD,EAAU,KAAK,IAAIA,EAASA,EAAUC,EAAaC,CAAa,EAEhE,MAAME,EAAiB,CAAC,GAAG/E,EAAM,CAAC,CAAC,EAAE,CAAC,EAAE,OAClCmB,EAAMa,EAAI,MAAM,EAAGyC,EAAUzE,EAAM,MAAQ+E,EAAiBJ,CAAO,EAEzE,GAAI,KAAK,IAAIF,EAASE,CAAO,EAAI,EAAG,CAChC,MAAMrD,EAAOH,EAAI,MAAM,EAAG,EAAE,EAC5B,MAAO,CACH,KAAM,KACN,IAAAA,EACA,KAAAG,EACA,OAAQ,KAAK,MAAM,aAAaA,CAAI,CAC5D,CACiB,CAED,MAAMA,EAAOH,EAAI,MAAM,EAAG,EAAE,EAC5B,MAAO,CACH,KAAM,SACN,IAAAA,EACA,KAAAG,EACA,OAAQ,KAAK,MAAM,aAAaA,CAAI,CACxD,CACa,CACJ,CACJ,CACD,SAASU,EAAK,CACV,MAAMf,EAAM,KAAK,MAAM,OAAO,KAAK,KAAKe,CAAG,EAC3C,GAAIf,EAAK,CACL,IAAIK,EAAOL,EAAI,CAAC,EAAE,QAAQ,MAAO,GAAG,EACpC,MAAM+D,EAAmB,OAAO,KAAK1D,CAAI,EACnC2D,EAA0B,KAAK,KAAK3D,CAAI,GAAK,KAAK,KAAKA,CAAI,EACjE,OAAI0D,GAAoBC,IACpB3D,EAAOA,EAAK,UAAU,EAAGA,EAAK,OAAS,CAAC,GAE5CA,EAAO7C,EAAS6C,EAAM,EAAI,EACnB,CACH,KAAM,WACN,IAAKL,EAAI,CAAC,EACV,KAAAK,CAChB,CACS,CACJ,CACD,GAAGU,EAAK,CACJ,MAAMf,EAAM,KAAK,MAAM,OAAO,GAAG,KAAKe,CAAG,EACzC,GAAIf,EACA,MAAO,CACH,KAAM,KACN,IAAKA,EAAI,CAAC,CAC1B,CAEK,CACD,IAAIe,EAAK,CACL,MAAMf,EAAM,KAAK,MAAM,OAAO,IAAI,KAAKe,CAAG,EAC1C,GAAIf,EACA,MAAO,CACH,KAAM,MACN,IAAKA,EAAI,CAAC,EACV,KAAMA,EAAI,CAAC,EACX,OAAQ,KAAK,MAAM,aAAaA,EAAI,CAAC,CAAC,CACtD,CAEK,CACD,SAASe,EAAK,CACV,MAAMf,EAAM,KAAK,MAAM,OAAO,SAAS,KAAKe,CAAG,EAC/C,GAAIf,EAAK,CACL,IAAIK,EAAM5B,EACV,OAAIuB,EAAI,CAAC,IAAM,KACXK,EAAO7C,EAASwC,EAAI,CAAC,CAAC,EACtBvB,EAAO,UAAY4B,IAGnBA,EAAO7C,EAASwC,EAAI,CAAC,CAAC,EACtBvB,EAAO4B,GAEJ,CACH,KAAM,OACN,IAAKL,EAAI,CAAC,EACV,KAAAK,EACA,KAAA5B,EACA,OAAQ,CACJ,CACI,KAAM,OACN,IAAK4B,EACL,KAAAA,CACH,CACJ,CACjB,CACS,CACJ,CACD,IAAIU,EAAK,CACL,IAAIf,EACJ,GAAIA,EAAM,KAAK,MAAM,OAAO,IAAI,KAAKe,CAAG,EAAG,CACvC,IAAIV,EAAM5B,EACV,GAAIuB,EAAI,CAAC,IAAM,IACXK,EAAO7C,EAASwC,EAAI,CAAC,CAAC,EACtBvB,EAAO,UAAY4B,MAElB,CAED,IAAI4D,EACJ,GACIA,EAAcjE,EAAI,CAAC,EACnBA,EAAI,CAAC,EAAI,KAAK,MAAM,OAAO,WAAW,KAAKA,EAAI,CAAC,CAAC,IAAI,CAAC,GAAK,SACtDiE,IAAgBjE,EAAI,CAAC,GAC9BK,EAAO7C,EAASwC,EAAI,CAAC,CAAC,EAClBA,EAAI,CAAC,IAAM,OACXvB,EAAO,UAAYuB,EAAI,CAAC,EAGxBvB,EAAOuB,EAAI,CAAC,CAEnB,CACD,MAAO,CACH,KAAM,OACN,IAAKA,EAAI,CAAC,EACV,KAAAK,EACA,KAAA5B,EACA,OAAQ,CACJ,CACI,KAAM,OACN,IAAK4B,EACL,KAAAA,CACH,CACJ,CACjB,CACS,CACJ,CACD,WAAWU,EAAK,CACZ,MAAMf,EAAM,KAAK,MAAM,OAAO,KAAK,KAAKe,CAAG,EAC3C,GAAIf,EAAK,CACL,IAAIK,EACJ,OAAI,KAAK,MAAM,MAAM,WACjBA,EAAOL,EAAI,CAAC,EAGZK,EAAO7C,EAASwC,EAAI,CAAC,CAAC,EAEnB,CACH,KAAM,OACN,IAAKA,EAAI,CAAC,EACV,KAAAK,CAChB,CACS,CACJ,CACL,CAKA,MAAM6D,GAAU,mBACVC,GAAY,uCACZC,GAAS,8GACTC,EAAK,qEACLC,GAAU,uCACVC,GAAS,wBACTC,GAAWxG,EAAK,kEAAkE,EACnF,QAAQ,QAASuG,EAAM,EACvB,WACCE,GAAa,uFACbC,GAAY,UACZC,GAAc,8BACdC,GAAM5G,EAAK,iGAAiG,EAC7G,QAAQ,QAAS2G,EAAW,EAC5B,QAAQ,QAAS,8DAA8D,EAC/E,WACCtD,GAAOrD,EAAK,sCAAsC,EACnD,QAAQ,QAASuG,EAAM,EACvB,WACCM,EAAO,gWAMPC,GAAW,gCACXrH,GAAOO,EAAK,mdASP,GAAG,EACT,QAAQ,UAAW8G,EAAQ,EAC3B,QAAQ,MAAOD,CAAI,EACnB,QAAQ,YAAa,0EAA0E,EAC/F,WACCE,GAAY/G,EAAKyG,EAAU,EAC5B,QAAQ,KAAMJ,CAAE,EAChB,QAAQ,UAAW,uBAAuB,EAC1C,QAAQ,YAAa,EAAE,EACvB,QAAQ,SAAU,EAAE,EACpB,QAAQ,aAAc,SAAS,EAC/B,QAAQ,SAAU,gDAAgD,EAClE,QAAQ,OAAQ,wBAAwB,EACxC,QAAQ,OAAQ,6DAA6D,EAC7E,QAAQ,MAAOQ,CAAI,EACnB,WACCG,GAAahH,EAAK,yCAAyC,EAC5D,QAAQ,YAAa+G,EAAS,EAC9B,WAICE,GAAc,CAChB,WAAAD,GACA,KAAMb,GACN,IAAAS,GACA,OAAAR,GACA,QAAAE,GACA,GAAAD,EACA,KAAA5G,GACA,SAAA+G,GACA,KAAAnD,GACA,QAAA6C,GACA,UAAAa,GACA,MAAOrG,EACP,KAAMgG,EACV,EAIMQ,GAAWlH,EAAK,6JAEsE,EACvF,QAAQ,KAAMqG,CAAE,EAChB,QAAQ,UAAW,uBAAuB,EAC1C,QAAQ,aAAc,SAAS,EAC/B,QAAQ,OAAQ,YAAY,EAC5B,QAAQ,SAAU,gDAAgD,EAClE,QAAQ,OAAQ,wBAAwB,EACxC,QAAQ,OAAQ,6DAA6D,EAC7E,QAAQ,MAAOQ,CAAI,EACnB,WACCM,GAAW,CACb,GAAGF,GACH,MAAOC,GACP,UAAWlH,EAAKyG,EAAU,EACrB,QAAQ,KAAMJ,CAAE,EAChB,QAAQ,UAAW,uBAAuB,EAC1C,QAAQ,YAAa,EAAE,EACvB,QAAQ,QAASa,EAAQ,EACzB,QAAQ,aAAc,SAAS,EAC/B,QAAQ,SAAU,gDAAgD,EAClE,QAAQ,OAAQ,wBAAwB,EACxC,QAAQ,OAAQ,6DAA6D,EAC7E,QAAQ,MAAOL,CAAI,EACnB,SAAU,CACnB,EAIMO,GAAgB,CAClB,GAAGH,GACH,KAAMjH,EAAK,wIAEiE,EACvE,QAAQ,UAAW8G,EAAQ,EAC3B,QAAQ,OAAQ,mKAGgB,EAChC,SAAU,EACf,IAAK,oEACL,QAAS,yBACT,OAAQpG,EACR,SAAU,mCACV,UAAWV,EAAKyG,EAAU,EACrB,QAAQ,KAAMJ,CAAE,EAChB,QAAQ,UAAW;AAAA,EAAiB,EACpC,QAAQ,WAAYG,EAAQ,EAC5B,QAAQ,SAAU,EAAE,EACpB,QAAQ,aAAc,SAAS,EAC/B,QAAQ,UAAW,EAAE,EACrB,QAAQ,QAAS,EAAE,EACnB,QAAQ,QAAS,EAAE,EACnB,QAAQ,OAAQ,EAAE,EAClB,SAAU,CACnB,EAIMa,GAAS,8CACTC,GAAa,sCACbC,GAAK,wBACLC,GAAa,8EAEbC,EAAe,eACfC,GAAc1H,EAAK,6BAA8B,GAAG,EACrD,QAAQ,eAAgByH,CAAY,EAAE,SAAQ,EAE7CE,GAAY,gDACZC,GAAiB5H,EAAK,oEAAqE,GAAG,EAC/F,QAAQ,SAAUyH,CAAY,EAC9B,WACCI,GAAoB7H,EAAK,wQAOY,IAAI,EAC1C,QAAQ,SAAUyH,CAAY,EAC9B,WAECK,GAAoB9H,EAAK,uNAMY,IAAI,EAC1C,QAAQ,SAAUyH,CAAY,EAC9B,WACCM,GAAiB/H,EAAK,cAAe,IAAI,EAC1C,QAAQ,SAAUyH,CAAY,EAC9B,WACCO,GAAWhI,EAAK,qCAAqC,EACtD,QAAQ,SAAU,8BAA8B,EAChD,QAAQ,QAAS,8IAA8I,EAC/J,WACCiI,GAAiBjI,EAAK8G,EAAQ,EAAE,QAAQ,YAAa,KAAK,EAAE,WAC5DtC,GAAMxE,EAAK,0JAKuB,EACnC,QAAQ,UAAWiI,EAAc,EACjC,QAAQ,YAAa,6EAA6E,EAClG,WACCC,EAAe,sDACfjG,GAAOjC,EAAK,+CAA+C,EAC5D,QAAQ,QAASkI,CAAY,EAC7B,QAAQ,OAAQ,sCAAsC,EACtD,QAAQ,QAAS,6DAA6D,EAC9E,WACCC,GAAUnI,EAAK,yBAAyB,EACzC,QAAQ,QAASkI,CAAY,EAC7B,QAAQ,MAAOvB,EAAW,EAC1B,WACCyB,GAASpI,EAAK,uBAAuB,EACtC,QAAQ,MAAO2G,EAAW,EAC1B,WACC0B,GAAgBrI,EAAK,wBAAyB,GAAG,EAClD,QAAQ,UAAWmI,EAAO,EAC1B,QAAQ,SAAUC,EAAM,EACxB,WAICE,GAAe,CACjB,WAAY5H,EACZ,eAAAqH,GACA,SAAAC,GACA,UAAAL,GACA,GAAAJ,GACA,KAAMD,GACN,IAAK5G,EACL,eAAAkH,GACA,kBAAAC,GACA,kBAAAC,GACJ,OAAIT,GACA,KAAApF,GACA,OAAAmG,GACA,YAAAV,GACA,QAAAS,GACA,cAAAE,GACA,IAAA7D,GACA,KAAMgD,GACN,IAAK9G,CACT,EAIM6H,GAAiB,CACnB,GAAGD,GACH,KAAMtI,EAAK,yBAAyB,EAC/B,QAAQ,QAASkI,CAAY,EAC7B,SAAU,EACf,QAASlI,EAAK,+BAA+B,EACxC,QAAQ,QAASkI,CAAY,EAC7B,SAAU,CACnB,EAIMM,EAAY,CACd,GAAGF,GACH,OAAQtI,EAAKqH,EAAM,EAAE,QAAQ,KAAM,MAAM,EAAE,SAAU,EACrD,IAAKrH,EAAK,mEAAoE,GAAG,EAC5E,QAAQ,QAAS,2EAA2E,EAC5F,SAAU,EACf,WAAY,6EACZ,IAAK,+CACL,KAAM,4NACV,EAIMyI,GAAe,CACjB,GAAGD,EACH,GAAIxI,EAAKuH,EAAE,EAAE,QAAQ,OAAQ,GAAG,EAAE,SAAU,EAC5C,KAAMvH,EAAKwI,EAAU,IAAI,EACpB,QAAQ,OAAQ,eAAe,EAC/B,QAAQ,UAAW,GAAG,EACtB,SAAU,CACnB,EAIME,EAAQ,CACV,OAAQzB,GACR,IAAKE,GACL,SAAUC,EACd,EACMuB,EAAS,CACX,OAAQL,GACR,IAAKE,EACL,OAAQC,GACR,SAAUF,EACd,EAKA,MAAMK,CAAO,CACT,OACA,QACA,MACA,UACA,YACA,YAAY9F,EAAS,CAEjB,KAAK,OAAS,GACd,KAAK,OAAO,MAAQ,OAAO,OAAO,IAAI,EACtC,KAAK,QAAUA,GAAWhE,EAC1B,KAAK,QAAQ,UAAY,KAAK,QAAQ,WAAa,IAAI+D,EACvD,KAAK,UAAY,KAAK,QAAQ,UAC9B,KAAK,UAAU,QAAU,KAAK,QAC9B,KAAK,UAAU,MAAQ,KACvB,KAAK,YAAc,GACnB,KAAK,MAAQ,CACT,OAAQ,GACR,WAAY,GACZ,IAAK,EACjB,EACQ,MAAMgG,EAAQ,CACV,MAAOH,EAAM,OACb,OAAQC,EAAO,MAC3B,EACY,KAAK,QAAQ,UACbE,EAAM,MAAQH,EAAM,SACpBG,EAAM,OAASF,EAAO,UAEjB,KAAK,QAAQ,MAClBE,EAAM,MAAQH,EAAM,IAChB,KAAK,QAAQ,OACbG,EAAM,OAASF,EAAO,OAGtBE,EAAM,OAASF,EAAO,KAG9B,KAAK,UAAU,MAAQE,CAC1B,CAID,WAAW,OAAQ,CACf,MAAO,CACH,MAAAH,EACA,OAAAC,CACZ,CACK,CAID,OAAO,IAAI5F,EAAKD,EAAS,CAErB,OADc,IAAI8F,EAAO9F,CAAO,EACnB,IAAIC,CAAG,CACvB,CAID,OAAO,UAAUA,EAAKD,EAAS,CAE3B,OADc,IAAI8F,EAAO9F,CAAO,EACnB,aAAaC,CAAG,CAChC,CAID,IAAIA,EAAK,CACLA,EAAMA,EACD,QAAQ,WAAY;AAAA,CAAI,EAC7B,KAAK,YAAYA,EAAK,KAAK,MAAM,EACjC,QAAS1B,EAAI,EAAGA,EAAI,KAAK,YAAY,OAAQA,IAAK,CAC9C,MAAMyH,EAAO,KAAK,YAAYzH,CAAC,EAC/B,KAAK,aAAayH,EAAK,IAAKA,EAAK,MAAM,CAC1C,CACD,YAAK,YAAc,GACZ,KAAK,MACf,CACD,YAAY/F,EAAKG,EAAS,GAAI,CACtB,KAAK,QAAQ,SACbH,EAAMA,EAAI,QAAQ,MAAO,MAAM,EAAE,QAAQ,SAAU,EAAE,EAGrDA,EAAMA,EAAI,QAAQ,eAAgB,CAAClD,EAAGkJ,EAASC,IACpCD,EAAU,OAAO,OAAOC,EAAK,MAAM,CAC7C,EAEL,IAAI1G,EACA2G,EACAC,EACAC,EACJ,KAAOpG,GACH,GAAI,OAAK,QAAQ,YACV,KAAK,QAAQ,WAAW,OACxB,KAAK,QAAQ,WAAW,MAAM,KAAMqG,IAC/B9G,EAAQ8G,EAAa,KAAK,CAAE,MAAO,IAAM,EAAErG,EAAKG,CAAM,IACtDH,EAAMA,EAAI,UAAUT,EAAM,IAAI,MAAM,EACpCY,EAAO,KAAKZ,CAAK,EACV,IAEJ,EACV,GAIL,IAAIA,EAAQ,KAAK,UAAU,MAAMS,CAAG,EAAG,CACnCA,EAAMA,EAAI,UAAUT,EAAM,IAAI,MAAM,EAChCA,EAAM,IAAI,SAAW,GAAKY,EAAO,OAAS,EAG1CA,EAAOA,EAAO,OAAS,CAAC,EAAE,KAAO;AAAA,EAGjCA,EAAO,KAAKZ,CAAK,EAErB,QACH,CAED,GAAIA,EAAQ,KAAK,UAAU,KAAKS,CAAG,EAAG,CAClCA,EAAMA,EAAI,UAAUT,EAAM,IAAI,MAAM,EACpC2G,EAAY/F,EAAOA,EAAO,OAAS,CAAC,EAEhC+F,IAAcA,EAAU,OAAS,aAAeA,EAAU,OAAS,SACnEA,EAAU,KAAO;AAAA,EAAO3G,EAAM,IAC9B2G,EAAU,MAAQ;AAAA,EAAO3G,EAAM,KAC/B,KAAK,YAAY,KAAK,YAAY,OAAS,CAAC,EAAE,IAAM2G,EAAU,MAG9D/F,EAAO,KAAKZ,CAAK,EAErB,QACH,CAED,GAAIA,EAAQ,KAAK,UAAU,OAAOS,CAAG,EAAG,CACpCA,EAAMA,EAAI,UAAUT,EAAM,IAAI,MAAM,EACpCY,EAAO,KAAKZ,CAAK,EACjB,QACH,CAED,GAAIA,EAAQ,KAAK,UAAU,QAAQS,CAAG,EAAG,CACrCA,EAAMA,EAAI,UAAUT,EAAM,IAAI,MAAM,EACpCY,EAAO,KAAKZ,CAAK,EACjB,QACH,CAED,GAAIA,EAAQ,KAAK,UAAU,GAAGS,CAAG,EAAG,CAChCA,EAAMA,EAAI,UAAUT,EAAM,IAAI,MAAM,EACpCY,EAAO,KAAKZ,CAAK,EACjB,QACH,CAED,GAAIA,EAAQ,KAAK,UAAU,WAAWS,CAAG,EAAG,CACxCA,EAAMA,EAAI,UAAUT,EAAM,IAAI,MAAM,EACpCY,EAAO,KAAKZ,CAAK,EACjB,QACH,CAED,GAAIA,EAAQ,KAAK,UAAU,KAAKS,CAAG,EAAG,CAClCA,EAAMA,EAAI,UAAUT,EAAM,IAAI,MAAM,EACpCY,EAAO,KAAKZ,CAAK,EACjB,QACH,CAED,GAAIA,EAAQ,KAAK,UAAU,KAAKS,CAAG,EAAG,CAClCA,EAAMA,EAAI,UAAUT,EAAM,IAAI,MAAM,EACpCY,EAAO,KAAKZ,CAAK,EACjB,QACH,CAED,GAAIA,EAAQ,KAAK,UAAU,IAAIS,CAAG,EAAG,CACjCA,EAAMA,EAAI,UAAUT,EAAM,IAAI,MAAM,EACpC2G,EAAY/F,EAAOA,EAAO,OAAS,CAAC,EAChC+F,IAAcA,EAAU,OAAS,aAAeA,EAAU,OAAS,SACnEA,EAAU,KAAO;AAAA,EAAO3G,EAAM,IAC9B2G,EAAU,MAAQ;AAAA,EAAO3G,EAAM,IAC/B,KAAK,YAAY,KAAK,YAAY,OAAS,CAAC,EAAE,IAAM2G,EAAU,MAExD,KAAK,OAAO,MAAM3G,EAAM,GAAG,IACjC,KAAK,OAAO,MAAMA,EAAM,GAAG,EAAI,CAC3B,KAAMA,EAAM,KACZ,MAAOA,EAAM,KACrC,GAEgB,QACH,CAED,GAAIA,EAAQ,KAAK,UAAU,MAAMS,CAAG,EAAG,CACnCA,EAAMA,EAAI,UAAUT,EAAM,IAAI,MAAM,EACpCY,EAAO,KAAKZ,CAAK,EACjB,QACH,CAED,GAAIA,EAAQ,KAAK,UAAU,SAASS,CAAG,EAAG,CACtCA,EAAMA,EAAI,UAAUT,EAAM,IAAI,MAAM,EACpCY,EAAO,KAAKZ,CAAK,EACjB,QACH,CAID,GADA4G,EAASnG,EACL,KAAK,QAAQ,YAAc,KAAK,QAAQ,WAAW,WAAY,CAC/D,IAAIsG,EAAa,IACjB,MAAMC,EAAUvG,EAAI,MAAM,CAAC,EAC3B,IAAIwG,EACJ,KAAK,QAAQ,WAAW,WAAW,QAASC,GAAkB,CAC1DD,EAAYC,EAAc,KAAK,CAAE,MAAO,IAAI,EAAIF,CAAO,EACnD,OAAOC,GAAc,UAAYA,GAAa,IAC9CF,EAAa,KAAK,IAAIA,EAAYE,CAAS,EAEnE,CAAiB,EACGF,EAAa,KAAYA,GAAc,IACvCH,EAASnG,EAAI,UAAU,EAAGsG,EAAa,CAAC,EAE/C,CACD,GAAI,KAAK,MAAM,MAAQ/G,EAAQ,KAAK,UAAU,UAAU4G,CAAM,GAAI,CAC9DD,EAAY/F,EAAOA,EAAO,OAAS,CAAC,EAChCiG,GAAwBF,EAAU,OAAS,aAC3CA,EAAU,KAAO;AAAA,EAAO3G,EAAM,IAC9B2G,EAAU,MAAQ;AAAA,EAAO3G,EAAM,KAC/B,KAAK,YAAY,MACjB,KAAK,YAAY,KAAK,YAAY,OAAS,CAAC,EAAE,IAAM2G,EAAU,MAG9D/F,EAAO,KAAKZ,CAAK,EAErB6G,EAAwBD,EAAO,SAAWnG,EAAI,OAC9CA,EAAMA,EAAI,UAAUT,EAAM,IAAI,MAAM,EACpC,QACH,CAED,GAAIA,EAAQ,KAAK,UAAU,KAAKS,CAAG,EAAG,CAClCA,EAAMA,EAAI,UAAUT,EAAM,IAAI,MAAM,EACpC2G,EAAY/F,EAAOA,EAAO,OAAS,CAAC,EAChC+F,GAAaA,EAAU,OAAS,QAChCA,EAAU,KAAO;AAAA,EAAO3G,EAAM,IAC9B2G,EAAU,MAAQ;AAAA,EAAO3G,EAAM,KAC/B,KAAK,YAAY,MACjB,KAAK,YAAY,KAAK,YAAY,OAAS,CAAC,EAAE,IAAM2G,EAAU,MAG9D/F,EAAO,KAAKZ,CAAK,EAErB,QACH,CACD,GAAIS,EAAK,CACL,MAAM0G,EAAS,0BAA4B1G,EAAI,WAAW,CAAC,EAC3D,GAAI,KAAK,QAAQ,OAAQ,CACrB,QAAQ,MAAM0G,CAAM,EACpB,KACH,KAEG,OAAM,IAAI,MAAMA,CAAM,CAE7B,EAEL,YAAK,MAAM,IAAM,GACVvG,CACV,CACD,OAAOH,EAAKG,EAAS,GAAI,CACrB,YAAK,YAAY,KAAK,CAAE,IAAAH,EAAK,OAAAG,CAAQ,CAAA,EAC9BA,CACV,CAID,aAAaH,EAAKG,EAAS,GAAI,CAC3B,IAAIZ,EAAO2G,EAAWC,EAElB5D,EAAYvC,EACZhC,EACA2I,EAAcnE,EAElB,GAAI,KAAK,OAAO,MAAO,CACnB,MAAMH,EAAQ,OAAO,KAAK,KAAK,OAAO,KAAK,EAC3C,GAAIA,EAAM,OAAS,EACf,MAAQrE,EAAQ,KAAK,UAAU,MAAM,OAAO,cAAc,KAAKuE,CAAS,IAAM,MACtEF,EAAM,SAASrE,EAAM,CAAC,EAAE,MAAMA,EAAM,CAAC,EAAE,YAAY,GAAG,EAAI,EAAG,EAAE,CAAC,IAChEuE,EAAYA,EAAU,MAAM,EAAGvE,EAAM,KAAK,EAAI,IAAM,IAAI,OAAOA,EAAM,CAAC,EAAE,OAAS,CAAC,EAAI,IAAMuE,EAAU,MAAM,KAAK,UAAU,MAAM,OAAO,cAAc,SAAS,EAI9K,CAED,MAAQvE,EAAQ,KAAK,UAAU,MAAM,OAAO,UAAU,KAAKuE,CAAS,IAAM,MACtEA,EAAYA,EAAU,MAAM,EAAGvE,EAAM,KAAK,EAAI,IAAM,IAAI,OAAOA,EAAM,CAAC,EAAE,OAAS,CAAC,EAAI,IAAMuE,EAAU,MAAM,KAAK,UAAU,MAAM,OAAO,UAAU,SAAS,EAG/J,MAAQvE,EAAQ,KAAK,UAAU,MAAM,OAAO,eAAe,KAAKuE,CAAS,IAAM,MAC3EA,EAAYA,EAAU,MAAM,EAAGvE,EAAM,KAAK,EAAI,KAAOuE,EAAU,MAAM,KAAK,UAAU,MAAM,OAAO,eAAe,SAAS,EAE7H,KAAOvC,GAMH,GALK2G,IACDnE,EAAW,IAEfmE,EAAe,GAEX,OAAK,QAAQ,YACV,KAAK,QAAQ,WAAW,QACxB,KAAK,QAAQ,WAAW,OAAO,KAAMN,IAChC9G,EAAQ8G,EAAa,KAAK,CAAE,MAAO,IAAM,EAAErG,EAAKG,CAAM,IACtDH,EAAMA,EAAI,UAAUT,EAAM,IAAI,MAAM,EACpCY,EAAO,KAAKZ,CAAK,EACV,IAEJ,EACV,GAIL,IAAIA,EAAQ,KAAK,UAAU,OAAOS,CAAG,EAAG,CACpCA,EAAMA,EAAI,UAAUT,EAAM,IAAI,MAAM,EACpCY,EAAO,KAAKZ,CAAK,EACjB,QACH,CAED,GAAIA,EAAQ,KAAK,UAAU,IAAIS,CAAG,EAAG,CACjCA,EAAMA,EAAI,UAAUT,EAAM,IAAI,MAAM,EACpC2G,EAAY/F,EAAOA,EAAO,OAAS,CAAC,EAChC+F,GAAa3G,EAAM,OAAS,QAAU2G,EAAU,OAAS,QACzDA,EAAU,KAAO3G,EAAM,IACvB2G,EAAU,MAAQ3G,EAAM,MAGxBY,EAAO,KAAKZ,CAAK,EAErB,QACH,CAED,GAAIA,EAAQ,KAAK,UAAU,KAAKS,CAAG,EAAG,CAClCA,EAAMA,EAAI,UAAUT,EAAM,IAAI,MAAM,EACpCY,EAAO,KAAKZ,CAAK,EACjB,QACH,CAED,GAAIA,EAAQ,KAAK,UAAU,QAAQS,EAAK,KAAK,OAAO,KAAK,EAAG,CACxDA,EAAMA,EAAI,UAAUT,EAAM,IAAI,MAAM,EACpC2G,EAAY/F,EAAOA,EAAO,OAAS,CAAC,EAChC+F,GAAa3G,EAAM,OAAS,QAAU2G,EAAU,OAAS,QACzDA,EAAU,KAAO3G,EAAM,IACvB2G,EAAU,MAAQ3G,EAAM,MAGxBY,EAAO,KAAKZ,CAAK,EAErB,QACH,CAED,GAAIA,EAAQ,KAAK,UAAU,SAASS,EAAKuC,EAAWC,CAAQ,EAAG,CAC3DxC,EAAMA,EAAI,UAAUT,EAAM,IAAI,MAAM,EACpCY,EAAO,KAAKZ,CAAK,EACjB,QACH,CAED,GAAIA,EAAQ,KAAK,UAAU,SAASS,CAAG,EAAG,CACtCA,EAAMA,EAAI,UAAUT,EAAM,IAAI,MAAM,EACpCY,EAAO,KAAKZ,CAAK,EACjB,QACH,CAED,GAAIA,EAAQ,KAAK,UAAU,GAAGS,CAAG,EAAG,CAChCA,EAAMA,EAAI,UAAUT,EAAM,IAAI,MAAM,EACpCY,EAAO,KAAKZ,CAAK,EACjB,QACH,CAED,GAAIA,EAAQ,KAAK,UAAU,IAAIS,CAAG,EAAG,CACjCA,EAAMA,EAAI,UAAUT,EAAM,IAAI,MAAM,EACpCY,EAAO,KAAKZ,CAAK,EACjB,QACH,CAED,GAAIA,EAAQ,KAAK,UAAU,SAASS,CAAG,EAAG,CACtCA,EAAMA,EAAI,UAAUT,EAAM,IAAI,MAAM,EACpCY,EAAO,KAAKZ,CAAK,EACjB,QACH,CAED,GAAI,CAAC,KAAK,MAAM,SAAWA,EAAQ,KAAK,UAAU,IAAIS,CAAG,GAAI,CACzDA,EAAMA,EAAI,UAAUT,EAAM,IAAI,MAAM,EACpCY,EAAO,KAAKZ,CAAK,EACjB,QACH,CAID,GADA4G,EAASnG,EACL,KAAK,QAAQ,YAAc,KAAK,QAAQ,WAAW,YAAa,CAChE,IAAIsG,EAAa,IACjB,MAAMC,EAAUvG,EAAI,MAAM,CAAC,EAC3B,IAAIwG,EACJ,KAAK,QAAQ,WAAW,YAAY,QAASC,GAAkB,CAC3DD,EAAYC,EAAc,KAAK,CAAE,MAAO,IAAI,EAAIF,CAAO,EACnD,OAAOC,GAAc,UAAYA,GAAa,IAC9CF,EAAa,KAAK,IAAIA,EAAYE,CAAS,EAEnE,CAAiB,EACGF,EAAa,KAAYA,GAAc,IACvCH,EAASnG,EAAI,UAAU,EAAGsG,EAAa,CAAC,EAE/C,CACD,GAAI/G,EAAQ,KAAK,UAAU,WAAW4G,CAAM,EAAG,CAC3CnG,EAAMA,EAAI,UAAUT,EAAM,IAAI,MAAM,EAChCA,EAAM,IAAI,MAAM,EAAE,IAAM,MACxBiD,EAAWjD,EAAM,IAAI,MAAM,EAAE,GAEjCoH,EAAe,GACfT,EAAY/F,EAAOA,EAAO,OAAS,CAAC,EAChC+F,GAAaA,EAAU,OAAS,QAChCA,EAAU,KAAO3G,EAAM,IACvB2G,EAAU,MAAQ3G,EAAM,MAGxBY,EAAO,KAAKZ,CAAK,EAErB,QACH,CACD,GAAIS,EAAK,CACL,MAAM0G,EAAS,0BAA4B1G,EAAI,WAAW,CAAC,EAC3D,GAAI,KAAK,QAAQ,OAAQ,CACrB,QAAQ,MAAM0G,CAAM,EACpB,KACH,KAEG,OAAM,IAAI,MAAMA,CAAM,CAE7B,EAEL,OAAOvG,CACV,CACL,CAKA,MAAMyG,CAAU,CACZ,QACA,YAAY7G,EAAS,CACjB,KAAK,QAAUA,GAAWhE,CAC7B,CACD,KAAK8K,EAAMC,EAAY3I,EAAS,CAC5B,MAAM4I,GAAQD,GAAc,IAAI,MAAM,MAAM,IAAI,CAAC,EAEjD,OADAD,EAAOA,EAAK,QAAQ,MAAO,EAAE,EAAI;AAAA,EAC5BE,EAKE,8BACDtK,EAASsK,CAAI,EACb,MACC5I,EAAU0I,EAAOpK,EAASoK,EAAM,EAAI,GACrC;AAAA,EARK,eACA1I,EAAU0I,EAAOpK,EAASoK,EAAM,EAAI,GACrC;AAAA,CAOb,CACD,WAAWG,EAAO,CACd,MAAO;AAAA,EAAiBA,CAAK;AAAA,CAChC,CACD,KAAKtK,EAAMiJ,EAAO,CACd,OAAOjJ,CACV,CACD,QAAQ4C,EAAMP,EAAOI,EAAK,CAEtB,MAAO,KAAKJ,CAAK,IAAIO,CAAI,MAAMP,CAAK;AAAA,CACvC,CACD,IAAK,CACD,MAAO;AAAA,CACV,CACD,KAAKkI,EAAMC,EAASC,EAAO,CACvB,MAAMC,EAAOF,EAAU,KAAO,KACxBG,EAAYH,GAAWC,IAAU,EAAM,WAAaA,EAAQ,IAAO,GACzE,MAAO,IAAMC,EAAOC,EAAW;AAAA,EAAQJ,EAAO,KAAOG,EAAO;AAAA,CAC/D,CACD,SAAS9H,EAAMgI,EAAMC,EAAS,CAC1B,MAAO,OAAOjI,CAAI;AAAA,CACrB,CACD,SAASiI,EAAS,CACd,MAAO,WACAA,EAAU,cAAgB,IAC3B,8BACT,CACD,UAAUjI,EAAM,CACZ,MAAO,MAAMA,CAAI;AAAA,CACpB,CACD,MAAMyC,EAAQkF,EAAM,CAChB,OAAIA,IACAA,EAAO,UAAUA,CAAI,YAClB;AAAA;AAAA,EAEDlF,EACA;AAAA,EACAkF,EACA;AAAA,CACT,CACD,SAASO,EAAS,CACd,MAAO;AAAA,EAASA,CAAO;AAAA,CAC1B,CACD,UAAUA,EAASC,EAAO,CACtB,MAAML,EAAOK,EAAM,OAAS,KAAO,KAInC,OAHYA,EAAM,MACZ,IAAIL,CAAI,WAAWK,EAAM,KAAK,KAC9B,IAAIL,CAAI,KACDI,EAAU,KAAKJ,CAAI;AAAA,CACnC,CAID,OAAO9H,EAAM,CACT,MAAO,WAAWA,CAAI,WACzB,CACD,GAAGA,EAAM,CACL,MAAO,OAAOA,CAAI,OACrB,CACD,SAASA,EAAM,CACX,MAAO,SAASA,CAAI,SACvB,CACD,IAAK,CACD,MAAO,MACV,CACD,IAAIA,EAAM,CACN,MAAO,QAAQA,CAAI,QACtB,CACD,KAAK5B,EAAM2B,EAAOC,EAAM,CACpB,MAAMoI,EAAYjK,GAASC,CAAI,EAC/B,GAAIgK,IAAc,KACd,OAAOpI,EAEX5B,EAAOgK,EACP,IAAIC,EAAM,YAAcjK,EAAO,IAC/B,OAAI2B,IACAsI,GAAO,WAAatI,EAAQ,KAEhCsI,GAAO,IAAMrI,EAAO,OACbqI,CACV,CACD,MAAMjK,EAAM2B,EAAOC,EAAM,CACrB,MAAMoI,EAAYjK,GAASC,CAAI,EAC/B,GAAIgK,IAAc,KACd,OAAOpI,EAEX5B,EAAOgK,EACP,IAAIC,EAAM,aAAajK,CAAI,UAAU4B,CAAI,IACzC,OAAID,IACAsI,GAAO,WAAWtI,CAAK,KAE3BsI,GAAO,IACAA,CACV,CACD,KAAKrI,EAAM,CACP,OAAOA,CACV,CACL,CAMA,MAAMsI,EAAc,CAEhB,OAAOtI,EAAM,CACT,OAAOA,CACV,CACD,GAAGA,EAAM,CACL,OAAOA,CACV,CACD,SAASA,EAAM,CACX,OAAOA,CACV,CACD,IAAIA,EAAM,CACN,OAAOA,CACV,CACD,KAAKA,EAAM,CACP,OAAOA,CACV,CACD,KAAKA,EAAM,CACP,OAAOA,CACV,CACD,KAAK5B,EAAM2B,EAAOC,EAAM,CACpB,MAAO,GAAKA,CACf,CACD,MAAM5B,EAAM2B,EAAOC,EAAM,CACrB,MAAO,GAAKA,CACf,CACD,IAAK,CACD,MAAO,EACV,CACL,CAKA,MAAMuI,CAAQ,CACV,QACA,SACA,aACA,YAAY9H,EAAS,CACjB,KAAK,QAAUA,GAAWhE,EAC1B,KAAK,QAAQ,SAAW,KAAK,QAAQ,UAAY,IAAI6K,EACrD,KAAK,SAAW,KAAK,QAAQ,SAC7B,KAAK,SAAS,QAAU,KAAK,QAC7B,KAAK,aAAe,IAAIgB,EAC3B,CAID,OAAO,MAAMzH,EAAQJ,EAAS,CAE1B,OADe,IAAI8H,EAAQ9H,CAAO,EACpB,MAAMI,CAAM,CAC7B,CAID,OAAO,YAAYA,EAAQJ,EAAS,CAEhC,OADe,IAAI8H,EAAQ9H,CAAO,EACpB,YAAYI,CAAM,CACnC,CAID,MAAMA,EAAQD,EAAM,GAAM,CACtB,IAAIyH,EAAM,GACV,QAASrJ,EAAI,EAAGA,EAAI6B,EAAO,OAAQ7B,IAAK,CACpC,MAAMiB,EAAQY,EAAO7B,CAAC,EAEtB,GAAI,KAAK,QAAQ,YAAc,KAAK,QAAQ,WAAW,WAAa,KAAK,QAAQ,WAAW,UAAUiB,EAAM,IAAI,EAAG,CAC/G,MAAMuI,EAAevI,EACfwI,EAAM,KAAK,QAAQ,WAAW,UAAUD,EAAa,IAAI,EAAE,KAAK,CAAE,OAAQ,IAAM,EAAEA,CAAY,EACpG,GAAIC,IAAQ,IAAS,CAAC,CAAC,QAAS,KAAM,UAAW,OAAQ,QAAS,aAAc,OAAQ,OAAQ,YAAa,MAAM,EAAE,SAASD,EAAa,IAAI,EAAG,CAC9IH,GAAOI,GAAO,GACd,QACH,CACJ,CACD,OAAQxI,EAAM,KAAI,CACd,IAAK,QACD,SAEJ,IAAK,KAAM,CACPoI,GAAO,KAAK,SAAS,KACrB,QACH,CACD,IAAK,UAAW,CACZ,MAAMK,EAAezI,EACrBoI,GAAO,KAAK,SAAS,QAAQ,KAAK,YAAYK,EAAa,MAAM,EAAGA,EAAa,MAAOnL,GAAS,KAAK,YAAYmL,EAAa,OAAQ,KAAK,YAAY,CAAC,CAAC,EAC1J,QACH,CACD,IAAK,OAAQ,CACT,MAAMC,EAAY1I,EAClBoI,GAAO,KAAK,SAAS,KAAKM,EAAU,KAAMA,EAAU,KAAM,CAAC,CAACA,EAAU,OAAO,EAC7E,QACH,CACD,IAAK,QAAS,CACV,MAAMC,EAAa3I,EACnB,IAAIwC,EAAS,GAETC,EAAO,GACX,QAASmG,EAAI,EAAGA,EAAID,EAAW,OAAO,OAAQC,IAC1CnG,GAAQ,KAAK,SAAS,UAAU,KAAK,YAAYkG,EAAW,OAAOC,CAAC,EAAE,MAAM,EAAG,CAAE,OAAQ,GAAM,MAAOD,EAAW,MAAMC,CAAC,CAAC,CAAE,EAE/HpG,GAAU,KAAK,SAAS,SAASC,CAAI,EACrC,IAAIiF,EAAO,GACX,QAASkB,EAAI,EAAGA,EAAID,EAAW,KAAK,OAAQC,IAAK,CAC7C,MAAMpK,EAAMmK,EAAW,KAAKC,CAAC,EAC7BnG,EAAO,GACP,QAASoG,EAAI,EAAGA,EAAIrK,EAAI,OAAQqK,IAC5BpG,GAAQ,KAAK,SAAS,UAAU,KAAK,YAAYjE,EAAIqK,CAAC,EAAE,MAAM,EAAG,CAAE,OAAQ,GAAO,MAAOF,EAAW,MAAME,CAAC,CAAC,CAAE,EAElHnB,GAAQ,KAAK,SAAS,SAASjF,CAAI,CACtC,CACD2F,GAAO,KAAK,SAAS,MAAM5F,EAAQkF,CAAI,EACvC,QACH,CACD,IAAK,aAAc,CACf,MAAMoB,EAAkB9I,EAClB0H,EAAO,KAAK,MAAMoB,EAAgB,MAAM,EAC9CV,GAAO,KAAK,SAAS,WAAWV,CAAI,EACpC,QACH,CACD,IAAK,OAAQ,CACT,MAAMqB,EAAY/I,EACZ2H,EAAUoB,EAAU,QACpBnB,EAAQmB,EAAU,MAClBC,EAAQD,EAAU,MACxB,IAAIrB,EAAO,GACX,QAASkB,EAAI,EAAGA,EAAIG,EAAU,MAAM,OAAQH,IAAK,CAC7C,MAAMtG,EAAOyG,EAAU,MAAMH,CAAC,EACxBZ,EAAU1F,EAAK,QACfyF,EAAOzF,EAAK,KAClB,IAAI2G,EAAW,GACf,GAAI3G,EAAK,KAAM,CACX,MAAM4G,EAAW,KAAK,SAAS,SAAS,CAAC,CAAClB,CAAO,EAC7CgB,EACI1G,EAAK,OAAO,OAAS,GAAKA,EAAK,OAAO,CAAC,EAAE,OAAS,aAClDA,EAAK,OAAO,CAAC,EAAE,KAAO4G,EAAW,IAAM5G,EAAK,OAAO,CAAC,EAAE,KAClDA,EAAK,OAAO,CAAC,EAAE,QAAUA,EAAK,OAAO,CAAC,EAAE,OAAO,OAAS,GAAKA,EAAK,OAAO,CAAC,EAAE,OAAO,CAAC,EAAE,OAAS,SAC/FA,EAAK,OAAO,CAAC,EAAE,OAAO,CAAC,EAAE,KAAO4G,EAAW,IAAM5G,EAAK,OAAO,CAAC,EAAE,OAAO,CAAC,EAAE,OAI9EA,EAAK,OAAO,QAAQ,CAChB,KAAM,OACN,KAAM4G,EAAW,GACzD,CAAqC,EAILD,GAAYC,EAAW,GAE9B,CACDD,GAAY,KAAK,MAAM3G,EAAK,OAAQ0G,CAAK,EACzCtB,GAAQ,KAAK,SAAS,SAASuB,EAAUlB,EAAM,CAAC,CAACC,CAAO,CAC3D,CACDI,GAAO,KAAK,SAAS,KAAKV,EAAMC,EAASC,CAAK,EAC9C,QACH,CACD,IAAK,OAAQ,CACT,MAAMuB,EAAYnJ,EAClBoI,GAAO,KAAK,SAAS,KAAKe,EAAU,KAAMA,EAAU,KAAK,EACzD,QACH,CACD,IAAK,YAAa,CACd,MAAMC,EAAiBpJ,EACvBoI,GAAO,KAAK,SAAS,UAAU,KAAK,YAAYgB,EAAe,MAAM,CAAC,EACtE,QACH,CACD,IAAK,OAAQ,CACT,IAAIC,EAAYrJ,EACZ0H,EAAO2B,EAAU,OAAS,KAAK,YAAYA,EAAU,MAAM,EAAIA,EAAU,KAC7E,KAAOtK,EAAI,EAAI6B,EAAO,QAAUA,EAAO7B,EAAI,CAAC,EAAE,OAAS,QACnDsK,EAAYzI,EAAO,EAAE7B,CAAC,EACtB2I,GAAQ;AAAA,GAAQ2B,EAAU,OAAS,KAAK,YAAYA,EAAU,MAAM,EAAIA,EAAU,MAEtFjB,GAAOzH,EAAM,KAAK,SAAS,UAAU+G,CAAI,EAAIA,EAC7C,QACH,CACD,QAAS,CACL,MAAMP,EAAS,eAAiBnH,EAAM,KAAO,wBAC7C,GAAI,KAAK,QAAQ,OACb,eAAQ,MAAMmH,CAAM,EACb,GAGP,MAAM,IAAI,MAAMA,CAAM,CAE7B,CACJ,CACJ,CACD,OAAOiB,CACV,CAID,YAAYxH,EAAQ0I,EAAU,CAC1BA,EAAWA,GAAY,KAAK,SAC5B,IAAIlB,EAAM,GACV,QAASrJ,EAAI,EAAGA,EAAI6B,EAAO,OAAQ7B,IAAK,CACpC,MAAMiB,EAAQY,EAAO7B,CAAC,EAEtB,GAAI,KAAK,QAAQ,YAAc,KAAK,QAAQ,WAAW,WAAa,KAAK,QAAQ,WAAW,UAAUiB,EAAM,IAAI,EAAG,CAC/G,MAAMwI,EAAM,KAAK,QAAQ,WAAW,UAAUxI,EAAM,IAAI,EAAE,KAAK,CAAE,OAAQ,IAAM,EAAEA,CAAK,EACtF,GAAIwI,IAAQ,IAAS,CAAC,CAAC,SAAU,OAAQ,OAAQ,QAAS,SAAU,KAAM,WAAY,KAAM,MAAO,MAAM,EAAE,SAASxI,EAAM,IAAI,EAAG,CAC7HoI,GAAOI,GAAO,GACd,QACH,CACJ,CACD,OAAQxI,EAAM,KAAI,CACd,IAAK,SAAU,CACX,MAAMuJ,EAAcvJ,EACpBoI,GAAOkB,EAAS,KAAKC,EAAY,IAAI,EACrC,KACH,CACD,IAAK,OAAQ,CACT,MAAMC,EAAWxJ,EACjBoI,GAAOkB,EAAS,KAAKE,EAAS,IAAI,EAClC,KACH,CACD,IAAK,OAAQ,CACT,MAAMC,EAAYzJ,EAClBoI,GAAOkB,EAAS,KAAKG,EAAU,KAAMA,EAAU,MAAO,KAAK,YAAYA,EAAU,OAAQH,CAAQ,CAAC,EAClG,KACH,CACD,IAAK,QAAS,CACV,MAAMI,EAAa1J,EACnBoI,GAAOkB,EAAS,MAAMI,EAAW,KAAMA,EAAW,MAAOA,EAAW,IAAI,EACxE,KACH,CACD,IAAK,SAAU,CACX,MAAMC,EAAc3J,EACpBoI,GAAOkB,EAAS,OAAO,KAAK,YAAYK,EAAY,OAAQL,CAAQ,CAAC,EACrE,KACH,CACD,IAAK,KAAM,CACP,MAAMM,EAAU5J,EAChBoI,GAAOkB,EAAS,GAAG,KAAK,YAAYM,EAAQ,OAAQN,CAAQ,CAAC,EAC7D,KACH,CACD,IAAK,WAAY,CACb,MAAMO,EAAgB7J,EACtBoI,GAAOkB,EAAS,SAASO,EAAc,IAAI,EAC3C,KACH,CACD,IAAK,KAAM,CACPzB,GAAOkB,EAAS,KAChB,KACH,CACD,IAAK,MAAO,CACR,MAAMQ,EAAW9J,EACjBoI,GAAOkB,EAAS,IAAI,KAAK,YAAYQ,EAAS,OAAQR,CAAQ,CAAC,EAC/D,KACH,CACD,IAAK,OAAQ,CACT,MAAMD,EAAYrJ,EAClBoI,GAAOkB,EAAS,KAAKD,EAAU,IAAI,EACnC,KACH,CACD,QAAS,CACL,MAAMlC,EAAS,eAAiBnH,EAAM,KAAO,wBAC7C,GAAI,KAAK,QAAQ,OACb,eAAQ,MAAMmH,CAAM,EACb,GAGP,MAAM,IAAI,MAAMA,CAAM,CAE7B,CACJ,CACJ,CACD,OAAOiB,CACV,CACL,CAEA,MAAM2B,CAAO,CACT,QACA,YAAYvJ,EAAS,CACjB,KAAK,QAAUA,GAAWhE,CAC7B,CACD,OAAO,iBAAmB,IAAI,IAAI,CAC9B,aACA,cACA,kBACR,CAAK,EAID,WAAWwN,EAAU,CACjB,OAAOA,CACV,CAID,YAAY7M,EAAM,CACd,OAAOA,CACV,CAID,iBAAiByD,EAAQ,CACrB,OAAOA,CACV,CACL,CAEA,MAAMqJ,EAAO,CACT,SAAW1N,EAAY,EACvB,QAAU,KAAK,WACf,MAAQ,KAAK2N,GAAe5D,EAAO,IAAKgC,EAAQ,KAAK,EACrD,YAAc,KAAK4B,GAAe5D,EAAO,UAAWgC,EAAQ,WAAW,EACvE,OAASA,EACT,SAAWjB,EACX,aAAegB,GACf,MAAQ/B,EACR,UAAY/F,EACZ,MAAQwJ,EACR,eAAeI,EAAM,CACjB,KAAK,IAAI,GAAGA,CAAI,CACnB,CAID,WAAWvJ,EAAQwJ,EAAU,CACzB,IAAIC,EAAS,CAAA,EACb,UAAWrK,KAASY,EAEhB,OADAyJ,EAASA,EAAO,OAAOD,EAAS,KAAK,KAAMpK,CAAK,CAAC,EACzCA,EAAM,KAAI,CACd,IAAK,QAAS,CACV,MAAM2I,EAAa3I,EACnB,UAAWyC,KAAQkG,EAAW,OAC1B0B,EAASA,EAAO,OAAO,KAAK,WAAW5H,EAAK,OAAQ2H,CAAQ,CAAC,EAEjE,UAAW5L,KAAOmK,EAAW,KACzB,UAAWlG,KAAQjE,EACf6L,EAASA,EAAO,OAAO,KAAK,WAAW5H,EAAK,OAAQ2H,CAAQ,CAAC,EAGrE,KACH,CACD,IAAK,OAAQ,CACT,MAAMrB,EAAY/I,EAClBqK,EAASA,EAAO,OAAO,KAAK,WAAWtB,EAAU,MAAOqB,CAAQ,CAAC,EACjE,KACH,CACD,QAAS,CACL,MAAM7B,EAAevI,EACjB,KAAK,SAAS,YAAY,cAAcuI,EAAa,IAAI,EACzD,KAAK,SAAS,WAAW,YAAYA,EAAa,IAAI,EAAE,QAAS+B,GAAgB,CAC7E,MAAM1J,EAAS2H,EAAa+B,CAAW,EAAE,KAAK,GAAQ,EACtDD,EAASA,EAAO,OAAO,KAAK,WAAWzJ,EAAQwJ,CAAQ,CAAC,CACpF,CAAyB,EAEI7B,EAAa,SAClB8B,EAASA,EAAO,OAAO,KAAK,WAAW9B,EAAa,OAAQ6B,CAAQ,CAAC,EAE5E,CACJ,CAEL,OAAOC,CACV,CACD,OAAOF,EAAM,CACT,MAAMI,EAAa,KAAK,SAAS,YAAc,CAAE,UAAW,CAAE,EAAE,YAAa,CAAA,GAC7E,OAAAJ,EAAK,QAASK,GAAS,CAEnB,MAAMC,EAAO,CAAE,GAAGD,GA8DlB,GA5DAC,EAAK,MAAQ,KAAK,SAAS,OAASA,EAAK,OAAS,GAE9CD,EAAK,aACLA,EAAK,WAAW,QAASE,GAAQ,CAC7B,GAAI,CAACA,EAAI,KACL,MAAM,IAAI,MAAM,yBAAyB,EAE7C,GAAI,aAAcA,EAAK,CACnB,MAAMC,EAAeJ,EAAW,UAAUG,EAAI,IAAI,EAC9CC,EAEAJ,EAAW,UAAUG,EAAI,IAAI,EAAI,YAAaP,EAAM,CAChD,IAAI3B,EAAMkC,EAAI,SAAS,MAAM,KAAMP,CAAI,EACvC,OAAI3B,IAAQ,KACRA,EAAMmC,EAAa,MAAM,KAAMR,CAAI,GAEhC3B,CACvC,EAG4B+B,EAAW,UAAUG,EAAI,IAAI,EAAIA,EAAI,QAE5C,CACD,GAAI,cAAeA,EAAK,CACpB,GAAI,CAACA,EAAI,OAAUA,EAAI,QAAU,SAAWA,EAAI,QAAU,SACtD,MAAM,IAAI,MAAM,6CAA6C,EAEjE,MAAME,EAAWL,EAAWG,EAAI,KAAK,EACjCE,EACAA,EAAS,QAAQF,EAAI,SAAS,EAG9BH,EAAWG,EAAI,KAAK,EAAI,CAACA,EAAI,SAAS,EAEtCA,EAAI,QACAA,EAAI,QAAU,QACVH,EAAW,WACXA,EAAW,WAAW,KAAKG,EAAI,KAAK,EAGpCH,EAAW,WAAa,CAACG,EAAI,KAAK,EAGjCA,EAAI,QAAU,WACfH,EAAW,YACXA,EAAW,YAAY,KAAKG,EAAI,KAAK,EAGrCH,EAAW,YAAc,CAACG,EAAI,KAAK,GAIlD,CACG,gBAAiBA,GAAOA,EAAI,cAC5BH,EAAW,YAAYG,EAAI,IAAI,EAAIA,EAAI,YAE/D,CAAiB,EACDD,EAAK,WAAaF,GAGlBC,EAAK,SAAU,CACf,MAAMlB,EAAW,KAAK,SAAS,UAAY,IAAIjC,EAAU,KAAK,QAAQ,EACtE,UAAWwD,KAAQL,EAAK,SAAU,CAC9B,GAAI,EAAEK,KAAQvB,GACV,MAAM,IAAI,MAAM,aAAauB,CAAI,kBAAkB,EAEvD,GAAIA,IAAS,UAET,SAEJ,MAAMC,EAAeD,EACfE,EAAeP,EAAK,SAASM,CAAY,EACzCH,EAAerB,EAASwB,CAAY,EAE1CxB,EAASwB,CAAY,EAAI,IAAIX,IAAS,CAClC,IAAI3B,EAAMuC,EAAa,MAAMzB,EAAUa,CAAI,EAC3C,OAAI3B,IAAQ,KACRA,EAAMmC,EAAa,MAAMrB,EAAUa,CAAI,GAEpC3B,GAAO,EACtC,CACiB,CACDiC,EAAK,SAAWnB,CACnB,CACD,GAAIkB,EAAK,UAAW,CAChB,MAAMQ,EAAY,KAAK,SAAS,WAAa,IAAIzK,EAAW,KAAK,QAAQ,EACzE,UAAWsK,KAAQL,EAAK,UAAW,CAC/B,GAAI,EAAEK,KAAQG,GACV,MAAM,IAAI,MAAM,cAAcH,CAAI,kBAAkB,EAExD,GAAI,CAAC,UAAW,QAAS,OAAO,EAAE,SAASA,CAAI,EAE3C,SAEJ,MAAMI,EAAgBJ,EAChBK,EAAgBV,EAAK,UAAUS,CAAa,EAC5CE,EAAgBH,EAAUC,CAAa,EAG7CD,EAAUC,CAAa,EAAI,IAAId,IAAS,CACpC,IAAI3B,EAAM0C,EAAc,MAAMF,EAAWb,CAAI,EAC7C,OAAI3B,IAAQ,KACRA,EAAM2C,EAAc,MAAMH,EAAWb,CAAI,GAEtC3B,CAC/B,CACiB,CACDiC,EAAK,UAAYO,CACpB,CAED,GAAIR,EAAK,MAAO,CACZ,MAAMY,EAAQ,KAAK,SAAS,OAAS,IAAIrB,EACzC,UAAWc,KAAQL,EAAK,MAAO,CAC3B,GAAI,EAAEK,KAAQO,GACV,MAAM,IAAI,MAAM,SAASP,CAAI,kBAAkB,EAEnD,GAAIA,IAAS,UAET,SAEJ,MAAMQ,EAAYR,EACZS,EAAYd,EAAK,MAAMa,CAAS,EAChCE,EAAWH,EAAMC,CAAS,EAC5BtB,EAAO,iBAAiB,IAAIc,CAAI,EAEhCO,EAAMC,CAAS,EAAKG,GAAQ,CACxB,GAAI,KAAK,SAAS,MACd,OAAO,QAAQ,QAAQF,EAAU,KAAKF,EAAOI,CAAG,CAAC,EAAE,KAAKhD,GAC7C+C,EAAS,KAAKH,EAAO5C,CAAG,CAClC,EAEL,MAAMA,EAAM8C,EAAU,KAAKF,EAAOI,CAAG,EACrC,OAAOD,EAAS,KAAKH,EAAO5C,CAAG,CAC3D,EAIwB4C,EAAMC,CAAS,EAAI,IAAIlB,IAAS,CAC5B,IAAI3B,EAAM8C,EAAU,MAAMF,EAAOjB,CAAI,EACrC,OAAI3B,IAAQ,KACRA,EAAM+C,EAAS,MAAMH,EAAOjB,CAAI,GAE7B3B,CACnC,CAEiB,CACDiC,EAAK,MAAQW,CAChB,CAED,GAAIZ,EAAK,WAAY,CACjB,MAAMiB,EAAa,KAAK,SAAS,WAC3BC,EAAiBlB,EAAK,WAC5BC,EAAK,WAAa,SAAUzK,EAAO,CAC/B,IAAIqK,EAAS,CAAA,EACb,OAAAA,EAAO,KAAKqB,EAAe,KAAK,KAAM1L,CAAK,CAAC,EACxCyL,IACApB,EAASA,EAAO,OAAOoB,EAAW,KAAK,KAAMzL,CAAK,CAAC,GAEhDqK,CAC3B,CACa,CACD,KAAK,SAAW,CAAE,GAAG,KAAK,SAAU,GAAGI,EACnD,CAAS,EACM,IACV,CACD,WAAW7M,EAAK,CACZ,YAAK,SAAW,CAAE,GAAG,KAAK,SAAU,GAAGA,GAChC,IACV,CACD,MAAM6C,EAAKD,EAAS,CAChB,OAAO8F,EAAO,IAAI7F,EAAKD,GAAW,KAAK,QAAQ,CAClD,CACD,OAAOI,EAAQJ,EAAS,CACpB,OAAO8H,EAAQ,MAAM1H,EAAQJ,GAAW,KAAK,QAAQ,CACxD,CACD0J,GAAerK,EAAO8L,EAAQ,CAC1B,MAAO,CAAClL,EAAKD,IAAY,CACrB,MAAMoL,EAAU,CAAE,GAAGpL,GACf5C,EAAM,CAAE,GAAG,KAAK,SAAU,GAAGgO,CAAO,EAEtC,KAAK,SAAS,QAAU,IAAQA,EAAQ,QAAU,KAC7ChO,EAAI,QACL,QAAQ,KAAK,oHAAoH,EAErIA,EAAI,MAAQ,IAEhB,MAAMiO,EAAa,KAAKC,GAAS,CAAC,CAAClO,EAAI,OAAQ,CAAC,CAACA,EAAI,KAAK,EAE1D,GAAI,OAAO6C,EAAQ,KAAeA,IAAQ,KACtC,OAAOoL,EAAW,IAAI,MAAM,gDAAgD,CAAC,EAEjF,GAAI,OAAOpL,GAAQ,SACf,OAAOoL,EAAW,IAAI,MAAM,wCACtB,OAAO,UAAU,SAAS,KAAKpL,CAAG,EAAI,mBAAmB,CAAC,EAKpE,GAHI7C,EAAI,QACJA,EAAI,MAAM,QAAUA,GAEpBA,EAAI,MACJ,OAAO,QAAQ,QAAQA,EAAI,MAAQA,EAAI,MAAM,WAAW6C,CAAG,EAAIA,CAAG,EAC7D,KAAKA,GAAOZ,EAAMY,EAAK7C,CAAG,CAAC,EAC3B,KAAKgD,GAAUhD,EAAI,MAAQA,EAAI,MAAM,iBAAiBgD,CAAM,EAAIA,CAAM,EACtE,KAAKA,GAAUhD,EAAI,WAAa,QAAQ,IAAI,KAAK,WAAWgD,EAAQhD,EAAI,UAAU,CAAC,EAAE,KAAK,IAAMgD,CAAM,EAAIA,CAAM,EAChH,KAAKA,GAAU+K,EAAO/K,EAAQhD,CAAG,CAAC,EAClC,KAAKT,GAAQS,EAAI,MAAQA,EAAI,MAAM,YAAYT,CAAI,EAAIA,CAAI,EAC3D,MAAM0O,CAAU,EAEzB,GAAI,CACIjO,EAAI,QACJ6C,EAAM7C,EAAI,MAAM,WAAW6C,CAAG,GAElC,IAAIG,EAASf,EAAMY,EAAK7C,CAAG,EACvBA,EAAI,QACJgD,EAAShD,EAAI,MAAM,iBAAiBgD,CAAM,GAE1ChD,EAAI,YACJ,KAAK,WAAWgD,EAAQhD,EAAI,UAAU,EAE1C,IAAIT,EAAOwO,EAAO/K,EAAQhD,CAAG,EAC7B,OAAIA,EAAI,QACJT,EAAOS,EAAI,MAAM,YAAYT,CAAI,GAE9BA,CACV,OACM4O,EAAG,CACN,OAAOF,EAAWE,CAAC,CACtB,CACb,CACK,CACDD,GAASE,EAAQC,EAAO,CACpB,OAAQF,GAAM,CAEV,GADAA,EAAE,SAAW;AAAA,2DACTC,EAAQ,CACR,MAAME,EAAM,iCACNhP,EAAS6O,EAAE,QAAU,GAAI,EAAI,EAC7B,SACN,OAAIE,EACO,QAAQ,QAAQC,CAAG,EAEvBA,CACV,CACD,GAAID,EACA,OAAO,QAAQ,OAAOF,CAAC,EAE3B,MAAMA,CAClB,CACK,CACL,CAEA,MAAMI,EAAiB,IAAIlC,GAC3B,SAASmC,EAAO3L,EAAK7C,EAAK,CACtB,OAAOuO,EAAe,MAAM1L,EAAK7C,CAAG,CACxC,CAMAwO,EAAO,QACHA,EAAO,WAAa,SAAU5L,EAAS,CACnC,OAAA2L,EAAe,WAAW3L,CAAO,EACjC4L,EAAO,SAAWD,EAAe,SACjC1P,GAAe2P,EAAO,QAAQ,EACvBA,CACf,EAIAA,EAAO,YAAc7P,EACrB6P,EAAO,SAAW5P,EAIlB4P,EAAO,IAAM,YAAajC,EAAM,CAC5B,OAAAgC,EAAe,IAAI,GAAGhC,CAAI,EAC1BiC,EAAO,SAAWD,EAAe,SACjC1P,GAAe2P,EAAO,QAAQ,EACvBA,CACX,EAIAA,EAAO,WAAa,SAAUxL,EAAQwJ,EAAU,CAC5C,OAAO+B,EAAe,WAAWvL,EAAQwJ,CAAQ,CACrD,EAQAgC,EAAO,YAAcD,EAAe,YAIpCC,EAAO,OAAS9D,EAChB8D,EAAO,OAAS9D,EAAQ,MACxB8D,EAAO,SAAW/E,EAClB+E,EAAO,aAAe/D,GACtB+D,EAAO,MAAQ9F,EACf8F,EAAO,MAAQ9F,EAAO,IACtB8F,EAAO,UAAY7L,EACnB6L,EAAO,MAAQrC,EACfqC,EAAO,MAAQA,EACCA,EAAO,QACJA,EAAO,WACdA,EAAO,IACAA,EAAO,WACNA,EAAO,YAEZ9D,EAAQ,MACThC,EAAO,ICr3Ed,SAAS+F,GAAgB7L,EAAS,CAOvC,GANI,OAAOA,GAAY,aACrBA,EAAU,CACR,UAAWA,CACjB,GAGM,CAACA,GAAW,OAAOA,EAAQ,WAAc,WAC3C,MAAM,IAAI,MAAM,iCAAiC,EAGnD,OAAI,OAAOA,EAAQ,YAAe,WAChCA,EAAQ,WAAa,aAGnB,OAAOA,EAAQ,gBAAmB,WACpCA,EAAQ,eAAiB,IAGpB,CACL,MAAO,CAAC,CAACA,EAAQ,MACjB,WAAWR,EAAO,CAChB,GAAIA,EAAM,OAAS,OACjB,OAGF,MAAMwH,EAAO8E,GAAQtM,EAAM,IAAI,EAE/B,GAAIQ,EAAQ,MACV,OAAO,QAAQ,QAAQA,EAAQ,UAAUR,EAAM,KAAMwH,EAAMxH,EAAM,MAAQ,EAAE,CAAC,EAAE,KAAKuM,GAAYvM,CAAK,CAAC,EAGvG,MAAMsH,EAAO9G,EAAQ,UAAUR,EAAM,KAAMwH,EAAMxH,EAAM,MAAQ,EAAE,EACjE,GAAIsH,aAAgB,QAClB,MAAM,IAAI,MAAM,iKAAiK,EAEnLiF,GAAYvM,CAAK,EAAEsH,CAAI,CACxB,EACD,eAAgB,GAChB,SAAU,CACR,KAAKA,EAAMkF,EAAY5N,EAAS,CAE1B,OAAO0I,GAAS,WAClB1I,EAAU0I,EAAK,QACfkF,EAAalF,EAAK,KAClBA,EAAOA,EAAK,MAEd,MAAME,EAAO8E,GAAQE,CAAU,EACzBC,EAAajF,EAAOhH,EAAQ,WAAauE,GAAOyC,CAAI,EAAIhH,EAAQ,eAChEkM,EAAYD,EACd,WAAWA,CAAU,IACrB,GACJ,OAAAnF,EAAOA,EAAK,QAAQ,MAAO,EAAE,EACtB,aAAaoF,CAAS,IAAI9N,EAAU0I,EAAOvC,GAAOuC,EAAM,EAAI,CAAC;AAAA,cACrE,CACF,CACL,CACA,CAEA,SAASgF,GAAQ9E,EAAM,CACrB,OAAQA,GAAQ,IAAI,MAAM,KAAK,EAAE,CAAC,CACpC,CAEA,SAAS+E,GAAYvM,EAAO,CAC1B,OAAQsH,GAAS,CACX,OAAOA,GAAS,UAAYA,IAAStH,EAAM,OAC7CA,EAAM,QAAU,GAChBA,EAAM,KAAOsH,EAEnB,CACA,CAGA,MAAM3K,GAAa,UACbC,GAAgB,IAAI,OAAOD,GAAW,OAAQ,GAAG,EACjDE,GAAqB,oDACrBC,GAAwB,IAAI,OAAOD,GAAmB,OAAQ,GAAG,EACjEE,GAAqB,CACzB,IAAK,QACL,IAAK,OACL,IAAK,OACL,IAAK,SACL,IAAK,OACP,EACMC,GAAwBC,GAAOF,GAAmBE,CAAE,EAC1D,SAAS8H,GAAO5H,EAAMC,EAAQ,CAC5B,GAAIA,GACF,GAAIT,GAAW,KAAKQ,CAAI,EACtB,OAAOA,EAAK,QAAQP,GAAeI,EAAoB,UAGrDH,GAAmB,KAAKM,CAAI,EAC9B,OAAOA,EAAK,QAAQL,GAAuBE,EAAoB,EAInE,OAAOG,CACT,CC/FO,MAAMQ,GAAQ,0+PCAfgP,GAAM,OAAO,eAKJ,MAAMC,EAAW,CAI9B,aAAe,CAGb,KAAK,YAEL,KAAK,MAAO,CACb,CAgBD,KAAMC,EAAOC,EAAc,CACzB,MAAMC,EAAO,KACb,IAAIC,EAASC,GAAKJ,EAAOC,IAAiB,EAAI,EAC9C,MAAMI,EAAeF,EAErB,KAAOL,GAAI,KAAKI,EAAK,YAAaC,CAAM,GACtCD,EAAK,YAAYG,CAAY,IAC7BF,EAASE,EAAe,IAAMH,EAAK,YAAYG,CAAY,EAG7D,OAAAH,EAAK,YAAYC,CAAM,EAAI,EAEpBA,CACR,CAOD,OAAS,CACP,KAAK,YAAc,OAAO,OAAO,IAAI,CACtC,CACH,CAgBO,SAASC,GAAMJ,EAAOC,EAAc,CACzC,OAAI,OAAOD,GAAU,SAAiB,IACjCC,IAAcD,EAAQA,EAAM,YAAa,GACvCA,EAAM,QAAQlP,GAAO,EAAE,EAAE,QAAQ,KAAM,GAAG,EACnD,CC3EA,IAAIwP,GAAU,IAAIC,GAEdC,GAAW,CAAA,EAER,SAASC,GAAa,CAAE,OAAAC,EAAS,GAAI,YAAAC,EAAc,EAAO,EAAG,GAAI,CACtE,MAAO,CACL,UAAW,GACX,MAAO,CACL,WAAW/M,EAAK,CACd,OAAK+M,GACHC,KAEKhN,CACR,CACF,EACD,SAAU,CACR,QAAQV,EAAMP,EAAOI,EAAK,CACxBA,EAAMA,EACH,YAAa,EACb,KAAM,EACN,QAAQ,kBAAmB,EAAE,EAChC,MAAM8N,EAAK,GAAGH,CAAM,GAAGJ,GAAQ,KAAKvN,CAAG,CAAC,GAClCoE,EAAU,CAAE,MAAAxE,EAAO,KAAAO,EAAM,GAAA2N,CAAE,EACjC,OAAAL,GAAS,KAAKrJ,CAAO,EAEd,KAAKxE,CAAK,QAAQkO,CAAE,KAAK3N,CAAI,MAAMP,CAAK;AAAA,CAChD,CACF,CACL,CACA,CAMO,SAASiO,IAAgB,CAC9BJ,GAAW,CAAA,EACXF,GAAU,IAAIC,EAChB,ECvCC,SAAUO,EAAO,CACjB,IAAIC,EAAc,6BACdC,EAAY,CACf,mBAAoB,CACnB,QAASD,EACT,MAAO,OACP,CACH,EAECD,EAAM,UAAU,MAAQ,CACvB,QAAW,MAEX,MAAS,CACR,QAAS,mEACT,WAAY,EACZ,EAKD,SAAY,CACX,CACC,QAAS,0FACT,OAAQE,EACR,MAAO,QACP,EACD,CACC,QAAS,4FACT,WAAY,GACZ,OAAQA,EACR,MAAO,QACP,CACD,EAKD,QAAW,CACV,QAAS,wFACT,WAAY,EACZ,EACD,IAAO,CACN,QAAS,uBACT,WAAY,EACZ,EAKD,SAAY,CACX,QAAS,4JACT,WAAY,GACZ,MAAO,YACP,EACD,SAAY,CACX,QAASD,EACT,MAAO,UACP,EACD,YAAe,UACjB,EAECD,EAAM,UAAU,IAAMA,EAAM,UAAU,MACtCA,EAAM,UAAU,QAAUA,EAAM,UAAU,KAC3C,GAAE,KAAK,GC/DN,SAAUA,EAAO,CAKjB,IAAIG,EAAU,0oCAEVC,EAAsB,CACzB,QAAS,4BACT,WAAY,GACZ,MAAO,cACP,OAAQ,IACV,EAEKC,EAAe,CAClB,KAAQD,EACR,YAAe,CACd,QAAS,OAAO,MAAQD,CAAO,EAC/B,MAAO,UACP,EACD,SAAY,CAEX,CACC,QAAS,sBACT,OAAQ,GACR,OAAQ,CAEP,SAAY,CACX,CACC,QAAS,uBACT,WAAY,EACZ,EACD,SACA,EACD,OAAU,8DAEV,SAAY,2DAEZ,YAAe,iBACf,CACD,EAED,CACC,QAAS,qCACT,OAAQ,GACR,OAAQ,CACP,SAAY,iBACZ,CACD,EAED,CACC,QAAS,cACT,OAAQ,GACR,OAAQ,CACP,SAAY,mCACZ,YAAe,SACf,YAAe,CACd,QAAS,OAAO,QAAUA,CAAO,EACjC,WAAY,GACZ,MAAO,UACP,CACD,CACD,EACD,oBACA,EAED,OAAU,sFACZ,EAECH,EAAM,UAAU,KAAO,CACtB,QAAW,CACV,QAAS,aACT,MAAO,WACP,EACD,QAAW,CACV,QAAS,kBACT,WAAY,EACZ,EACD,gBAAiB,CAKhB,CAEC,QAAS,kDACT,WAAY,GACZ,MAAO,UACP,EACD,CAEC,QAAS,8BACT,MAAO,UACP,CACD,EAED,gBAAiB,CAChB,QAAS,sCACT,MAAO,WACP,WAAY,EACZ,EAGD,cAAe,CACd,QAAS,0CACT,OAAQ,CACP,YAAe,CACd,QAAS,OAAO,uBAAyBG,CAAO,EAChD,WAAY,GACZ,MAAO,UACP,CACD,EACD,MAAO,WACP,WAAY,EACZ,EAED,UAAa,CACZ,QAAS,qDACT,MAAO,WACP,WAAY,EACZ,EACD,OAAU,CAET,CACC,QAAS,mDACT,WAAY,GACZ,OAAQ,GACR,OAAQE,CACR,EAGD,CACC,QAAS,2DACT,WAAY,GACZ,OAAQ,GACR,OAAQ,CACP,KAAQD,CACR,CACD,EAED,CAEC,QAAS,0EACT,WAAY,GACZ,OAAQ,GACR,OAAQC,CACR,EACD,CAEC,QAAS,oBACT,WAAY,GACZ,OAAQ,EACR,EACD,CAEC,QAAS,2BACT,OAAQ,GACR,OAAQ,CACP,OAAUA,EAAa,MACvB,CACD,CACD,EACD,YAAe,CACd,QAAS,OAAO,OAASF,CAAO,EAChC,MAAO,UACP,EACD,SAAYE,EAAa,SACzB,SAAY,CACX,QAAS,6kDACT,WAAY,EACZ,EACD,QAAW,CACV,QAAS,gHACT,WAAY,EACZ,EAED,QAAW,CACV,QAAS,6SACT,WAAY,GAEZ,MAAO,YACP,EACD,QAAW,CACV,QAAS,iDACT,WAAY,EACZ,EACD,kBAAmB,CAClB,QAAS,UACT,MAAO,WACP,EACD,SAAY,CAEX,QAAS,8EACT,OAAQ,CACP,kBAAmB,CAClB,QAAS,MACT,MAAO,WACP,CACD,CACD,EACD,YAAe,iCACf,OAAU,CACT,QAAS,qCACT,WAAY,EACZ,CACH,EAECD,EAAoB,OAASJ,EAAM,UAAU,KAqB7C,QAlBIM,EAAa,CAChB,UACA,gBACA,gBACA,cACA,YACA,SACA,cACA,WACA,UACA,UACA,UACA,kBACA,WACA,cACA,QACF,EACKC,EAASF,EAAa,SAAS,CAAC,EAAE,OAC7BjP,EAAI,EAAGA,EAAIkP,EAAW,OAAQlP,IACtCmP,EAAOD,EAAWlP,CAAC,CAAC,EAAI4O,EAAM,UAAU,KAAKM,EAAWlP,CAAC,CAAC,EAG3D4O,EAAM,UAAU,GAAKA,EAAM,UAAU,KACrCA,EAAM,UAAU,MAAQA,EAAM,UAAU,IACzC,GAAE,KAAK,EC7NP,MAAMQ,GAAiB,8pBAEjBC,GAAiB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQjBC,GAAkB;AAAA;AAAA;AAAA,EAKlBC,GAAmB;AAAA,4BACGF,EAAc;AAAA,wBAClBC,EAAe;AAAA,WAGjCE,GAAc,UACdC,GAAiB,IAAI,OAAOD,GAAY,OAAQ,GAAG,EACnDE,GACL,oDACKC,GAA2B,IAAI,OAAOD,GAAsB,OAAQ,GAAG,EACvEE,GAA2C,CAChD,IAAK,QACL,IAAK,OACL,IAAK,OACL,IAAK,SACL,IAAK,OACN,EAEMC,GAA0B3R,GAC/B0R,GAAoB1R,CAAE,GAAK,GAE5B,SAAS8H,EAAO5H,EAAcC,EAA0B,CACvD,GAAIA,GACC,GAAAmR,GAAY,KAAKpR,CAAI,EACjB,OAAAA,EAAK,QAAQqR,GAAgBI,EAAsB,UAGvDH,GAAsB,KAAKtR,CAAI,EAC3B,OAAAA,EAAK,QAAQuR,GAA0BE,EAAsB,EAI/D,OAAAzR,CACR,CASA,SAAS0R,GACRC,EACY,CACZ,MAAMC,EAAoBD,EAAW,IAAKE,IAAe,CACxD,MAAO,IAAI,OAAOA,EAAU,KAAK,QAAQ,yBAA0B,MAAM,CAAC,EAC1E,IAAK,IAAI,OAAOA,EAAU,MAAM,QAAQ,yBAA0B,MAAM,CAAC,CACxE,EAAA,EAEK,MAAA,CACN,KAAM,QACN,MAAO,QACP,MAAMvO,EAAa,CAClB,UAAWwO,KAAWF,EAAmB,CACxC,MAAMtQ,EAAQgC,EAAI,MAAMwO,EAAQ,KAAK,EACrC,GAAIxQ,EACH,OAAOA,EAAM,KAEf,CACO,MAAA,EACR,EACA,UAAUgC,EAAaG,EAAa,CACnC,UAAWqO,KAAWF,EAAmB,CACxC,MAAMtQ,EAAQ,IAAI,OACjB,GAAGwQ,EAAQ,MAAM,MAAM,eAAeA,EAAQ,IAAI,MAAM,EAAA,EACvD,KAAKxO,CAAG,EACV,GAAIhC,EACI,MAAA,CACN,KAAM,QACN,IAAKA,EAAM,CAAC,EACZ,KAAMA,EAAM,CAAC,EAAE,KAAK,CAAA,CAGvB,CACD,EACA,SAASuB,EAAY,CACb,MAAA,4BAA4BA,EAAM,IAAI,QAC9C,CAAA,CAEF,CAEA,SAASkP,IAAoC,CACrC,MAAA,CACN,KAAM,UACN,MAAO,QACP,MAAMzO,EAAK,CACH,OAAAA,EAAI,MAAM,kBAAkB,GAAG,KACvC,EACA,UAAUA,EAAK,CACR,MAAAhC,EAAQ,2CAA2C,KAAKgC,CAAG,EACjE,GAAIhC,EACI,MAAA,CACN,KAAM,UACN,IAAKA,EAAM,CAAC,EACZ,KAAMA,EAAM,CAAC,EAAE,KAAK,CAAA,CAIvB,EACA,SAASuB,EAAO,CACR,MAAA,wBAAwBA,EAAM,IAAI;AAAA,CAC1C,CAAA,CAEF,CAEA,MAAMsJ,GAA+D,CACpE,KAEChC,EACAC,EACA3I,EACC,CACD,MAAM4I,GAAQD,GAAc,IAAI,MAAM,KAAK,IAAI,CAAC,GAAK,GAGjD,OAFJD,EAAOA,EAAK,QAAQ,MAAO,EAAE,EAAI;AAAA,EAE7B,CAACE,GAAQA,IAAS,UAKpB,0BACA8G,GACA,eACC1P,EAAU0I,EAAOvC,EAAOuC,EAAM,EAAI,GACnC;AAAA,EAID,0BACAgH,GACA,8BAEAvJ,EAAOyC,CAAI,EACX,MACC5I,EAAU0I,EAAOvC,EAAOuC,EAAM,EAAI,GACnC;AAAA,CAEF,CACD,EAEM6F,GAAU,IAAIC,GAEb,SAAS+B,GAAc,CAC7B,aAAAC,EACA,YAAAC,EACA,iBAAAC,CACD,EAIkB,CACX,MAAAlD,EAAS,IAAInC,GAEZmC,EAAA,IACN,CACC,IAAK,GACL,SAAU,GACV,OAAQiD,CACT,EACAhD,GAAgB,CACf,UAAW,CAAC/E,EAAcE,IACrB+H,EAAAA,YAAmB/H,CAAI,EACnBgI,EAAAA,UAAgBlI,EAAMmI,EAAAA,UAAgBjI,CAAI,EAAGA,CAAI,EAElDF,CACR,CACA,EACD,CAAE,SAAAgC,EAAS,CAAA,EAGR8F,IACIhD,EAAA,IAAIkB,IAAc,EACzBlB,EAAO,IAAI,CACV,WAAY,CACX,CACC,KAAM,UACN,MAAO,QACP,SAASpM,EAAO,CACT,MAAAJ,EAAMI,EAAM,IAChB,YAAA,EACA,KAAK,EACL,QAAQ,kBAAmB,EAAE,EACzB0N,EAAK,IAAMP,GAAQ,KAAKvN,CAAG,EAC3BJ,EAAQQ,EAAM,MACdD,EAAO,KAAK,OAAO,YAAYC,EAAM,MAAO,EAE3C,MAAA,KAAKR,CAAK,QAAQkO,CAAE,wCAAwCA,CAAE,KAAKS,EAAc,OAAOpO,CAAI,MAAMP,CAAK;AAAA,CAC/G,CACD,CACD,CAAA,CACA,GAGF,MAAMkQ,EAAmBR,KACnBS,EAAiBd,GAAqBS,CAAgB,EAE5D,OAAAlD,EAAO,IAAI,CACV,WAAY,CAACsD,EAAkBC,CAAc,CAAA,CAC7C,EAEMvD,CACR,CCnOA,MAAMwD,EAAa/C,GACR,KAAK,MAAM,KAAK,UAAUA,CAAK,CAAC,EAErCgD,GAAahD,GACPA,EAAM,WAAa,EAEzBiD,GAAkBjD,GACbkD,GAAgB,IAAIlD,EAAM,OAAO,EAEtCmD,GAAmBnD,GACb,WAAYA,EAElBoD,GAAmBpD,GACbA,EAAM,UAAY,SAExBqD,GAAuBrD,GACjB,eAAgBA,EAEtBsD,GAAsBtD,GAChB,aAAcA,EAEpBuD,GAAqB,IAAM,CAC7B,MAAMC,EAAK,wBACX,OAAQC,GACGD,EAAG,KAAKC,CAAG,CAE1B,KACMC,IAA0B,IAAM,CAClC,MAAMF,EAAK,oBACX,OAAQC,GACGD,EAAG,KAAKC,CAAG,CAE1B,KACME,GAAaC,GAAS,CACxB,MAAMC,EAAS,CAAA,EACf,QAAS3R,EAAI,EAAGI,EAAIsR,EAAK,OAAQ1R,EAAII,EAAGJ,IAAK,CACzC,MAAM4R,EAAMF,EAAK1R,CAAC,EAClB,UAAW6R,KAAOD,EACTD,EAAOE,CAAG,EAIXF,EAAOE,CAAG,EAAIF,EAAOE,CAAG,EAAE,OAAOD,EAAIC,CAAG,CAAC,EAHzCF,EAAOE,CAAG,EAAID,EAAIC,CAAG,CAMhC,CACD,OAAOF,CACX,EACMG,GAAwB,CAACC,EAAQ1G,IAAa,CAChD,IAAI2G,EAAUD,EAAO,WACrB,KAAOC,GAAS,CACZ,MAAMvK,EAAOuK,EAAQ,YACjBlB,GAAUkB,CAAO,IACjB3G,EAAS2G,EAASD,CAAM,EACpBC,EAAQ,YACRF,GAAsBE,EAAS3G,CAAQ,GAG/C2G,EAAUvK,CACb,CACL,EACMwK,GAA2B,CAACF,EAAQ1G,IAAa,CACnD,MAAM6G,EAAW,SAAS,mBAAmBH,EAAQ,WAAW,YAAY,EAC5E,IAAIC,EACJ,KAAOA,EAAUE,EAAS,YAAY,CAClC,MAAMH,EAASC,EAAQ,WAClBD,GAEL1G,EAAS2G,EAASD,CAAM,CAC3B,CACL,EACMI,GAAmB,CAACJ,EAAQ1G,IACV,CAAC,CAAC,WAAW,UAAY,CAAC,CAAC,WAAW,SAAS,mBAExD4G,GAAyBF,EAAQ1G,CAAQ,EAGzCyG,GAAsBC,EAAQ1G,CAAQ,EC7E/C+G,GAAsB,CACxB,IACA,OACA,UACA,UACA,OACA,UACA,QACA,QACA,IACA,MACA,MACA,UACA,MACA,aACA,OACA,KACA,SACA,SACA,UACA,SACA,OACA,OACA,MACA,WACA,WACA,KACA,MACA,UACA,MACA,SACA,MACA,MACA,KACA,KACA,KACA,WACA,aACA,SACA,OACA,SACA,OACA,KACA,KACA,KACA,KACA,KACA,KACA,OACA,SACA,SACA,KACA,OACA,IACA,MACA,QACA,MACA,MACA,SACA,QACA,QACA,SACA,KACA,OACA,UACA,OACA,MACA,OACA,UACA,OACA,OACA,QACA,MACA,OACA,KACA,WACA,SACA,SACA,IACA,UACA,QACA,MACA,WACA,IACA,KACA,KACA,KACA,MACA,OACA,IACA,OACA,UACA,SACA,aACA,QACA,SACA,OACA,SACA,SACA,QACA,MACA,UACA,MACA,QACA,QACA,KACA,QACA,KACA,QACA,OACA,KACA,QACA,KACA,IACA,KACA,MACA,QACA,KACJ,EACMC,GAAyB,CAC3B,WACA,UACA,OACA,SACA,QACA,YACA,SACA,OAEA,WACA,QACA,KACJ,EACMC,GAAgB,IAAI,IAAI,CAC1B,GAAGF,GACH,GAAGC,EACP,CAAC,EACKE,GAAqB,CACvB,MACA,IACA,WACA,cACA,eACA,eACA,gBACA,mBACA,SACA,WACA,OACA,OACA,UACA,SACA,OACA,IACA,QACA,WACA,QACA,QACA,OACA,iBACA,SACA,OACA,WACA,QACA,OACA,UACA,UACA,WACA,iBACA,OACA,OACA,QACA,SACA,SACA,OACA,WACA,QACA,OACA,QACA,OACA,QAEA,UACA,gBACA,sBACA,cACA,mBACA,oBACA,oBACA,iBACA,UACA,UACA,UACA,UACA,UACA,iBACA,UACA,UACA,cACA,eACA,WACA,eACA,qBACA,cACA,SACA,cACJ,EACMC,GAAwB,CAC1B,UACA,gBACA,SACA,UACA,eACA,YACA,mBACA,iBACA,gBACA,gBACA,gBACA,QACA,YACA,OACA,eACA,YACA,UACA,gBACA,SACA,MACA,aACA,UACA,KACJ,EACMC,GAAe,IAAI,IAAI,CACzB,GAAGF,GACH,GAAGC,EACP,CAAC,EACKE,GAAsB,CACxB,OACA,WACA,SACA,UACA,QACA,SACA,KACA,aACA,gBACA,KACA,KACA,QACA,UACA,WACA,QACA,OACA,KACA,SACA,QACA,SACA,OACA,OACA,UACA,SACA,MACA,QACA,MACA,SACA,YACJ,EACMC,GAAyB,CAC3B,UACA,cACA,aACA,WACA,YACA,UACA,UACA,SACA,SACA,QACA,YACA,aACA,iBACA,cACA,MACJ,EACMC,GAAgB,IAAI,IAAI,CAC1B,GAAGF,GACH,GAAGC,EACP,CAAC,EAEKE,GAAwB,CAC1B,OACA,SACA,iBACA,YACA,SACA,QACA,QACA,QACA,kBACA,MACA,SACA,UACA,KACA,QACA,iBACA,eACA,cACA,YACA,uBACA,WACA,OACA,aACA,WACA,UACA,SACA,cACA,UACA,cACA,cACA,YACA,OACA,UACA,UACA,UACA,OACA,QACA,UACA,QACA,OACA,WACA,WACA,QACA,OACA,UACA,UACA,UACA,kBACA,WACA,eACA,wBACA,SACA,cACA,MACA,OACA,WACA,UACA,WACA,UACA,QACA,MACA,YACA,UACA,WACA,0BACA,wBACA,yBACA,WACA,YACA,gBACA,UACA,MACA,eACA,QACA,cACA,OACA,MACA,OACA,aACA,cACA,aACA,iBACA,aACA,QACA,cACA,UACA,SACA,SACA,OACA,OACA,WACA,gBACA,SACA,aACA,KACA,aACA,cACA,aACA,iBACA,mBACA,cACA,QACA,YACA,YACA,YACA,QACA,UACA,OACA,QACA,OACA,WACA,cACA,aACA,OACA,OACA,UACA,WACA,OACA,MACA,SACA,WACA,eACA,cACA,MACA,YACA,YACA,QACA,SACA,MACA,YACA,WACA,QACA,OACA,SACA,WACA,QACA,WACA,UACA,aACA,SACA,SACA,OACA,UACA,OACA,UACA,OACA,cACA,cACA,SACA,SACA,UACA,SACA,WACA,iBACA,MACA,kBACA,WACA,YACA,MACA,WACA,OACA,OACA,UACA,QACA,UACA,SACA,QACA,SACA,eACA,cACA,YACA,SACA,WACA,aACA,2BACA,QACA,OACA,QACA,OACA,OACA,aACA,MACA,UACA,SACA,UACA,QACA,OACA,QACA,UACA,WACA,SACA,OACA,QACA,YACA,YACA,YACA,aACA,OACA,SACA,SACA,QACA,YACA,UACA,wBACA,QACA,SACA,kBACA,QACA,MACJ,EASMC,GAAuB,CACzB,gBACA,aACA,WACA,qBACA,SACA,gBACA,gBACA,UACA,gBACA,iBACA,QACA,OACA,KACA,QACA,OACA,gBACA,YACA,YACA,QACA,sBACA,8BACA,gBACA,kBACA,KACA,KACA,IACA,KACA,KACA,kBACA,YACA,UACA,UACA,oBACA,MACA,WACA,YACA,MACA,OACA,eACA,YACA,SACA,cACA,cACA,gBACA,cACA,YACA,mBACA,eACA,aACA,eACA,cACA,KACA,KACA,KACA,KACA,aACA,WACA,gBACA,oBACA,SACA,OACA,KACA,kBACA,KACA,MACA,IACA,KACA,KACA,KACA,KACA,UACA,YACA,aACA,WACA,OACA,eACA,iBACA,eACA,mBACA,iBACA,QACA,aACA,aACA,eACA,eACA,cACA,cACA,mBACA,YACA,MACA,OACA,QACA,SACA,OACA,MACA,OACA,aACA,SACA,WACA,UACA,QACA,SACA,cACA,SACA,WACA,cACA,OACA,aACA,sBACA,mBACA,eACA,SACA,gBACA,sBACA,iBACA,IACA,KACA,KACA,SACA,OACA,OACA,cACA,YACA,UACA,SACA,SACA,QACA,OACA,kBACA,mBACA,mBACA,eACA,cACA,eACA,cACA,aACA,eACA,mBACA,oBACA,iBACA,kBACA,oBACA,iBACA,SACA,eACA,QACA,eACA,iBACA,WACA,UACA,UACA,YACA,mBACA,cACA,kBACA,iBACA,aACA,OACA,KACA,KACA,UACA,SACA,UACA,aACA,UACA,aACA,gBACA,gBACA,QACA,eACA,OACA,eACA,mBACA,mBACA,IACA,KACA,KACA,QACA,IACA,KACA,KACA,IACA,YACJ,EAMMC,GAAwB,CAC1B,SACA,cACA,QACA,WACA,QACA,eACA,cACA,aACA,aACA,QACA,MACA,UACA,eACA,WACA,QACA,QACA,SACA,OACA,KACA,UACA,SACA,gBACA,SACA,SACA,iBACA,YACA,WACA,cACA,UACA,UACA,gBACA,WACA,WACA,OACA,WACA,WACA,aACA,UACA,SACA,SACA,cACA,gBACA,uBACA,YACA,YACA,aACA,WACA,iBACA,iBACA,YACA,UACA,QACA,OACJ,EAOMC,EAAa,CACf,KAAM,+BACN,IAAK,6BACL,KAAM,oCACV,EACMC,GAAsB,CACxB,CAACD,EAAW,IAAI,EAAGV,GACnB,CAACU,EAAW,GAAG,EAAGP,GAClB,CAACO,EAAW,IAAI,EAAGJ,EACvB,EAMMM,GAAmB,CACrB,CAACF,EAAW,IAAI,EAAG,OACnB,CAACA,EAAW,GAAG,EAAG,MAClB,CAACA,EAAW,IAAI,EAAG,MACvB,EACMG,GAAsB,CACxB,CAACH,EAAW,IAAI,EAAG,GACnB,CAACA,EAAW,GAAG,EAAG,OAClB,CAACA,EAAW,IAAI,EAAG,OACvB,EAEMhC,GAAkB,IAAI,IAAI,CAC5B,IACA,OACA,SACA,OACA,SACA,OACJ,CAAC,EAEKoC,GAAW,CACb,cAAe,GACf,oBAAqB,GACrB,mBAAoB,GACpB,cAAe,CACX,GAAGhB,GACH,GAAGG,GAAmB,IAAIvT,GAAQ,OAAOA,CAAI,EAAE,EAC/C,GAAG0T,GAAoB,IAAI1T,GAAQ,QAAQA,CAAI,EAAE,CACpD,EACD,gBAAiByS,GAAU,CACvB,OAAO,YAAYoB,GAAsB,IAAI7T,GAAQ,CAACA,EAAM,CAAC,GAAG,CAAC,CAAC,CAAC,EACnE,OAAO,YAAY8T,GAAqB,IAAI9T,GAAQ,CAACA,EAAM,CAAC,OAAO,CAAC,CAAC,CAAC,EACtE,OAAO,YAAY+T,GAAsB,IAAI/T,GAAQ,CAACA,EAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,CAChF,CAAK,CACL,ECzyBA,IAAIqU,EAAkE,SAAUC,EAAUC,EAAOzF,EAAO0F,EAAMC,EAAG,CAC7G,GAAID,IAAS,IAAK,MAAM,IAAI,UAAU,gCAAgC,EACtE,GAAIA,IAAS,KAAO,CAACC,EAAG,MAAM,IAAI,UAAU,+CAA+C,EAC3F,GAAI,OAAOF,GAAU,WAAaD,IAAaC,GAAS,CAACE,EAAI,CAACF,EAAM,IAAID,CAAQ,EAAG,MAAM,IAAI,UAAU,yEAAyE,EAChL,OAAQE,IAAS,IAAMC,EAAE,KAAKH,EAAUxF,CAAK,EAAI2F,EAAIA,EAAE,MAAQ3F,EAAQyF,EAAM,IAAID,EAAUxF,CAAK,EAAIA,CACxG,EACI4F,EAAkE,SAAUJ,EAAUC,EAAOC,EAAMC,EAAG,CACtG,GAAID,IAAS,KAAO,CAACC,EAAG,MAAM,IAAI,UAAU,+CAA+C,EAC3F,GAAI,OAAOF,GAAU,WAAaD,IAAaC,GAAS,CAACE,EAAI,CAACF,EAAM,IAAID,CAAQ,EAAG,MAAM,IAAI,UAAU,0EAA0E,EACjL,OAAOE,IAAS,IAAMC,EAAID,IAAS,IAAMC,EAAE,KAAKH,CAAQ,EAAIG,EAAIA,EAAE,MAAQF,EAAM,IAAID,CAAQ,CAChG,EACIK,EAAyBC,EAAyBC,EAMtD,MAAMC,EAAS,CAEX,YAAYC,EAAgB,GAAI,CAE5BJ,EAAwB,IAAI,KAAM,MAAM,EACxCC,EAAwB,IAAI,KAAM,MAAM,EACxCC,EAA0B,IAAI,KAAM,MAAM,EAE1C,KAAK,iBAAmB,IACbhD,EAAU6C,EAAuB,KAAMC,EAAyB,GAAG,CAAC,EAE/E,KAAK,SAAYK,GAAU,CAGvB,MAAMC,EAAgBP,EAAuB,KAAME,EAAyB,GAAG,EACzEM,EAAkBR,EAAuB,KAAMG,EAA2B,GAAG,EACnF,OAAA1B,GAAiB6B,EAAO,CAAC3S,EAAM0Q,IAAW,CACtC,MAAMoC,EAAY9S,EAAK,cAAgB2R,EAAW,KAC5CoB,EAAkBrC,EAAO,cAAmBiB,EAAW,KACvDqB,EAAWpB,GAAoBkB,CAAS,EACxCG,EAAOpB,GAAiBiB,CAAS,EACjC3F,EAAS2E,GAAoBgB,CAAS,EACtChR,EAAM9B,EAAK,QAAQ,YAAW,EAC9BkT,EAAc,GAAG/F,CAAM,GAAGrL,CAAG,GAE7BqR,GAAc,GAAGhG,CAAM,IAC7B,GAAI,CAAC6F,EAAS,IAAIlR,CAAG,GAAK,CAAC8Q,EAAc,IAAIM,CAAW,GAAMJ,IAAcC,GAAmBjR,IAAQmR,EACnGvC,EAAO,YAAY1Q,CAAI,MAEtB,CACD,MAAMoT,GAAapT,EAAK,oBAClBqT,GAAmBD,GAAW,OACpC,GAAIC,GAAkB,CAClB,QAAS1U,EAAI,EAAGA,EAAI0U,GAAkB1U,IAAK,CACvC,MAAM2U,GAAYF,GAAWzU,CAAC,EACxB4U,EAAgBV,EAAgBS,EAAS,GAC3C,CAACC,GAAkB,CAACA,EAAc,IAAIJ,EAAW,GAAK,CAACI,EAAc,IAAIL,CAAW,IACpFlT,EAAK,gBAAgBsT,EAAS,CAErC,CACD,GAAI5D,GAAe1P,CAAI,EACnB,GAAI+P,GAAmB/P,CAAI,EAAG,CAC1B,MAAMjC,EAAOiC,EAAK,aAAa,MAAM,EACjCjC,GAAQoS,GAAuBpS,CAAI,GAAKiS,EAAkBhQ,EAAK,QAAQ,GACvEA,EAAK,gBAAgB,MAAM,CAElC,MACQ4P,GAAgB5P,CAAI,EACrBgQ,EAAkBhQ,EAAK,MAAM,GAC7BA,EAAK,gBAAgB,QAAQ,EAG5B8P,GAAoB9P,CAAI,EACzBgQ,EAAkBhQ,EAAK,UAAU,GACjCA,EAAK,gBAAgB,YAAY,EAGhC6P,GAAgB7P,CAAI,IACrBgQ,EAAkBhQ,EAAK,GAAG,GAC1BA,EAAK,gBAAgB,YAAY,EAErCA,EAAK,aAAa,UAAW,eAAe,EAGvD,CACJ,CACjB,CAAa,EACM2S,CACnB,EACQ,KAAK,YAAc,CAACa,EAASb,IAAU,CACnC,MAAM,IAAI,MAAM,sCAAsC,CAClE,EACQ,KAAM,CAAE,cAAAc,EAAe,oBAAAC,EAAqB,mBAAAC,EAAoB,cAAAC,EAAe,aAAAC,EAAc,eAAAC,CAAgB,EAAGpB,EAChH,GAAIe,IAAkB,GAClB,MAAM,IAAI,MAAM,8CAA8C,EAClE,GAAIC,EACA,MAAM,IAAI,MAAM,mDAAmD,EACvE,GAAIC,EACA,MAAM,IAAI,MAAM,kDAAkD,EACtE,GAAIC,EACA,MAAM,IAAI,MAAM,mEAAmE,EACvF,GAAIC,EACA,MAAM,IAAI,MAAM,kEAAkE,EACtF,GAAIC,EACA,MAAM,IAAI,MAAM,sEAAsE,EAC1F9B,EAAuB,KAAMM,EAAyB9C,EAAUuC,EAAQ,EAAG,GAAG,EAC9E,KAAM,CAAE,cAAAa,EAAe,gBAAAC,CAAiB,EAAGH,EACvCE,IACAP,EAAuB,KAAMC,EAAyB,GAAG,EAAE,cAAgBI,EAAc,eACzFG,IACAR,EAAuB,KAAMC,EAAyB,GAAG,EAAE,gBAAkBI,EAAc,iBAC/FV,EAAuB,KAAMO,EAAyB,IAAI,IAAIF,EAAuB,KAAMC,EAAyB,GAAG,EAAE,aAAa,EAAG,GAAG,EAC5IN,EAAuB,KAAMQ,EAA2B,OAAO,YAAY,OAAO,QAAQH,EAAuB,KAAMC,EAAyB,GAAG,EAAE,iBAAmB,EAAE,EAAE,IAAI,CAAC,CAACkB,EAASJ,CAAU,IAAM,CAACI,EAAS,IAAI,IAAIJ,CAAU,CAAC,CAAC,CAAC,EAAG,GAAG,CACnP,CACL,CACAd,EAA0B,IAAI,QAAWC,EAA0B,IAAI,QAAWC,EAA4B,IAAI,QAElHC,GAAS,wBAA0B,IACxBjD,EAAUuC,EAAQ", "x_google_ignoreList": [0, 1, 2, 3, 4, 5, 6, 8, 9, 10]}