import{$ as De,s as Te,l as qe,q as Ge,d as Me}from"./index-DJ2rNx9E.js";import{s as Ve}from"./Blocks-nqcZ2TmD.js";import{C as Ie}from"./clear-CvR6Cdu7.js";/* empty css                                                        */import"./MarkdownCode.svelte_svelte_type_style_lang-Bl8p72Ow.js";import{c as Ue}from"./Dropdown-Cb5zL1i6.js";import"./StreamingBar.svelte_svelte_type_style_lang-DlRzv56K.js";/* empty css                                              */import{C as Re}from"./Checkbox-CjOIpf6b.js";import"./svelte/svelte.js";import"./Toast-BDCWOwui.js";import"./index-BALGG9zl.js";import"./utils-BsGrhMNe.js";import"./prism-python-CeMtt1IT.js";import"./BlockTitle-C6qeQAMx.js";import"./Info-D7HP20hi.js";import"./MarkdownCode-Cdb8e5t4.js";import"./DropdownArrow-DYWFcSFn.js";const{SvelteComponent:Ee,append:E,attr:W,component_subscribe:We,create_component:Ae,destroy_component:Be,detach:ne,element:B,flush:Fe,init:je,insert:le,listen:He,mount_component:Ne,safe_not_equal:Ye,set_data:ze,space:oe,src_url_equal:Ze,text:Le,transition_in:Je,transition_out:Ke}=window.__gradio__svelte__internal,{createEventDispatcher:Oe}=window.__gradio__svelte__internal;function Qe(o){let e,t,u,a,i,r=o[1]("common.settings")+"",d,$,S,C,_,g,h,y,k,T;return h=new Ie({}),{c(){e=B("h2"),t=B("img"),a=oe(),i=B("div"),d=Le(r),$=oe(),S=B("div"),C=Le(o[0]),_=oe(),g=B("button"),Ae(h.$$.fragment),Ze(t.src,u=Ve)||W(t,"src",u),W(t,"alt",""),W(t,"class","svelte-1b2d8fn"),W(S,"class","url svelte-1b2d8fn"),W(i,"class","title svelte-1b2d8fn"),W(e,"class","svelte-1b2d8fn"),W(g,"class","svelte-1b2d8fn")},m(f,w){le(f,e,w),E(e,t),E(e,a),E(e,i),E(i,d),E(i,$),E(i,S),E(S,C),le(f,_,w),le(f,g,w),Ne(h,g,null),y=!0,k||(T=He(g,"click",o[3]),k=!0)},p(f,[w]){(!y||w&2)&&r!==(r=f[1]("common.settings")+"")&&ze(d,r),(!y||w&1)&&ze(C,f[0])},i(f){y||(Je(h.$$.fragment,f),y=!0)},o(f){Ke(h.$$.fragment,f),y=!1},d(f){f&&(ne(e),ne(_),ne(g)),Be(h),k=!1,T()}}}function Xe(o,e,t){let u;We(o,De,d=>t(1,u=d));let{root:a}=e;const i=Oe();Te();const r=()=>i("close");return o.$$set=d=>{"root"in d&&t(0,a=d.root)},[a,u,i,r]}class xe extends Ee{constructor(e){super(),je(this,e,Xe,Qe,Ye,{root:0})}get root(){return this.$$.ctx[0]}set root(e){this.$$set({root:e}),Fe()}}const et="data:image/svg+xml,%3csvg%20viewBox='0%200%2020%2020'%20version='1.1'%20xmlns='http://www.w3.org/2000/svg'%20xmlns:xlink='http://www.w3.org/1999/xlink'%20fill='%23000000'%3e%3cg%20id='SVGRepo_bgCarrier'%20stroke-width='0'%3e%3c/g%3e%3cg%20id='SVGRepo_tracerCarrier'%20stroke-linecap='round'%20stroke-linejoin='round'%3e%3c/g%3e%3cg%20id='SVGRepo_iconCarrier'%3e%3ctitle%3erecord%20[%23982]%3c/title%3e%3cdesc%3eCreated%20with%20Sketch.%3c/desc%3e%3cdefs%3e%3c/defs%3e%3cg%20id='Page-1'%20stroke='none'%20stroke-width='1'%20fill='none'%20fill-rule='evenodd'%3e%3cg%20id='Dribbble-Light-Preview'%20transform='translate(-380.000000,%20-3839.000000)'%20fill='%23808080'%3e%3cg%20id='icons'%20transform='translate(56.000000,%20160.000000)'%3e%3cpath%20d='M338,3689%20C338,3691.209%20336.209,3693%20334,3693%20C331.791,3693%20330,3691.209%20330,3689%20C330,3686.791%20331.791,3685%20334,3685%20C336.209,3685%20338,3686.791%20338,3689%20M334,3697%20C329.589,3697%20326,3693.411%20326,3689%20C326,3684.589%20329.589,3681%20334,3681%20C338.411,3681%20342,3684.589%20342,3689%20C342,3693.411%20338.411,3697%20334,3697%20M334,3679%20C328.477,3679%20324,3683.477%20324,3689%20C324,3694.523%20328.477,3699%20334,3699%20C339.523,3699%20344,3694.523%20344,3689%20C344,3683.477%20339.523,3679%20334,3679'%20id='record-[%23982]'%3e%3c/path%3e%3c/g%3e%3c/g%3e%3c/g%3e%3c/g%3e%3c/svg%3e",{SvelteComponent:tt,append:n,attr:m,bubble:nt,component_subscribe:lt,create_component:Y,destroy_component:Z,detach:R,element:c,flush:F,init:ot,insert:z,listen:Q,mount_component:J,noop:st,run_all:it,safe_not_equal:at,set_data:j,space:P,text:D,transition_in:K,transition_out:O}=window.__gradio__svelte__internal,{onMount:rt}=window.__gradio__svelte__internal,{createEventDispatcher:ct}=window.__gradio__svelte__internal;function Pe(o){let e,t,u=o[7]("common.display_theme")+"",a,i,r,d,$,S,C,_,g,h,y,k,T,f,w,M;return{c(){e=c("div"),t=c("h2"),a=D(u),i=P(),r=c("p"),d=c("li"),$=c("button"),$.textContent="☀︎  Light",C=P(),_=c("li"),g=c("button"),g.textContent="⏾   Dark",y=P(),k=c("li"),T=c("button"),T.textContent="🖥︎  System",m(t,"class","svelte-1asl00n"),m($,"class","svelte-1asl00n"),m(d,"class",S="theme-button "+(o[6]==="light"?"current-theme":"inactive-theme")+" svelte-1asl00n"),m(g,"class","svelte-1asl00n"),m(_,"class",h="theme-button "+(o[6]==="dark"?"current-theme":"inactive-theme")+" svelte-1asl00n"),m(T,"class","svelte-1asl00n"),m(k,"class",f="theme-button "+(o[6]==="system"?"current-theme":"inactive-theme")+" svelte-1asl00n"),m(r,"class","padded theme-buttons svelte-1asl00n"),m(e,"class","banner-wrap svelte-1asl00n")},m(p,l){z(p,e,l),n(e,t),n(t,a),n(e,i),n(e,r),n(r,d),n(d,$),n(r,C),n(r,_),n(_,g),n(r,y),n(r,k),n(k,T),w||(M=[Q(d,"click",o[14]),Q(_,"click",o[15]),Q(k,"click",o[16])],w=!0)},p(p,l){l&128&&u!==(u=p[7]("common.display_theme")+"")&&j(a,u),l&64&&S!==(S="theme-button "+(p[6]==="light"?"current-theme":"inactive-theme")+" svelte-1asl00n")&&m(d,"class",S),l&64&&h!==(h="theme-button "+(p[6]==="dark"?"current-theme":"inactive-theme")+" svelte-1asl00n")&&m(_,"class",h),l&64&&f!==(f="theme-button "+(p[6]==="system"?"current-theme":"inactive-theme")+" svelte-1asl00n")&&m(k,"class",f)},d(p){p&&R(e),w=!1,it(M)}}}function mt(o){let e,t,u;return{c(){e=D(`Progressive Web App is not enabled for this app. To enable it, start your
			Gradio app with `),t=c("code"),t.textContent="launch(pwa=True)",u=D(".")},m(a,i){z(a,e,i),z(a,t,i),z(a,u,i)},p:st,d(a){a&&(R(e),R(t),R(u))}}}function ut(o){let e,t,u,a;return{c(){e=D("You can install this app as a Progressive Web App on your device. Visit "),t=c("a"),u=D(o[0]),a=D(" and click the install button in the URL address bar of your browser."),m(t,"href",o[0]),m(t,"target","_blank"),m(t,"class","svelte-1asl00n")},m(i,r){z(i,e,r),z(i,t,r),n(t,u),z(i,a,r)},p(i,r){r&1&&j(u,i[0]),r&1&&m(t,"href",i[0])},d(i){i&&(R(e),R(t),R(a))}}}function dt(o){let e,t,u,a,i,r,d=o[7]("common.language")+"",$,S,C,_,g,h,y,k=o[7]("common.pwa")+"",T,f,w,M,p,l,q=o[7]("common.screen_studio")+"",X,se,H,ie,v,ae,re,ce,me,x,ue,de,_e,ee,fe,he,pe,be,V,ge,I,ve,A,U,te,we;t=new xe({props:{root:o[0]}}),t.$on("close",o[13]);let L=o[3]===null&&Pe(o);_=new Ue({props:{label:"Language",choices:qe,show_label:!1,value:o[5]}}),_.$on("change",o[10]);function ke(s,b){return s[4]?ut:mt}let N=ke(o),G=N(o);return V=new Re({props:{label:"Include automatic zoom in/out",interactive:!0,value:o[1]}}),V.$on("change",o[11]),I=new Re({props:{label:"Include automatic video trimming",interactive:!0,value:o[2]}}),I.$on("change",o[12]),{c(){e=c("div"),Y(t.$$.fragment),u=P(),L&&L.c(),a=P(),i=c("div"),r=c("h2"),$=D(d),S=P(),C=c("p"),Y(_.$$.fragment),g=P(),h=c("div"),y=c("h2"),T=D(k),f=P(),w=c("p"),G.c(),M=P(),p=c("div"),l=c("h2"),X=D(q),se=P(),H=c("span"),H.textContent="beta",ie=P(),v=c("p"),ae=D(`Screen Studio allows you to record your screen and generates a video of your
		app with automatically adding zoom in and zoom out effects as well as
		trimming the video to remove the prediction time.
		`),re=c("br"),ce=c("br"),me=D(`
		Start recording by clicking the `),x=c("i"),x.textContent="Start Recording",ue=D(` button below and then
		sharing the current browser tab of your Gradio demo. Use your app as you
		would normally to generate a prediction.
		`),de=c("br"),_e=D(`
		Stop recording by clicking the `),ee=c("i"),ee.textContent="Stop Recording",fe=D(` button in the footer of
		the demo.
		`),he=c("br"),pe=c("br"),be=P(),Y(V.$$.fragment),ge=P(),Y(I.$$.fragment),ve=P(),A=c("button"),A.innerHTML=`<img src="${et}" alt="Start Recording" class="svelte-1asl00n"/>
		Start Recording`,m(e,"class","banner-wrap svelte-1asl00n"),m(r,"class","svelte-1asl00n"),m(C,"class","padded svelte-1asl00n"),m(i,"class","banner-wrap svelte-1asl00n"),m(y,"class","svelte-1asl00n"),m(w,"class","padded svelte-1asl00n"),m(h,"class","banner-wrap svelte-1asl00n"),m(H,"class","beta-tag svelte-1asl00n"),m(l,"class","svelte-1asl00n"),m(v,"class","padded svelte-1asl00n"),m(A,"class","record-button svelte-1asl00n"),m(p,"class","banner-wrap svelte-1asl00n")},m(s,b){z(s,e,b),J(t,e,null),z(s,u,b),L&&L.m(s,b),z(s,a,b),z(s,i,b),n(i,r),n(r,$),n(i,S),n(i,C),J(_,C,null),z(s,g,b),z(s,h,b),n(h,y),n(y,T),n(h,f),n(h,w),G.m(w,null),z(s,M,b),z(s,p,b),n(p,l),n(l,X),n(l,se),n(l,H),n(p,ie),n(p,v),n(v,ae),n(v,re),n(v,ce),n(v,me),n(v,x),n(v,ue),n(v,de),n(v,_e),n(v,ee),n(v,fe),n(v,he),n(v,pe),n(v,be),J(V,v,null),n(v,ge),J(I,v,null),n(p,ve),n(p,A),U=!0,te||(we=Q(A,"click",o[17]),te=!0)},p(s,[b]){const $e={};b&1&&($e.root=s[0]),t.$set($e),s[3]===null?L?L.p(s,b):(L=Pe(s),L.c(),L.m(a.parentNode,a)):L&&(L.d(1),L=null),(!U||b&128)&&d!==(d=s[7]("common.language")+"")&&j($,d);const Ce={};b&32&&(Ce.value=s[5]),_.$set(Ce),(!U||b&128)&&k!==(k=s[7]("common.pwa")+"")&&j(T,k),N===(N=ke(s))&&G?G.p(s,b):(G.d(1),G=N(s),G&&(G.c(),G.m(w,null))),(!U||b&128)&&q!==(q=s[7]("common.screen_studio")+"")&&j(X,q);const ye={};b&2&&(ye.value=s[1]),V.$set(ye);const Se={};b&4&&(Se.value=s[2]),I.$set(Se)},i(s){U||(K(t.$$.fragment,s),K(_.$$.fragment,s),K(V.$$.fragment,s),K(I.$$.fragment,s),U=!0)},o(s){O(t.$$.fragment,s),O(_.$$.fragment,s),O(V.$$.fragment,s),O(I.$$.fragment,s),U=!1},d(s){s&&(R(e),R(u),R(a),R(i),R(g),R(h),R(M),R(p)),Z(t),L&&L.d(s),Z(_),G.d(),Z(V),Z(I),te=!1,we()}}}function _t(o,e,t){let u;lt(o,De,l=>t(7,u=l));let{root:a}=e,{space_id:i}=e,{pwa_enabled:r}=e;const d=ct();a===""&&(a=location.protocol+"//"+location.host+location.pathname),a.endsWith("/")||(a+="/");function $(l){const q=new URL(window.location.href);l==="system"?(q.searchParams.delete("__theme"),t(6,C="system")):(q.searchParams.set("__theme",l),t(6,C=l)),window.location.href=q.toString()}rt(()=>{document.body.style.overflow="hidden","parentIFrame"in window&&window.parentIFrame?.scrollTo(0,0);const q=new URL(window.location.href).searchParams.get("__theme");return t(6,C=q||"system"),()=>{document.body.style.overflow="auto"}});let S,C="system",{allow_zoom:_=!0}=e,{allow_video_trim:g=!0}=e;Ge.subscribe(l=>{l&&t(5,S=l)});function h(l){const q=l.detail;Me(q)}function y(l){t(1,_=l.detail)}function k(l){t(2,g=l.detail)}Te();function T(l){nt.call(this,o,l)}const f=()=>$("light"),w=()=>$("dark"),M=()=>$("system"),p=()=>{d("close"),d("start_recording")};return o.$$set=l=>{"root"in l&&t(0,a=l.root),"space_id"in l&&t(3,i=l.space_id),"pwa_enabled"in l&&t(4,r=l.pwa_enabled),"allow_zoom"in l&&t(1,_=l.allow_zoom),"allow_video_trim"in l&&t(2,g=l.allow_video_trim)},[a,_,g,i,r,S,C,u,d,$,h,y,k,T,f,w,M,p]}class qt extends tt{constructor(e){super(),ot(this,e,_t,dt,at,{root:0,space_id:3,pwa_enabled:4,allow_zoom:1,allow_video_trim:2})}get root(){return this.$$.ctx[0]}set root(e){this.$$set({root:e}),F()}get space_id(){return this.$$.ctx[3]}set space_id(e){this.$$set({space_id:e}),F()}get pwa_enabled(){return this.$$.ctx[4]}set pwa_enabled(e){this.$$set({pwa_enabled:e}),F()}get allow_zoom(){return this.$$.ctx[1]}set allow_zoom(e){this.$$set({allow_zoom:e}),F()}get allow_video_trim(){return this.$$.ctx[2]}set allow_video_trim(e){this.$$set({allow_video_trim:e}),F()}}export{qt as default};
//# sourceMappingURL=Settings-BB3Zmx2X.js.map
