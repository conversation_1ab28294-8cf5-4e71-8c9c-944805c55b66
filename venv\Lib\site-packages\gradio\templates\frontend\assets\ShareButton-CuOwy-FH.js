import{I as u}from"./IconButton-C_HS7fTi.js";import{C as _}from"./Community-Dw1micSV.js";/* empty css                                                        */import"./index-DJ2rNx9E.js";import{S as h}from"./utils-BsGrhMNe.js";const{SvelteComponent:g,create_component:d,destroy_component:p,flush:c,init:v,mount_component:w,safe_not_equal:S,transition_in:b,transition_out:y}=window.__gradio__svelte__internal,{createEventDispatcher:C}=window.__gradio__svelte__internal;function I(a){let t,r;return t=new u({props:{Icon:_,label:a[2]("common.share"),pending:a[3]}}),t.$on("click",a[5]),{c(){d(t.$$.fragment)},m(e,o){w(t,e,o),r=!0},p(e,[o]){const i={};o&4&&(i.label=e[2]("common.share")),o&8&&(i.pending=e[3]),t.$set(i)},i(e){r||(b(t.$$.fragment,e),r=!0)},o(e){y(t.$$.fragment,e),r=!1},d(e){p(t,e)}}}function k(a,t,r){const e=C();let{formatter:o}=t,{value:i}=t,{i18n:m}=t,s=!1;const l=async()=>{try{r(3,s=!0);const n=await o(i);e("share",{description:n})}catch(n){console.error(n);let f=n instanceof h?n.message:"Share failed.";e("error",f)}finally{r(3,s=!1)}};return a.$$set=n=>{"formatter"in n&&r(0,o=n.formatter),"value"in n&&r(1,i=n.value),"i18n"in n&&r(2,m=n.i18n)},[o,i,m,s,e,l]}class j extends g{constructor(t){super(),v(this,t,k,I,S,{formatter:0,value:1,i18n:2})}get formatter(){return this.$$.ctx[0]}set formatter(t){this.$$set({formatter:t}),c()}get value(){return this.$$.ctx[1]}set value(t){this.$$set({value:t}),c()}get i18n(){return this.$$.ctx[2]}set i18n(t){this.$$set({i18n:t}),c()}}export{j as S};
//# sourceMappingURL=ShareButton-CuOwy-FH.js.map
