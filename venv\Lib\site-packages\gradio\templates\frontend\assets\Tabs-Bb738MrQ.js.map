{"version": 3, "file": "Tabs-Bb738MrQ.js", "sources": ["../../../../js/tabs/shared/OverflowIcon.svelte", "../../../../js/tabs/shared/Tabs.svelte"], "sourcesContent": ["<svg\n\twidth=\"16\"\n\theight=\"16\"\n\tviewBox=\"0 0 16 16\"\n\tfill=\"none\"\n\txmlns=\"http://www.w3.org/2000/svg\"\n>\n\t<circle cx=\"2.5\" cy=\"8\" r=\"1.5\" fill=\"currentColor\" />\n\t<circle cx=\"8\" cy=\"8\" r=\"1.5\" fill=\"currentColor\" />\n\t<circle cx=\"13.5\" cy=\"8\" r=\"1.5\" fill=\"currentColor\" />\n</svg>\n", "<script context=\"module\" lang=\"ts\">\n\texport const TABS = {};\n\n\texport interface Tab {\n\t\tlabel: string;\n\t\tid: string | number;\n\t\telem_id: string | undefined;\n\t\tvisible: boolean;\n\t\tinteractive: boolean;\n\t\tscale: number | null;\n\t}\n</script>\n\n<script lang=\"ts\">\n\timport { setContext, createEventDispatcher, tick, onMount } from \"svelte\";\n\timport OverflowIcon from \"./OverflowIcon.svelte\";\n\timport { writable } from \"svelte/store\";\n\timport type { SelectData } from \"@gradio/utils\";\n\n\texport let visible = true;\n\texport let elem_id = \"\";\n\texport let elem_classes: string[] = [];\n\texport let selected: number | string;\n\texport let initial_tabs: Tab[];\n\n\tlet tabs: (Tab | null)[] = [...initial_tabs];\n\tlet visible_tabs: (Tab | null)[] = [...initial_tabs];\n\tlet overflow_tabs: (Tab | null)[] = [];\n\tlet overflow_menu_open = false;\n\tlet overflow_menu: HTMLElement;\n\n\t$: has_tabs = tabs.length > 0;\n\n\tlet tab_nav_el: HTMLDivElement;\n\n\tconst selected_tab = writable<false | number | string>(\n\t\tselected || tabs[0]?.id || false\n\t);\n\tconst selected_tab_index = writable<number>(\n\t\ttabs.findIndex((t) => t?.id === selected) || 0\n\t);\n\tconst dispatch = createEventDispatcher<{\n\t\tchange: undefined;\n\t\tselect: SelectData;\n\t}>();\n\n\tlet is_overflowing = false;\n\tlet overflow_has_selected_tab = false;\n\tlet tab_els: Record<string | number, HTMLElement> = {};\n\n\tonMount(() => {\n\t\tconst observer = new IntersectionObserver((entries) => {\n\t\t\thandle_menu_overflow();\n\t\t});\n\t\tobserver.observe(tab_nav_el);\n\t});\n\n\tsetContext(TABS, {\n\t\tregister_tab: (tab: Tab, order: number) => {\n\t\t\ttabs[order] = tab;\n\n\t\t\tif ($selected_tab === false && tab.visible && tab.interactive) {\n\t\t\t\t$selected_tab = tab.id;\n\t\t\t\t$selected_tab_index = order;\n\t\t\t}\n\t\t\treturn order;\n\t\t},\n\t\tunregister_tab: (tab: Tab, order: number) => {\n\t\t\tif ($selected_tab === tab.id) {\n\t\t\t\t$selected_tab = tabs[0]?.id || false;\n\t\t\t}\n\t\t\ttabs[order] = null;\n\t\t},\n\t\tselected_tab,\n\t\tselected_tab_index\n\t});\n\n\tfunction change_tab(id: string | number | undefined): void {\n\t\tconst tab_to_activate = tabs.find((t) => t?.id === id);\n\t\tif (\n\t\t\tid !== undefined &&\n\t\t\ttab_to_activate &&\n\t\t\ttab_to_activate.interactive &&\n\t\t\ttab_to_activate.visible &&\n\t\t\t$selected_tab !== tab_to_activate.id\n\t\t) {\n\t\t\tselected = id;\n\t\t\t$selected_tab = id;\n\t\t\t$selected_tab_index = tabs.findIndex((t) => t?.id === id);\n\t\t\tdispatch(\"change\");\n\t\t\toverflow_menu_open = false;\n\t\t}\n\t}\n\n\t$: tabs, selected !== null && change_tab(selected);\n\t$: tabs, tab_nav_el, tab_els, handle_menu_overflow();\n\n\tfunction handle_outside_click(event: MouseEvent): void {\n\t\tif (\n\t\t\toverflow_menu_open &&\n\t\t\toverflow_menu &&\n\t\t\t!overflow_menu.contains(event.target as Node)\n\t\t) {\n\t\t\toverflow_menu_open = false;\n\t\t}\n\t}\n\n\tasync function handle_menu_overflow(): Promise<void> {\n\t\tif (!tab_nav_el) return;\n\n\t\tawait tick();\n\t\tconst tab_nav_size = tab_nav_el.getBoundingClientRect();\n\n\t\tlet max_width = tab_nav_size.width;\n\t\tconst tab_sizes = get_tab_sizes(tabs, tab_els);\n\t\tlet last_visible_index = 0;\n\t\tconst offset = tab_nav_size.left;\n\n\t\tfor (let i = tabs.length - 1; i >= 0; i--) {\n\t\t\tconst tab = tabs[i];\n\t\t\tif (!tab) continue;\n\t\t\tconst tab_rect = tab_sizes[tab.id];\n\t\t\tif (!tab_rect) continue;\n\t\t\tif (tab_rect.right - offset < max_width) {\n\t\t\t\tlast_visible_index = i;\n\t\t\t\tbreak;\n\t\t\t}\n\t\t}\n\n\t\toverflow_tabs = tabs.slice(last_visible_index + 1);\n\t\tvisible_tabs = tabs.slice(0, last_visible_index + 1);\n\n\t\toverflow_has_selected_tab = handle_overflow_has_selected_tab($selected_tab);\n\t\tis_overflowing = overflow_tabs.length > 0;\n\t}\n\n\t$: overflow_has_selected_tab =\n\t\thandle_overflow_has_selected_tab($selected_tab);\n\n\tfunction handle_overflow_has_selected_tab(\n\t\tselected_tab: number | string | false\n\t): boolean {\n\t\tif (selected_tab === false) return false;\n\t\treturn overflow_tabs.some((t) => t?.id === selected_tab);\n\t}\n\n\tfunction get_tab_sizes(\n\t\ttabs: (Tab | null)[],\n\t\ttab_els: Record<string | number, HTMLElement>\n\t): Record<string | number, DOMRect> {\n\t\tconst tab_sizes: Record<string | number, DOMRect> = {};\n\t\ttabs.forEach((tab) => {\n\t\t\tif (!tab) return;\n\t\t\ttab_sizes[tab.id] = tab_els[tab.id]?.getBoundingClientRect();\n\t\t});\n\t\treturn tab_sizes;\n\t}\n\n\t$: tab_scale =\n\t\ttabs[$selected_tab_index >= 0 ? $selected_tab_index : 0]?.scale;\n</script>\n\n<svelte:window\n\ton:resize={handle_menu_overflow}\n\ton:click={handle_outside_click}\n/>\n\n<div\n\tclass=\"tabs {elem_classes.join(' ')}\"\n\tclass:hide={!visible}\n\tid={elem_id}\n\tstyle:flex-grow={tab_scale}\n>\n\t{#if has_tabs}\n\t\t<div class=\"tab-wrapper\">\n\t\t\t<div class=\"tab-container visually-hidden\" aria-hidden=\"true\">\n\t\t\t\t{#each tabs as t, i}\n\t\t\t\t\t{#if t?.visible}\n\t\t\t\t\t\t<button bind:this={tab_els[t.id]}>\n\t\t\t\t\t\t\t{t?.label}\n\t\t\t\t\t\t</button>\n\t\t\t\t\t{/if}\n\t\t\t\t{/each}\n\t\t\t</div>\n\t\t\t<div class=\"tab-container\" bind:this={tab_nav_el} role=\"tablist\">\n\t\t\t\t{#each visible_tabs as t, i}\n\t\t\t\t\t{#if t?.visible}\n\t\t\t\t\t\t<button\n\t\t\t\t\t\t\trole=\"tab\"\n\t\t\t\t\t\t\tclass:selected={t.id === $selected_tab}\n\t\t\t\t\t\t\taria-selected={t.id === $selected_tab}\n\t\t\t\t\t\t\taria-controls={t.elem_id}\n\t\t\t\t\t\t\tdisabled={!t.interactive}\n\t\t\t\t\t\t\taria-disabled={!t.interactive}\n\t\t\t\t\t\t\tid={t.elem_id ? t.elem_id + \"-button\" : null}\n\t\t\t\t\t\t\tdata-tab-id={t.id}\n\t\t\t\t\t\t\ton:click={() => {\n\t\t\t\t\t\t\t\tif (t.id !== $selected_tab) {\n\t\t\t\t\t\t\t\t\tchange_tab(t.id);\n\t\t\t\t\t\t\t\t\tdispatch(\"select\", { value: t.label, index: i });\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}}\n\t\t\t\t\t\t>\n\t\t\t\t\t\t\t{t?.label !== undefined ? t?.label : \"Tab \" + (i + 1)}\n\t\t\t\t\t\t</button>\n\t\t\t\t\t{/if}\n\t\t\t\t{/each}\n\t\t\t</div>\n\t\t\t<span\n\t\t\t\tclass=\"overflow-menu\"\n\t\t\t\tclass:hide={!is_overflowing || !overflow_tabs.some((t) => t?.visible)}\n\t\t\t\tbind:this={overflow_menu}\n\t\t\t>\n\t\t\t\t<button\n\t\t\t\t\ton:click|stopPropagation={() =>\n\t\t\t\t\t\t(overflow_menu_open = !overflow_menu_open)}\n\t\t\t\t\tclass:overflow-item-selected={overflow_has_selected_tab}\n\t\t\t\t>\n\t\t\t\t\t<OverflowIcon />\n\t\t\t\t</button>\n\t\t\t\t<div class=\"overflow-dropdown\" class:hide={!overflow_menu_open}>\n\t\t\t\t\t{#each overflow_tabs as t}\n\t\t\t\t\t\t{#if t?.visible}\n\t\t\t\t\t\t\t<button\n\t\t\t\t\t\t\t\ton:click={() => change_tab(t?.id)}\n\t\t\t\t\t\t\t\tclass:selected={t?.id === $selected_tab}\n\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t{t?.label}\n\t\t\t\t\t\t\t</button>\n\t\t\t\t\t\t{/if}\n\t\t\t\t\t{/each}\n\t\t\t\t</div>\n\t\t\t</span>\n\t\t</div>\n\t{/if}\n\t<slot />\n</div>\n\n<style>\n\t.tabs {\n\t\tposition: relative;\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\tgap: var(--layout-gap);\n\t}\n\n\t.hide {\n\t\tdisplay: none;\n\t}\n\n\t.tab-wrapper {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: space-between;\n\t\tposition: relative;\n\t\theight: var(--size-8);\n\t\tpadding-bottom: var(--size-2);\n\t}\n\n\t.tab-container {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\twidth: 100%;\n\t\tposition: relative;\n\t\toverflow: hidden;\n\t\theight: var(--size-8);\n\t}\n\n\t.tab-container::after {\n\t\tcontent: \"\";\n\t\tposition: absolute;\n\t\tbottom: 0;\n\t\tleft: 0;\n\t\tright: 0;\n\t\theight: 1px;\n\t\tbackground-color: var(--border-color-primary);\n\t}\n\n\t.overflow-menu {\n\t\tflex-shrink: 0;\n\t\tmargin-left: var(--size-2);\n\t}\n\n\tbutton {\n\t\tmargin-bottom: 0;\n\t\tborder: none;\n\t\tborder-radius: 0;\n\t\tpadding: 0 var(--size-4);\n\t\tcolor: var(--body-text-color);\n\t\tfont-weight: var(--section-header-text-weight);\n\t\tfont-size: var(--section-header-text-size);\n\t\ttransition: background-color color 0.2s ease-out;\n\t\tbackground-color: transparent;\n\t\theight: 100%;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\twhite-space: nowrap;\n\t\tposition: relative;\n\t}\n\n\tbutton:disabled {\n\t\topacity: 0.5;\n\t\tcursor: not-allowed;\n\t}\n\n\tbutton:hover:not(:disabled):not(.selected) {\n\t\tbackground-color: var(--background-fill-secondary);\n\t\tcolor: var(--body-text-color);\n\t}\n\n\t.selected {\n\t\tbackground-color: transparent;\n\t\tcolor: var(--color-accent);\n\t\tposition: relative;\n\t}\n\n\t.selected::after {\n\t\tcontent: \"\";\n\t\tposition: absolute;\n\t\tbottom: 0;\n\t\tleft: 0;\n\t\twidth: 100%;\n\t\theight: 2px;\n\t\tbackground-color: var(--color-accent);\n\t\tanimation: fade-grow 0.2s ease-out forwards;\n\t\ttransform-origin: center;\n\t\tz-index: 1;\n\t}\n\n\t@keyframes fade-grow {\n\t\tfrom {\n\t\t\topacity: 0;\n\t\t\ttransform: scaleX(0.8);\n\t\t}\n\t\tto {\n\t\t\topacity: 1;\n\t\t\ttransform: scaleX(1);\n\t\t}\n\t}\n\n\t.overflow-dropdown {\n\t\tposition: absolute;\n\t\ttop: calc(100% + var(--size-2));\n\t\tright: 0;\n\t\tbackground-color: var(--background-fill-primary);\n\t\tborder: 1px solid var(--border-color-primary);\n\t\tborder-radius: var(--radius-sm);\n\t\tz-index: var(--layer-5);\n\t\tbox-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);\n\t\tpadding: var(--size-2);\n\t\tmin-width: 150px;\n\t\twidth: max-content;\n\t}\n\n\t.overflow-dropdown button {\n\t\tdisplay: block;\n\t\twidth: 100%;\n\t\ttext-align: left;\n\t\tpadding: var(--size-2);\n\t\twhite-space: nowrap;\n\t\toverflow: hidden;\n\t\ttext-overflow: ellipsis;\n\t}\n\n\t.overflow-menu > button {\n\t\tpadding: var(--size-1) var(--size-2);\n\t\tmin-width: auto;\n\t\tborder: 1px solid var(--border-color-primary);\n\t\tborder-radius: var(--radius-sm);\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t}\n\n\t.overflow-menu > button:hover {\n\t\tbackground-color: var(--background-fill-secondary);\n\t}\n\n\t.overflow-menu :global(svg) {\n\t\twidth: 16px;\n\t\theight: 16px;\n\t}\n\n\t.overflow-item-selected :global(svg) {\n\t\tcolor: var(--color-accent);\n\t}\n\n\t.visually-hidden {\n\t\tposition: absolute;\n\t\twidth: 1px;\n\t\theight: 1px;\n\t\tpadding: 0;\n\t\tmargin: -1px;\n\t\toverflow: hidden;\n\t\tclip: rect(0, 0, 0, 0);\n\t\twhite-space: nowrap;\n\t\tborder: 0;\n\t}\n</style>\n"], "names": ["insert", "target", "svg", "anchor", "append", "circle0", "circle1", "circle2", "createEventDispatcher", "tick", "ctx", "i", "func", "div3", "div0", "div1", "span", "button", "div2", "t0_value", "dirty", "set_data", "t0", "if_block", "create_if_block_3", "attr", "button_aria_controls_value", "button_disabled_value", "button_aria_disabled_value", "button_id_value", "button_data_tab_id_value", "create_if_block_2", "create_if_block_1", "create_if_block", "div", "TABS", "get_tab_sizes", "tabs", "tab_els", "tab_sizes", "tab", "visible", "$$props", "elem_id", "elem_classes", "selected", "initial_tabs", "visible_tabs", "overflow_tabs", "overflow_menu_open", "overflow_menu", "tab_nav_el", "selected_tab", "writable", "selected_tab_index", "t", "dispatch", "is_overflowing", "overflow_has_selected_tab", "onMount", "entries", "handle_menu_overflow", "setContext", "order", "$$invalidate", "$selected_tab", "set_store_value", "$selected_tab_index", "change_tab", "id", "tab_to_activate", "handle_outside_click", "event", "tab_nav_size", "max_width", "last_visible_index", "offset", "tab_rect", "handle_overflow_has_selected_tab", "$$value", "click_handler_1", "has_tabs", "tab_scale"], "mappings": "+mBAAAA,GAUKC,EAAAC,EAAAC,CAAA,EAHJC,EAAqDF,EAAAG,CAAA,EACrDD,EAAmDF,EAAAI,CAAA,EACnDF,EAAsDF,EAAAK,CAAA,opBCKjC,sBAAAC,GAAA,KAAAC,aAA4C,EAAA,OAAA,+QAkKvDC,EAAI,CAAA,CAAA,uBAAT,OAAIC,GAAA,6BASCD,EAAY,CAAA,CAAA,uBAAjB,OAAIC,GAAA,0CAoCED,EAAa,CAAA,CAAA,uBAAlB,OAAIC,GAAA,4cALwBD,EAAyB,EAAA,CAAA,8DAIZA,EAAkB,CAAA,CAAA,0DAVjDA,EAAc,EAAA,GAAA,CAAKA,EAAa,CAAA,EAAC,KAAIE,EAAA,CAAA,oDApCpDZ,EA2DKC,EAAAY,EAAAV,CAAA,EA1DJC,EAQKS,EAAAC,CAAA,0DACLV,EAuBKS,EAAAE,CAAA,mEACLX,EAwBMS,EAAAG,CAAA,EAnBLZ,EAMQY,EAAAC,CAAA,sBACRb,EAWKY,EAAAE,CAAA,4HAvDER,EAAI,CAAA,CAAA,oBAAT,OAAIC,GAAA,EAAA,mHAAJ,2BASKD,EAAY,CAAA,CAAA,oBAAjB,OAAIC,GAAA,EAAA,mHAAJ,wDA+B6BD,EAAyB,EAAA,CAAA,mBAKhDA,EAAa,CAAA,CAAA,oBAAlB,OAAIC,GAAA,EAAA,mHAAJ,mCADyCD,EAAkB,CAAA,CAAA,+BAVjDA,EAAc,EAAA,GAAA,CAAKA,EAAa,CAAA,EAAC,KAAIE,EAAA,CAAA,oKA/B9CO,EAAAT,OAAG,MAAK,uIADVV,EAEQC,EAAAgB,EAAAd,CAAA,gCADNiB,EAAA,CAAA,EAAA,GAAAD,KAAAA,EAAAT,OAAG,MAAK,KAAAW,EAAAC,EAAAH,CAAA,wEAFNI,EAAAb,OAAG,SAAOc,GAAAd,CAAA,iEAAVA,OAAG,4HA0BLA,EAAC,EAAA,GAAE,QAAU,OAAYA,OAAG,MAAQ,QAAUA,EAAC,EAAA,EAAG,IAAC,6IAbrCA,EAAC,EAAA,EAAC,KAAOA,EAAa,CAAA,CAAA,EACtBe,EAAAR,EAAA,gBAAAS,EAAAhB,MAAE,OAAO,EACbO,EAAA,SAAAU,EAAA,CAAAjB,MAAE,YACGe,EAAAR,EAAA,gBAAAW,EAAA,CAAAlB,MAAE,WAAW,EACzBe,EAAAR,EAAA,KAAAY,EAAAnB,MAAE,QAAUA,EAAC,EAAA,EAAC,QAAU,UAAY,IAAI,EAC/Be,EAAAR,EAAA,cAAAa,EAAApB,MAAE,EAAE,+CANDA,EAAC,EAAA,EAAC,KAAOA,EAAa,CAAA,CAAA,UAFvCV,EAiBQC,EAAAgB,EAAAd,CAAA,yEADNO,EAAC,EAAA,GAAE,QAAU,OAAYA,OAAG,MAAQ,QAAUA,EAAC,EAAA,EAAG,IAAC,KAAAW,EAAAC,EAAAH,CAAA,mBAbrCT,EAAC,EAAA,EAAC,KAAOA,EAAa,CAAA,2BACtBU,EAAA,CAAA,EAAA,KAAAM,KAAAA,EAAAhB,MAAE,iCACNU,EAAA,CAAA,EAAA,KAAAO,KAAAA,EAAA,CAAAjB,MAAE,6BACGU,EAAA,CAAA,EAAA,KAAAQ,KAAAA,EAAA,CAAAlB,MAAE,qCACdU,EAAA,CAAA,EAAA,KAAAS,KAAAA,EAAAnB,MAAE,QAAUA,EAAC,EAAA,EAAC,QAAU,UAAY,mBAC3BU,EAAA,CAAA,EAAA,KAAAU,KAAAA,EAAApB,MAAE,mDANCA,EAAC,EAAA,EAAC,KAAOA,EAAa,CAAA,CAAA,gDAHnCa,EAAAb,OAAG,SAAOqB,GAAArB,CAAA,iEAAVA,OAAG,yHAyCJS,EAAAT,OAAG,MAAK,gIAFOA,EAAC,EAAA,GAAE,KAAOA,EAAa,CAAA,CAAA,UAFxCV,EAKQC,EAAAgB,EAAAd,CAAA,uDADNiB,EAAA,CAAA,EAAA,KAAAD,KAAAA,EAAAT,OAAG,MAAK,KAAAW,EAAAC,EAAAH,CAAA,2BAFOT,EAAC,EAAA,GAAE,KAAOA,EAAa,CAAA,CAAA,gDAHpCa,EAAAb,OAAG,SAAOsB,GAAAtB,CAAA,iEAAVA,OAAG,qIAjDRA,EAAQ,EAAA,GAAAuB,GAAAvB,CAAA,iHALAA,EAAY,CAAA,EAAC,KAAK,GAAG,EAAA,iBAAA,WAE9BA,EAAO,CAAA,CAAA,cADEA,EAAO,CAAA,CAAA,mBAEHA,EAAS,EAAA,CAAA,UAJ3BV,EAqEKC,EAAAiC,EAAA/B,CAAA,qEAzEOO,EAAoB,EAAA,CAAA,mBACrBA,EAAoB,EAAA,CAAA,iBASzBA,EAAQ,EAAA,oNALAA,EAAY,CAAA,EAAC,KAAK,GAAG,EAAA,2DAE9BA,EAAO,CAAA,CAAA,4BADEA,EAAO,CAAA,CAAA,8BAEHA,EAAS,EAAA,CAAA,mGA1Kb,MAAAyB,GAAA,CAAA,EAiJJ,SAAAC,GACRC,EACAC,EAAAA,CAEM,MAAAC,EAAA,CAAA,EACNF,OAAAA,EAAK,QAASG,GAAA,CACRA,IACLD,EAAUC,EAAI,EAAE,EAAIF,EAAQE,EAAI,EAAE,GAAG,2BAE/BD,WAuD+C,GAAM,GAAG,mEA/LrD,QAAAE,EAAU,EAAA,EAAAC,GACV,QAAAC,EAAU,EAAA,EAAAD,EACV,CAAA,aAAAE,EAAA,EAAA,EAAAF,EACA,CAAA,SAAAG,CAAA,EAAAH,EACA,CAAA,aAAAI,CAAA,EAAAJ,EAEPL,EAAA,CAAA,GAA2BS,CAAY,EACvCC,EAAA,CAAA,GAAmCD,CAAY,EAC/CE,EAAA,CAAA,EACAC,EAAqB,GACrBC,EAIAC,EAEE,MAAAC,EAAeC,GACpBR,GAAYR,EAAK,CAAC,GAAG,IAAM,EAAA,sBAEtB,MAAAiB,EAAqBD,GAC1BhB,EAAK,UAAWkB,GAAMA,GAAG,KAAOV,CAAQ,GAAK,CAAA,6BAExCW,EAAWhD,SAKbiD,EAAiB,GACjBC,EAA4B,GAC5BpB,EAAA,CAAA,EAEJqB,GAAA,IAAA,CACO,IAAe,qBAAsBC,GAAA,CAC1CC,MAEQ,QAAQV,CAAU,IAG5BW,GAAW3B,GAAA,CACV,aAAA,CAAeK,EAAUuB,KACxBC,EAAA,EAAA3B,EAAK0B,CAAK,EAAIvB,EAAAH,CAAA,EAEV4B,IAAkB,IAASzB,EAAI,SAAWA,EAAI,cACjD0B,EAAAd,EAAAa,EAAgBzB,EAAI,GAAAyB,CAAA,MACpBE,EAAsBJ,EAAAI,CAAA,GAEhBJ,GAER,eAAA,CAAiBvB,EAAUuB,IAAA,CACtBE,IAAkBzB,EAAI,QACzByB,EAAgB5B,EAAK,CAAC,GAAG,IAAM,GAAA4B,CAAA,EAEhCD,EAAA,EAAA3B,EAAK0B,CAAK,EAAI,KAAA1B,CAAA,GAEf,aAAAe,EACA,mBAAAE,aAGQc,EAAWC,EAAA,CACb,MAAAC,EAAkBjC,EAAK,KAAMkB,GAAMA,GAAG,KAAOc,CAAE,EAEpDA,IAAA,QACAC,GACAA,EAAgB,aAChBA,EAAgB,SAChBL,IAAkBK,EAAgB,UAElCzB,EAAWwB,CAAA,MACXJ,EAAgBI,EAAAJ,CAAA,EAChBC,EAAAZ,EAAAa,EAAsB9B,EAAK,UAAWkB,GAAMA,GAAG,KAAOc,CAAE,EAAAF,CAAA,EACxDX,EAAS,QAAQ,MACjBP,EAAqB,EAAA,YAOdsB,GAAqBC,EAAA,CAE5BvB,GACAC,GACC,CAAAA,EAAc,SAASsB,EAAM,MAAc,OAE5CvB,EAAqB,EAAA,EAIR,eAAAY,GAAA,CACT,GAAA,CAAAV,EAAA,OAEC,MAAA1C,GAAA,EACA,MAAAgE,EAAetB,EAAW,wBAE5B,IAAAuB,EAAYD,EAAa,YACvBlC,EAAYH,GAAcC,EAAMC,CAAO,MACzCqC,EAAqB,EACnB,MAAAC,GAASH,EAAa,KAEnB,QAAA9D,EAAI0B,EAAK,OAAS,EAAG1B,GAAK,EAAGA,IAAA,CAC/B,MAAA6B,GAAMH,EAAK1B,CAAC,EACb,GAAA,CAAA6B,GAAA,eACCqC,GAAWtC,EAAUC,GAAI,EAAE,EAC5B,GAAAqC,IACDA,GAAS,MAAQD,GAASF,EAAA,CAC7BC,EAAqBhE,aAKvBqC,EAAgBX,EAAK,MAAMsC,EAAqB,CAAC,CAAA,EACjDX,EAAA,EAAAjB,EAAeV,EAAK,MAAM,EAAGsC,EAAqB,CAAC,CAAA,EAEnDX,EAAA,GAAAN,EAA4BoB,GAAiCb,CAAa,CAAA,EAC1ED,EAAA,GAAAP,EAAiBT,EAAc,OAAS,CAAA,WAMhC8B,GACR1B,EAAAA,CAEIA,OAAAA,IAAiB,GAAc,GAC5BJ,EAAc,KAAMO,GAAMA,GAAG,KAAOH,CAAY,8CAmChCd,EAAQiB,EAAE,EAAE,EAAAwB,4BAmBzBxB,EAAE,KAAOU,IACZG,EAAWb,EAAE,EAAE,EACfC,EAAS,SAAQ,CAAI,MAAOD,EAAE,MAAO,MAAO5C,CAAC,CAAA,8CAfbwC,EAAU4B,WA+B5C,MAAAC,GAAA,IAAAhB,EAAA,EAAAf,GAAsBA,CAAkB,QASvBmB,EAAWb,GAAG,EAAE,4CAbzBL,EAAa6B,2RApLxBf,EAAA,GAAAiB,EAAW5C,EAAK,OAAS,CAAA,yBA+DnBQ,IAAa,MAAQuB,EAAWvB,CAAQ,oBACnBgB,EAAA,oBAyC3BG,EAAA,GAAAN,EACFoB,GAAiCb,CAAa,CAAA,yBAqB/CD,EAAA,GAAGkB,EACF7C,EAAK8B,GAAuB,EAAIA,EAAsB,CAAC,GAAG,KAAA"}