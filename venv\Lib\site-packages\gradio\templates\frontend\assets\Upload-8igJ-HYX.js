import{n as Te}from"./index-DJ2rNx9E.js";/* empty css                                             */const{SvelteComponent:Ue,append:Fe,attr:q,detach:He,init:Ie,insert:Me,noop:X,safe_not_equal:Oe,svg_element:fe}=window.__gradio__svelte__internal;function je(t){let e,n;return{c(){e=fe("svg"),n=fe("path"),q(n,"fill","currentColor"),q(n,"d","M200 32h-36.26a47.92 47.92 0 0 0-71.48 0H56a16 16 0 0 0-16 16v168a16 16 0 0 0 16 16h144a16 16 0 0 0 16-16V48a16 16 0 0 0-16-16m-72 0a32 32 0 0 1 32 32H96a32 32 0 0 1 32-32m72 184H56V48h26.75A47.9 47.9 0 0 0 80 64v8a8 8 0 0 0 8 8h80a8 8 0 0 0 8-8v-8a47.9 47.9 0 0 0-2.75-16H200Z"),q(e,"xmlns","http://www.w3.org/2000/svg"),q(e,"width","100%"),q(e,"height","100%"),q(e,"viewBox","0 0 256 256")},m(l,i){Me(l,e,i),Fe(e,n)},p:X,i:X,o:X,d(l){l&&He(e)}}}class Et extends Ue{constructor(e){super(),Ie(this,e,null,je,Oe,{})}}const{SvelteComponent:Ne,append:Y,attr:y,detach:Be,init:Ve,insert:Je,noop:x,safe_not_equal:Re,svg_element:M}=window.__gradio__svelte__internal;function Ze(t){let e,n,l,i;return{c(){e=M("svg"),n=M("path"),l=M("polyline"),i=M("line"),y(n,"d","M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"),y(l,"points","17 8 12 3 7 8"),y(i,"x1","12"),y(i,"y1","3"),y(i,"x2","12"),y(i,"y2","15"),y(e,"xmlns","http://www.w3.org/2000/svg"),y(e,"width","90%"),y(e,"height","90%"),y(e,"viewBox","0 0 24 24"),y(e,"fill","none"),y(e,"stroke","currentColor"),y(e,"stroke-width","2"),y(e,"stroke-linecap","round"),y(e,"stroke-linejoin","round"),y(e,"class","feather feather-upload")},m(o,d){Je(o,e,d),Y(e,n),Y(e,l),Y(e,i)},p:x,i:x,o:x,d(o){o&&Be(e)}}}let Lt=class extends Ne{constructor(e){super(),Ve(this,e,null,Ze,Re,{})}};const{SvelteComponent:Ge,append:k,attr:P,detach:ge,element:W,flush:O,init:Ke,insert:me,noop:ue,safe_not_equal:Qe,set_data:j,set_style:$,space:ee,text:T,toggle_class:de}=window.__gradio__svelte__internal,{onMount:Xe,createEventDispatcher:Ye,onDestroy:xe}=window.__gradio__svelte__internal;function _e(t){let e,n,l,i,o=F(t[2])+"",d,_,s,u,a=t[2].orig_name+"",f;return{c(){e=W("div"),n=W("span"),l=W("div"),i=W("progress"),d=T(o),s=ee(),u=W("span"),f=T(a),$(i,"visibility","hidden"),$(i,"height","0"),$(i,"width","0"),i.value=_=F(t[2]),P(i,"max","100"),P(i,"class","svelte-1vsfomn"),P(l,"class","progress-bar svelte-1vsfomn"),P(u,"class","file-name svelte-1vsfomn"),P(e,"class","file svelte-1vsfomn")},m(c,m){me(c,e,m),k(e,n),k(n,l),k(l,i),k(i,d),k(e,s),k(e,u),k(u,f)},p(c,m){m&4&&o!==(o=F(c[2])+"")&&j(d,o),m&4&&_!==(_=F(c[2]))&&(i.value=_),m&4&&a!==(a=c[2].orig_name+"")&&j(f,a)},d(c){c&&ge(e)}}}function $e(t){let e,n,l,i=t[0].length+"",o,d,_=t[0].length>1?"files":"file",s,u,a,f=t[2]&&_e(t);return{c(){e=W("div"),n=W("span"),l=T("Uploading "),o=T(i),d=ee(),s=T(_),u=T("..."),a=ee(),f&&f.c(),P(n,"class","uploading svelte-1vsfomn"),P(e,"class","wrap svelte-1vsfomn"),de(e,"progress",t[1])},m(c,m){me(c,e,m),k(e,n),k(n,l),k(n,o),k(n,d),k(n,s),k(n,u),k(e,a),f&&f.m(e,null)},p(c,[m]){m&1&&i!==(i=c[0].length+"")&&j(o,i),m&1&&_!==(_=c[0].length>1?"files":"file")&&j(s,_),c[2]?f?f.p(c,m):(f=_e(c),f.c(),f.m(e,null)):f&&(f.d(1),f=null),m&2&&de(e,"progress",c[1])},i:ue,o:ue,d(c){c&&ge(e),f&&f.d()}}}function F(t){return t.progress*100/(t.size||0)||0}function et(t){let e=0;return t.forEach(n=>{e+=F(n)}),document.documentElement.style.setProperty("--upload-progress-width",(e/t.length).toFixed(2)+"%"),e/t.length}function tt(t,e,n){let{upload_id:l}=e,{root:i}=e,{files:o}=e,{stream_handler:d}=e,_,s=!1,u,a,f=o.map(b=>({...b,progress:0}));const c=Ye();function m(b,L){n(0,f=f.map(S=>(S.orig_name===b&&(S.progress+=L),S)))}return Xe(async()=>{if(_=await d(new URL(`${i}/gradio_api/upload_progress?upload_id=${l}`)),_==null)throw new Error("Event source is not defined");_.onmessage=async function(b){const L=JSON.parse(b.data);s||n(1,s=!0),L.msg==="done"?(_?.close(),c("done")):(n(7,u=L),m(L.orig_name,L.chunk_size))}}),xe(()=>{(_!=null||_!=null)&&_.close()}),t.$$set=b=>{"upload_id"in b&&n(3,l=b.upload_id),"root"in b&&n(4,i=b.root),"files"in b&&n(5,o=b.files),"stream_handler"in b&&n(6,d=b.stream_handler)},t.$$.update=()=>{t.$$.dirty&1&&et(f),t.$$.dirty&129&&n(2,a=u||f[0])},[f,s,a,l,i,o,d,u]}class nt extends Ge{constructor(e){super(),Ke(this,e,tt,$e,Qe,{upload_id:3,root:4,files:5,stream_handler:6})}get upload_id(){return this.$$.ctx[3]}set upload_id(e){this.$$set({upload_id:e}),O()}get root(){return this.$$.ctx[4]}set root(e){this.$$set({root:e}),O()}get files(){return this.$$.ctx[5]}set files(e){this.$$set({files:e}),O()}get stream_handler(){return this.$$.ctx[6]}set stream_handler(e){this.$$set({stream_handler:e}),O()}}function it(){let t,e;return{drag(n,l={}){e=l;function i(){t=document.createElement("input"),t.type="file",t.style.display="none",t.setAttribute("aria-label","File upload"),t.setAttribute("data-testid","file-upload");const f=Array.isArray(e.accepted_types)?e.accepted_types.join(","):e.accepted_types||void 0;f&&(t.accept=f),t.multiple=e.mode==="multiple"||!1,e.mode==="directory"&&(t.webkitdirectory=!0,t.setAttribute("directory",""),t.setAttribute("mozdirectory","")),n.appendChild(t)}i();function o(f){f.preventDefault(),f.stopPropagation()}function d(f){f.preventDefault(),f.stopPropagation(),e.on_drag_change?.(!0)}function _(f){f.preventDefault(),f.stopPropagation(),e.on_drag_change?.(!1)}function s(f){if(f.preventDefault(),f.stopPropagation(),e.on_drag_change?.(!1),!f.dataTransfer?.files)return;const c=Array.from(f.dataTransfer.files);c.length>0&&e.on_files?.(c)}function u(){e.disable_click||(t.value="",t.click())}function a(){if(t.files){const f=Array.from(t.files);f.length>0&&e.on_files?.(f)}}return n.addEventListener("drag",o),n.addEventListener("dragstart",o),n.addEventListener("dragend",o),n.addEventListener("dragover",o),n.addEventListener("dragenter",d),n.addEventListener("dragleave",_),n.addEventListener("drop",s),n.addEventListener("click",u),t.addEventListener("change",a),{update(f){e=f,t.remove(),i(),t.addEventListener("change",a)},destroy(){n.removeEventListener("drag",o),n.removeEventListener("dragstart",o),n.removeEventListener("dragend",o),n.removeEventListener("dragover",o),n.removeEventListener("dragenter",d),n.removeEventListener("dragleave",_),n.removeEventListener("drop",s),n.removeEventListener("click",u),t.removeEventListener("change",a),t.remove()}}},open_file_upload(){t&&(t.value="",t.click())}}}const{SvelteComponent:lt,action_destroyer:rt,attr:E,check_outros:pe,create_component:st,create_slot:be,destroy_component:at,detach:B,element:ve,empty:we,flush:p,get_all_dirty_from_scope:ye,get_slot_changes:ke,group_outros:Ee,init:ot,insert:V,is_function:ft,listen:ut,mount_component:dt,safe_not_equal:_t,set_style:N,toggle_class:g,transition_in:C,transition_out:z,update_slot_base:Le}=window.__gradio__svelte__internal,{createEventDispatcher:ct,tick:ht,getContext:Ct}=window.__gradio__svelte__internal;function gt(t){let e,n,l,i,o,d,_;const s=t[30].default,u=be(s,t,t[29],null);return{c(){e=ve("button"),u&&u.c(),E(e,"tabindex",n=t[9]?-1:0),E(e,"aria-label",l=t[14]||"Click to upload or drop files"),E(e,"aria-dropeffect","copy"),E(e,"class","svelte-edrmkl"),g(e,"hidden",t[9]),g(e,"center",t[4]),g(e,"boundedheight",t[3]),g(e,"flex",t[5]),g(e,"disable_click",t[7]),g(e,"icon-mode",t[12]),N(e,"height",t[12]?"":t[13]?typeof t[13]=="number"?t[13]+"px":t[13]:"100%")},m(a,f){V(a,e,f),u&&u.m(e,null),o=!0,d||(_=rt(i=t[19].call(null,e,{on_drag_change:he,on_files:t[31],accepted_types:t[18],mode:t[6],disable_click:t[7]})),d=!0)},p(a,f){u&&u.p&&(!o||f[0]&536870912)&&Le(u,s,a,a[29],o?ke(s,a[29],f,null):ye(a[29]),null),(!o||f[0]&512&&n!==(n=a[9]?-1:0))&&E(e,"tabindex",n),(!o||f[0]&16384&&l!==(l=a[14]||"Click to upload or drop files"))&&E(e,"aria-label",l),i&&ft(i.update)&&f[0]&262336&&i.update.call(null,{on_drag_change:he,on_files:a[31],accepted_types:a[18],mode:a[6],disable_click:a[7]}),(!o||f[0]&512)&&g(e,"hidden",a[9]),(!o||f[0]&16)&&g(e,"center",a[4]),(!o||f[0]&8)&&g(e,"boundedheight",a[3]),(!o||f[0]&32)&&g(e,"flex",a[5]),(!o||f[0]&128)&&g(e,"disable_click",a[7]),(!o||f[0]&4096)&&g(e,"icon-mode",a[12]),f[0]&12288&&N(e,"height",a[12]?"":a[13]?typeof a[13]=="number"?a[13]+"px":a[13]:"100%")},i(a){o||(C(u,a),o=!0)},o(a){z(u,a),o=!1},d(a){a&&B(e),u&&u.d(a),d=!1,_()}}}function mt(t){let e,n,l=!t[9]&&ce(t);return{c(){l&&l.c(),e=we()},m(i,o){l&&l.m(i,o),V(i,e,o),n=!0},p(i,o){i[9]?l&&(Ee(),z(l,1,1,()=>{l=null}),pe()):l?(l.p(i,o),o[0]&512&&C(l,1)):(l=ce(i),l.c(),C(l,1),l.m(e.parentNode,e))},i(i){n||(C(l),n=!0)},o(i){z(l),n=!1},d(i){i&&B(e),l&&l.d(i)}}}function pt(t){let e,n,l,i,o,d;const _=t[30].default,s=be(_,t,t[29],null);return{c(){e=ve("button"),s&&s.c(),E(e,"tabindex",n=t[9]?-1:0),E(e,"aria-label",l=t[14]||"Paste from clipboard"),E(e,"class","svelte-edrmkl"),g(e,"hidden",t[9]),g(e,"center",t[4]),g(e,"boundedheight",t[3]),g(e,"flex",t[5]),g(e,"icon-mode",t[12]),N(e,"height",t[12]?"":t[13]?typeof t[13]=="number"?t[13]+"px":t[13]:"100%")},m(u,a){V(u,e,a),s&&s.m(e,null),i=!0,o||(d=ut(e,"click",t[15]),o=!0)},p(u,a){s&&s.p&&(!i||a[0]&536870912)&&Le(s,_,u,u[29],i?ke(_,u[29],a,null):ye(u[29]),null),(!i||a[0]&512&&n!==(n=u[9]?-1:0))&&E(e,"tabindex",n),(!i||a[0]&16384&&l!==(l=u[14]||"Paste from clipboard"))&&E(e,"aria-label",l),(!i||a[0]&512)&&g(e,"hidden",u[9]),(!i||a[0]&16)&&g(e,"center",u[4]),(!i||a[0]&8)&&g(e,"boundedheight",u[3]),(!i||a[0]&32)&&g(e,"flex",u[5]),(!i||a[0]&4096)&&g(e,"icon-mode",u[12]),a[0]&12288&&N(e,"height",u[12]?"":u[13]?typeof u[13]=="number"?u[13]+"px":u[13]:"100%")},i(u){i||(C(s,u),i=!0)},o(u){z(s,u),i=!1},d(u){u&&B(e),s&&s.d(u),o=!1,d()}}}function ce(t){let e,n;return e=new nt({props:{root:t[8],upload_id:t[16],files:t[17],stream_handler:t[11]}}),{c(){st(e.$$.fragment)},m(l,i){dt(e,l,i),n=!0},p(l,i){const o={};i[0]&256&&(o.root=l[8]),i[0]&65536&&(o.upload_id=l[16]),i[0]&131072&&(o.files=l[17]),i[0]&2048&&(o.stream_handler=l[11]),e.$set(o)},i(l){n||(C(e.$$.fragment,l),n=!0)},o(l){z(e.$$.fragment,l),n=!1},d(l){at(e,l)}}}function bt(t){let e,n,l,i;const o=[pt,mt,gt],d=[];function _(s,u){return s[0]==="clipboard"?0:s[2]&&s[10]?1:2}return e=_(t),n=d[e]=o[e](t),{c(){n.c(),l=we()},m(s,u){d[e].m(s,u),V(s,l,u),i=!0},p(s,u){let a=e;e=_(s),e===a?d[e].p(s,u):(Ee(),z(d[a],1,1,()=>{d[a]=null}),pe(),n=d[e],n?n.p(s,u):(n=d[e]=o[e](s),n.c()),C(n,1),n.m(l.parentNode,l))},i(s){i||(C(n),i=!0)},o(s){z(n),i=!1},d(s){s&&B(l),d[e].d(s)}}}function vt(t,e,n){if(!t||t==="*"||t==="file/*"||Array.isArray(t)&&t.some(i=>i==="*"||i==="file/*"))return!0;let l;if(typeof t=="string")l=t.split(",").map(i=>i.trim());else if(Array.isArray(t))l=t;else return!1;return l.includes(e)||l.some(i=>{const[o]=i.split("/").map(d=>d.trim());return i.endsWith("/*")&&n.startsWith(o+"/")})}const he=t=>t=t;function wt(t,e,n){let l,{$$slots:i={},$$scope:o}=e;const{drag:d,open_file_upload:_}=it();let{filetype:s=null}=e,{dragging:u=!1}=e,{boundedheight:a=!0}=e,{center:f=!0}=e,{flex:c=!0}=e,{file_count:m="single"}=e,{disable_click:b=!1}=e,{root:L}=e,{hidden:S=!1}=e,{format:H="file"}=e,{uploading:U=!1}=e,{show_progress:te=!0}=e,{max_file_size:J=null}=e,{upload:R}=e,{stream_handler:ne}=e,{icon_upload:ie=!1}=e,{height:le=void 0}=e,{aria_label:re=void 0}=e;function Ae(){_()}let Z,G,D,se=null;const Ce=()=>{if(typeof navigator<"u"){const r=navigator.userAgent.toLowerCase();return r.indexOf("iphone")>-1||r.indexOf("ipad")>-1}return!1},A=ct(),Pe=["image","video","audio","text","file"],K=r=>l&&r.startsWith(".")?(se=!0,r):l&&r.includes("file/*")?"*":r.startsWith(".")||r.endsWith("/*")?r:Pe.includes(r)?r+"/*":"."+r;function We(){navigator.clipboard.read().then(async r=>{for(let h=0;h<r.length;h++){const v=r[h].types.find(w=>w.startsWith("image/"));if(v){r[h].getType(v).then(async w=>{const Q=new File([w],`clipboard.${v.replace("image/","")}`);await I([Q])});break}}})}function ze(){_()}async function Se(r){await ht(),n(16,Z=Math.random().toString(36).substring(2,15)),n(2,U=!0);try{const h=await R(r,L,Z,J??1/0);return A("load",m==="single"?h?.[0]:h),n(2,U=!1),h||[]}catch(h){return A("error",h.message),n(2,U=!1),[]}}async function I(r){if(!r.length)return;let h=r.map(v=>new File([v],v instanceof File?v.name:"file",{type:v.type}));return l&&se&&(h=h.filter(v=>ae(v)?!0:(A("error",`Invalid file type: ${v.name}. Only ${s} allowed.`),!1)),h.length===0)?[]:(n(17,G=await Te(h)),await Se(G))}function ae(r){return s?(Array.isArray(s)?s:[s]).some(v=>{const w=K(v);if(w.startsWith("."))return r.name.toLowerCase().endsWith(w.toLowerCase());if(w==="*")return!0;if(w.endsWith("/*")){const[Q]=w.split("/");return r.type.startsWith(Q+"/")}return r.type===w}):!0}async function oe(r){const h=r.filter(v=>{const w="."+v.name.split(".").pop();return w&&vt(D,w,v.type)||(w&&Array.isArray(s)?s.includes(w):w===s)?!0:(A("error",`Invalid file type only ${s} allowed.`),!1)});if(H!="blob")await I(h);else{if(m==="single"){A("load",h[0]);return}A("load",h)}}async function De(r){if(n(1,u=!1),!r.dataTransfer?.files)return;const h=Array.from(r.dataTransfer.files).filter(ae);if(H!="blob")await I(h);else{if(m==="single"){A("load",h[0]);return}A("load",h)}}const qe=r=>oe(r);return t.$$set=r=>{"filetype"in r&&n(0,s=r.filetype),"dragging"in r&&n(1,u=r.dragging),"boundedheight"in r&&n(3,a=r.boundedheight),"center"in r&&n(4,f=r.center),"flex"in r&&n(5,c=r.flex),"file_count"in r&&n(6,m=r.file_count),"disable_click"in r&&n(7,b=r.disable_click),"root"in r&&n(8,L=r.root),"hidden"in r&&n(9,S=r.hidden),"format"in r&&n(21,H=r.format),"uploading"in r&&n(2,U=r.uploading),"show_progress"in r&&n(10,te=r.show_progress),"max_file_size"in r&&n(22,J=r.max_file_size),"upload"in r&&n(23,R=r.upload),"stream_handler"in r&&n(11,ne=r.stream_handler),"icon_upload"in r&&n(12,ie=r.icon_upload),"height"in r&&n(13,le=r.height),"aria_label"in r&&n(14,re=r.aria_label),"$$scope"in r&&n(29,o=r.$$scope)},t.$$.update=()=>{t.$$.dirty[0]&268435457&&(s==null?n(18,D=null):typeof s=="string"?n(18,D=K(s)):l&&s.includes("file/*")?n(18,D="*"):(n(0,s=s.map(K)),n(18,D=s.join(", "))))},n(28,l=Ce()),[s,u,U,a,f,c,m,b,L,S,te,ne,ie,le,re,We,Z,G,D,d,oe,H,J,R,Ae,ze,I,De,l,o,i,qe]}class Pt extends lt{constructor(e){super(),ot(this,e,wt,bt,_t,{filetype:0,dragging:1,boundedheight:3,center:4,flex:5,file_count:6,disable_click:7,root:8,hidden:9,format:21,uploading:2,show_progress:10,max_file_size:22,upload:23,stream_handler:11,icon_upload:12,height:13,aria_label:14,open_upload:24,paste_clipboard:15,open_file_upload:25,load_files:26,load_files_from_drop:27},null,[-1,-1])}get filetype(){return this.$$.ctx[0]}set filetype(e){this.$$set({filetype:e}),p()}get dragging(){return this.$$.ctx[1]}set dragging(e){this.$$set({dragging:e}),p()}get boundedheight(){return this.$$.ctx[3]}set boundedheight(e){this.$$set({boundedheight:e}),p()}get center(){return this.$$.ctx[4]}set center(e){this.$$set({center:e}),p()}get flex(){return this.$$.ctx[5]}set flex(e){this.$$set({flex:e}),p()}get file_count(){return this.$$.ctx[6]}set file_count(e){this.$$set({file_count:e}),p()}get disable_click(){return this.$$.ctx[7]}set disable_click(e){this.$$set({disable_click:e}),p()}get root(){return this.$$.ctx[8]}set root(e){this.$$set({root:e}),p()}get hidden(){return this.$$.ctx[9]}set hidden(e){this.$$set({hidden:e}),p()}get format(){return this.$$.ctx[21]}set format(e){this.$$set({format:e}),p()}get uploading(){return this.$$.ctx[2]}set uploading(e){this.$$set({uploading:e}),p()}get show_progress(){return this.$$.ctx[10]}set show_progress(e){this.$$set({show_progress:e}),p()}get max_file_size(){return this.$$.ctx[22]}set max_file_size(e){this.$$set({max_file_size:e}),p()}get upload(){return this.$$.ctx[23]}set upload(e){this.$$set({upload:e}),p()}get stream_handler(){return this.$$.ctx[11]}set stream_handler(e){this.$$set({stream_handler:e}),p()}get icon_upload(){return this.$$.ctx[12]}set icon_upload(e){this.$$set({icon_upload:e}),p()}get height(){return this.$$.ctx[13]}set height(e){this.$$set({height:e}),p()}get aria_label(){return this.$$.ctx[14]}set aria_label(e){this.$$set({aria_label:e}),p()}get open_upload(){return this.$$.ctx[24]}get paste_clipboard(){return this.$$.ctx[15]}get open_file_upload(){return this.$$.ctx[25]}get load_files(){return this.$$.ctx[26]}get load_files_from_drop(){return this.$$.ctx[27]}}export{Et as I,Lt as U,Pt as a,it as c};
//# sourceMappingURL=Upload-8igJ-HYX.js.map
