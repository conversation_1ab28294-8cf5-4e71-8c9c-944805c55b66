{"version": 3, "file": "iblCdfx.fragment-Dl2yb_68.js", "sources": ["../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/ShadersWGSL/iblCdfx.fragment.js"], "sourcesContent": ["// Do not edit.\nimport { ShaderStore } from \"../Engines/shaderStore.js\";\nconst name = \"iblCdfxPixelShader\";\nconst shader = `#define PI 3.1415927\nvarying vUV: vec2f;var cdfy: texture_2d<f32>;@fragment\nfn main(input: FragmentInputs)->FragmentOutputs {var cdfyRes=textureDimensions(cdfy,0);var currentPixel=vec2u(fragmentInputs.position.xy);var cdfx: f32=0.0;for (var x: u32=1; x<=currentPixel.x; x++) {cdfx+=textureLoad(cdfy, vec2u(x-1,cdfyRes.y-1),0).x;}\nfragmentOutputs.color= vec4f( vec3f(cdfx),1.0);}`;\n// Sideeffect\nif (!ShaderStore.ShadersStoreWGSL[name]) {\n    ShaderStore.ShadersStoreWGSL[name] = shader;\n}\n/** @internal */\nexport const iblCdfxPixelShaderWGSL = { name, shader };\n//# sourceMappingURL=iblCdfx.fragment.js.map"], "names": ["name", "shader", "ShaderStore", "iblCdfxPixelShaderWGSL"], "mappings": "+FAEA,MAAMA,EAAO,qBACPC,EAAS;AAAA;AAAA;AAAA,kDAKVC,EAAY,iBAAiBF,CAAI,IAClCE,EAAY,iBAAiBF,CAAI,EAAIC,GAG7B,MAACE,EAAyB,CAAE,KAAAH,EAAM,OAAAC,CAAM", "x_google_ignoreList": [0]}