{"version": 3, "file": "iblIcdf.fragment-DZdhY0fq.js", "sources": ["../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/Shaders/iblIcdf.fragment.js"], "sourcesContent": ["// Do not edit.\nimport { ShaderStore } from \"../Engines/shaderStore.js\";\nimport \"./ShadersInclude/helperFunctions.js\";\nconst name = \"iblIcdfPixelShader\";\nconst shader = `precision highp sampler2D;\n#include<helperFunctions>\nvarying vec2 vUV;\n#ifdef IBL_USE_CUBE_MAP\nuniform samplerCube iblSource;\n#else\nuniform sampler2D iblSource;\n#endif\nuniform sampler2D scaledLuminanceSampler;uniform int iblWidth;uniform int iblHeight;uniform sampler2D cdfx;uniform sampler2D cdfy;float fetchLuminance(vec2 coords) {\n#ifdef IBL_USE_CUBE_MAP\nvec3 direction=equirectangularToCubemapDirection(coords);vec3 color=textureCubeLodEXT(iblSource,direction,0.0).rgb;\n#else\nvec3 color=textureLod(iblSource,coords,0.0).rgb;\n#endif\nreturn dot(color,LuminanceEncodeApprox);}\nfloat fetchCDFx(int x) { return texelFetch(cdfx,ivec2(x,0),0).x; }\nfloat bisectx(int size,float targetValue) {int a=0,b=size-1;while (b-a>1) {int c=a+b>>1;if (fetchCDFx(c)<targetValue)\na=c;else\nb=c;}\nreturn mix(float(a),float(b),\n(targetValue-fetchCDFx(a))/(fetchCDFx(b)-fetchCDFx(a))) /\nfloat(size-1);}\nfloat fetchCDFy(int y,int invocationId) {return texelFetch(cdfy,ivec2(invocationId,y),0).x;}\nfloat bisecty(int size,float targetValue,int invocationId) {int a=0,b=size-1;while (b-a>1) {int c=a+b>>1;if (fetchCDFy(c,invocationId)<targetValue)\na=c;else\nb=c;}\nreturn mix(float(a),float(b),\n(targetValue-fetchCDFy(a,invocationId)) /\n(fetchCDFy(b,invocationId)-fetchCDFy(a,invocationId))) /\nfloat(size-1);}\nvoid main(void) {ivec2 cdfxSize=textureSize(cdfx,0);int cdfWidth=cdfxSize.x;int icdfWidth=cdfWidth-1;ivec2 currentPixel=ivec2(gl_FragCoord.xy);vec3 outputColor=vec3(1.0);if (currentPixel.x==0) {outputColor.x=0.0;} else if (currentPixel.x==icdfWidth-1) {outputColor.x=1.0;} else {float targetValue=fetchCDFx(cdfWidth-1)*vUV.x;outputColor.x=bisectx(cdfWidth,targetValue);}\nivec2 cdfySize=textureSize(cdfy,0);int cdfHeight=cdfySize.y;if (currentPixel.y==0) {outputColor.y=0.0;} else if (currentPixel.y==cdfHeight-2) {outputColor.y=1.0;} else {float targetValue=fetchCDFy(cdfHeight-1,currentPixel.x)*vUV.y;outputColor.y=max(bisecty(cdfHeight,targetValue,currentPixel.x),0.0);}\nvec2 size=vec2(textureSize(scaledLuminanceSampler,0));float highestMip=floor(log2(size.x));float normalization=texture(scaledLuminanceSampler,vUV,highestMip).r;float pixelLuminance=fetchLuminance(vUV);outputColor.z=pixelLuminance/(2.0*PI*normalization);gl_FragColor=vec4(outputColor,1.0);}\n`;\n// Sideeffect\nif (!ShaderStore.ShadersStore[name]) {\n    ShaderStore.ShadersStore[name] = shader;\n}\n/** @internal */\nexport const iblIcdfPixelShader = { name, shader };\n//# sourceMappingURL=iblIcdf.fragment.js.map"], "names": ["name", "shader", "ShaderStore", "iblIcdfPixelShader"], "mappings": "qIAGA,MAAMA,EAAO,qBACPC,EAAS;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAmCVC,EAAY,aAAaF,CAAI,IAC9BE,EAAY,aAAaF,CAAI,EAAIC,GAGzB,MAACE,EAAqB,CAAE,KAAAH,EAAM,OAAAC,CAAM", "x_google_ignoreList": [0]}