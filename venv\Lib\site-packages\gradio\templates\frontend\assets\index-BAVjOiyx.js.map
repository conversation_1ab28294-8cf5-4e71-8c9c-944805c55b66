{"version": 3, "file": "index-BAVjOiyx.js", "sources": ["../../../../js/audio/Index.svelte"], "sourcesContent": ["<svelte:options accessors={true} />\n\n<script lang=\"ts\">\n\timport type { Gradio, ShareData } from \"@gradio/utils\";\n\n\timport type { FileData } from \"@gradio/client\";\n\timport type { LoadingStatus } from \"@gradio/statustracker\";\n\timport { afterUpdate, onMount } from \"svelte\";\n\n\timport StaticAudio from \"./static/StaticAudio.svelte\";\n\timport InteractiveAudio from \"./interactive/InteractiveAudio.svelte\";\n\timport { StatusTracker } from \"@gradio/statustracker\";\n\timport { Block, UploadText } from \"@gradio/atoms\";\n\timport type { WaveformOptions } from \"./shared/types\";\n\n\texport let value_is_output = false;\n\texport let elem_id = \"\";\n\texport let elem_classes: string[] = [];\n\texport let visible = true;\n\texport let interactive: boolean;\n\texport let value: null | FileData = null;\n\texport let sources:\n\t\t| [\"microphone\"]\n\t\t| [\"upload\"]\n\t\t| [\"microphone\", \"upload\"]\n\t\t| [\"upload\", \"microphone\"];\n\texport let label: string;\n\texport let root: string;\n\texport let show_label: boolean;\n\texport let container = true;\n\texport let scale: number | null = null;\n\texport let min_width: number | undefined = undefined;\n\texport let loading_status: LoadingStatus;\n\texport let autoplay = false;\n\texport let loop = false;\n\texport let show_download_button: boolean;\n\texport let show_share_button = false;\n\texport let editable = true;\n\texport let waveform_options: WaveformOptions = {\n\t\tshow_recording_waveform: true\n\t};\n\texport let pending: boolean;\n\texport let streaming: boolean;\n\texport let stream_every: number;\n\texport let input_ready: boolean;\n\texport let recording = false;\n\tlet uploading = false;\n\t$: input_ready = !uploading;\n\n\tlet stream_state = \"closed\";\n\tlet _modify_stream: (state: \"open\" | \"closed\" | \"waiting\") => void;\n\texport function modify_stream_state(\n\t\tstate: \"open\" | \"closed\" | \"waiting\"\n\t): void {\n\t\tstream_state = state;\n\t\t_modify_stream(state);\n\t}\n\texport const get_stream_state: () => void = () => stream_state;\n\texport let set_time_limit: (time: number) => void;\n\texport let gradio: Gradio<{\n\t\tinput: never;\n\t\tchange: typeof value;\n\t\tstream: typeof value;\n\t\terror: string;\n\t\twarning: string;\n\t\tedit: never;\n\t\tplay: never;\n\t\tpause: never;\n\t\tstop: never;\n\t\tend: never;\n\t\tstart_recording: never;\n\t\tpause_recording: never;\n\t\tstop_recording: never;\n\t\tupload: never;\n\t\tclear: never;\n\t\tshare: ShareData;\n\t\tclear_status: LoadingStatus;\n\t\tclose_stream: string;\n\t}>;\n\n\tlet old_value: null | FileData = null;\n\n\tlet active_source: \"microphone\" | \"upload\";\n\n\tlet initial_value: null | FileData = value;\n\n\t$: if (value && initial_value === null) {\n\t\tinitial_value = value;\n\t}\n\n\tconst handle_reset_value = (): void => {\n\t\tif (initial_value === null || value === initial_value) {\n\t\t\treturn;\n\t\t}\n\n\t\tvalue = initial_value;\n\t};\n\n\t$: {\n\t\tif (JSON.stringify(value) !== JSON.stringify(old_value)) {\n\t\t\told_value = value;\n\t\t\tgradio.dispatch(\"change\");\n\t\t\tif (!value_is_output) {\n\t\t\t\tgradio.dispatch(\"input\");\n\t\t\t}\n\t\t}\n\t}\n\n\tlet dragging: boolean;\n\n\t$: if (!active_source && sources) {\n\t\tactive_source = sources[0];\n\t}\n\n\tlet waveform_settings: Record<string, any>;\n\n\tlet color_accent = \"darkorange\";\n\n\tonMount(() => {\n\t\tcolor_accent = getComputedStyle(document?.documentElement).getPropertyValue(\n\t\t\t\"--color-accent\"\n\t\t);\n\t\tset_trim_region_colour();\n\t\twaveform_settings.waveColor = waveform_options.waveform_color || \"#9ca3af\";\n\t\twaveform_settings.progressColor =\n\t\t\twaveform_options.waveform_progress_color || color_accent;\n\t\twaveform_settings.mediaControls = waveform_options.show_controls;\n\t\twaveform_settings.sampleRate = waveform_options.sample_rate || 44100;\n\t});\n\n\t$: waveform_settings = {\n\t\theight: 50,\n\n\t\tbarWidth: 2,\n\t\tbarGap: 3,\n\t\tcursorWidth: 2,\n\t\tcursorColor: \"#ddd5e9\",\n\t\tautoplay: autoplay,\n\t\tbarRadius: 10,\n\t\tdragToSeek: true,\n\t\tnormalize: true,\n\t\tminPxPerSec: 20\n\t};\n\n\tconst trim_region_settings = {\n\t\tcolor: waveform_options.trim_region_color,\n\t\tdrag: true,\n\t\tresize: true\n\t};\n\n\tfunction set_trim_region_colour(): void {\n\t\tdocument.documentElement.style.setProperty(\n\t\t\t\"--trim-region-color\",\n\t\t\ttrim_region_settings.color || color_accent\n\t\t);\n\t}\n\n\tfunction handle_error({ detail }: CustomEvent<string>): void {\n\t\tconst [level, status] = detail.includes(\"Invalid file type\")\n\t\t\t? [\"warning\", \"complete\"]\n\t\t\t: [\"error\", \"error\"];\n\t\tloading_status = loading_status || {};\n\t\tloading_status.status = status as LoadingStatus[\"status\"];\n\t\tloading_status.message = detail;\n\t\tgradio.dispatch(level as \"error\" | \"warning\", detail);\n\t}\n\n\tafterUpdate(() => {\n\t\tvalue_is_output = false;\n\t});\n</script>\n\n{#if !interactive}\n\t<Block\n\t\tvariant={\"solid\"}\n\t\tborder_mode={dragging ? \"focus\" : \"base\"}\n\t\tpadding={false}\n\t\tallow_overflow={false}\n\t\t{elem_id}\n\t\t{elem_classes}\n\t\t{visible}\n\t\t{container}\n\t\t{scale}\n\t\t{min_width}\n\t>\n\t\t<StatusTracker\n\t\t\tautoscroll={gradio.autoscroll}\n\t\t\ti18n={gradio.i18n}\n\t\t\t{...loading_status}\n\t\t\ton:clear_status={() => gradio.dispatch(\"clear_status\", loading_status)}\n\t\t/>\n\n\t\t<StaticAudio\n\t\t\ti18n={gradio.i18n}\n\t\t\t{show_label}\n\t\t\t{show_download_button}\n\t\t\t{show_share_button}\n\t\t\t{value}\n\t\t\t{label}\n\t\t\t{loop}\n\t\t\t{waveform_settings}\n\t\t\t{waveform_options}\n\t\t\t{editable}\n\t\t\ton:share={(e) => gradio.dispatch(\"share\", e.detail)}\n\t\t\ton:error={(e) => gradio.dispatch(\"error\", e.detail)}\n\t\t\ton:play={() => gradio.dispatch(\"play\")}\n\t\t\ton:pause={() => gradio.dispatch(\"pause\")}\n\t\t\ton:stop={() => gradio.dispatch(\"stop\")}\n\t\t/>\n\t</Block>\n{:else}\n\t<Block\n\t\tvariant={value === null && active_source === \"upload\" ? \"dashed\" : \"solid\"}\n\t\tborder_mode={dragging ? \"focus\" : \"base\"}\n\t\tpadding={false}\n\t\tallow_overflow={false}\n\t\t{elem_id}\n\t\t{elem_classes}\n\t\t{visible}\n\t\t{container}\n\t\t{scale}\n\t\t{min_width}\n\t>\n\t\t<StatusTracker\n\t\t\tautoscroll={gradio.autoscroll}\n\t\t\ti18n={gradio.i18n}\n\t\t\t{...loading_status}\n\t\t\ton:clear_status={() => gradio.dispatch(\"clear_status\", loading_status)}\n\t\t/>\n\t\t<InteractiveAudio\n\t\t\t{label}\n\t\t\t{show_label}\n\t\t\t{show_download_button}\n\t\t\t{value}\n\t\t\ton:change={({ detail }) => (value = detail)}\n\t\t\ton:stream={({ detail }) => {\n\t\t\t\tvalue = detail;\n\t\t\t\tgradio.dispatch(\"stream\", value);\n\t\t\t}}\n\t\t\ton:drag={({ detail }) => (dragging = detail)}\n\t\t\t{root}\n\t\t\t{sources}\n\t\t\t{active_source}\n\t\t\t{pending}\n\t\t\t{streaming}\n\t\t\tbind:recording\n\t\t\t{loop}\n\t\t\tmax_file_size={gradio.max_file_size}\n\t\t\t{handle_reset_value}\n\t\t\t{editable}\n\t\t\tbind:dragging\n\t\t\tbind:uploading\n\t\t\ton:edit={() => gradio.dispatch(\"edit\")}\n\t\t\ton:play={() => gradio.dispatch(\"play\")}\n\t\t\ton:pause={() => gradio.dispatch(\"pause\")}\n\t\t\ton:stop={() => gradio.dispatch(\"stop\")}\n\t\t\ton:start_recording={() => gradio.dispatch(\"start_recording\")}\n\t\t\ton:pause_recording={() => gradio.dispatch(\"pause_recording\")}\n\t\t\ton:stop_recording={(e) => gradio.dispatch(\"stop_recording\")}\n\t\t\ton:upload={() => gradio.dispatch(\"upload\")}\n\t\t\ton:clear={() => gradio.dispatch(\"clear\")}\n\t\t\ton:error={handle_error}\n\t\t\ton:close_stream={() => gradio.dispatch(\"close_stream\", \"stream\")}\n\t\t\ti18n={gradio.i18n}\n\t\t\t{waveform_settings}\n\t\t\t{waveform_options}\n\t\t\t{trim_region_settings}\n\t\t\t{stream_every}\n\t\t\tbind:modify_stream={_modify_stream}\n\t\t\tbind:set_time_limit\n\t\t\tupload={(...args) => gradio.client.upload(...args)}\n\t\t\tstream_handler={(...args) => gradio.client.stream(...args)}\n\t\t>\n\t\t\t<UploadText i18n={gradio.i18n} type=\"audio\" />\n\t\t</InteractiveAudio>\n\t</Block>\n{/if}\n"], "names": ["afterUpdate", "onMount", "ctx", "dirty", "block_changes", "uploadtext_changes", "interactiveaudio_changes", "staticaudio_changes", "value_is_output", "$$props", "elem_id", "elem_classes", "visible", "interactive", "value", "sources", "label", "root", "show_label", "container", "scale", "min_width", "loading_status", "autoplay", "loop", "show_download_button", "show_share_button", "editable", "waveform_options", "pending", "streaming", "stream_every", "input_ready", "recording", "uploading", "stream_state", "_modify_stream", "modify_stream_state", "state", "get_stream_state", "set_time_limit", "gradio", "old_value", "active_source", "initial_value", "handle_reset_value", "dragging", "waveform_settings", "color_accent", "set_trim_region_colour", "$$invalidate", "trim_region_settings", "handle_error", "detail", "level", "status", "clear_status_handler", "e", "clear_status_handler_1", "func", "args", "func_1", "stop_recording_handler", "close_stream_handler"], "mappings": "msDAOU,CAAA,YAAAA,GAAA,QAAAC,IAA4B,OAAA,yEA6M3B,QAAAC,EAAU,CAAA,IAAA,MAAQA,QAAkB,SAAW,SAAW,oBACtDA,EAAQ,EAAA,EAAG,QAAU,eACzB,kBACO,oMAHPC,EAAA,CAAA,EAAA,WAAAC,EAAA,QAAAF,EAAU,CAAA,IAAA,MAAQA,QAAkB,SAAW,SAAW,wCACtDA,EAAQ,EAAA,EAAG,QAAU,0XAvCzB,oBACIA,EAAQ,EAAA,EAAG,QAAU,eACzB,kBACO,mOAFHA,EAAQ,EAAA,EAAG,QAAU,mXAkGf,KAAAA,MAAO,oFAAPC,EAAA,CAAA,EAAA,UAAAE,EAAA,KAAAH,MAAO,+IAjDb,WAAAA,MAAO,YACb,CAAA,KAAAA,MAAO,IAAI,EACbA,EAAc,CAAA,qXAqBH,cAAAA,MAAO,sDAgBhB,KAAAA,MAAO,4RAKOA,EAAc,EAAA,IAAA,yBAAdA,EAAc,EAAA,ugBAPxBA,EAAY,EAAA,CAAA,sKArCV,WAAAA,MAAO,YACbC,EAAA,CAAA,EAAA,SAAA,CAAA,KAAAD,MAAO,IAAI,aACbA,EAAc,CAAA,CAAA,gVAqBHC,EAAA,CAAA,EAAA,UAAAG,EAAA,cAAAJ,MAAO,+CAgBhBC,EAAA,CAAA,EAAA,UAAAG,EAAA,KAAAJ,MAAO,icAKOA,EAAc,EAAA,wPAlFtB,WAAAA,MAAO,YACb,CAAA,KAAAA,MAAO,IAAI,EACbA,EAAc,CAAA,4HAKZ,KAAAA,MAAO,0ZAPD,WAAAA,MAAO,YACbC,EAAA,CAAA,EAAA,SAAA,CAAA,KAAAD,MAAO,IAAI,aACbA,EAAc,CAAA,CAAA,4BAKZC,EAAA,CAAA,EAAA,UAAAI,EAAA,KAAAL,MAAO,+gBArBVA,EAAW,CAAA,IAAA,8TA7JL,gBAAAM,EAAkB,EAAA,EAAAC,GAClB,QAAAC,EAAU,EAAA,EAAAD,EACV,CAAA,aAAAE,EAAA,EAAA,EAAAF,GACA,QAAAG,EAAU,EAAA,EAAAH,EACV,CAAA,YAAAI,CAAA,EAAAJ,GACA,MAAAK,EAAyB,IAAA,EAAAL,EACzB,CAAA,QAAAM,CAAA,EAAAN,EAKA,CAAA,MAAAO,CAAA,EAAAP,EACA,CAAA,KAAAQ,CAAA,EAAAR,EACA,CAAA,WAAAS,CAAA,EAAAT,GACA,UAAAU,EAAY,EAAA,EAAAV,GACZ,MAAAW,EAAuB,IAAA,EAAAX,GACvB,UAAAY,EAAgC,MAAA,EAAAZ,EAChC,CAAA,eAAAa,CAAA,EAAAb,GACA,SAAAc,EAAW,EAAA,EAAAd,GACX,KAAAe,EAAO,EAAA,EAAAf,EACP,CAAA,qBAAAgB,CAAA,EAAAhB,GACA,kBAAAiB,EAAoB,EAAA,EAAAjB,GACpB,SAAAkB,EAAW,EAAA,EAAAlB,EACX,CAAA,iBAAAmB,EAAA,CACV,wBAAyB,EAAA,CAAA,EAAAnB,EAEf,CAAA,QAAAoB,CAAA,EAAApB,EACA,CAAA,UAAAqB,CAAA,EAAArB,EACA,CAAA,aAAAsB,CAAA,EAAAtB,EACA,CAAA,YAAAuB,CAAA,EAAAvB,GACA,UAAAwB,EAAY,EAAA,EAAAxB,EACnByB,EAAY,GAGZC,EAAe,SACfC,WACYC,GACfC,EAAA,CAEAH,EAAeG,EACfF,EAAeE,CAAK,QAERC,GAAqC,IAAAJ,EACvC,GAAA,CAAA,eAAAK,CAAA,EAAA/B,EACA,CAAA,OAAAgC,CAAA,EAAAhC,EAqBPiC,EAA6B,KAE7BC,EAEAC,EAAiC9B,EAM/B,MAAA+B,GAAA,IAAA,CACDD,IAAkB,MAAQ9B,IAAU8B,OAIxC9B,EAAQ8B,CAAA,GAaL,IAAAE,EAMAC,EAEAC,EAAe,aAEnB/C,GAAA,IAAA,CACC+C,EAAe,iBAAiB,UAAU,eAAe,EAAE,iBAC1D,gBAAA,EAEDC,UACAF,EAAkB,UAAYnB,EAAiB,gBAAkB,UAAAmB,CAAA,OACjEA,EAAkB,cACjBnB,EAAiB,yBAA2BoB,EAAAD,CAAA,EAC7CG,EAAA,GAAAH,EAAkB,cAAgBnB,EAAiB,cAAAmB,CAAA,OACnDA,EAAkB,WAAanB,EAAiB,aAAe,MAAAmB,CAAA,IAiB1D,MAAAI,EAAA,CACL,MAAOvB,EAAiB,kBACxB,KAAM,GACN,OAAQ,IAGA,SAAAqB,IAAA,CACR,SAAS,gBAAgB,MAAM,YAC9B,sBACAE,EAAqB,OAASH,CAAA,WAIvBI,GAAe,CAAA,OAAAC,GAAA,CAChB,KAAA,CAAAC,GAAOC,EAAM,EAAIF,EAAO,SAAS,mBAAmB,EACvD,CAAA,UAAW,UAAU,EACrB,CAAA,QAAS,OAAO,MACpB/B,EAAiBA,GAAA,CAAA,CAAA,EACjB4B,EAAA,EAAA5B,EAAe,OAASiC,GAAAjC,CAAA,EACxB4B,EAAA,EAAA5B,EAAe,QAAU+B,EAAA/B,CAAA,EACzBmB,EAAO,SAASa,GAA8BD,CAAM,EAGrDrD,GAAA,IAAA,MACCQ,EAAkB,EAAA,IAqBM,MAAAgD,GAAA,IAAAf,EAAO,SAAS,eAAgBnB,CAAc,KAc1DmC,GAAMhB,EAAO,SAAS,QAASgB,EAAE,MAAM,KACvCA,GAAMhB,EAAO,SAAS,QAASgB,EAAE,MAAM,SACnChB,EAAO,SAAS,MAAM,SACrBA,EAAO,SAAS,OAAO,SACxBA,EAAO,SAAS,MAAM,EAoBdiB,GAAA,IAAAjB,EAAO,SAAS,eAAgBnB,CAAc,EA2CzDqC,GAAA,IAAAC,IAASnB,EAAO,OAAO,UAAUmB,CAAI,EAC7BC,GAAA,IAAAD,IAASnB,EAAO,OAAO,UAAUmB,CAAI,iGAHrCxB,EAActB,+CAlCpB,OAAAuC,CAAM,IAAAH,EAAA,EAAQpC,EAAQuC,CAAM,OAC5B,OAAAA,KAAM,CACnBH,EAAA,EAAApC,EAAQuC,CAAM,EACdZ,EAAO,SAAS,SAAU3B,CAAK,QAEpB,OAAAuC,CAAM,IAAAH,EAAA,GAAQJ,EAAWO,CAAM,SAa5BZ,EAAO,SAAS,MAAM,SACtBA,EAAO,SAAS,MAAM,SACrBA,EAAO,SAAS,OAAO,SACxBA,EAAO,SAAS,MAAM,SACXA,EAAO,SAAS,iBAAiB,SACjCA,EAAO,SAAS,iBAAiB,EACvCqB,GAAAL,GAAMhB,EAAO,SAAS,gBAAgB,SACzCA,EAAO,SAAS,QAAQ,SACzBA,EAAO,SAAS,OAAO,EAEhBsB,GAAA,IAAAtB,EAAO,SAAS,eAAgB,QAAQ,gkCAvNjES,EAAA,GAAGlB,EAAe,CAAAE,CAAA,qCAuCXpB,GAAS8B,IAAkB,WACjCA,EAAgB9B,CAAA,0CAYZ,KAAK,UAAUA,CAAK,IAAM,KAAK,UAAU4B,CAAS,SACrDA,EAAY5B,CAAA,EACZ2B,EAAO,SAAS,QAAQ,EACnBjC,GACJiC,EAAO,SAAS,OAAO,2BAO1B,CAAQE,GAAiB5B,GACxBmC,EAAA,GAAAP,EAAgB5B,EAAQ,CAAC,CAAA,mBAmBvBmC,EAAA,GAAAH,EAAA,CACF,OAAQ,GAER,SAAU,EACV,OAAQ,EACR,YAAa,EACb,YAAa,UACb,SAAAxB,EACA,UAAW,GACX,WAAY,GACZ,UAAW,GACX,YAAa"}