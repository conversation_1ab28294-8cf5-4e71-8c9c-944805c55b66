const __vite__fileDeps=["./Index-CaC1GRz3.js","./Block-CJdXVpa7.js","./IconButtonWrapper-BqcF4N5S.css","./MarkdownCode.svelte_svelte_type_style_lang-Bl8p72Ow.js","./prism-python-CeMtt1IT.js","./MarkdownCode-DVeJpfiO.css","./index-DYtg3pip.js","./StreamingBar.svelte_svelte_type_style_lang-DlRzv56K.js","./StreamingBar-Cgs5stVH.css","./IconButton-C_HS7fTi.js","./Clear-By3xiIwg.js","./Index-BrpXEgHX.js","./Index-D-5U5m91.css","./Index-C5NYahSl.css","./Index-GuD1Tw7E.js","./BlockLabel-3KxTaaiM.js","./Empty-ZqppqzTN.js","./Image-Bsh8Umrh.js","./IconButtonWrapper--EIOWuEM.js","./FullscreenButton-BG4mOKmH.js","./file-url-DoxvUUVV.js","./DownloadLink.svelte_svelte_type_style_lang-C_5UIfol.js","./DownloadLink-CqD3Uu0l.css","./Index-E3yBBMTH.css","./StaticAudio-DqD2VoK2.js","./utils-BsGrhMNe.js","./ShareButton-CuOwy-FH.js","./Community-Dw1micSV.js","./Download-DVtk-Jv3.js","./Music-CDm0RGMk.js","./AudioPlayer-BwH4nL4X.js","./Trim-JQYgj7Jd.js","./Play-B0Q0U1Qz.js","./Undo-DCjBnnSO.js","./hls-CnVhpNcu.js","./AudioPlayer-BAKhejK8.css","./DownloadLink-QIttOhoR.js","./Example-BQyGztrG.js","./Example-D7K5RtQ2.css","./index-BAVjOiyx.js","./InteractiveAudio-DPLJ9YFZ.js","./Upload-8igJ-HYX.js","./Upload-L7mprsyN.css","./ModifyUpload-Bl9a-zWl.js","./Edit-BpRIf5rU.js","./SelectSource-DG1n31De.js","./StreamingBar-BpqwJkLy.js","./InteractiveAudio-B76TQFG-.css","./UploadText-Wd7ORU21.js","./Index-BedFhM2U.js","./Index-DHm3X9_J.js","./__vite-browser-external-D7Ct-6yo.js","./Index-DXQ3WLFM.js","./Button-BY4Yg2Ti.js","./Image-CnqB5dbD.js","./Image-B8dFOee4.css","./Button-DTh9AgeE.css","./ImagePreview-C_qhEOxI.css","./Index-PzJAyUHf.js","./Check-CEkiXcyC.js","./Copy-CxQ9EyK2.js","./File-BQ_9P3Ye.js","./MarkdownCode-Cdb8e5t4.js","./index-BALGG9zl.js","./index-CnqicUFC.js","./Trash-RbZEwH-j.js","./Index-rX6M_998.css","./Example-CZ-iEz1g.js","./Index-oVWU9m6e.js","./Info-D7HP20hi.js","./Checkbox-CjOIpf6b.js","./Checkbox-COx9d1js.css","./Example-DccrJI--.js","./Index-DMs26Evw.js","./BlockTitle-C6qeQAMx.js","./Index-DMKGW8pW.css","./Example-DQvZIwBt.js","./Example-oomIF0ca.css","./Index-BMproeh5.js","./Code-DGNrTu_I.js","./Index-DloLYeAi.css","./Example-BaLyJYAe.js","./Example-Bw8Q_3wB.css","./Index-DQ3_4eWv.js","./tinycolor-DhRrpXkc.js","./Index-DwWu86Nh.css","./index-Bp4yBqQw.js","./Embed-DP0vgn8y.js","./Example-CqPGqNav.js","./Example-1kVNej19.css","./Index-ALdaZ21I.js","./index-tFQomdd2.js","./dsv-DB8NKgIY.js","./DropdownArrow-DYWFcSFn.js","./Index-B6-B2pPO.js","./ImagePreview-CpxjYXeK.js","./utils-Gtzs_Zla.js","./ImageUploader-3ubqEwhl.js","./Square-oAGqOwsh.js","./ImageUploader-DMdYP1a9.css","./Example-CC8yxxGn.js","./Example-DikqVAPo.css","./Index-CF60ftEl.css","./Index-Dvl64pLq.js","./Example-BdAjEacD.js","./Example-ClKJOMGh.css","./Index-D3f6Hf9S.css","./Textbox-jWD3sCxr.css","./Index-D2QAi4I9.js","./Example-BBLMS951.js","./Index-Bk5ZTHOI.css","./Index-CybTFp6a.js","./Index-tcNSQSor.css","./Example-BgQNfMWT.js","./Index-DR-TuqpS.js","./Dropdown-Cb5zL1i6.js","./Dropdown-CWxB-qJp.css","./Example-DrmWnoSo.js","./Example-DpWs9cEC.css","./Index-DfTFkCtq.js","./FileUpload-DH9xd7bM.js","./FileUpload-CQVu-hjH.css","./Example-CIFMxn5c.js","./Example-DfhEULNF.css","./Index-DtzkFa5_.js","./Index-BKaa_GXG.css","./Index-DE1Sah7F.js","./Index-12OnbRhk.css","./Gallery-GP9wgZoX.js","./Video-DtShVFLe.js","./Video-DJw86Ppo.css","./Gallery-CUVAWrOv.css","./Index-BzsA-dBW.js","./Index-WEzAIkMk.js","./Index-Cgj6KPvj.css","./Index-B14Vg3Db.js","./color-DTCUJ6g1.js","./Index-Dwy3Ni24.css","./Index-C0bxGwtU.js","./Index-Csm0OGa9.css","./Example-C2a4WxRl.js","./Example-CSw4pLi5.css","./Example-BHKzkkLr.js","./Example-6rv12T44.css","./Index-DCEUuTr0.js","./Index-ClP1ItfE.css","./Example-j_9MW44b.js","./Example-fMB4cHw6.css","./Index-CVEjRJ8U.js","./select-BigU4G0v.js","./dispatch-kxCwF96_.js","./Index-CTOeqf6n.css","./Example-D_6QaySw.js","./JSON-9ixhbEcL.js","./JSON-DItMwpQq.css","./Example-CG7uBGLE.css","./Index-5i89YVR8.js","./Index-DeQezhBd.js","./LineChart-CKh1Fdep.js","./Index-D3BKJl5I.css","./Example-Dijoz-Uw.js","./Index-DTliZ1wA.js","./Index-BTXaMQgd.css","./Example-uQ8MuYg6.js","./Index-CxIzfvY9.js","./Index-Be3F7oKw.css","./Example-BfnVJ_3N.js","./Example-CCTTJ5R1.css","./Index-7wV0551G.js","./Send-DyoOovnk.js","./Video-fsmLZWjA.js","./Index-1zeyvqK2.css","./Index-BmjPKxr3.js","./Index-WdTVQ0oj.css","./Example-CqL1e7EB.js","./Index-DlaooGe8.js","./Index-Dclo02rM.css","./Example-C9__vDgN.js","./Index-BSKlTVUx.js","./Index-D2tdhwRT.css","./Plot-DPlFUnzo.js","./Index-BR1sVb0F.js","./Example-BoMLuz1A.js","./Index-DurZmyFC.js","./Index--UpFQsHg.css","./Index-BFah9pAF.js","./Index-CfowPFmo.css","./Index-Gj0WTAu3.js","./Index-w1Ra_uQ1.css","./Index-BlWK1-fD.js","./Index-B0hFno2n.css","./Example-BrizabXh.js","./Index-3JI__9QL.js","./Index-zdBaScbz.css","./index-DOyaaCjr.js","./Toast-BDCWOwui.js","./Index-CBYcBFye.js","./Tabs-Bb738MrQ.js","./Tabs-C0qLuAtA.css","./Index-Gmwqb-vD.css","./Index-CiKJIoEh.js","./Index-BFGcUqmi.js","./Textbox-Co0OLweQ.js","./Index-B6Hnnfyx.js","./Index-DYDmCduo.css","./VideoPreview-31w_cOLN.js","./VideoPreview-wQufNXbv.css","./Example-q2WHRP-V.js","./Example-B5CSTz0f.css","./index-D0nAiVXY.js","./index-CFBZQE_H.css","./Example-DxdiEFS_.js","./Index-Bh2vG2zW.js","./Index-CgDrEMlk.css","./Index-BgyqPiU2.js","./Index-BJ_RfjVB.css"],__vite__mapDeps=i=>i.map(i=>__vite__fileDeps[i]);
import*as et from"./svelte/svelte.js";(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const i of document.querySelectorAll('link[rel="modulepreload"]'))n(i);new MutationObserver(i=>{for(const o of i)if(o.type==="childList")for(const s of o.addedNodes)s.tagName==="LINK"&&s.rel==="modulepreload"&&n(s)}).observe(document,{childList:!0,subtree:!0});function r(i){const o={};return i.integrity&&(o.integrity=i.integrity),i.referrerPolicy&&(o.referrerPolicy=i.referrerPolicy),i.crossOrigin==="use-credentials"?o.credentials="include":i.crossOrigin==="anonymous"?o.credentials="omit":o.credentials="same-origin",o}function n(i){if(i.ep)return;i.ep=!0;const o=r(i);fetch(i.href,o)}})();const An="modulepreload",On=function(e,t){return new URL(e,t).href},kt={},h=function(t,r,n){let i=Promise.resolve();if(r&&r.length>0){const o=document.getElementsByTagName("link"),s=document.querySelector("meta[property=csp-nonce]"),a=s?.nonce||s?.getAttribute("nonce");i=Promise.all(r.map(c=>{if(c=On(c,n),c in kt)return;kt[c]=!0;const u=c.endsWith(".css"),l=u?'[rel="stylesheet"]':"";if(!!n)for(let m=o.length-1;m>=0;m--){const _=o[m];if(_.href===c&&(!u||_.rel==="stylesheet"))return}else if(document.querySelector(`link[href="${c}"]${l}`))return;const p=document.createElement("link");if(p.rel=u?"stylesheet":An,u||(p.as="script",p.crossOrigin=""),p.href=c,a&&p.setAttribute("nonce",a),document.head.appendChild(p),u)return new Promise((m,_)=>{p.addEventListener("load",m),p.addEventListener("error",()=>_(new Error(`Unable to preload CSS for ${c}`)))})}))}return i.then(()=>t()).catch(o=>{const s=new Event("vite:preloadError",{cancelable:!0});if(s.payload=o,window.dispatchEvent(s),!s.defaultPrevented)throw o})};var tt=new Intl.Collator(0,{numeric:1}).compare;function gr(e,t,r){return e=e.split("."),t=t.split("."),tt(e[0],t[0])||tt(e[1],t[1])||(t[2]=t.slice(2).join("."),r=/[.-]/.test(e[2]=e.slice(2).join(".")),r==/[.-]/.test(t[2])?tt(e[2],t[2]):r?-1:1)}const Pn="host",br="queue/data",Rn="queue/join",Ut="upload",Ln="login",jt="config",In="info",Dn="runtime",Hn="sleeptime",Nn="heartbeat",Cn="component_server",Bn="reset",Mn="cancel",kn="https://gradio-space-api-fetcher-v2.hf.space/api",Er="This application is currently busy. Please try again. ",me="Connection errored out. ",ae="Could not resolve app config. ",Un="Could not get space status. ",jn="Could not get API info. ",St="Space metadata could not be loaded. ",Vn="Invalid URL. A full URL path is required.",$n="Not authorized to access this space. ",yr="Invalid credentials. Could not login. ",Gn="Login credentials are required to access this space.",Fn="File system access is only available in Node.js environments",vr="Root URL not found in client config",zn="Error uploading file";function qn(e,t,r){return t.startsWith("http://")||t.startsWith("https://")?r?e:t:e+t}async function Vt(e,t,r){try{return(await(await fetch(`https://huggingface.co/api/spaces/${e}/jwt`,{headers:{Authorization:`Bearer ${t}`,...r?{Cookie:r}:{}}})).json()).token||!1}catch{return!1}}function Xn(e){let t={};return e.forEach(({api_name:r,id:n})=>{r&&(t[r]=n)}),t}async function Wn(e){const t=this.options.hf_token?{Authorization:`Bearer ${this.options.hf_token}`}:{};if(t["Content-Type"]="application/json",typeof window<"u"&&window.gradio_config&&location.origin!=="http://localhost:9876"&&!window.gradio_config.dev_mode)return window.gradio_config.current_page&&(e=e.substring(0,e.lastIndexOf("/"))),window.gradio_config.root=e,{...window.gradio_config};if(e){let r=Tr(e,this.deep_link?jt+"?deep_link="+this.deep_link:jt);const n=await this.fetch(r,{headers:t,credentials:"include"});return Jn(n,e,!!this.options.auth)}throw new Error(ae)}async function Jn(e,t,r){if(e?.status===401&&!r){const i=(await e.json())?.detail?.auth_message;throw new Error(i||Gn)}else if(e?.status===401&&r)throw new Error(yr);if(e?.status===200){let n=await e.json();return n.root=t,n.dependencies?.forEach((i,o)=>{i.id===void 0&&(i.id=o)}),n}else if(e?.status===401)throw new Error($n);throw new Error(ae)}async function Zn(){const{http_protocol:e,host:t}=await Xe(this.app_reference,this.options.hf_token);try{if(this.options.auth){const r=await wr(e,t,this.options.auth,this.fetch,this.options.hf_token);r&&this.set_cookies(r)}}catch(r){throw Error(r.message)}}async function wr(e,t,r,n,i){const o=new FormData;o.append("username",r?.[0]),o.append("password",r?.[1]);let s={};i&&(s.Authorization=`Bearer ${i}`);const a=await n(`${e}//${t}/${Ln}`,{headers:s,method:"POST",body:o,credentials:"include"});if(a.status===200)return a.headers.get("set-cookie");throw a.status===401?new Error(yr):new Error(St)}function rt(e){if(e.startsWith("http")){const{protocol:t,host:r,pathname:n}=new URL(e);return{ws_protocol:t==="https:"?"wss":"ws",http_protocol:t,host:r+(n!=="/"?n:"")}}else if(e.startsWith("file:"))return{ws_protocol:"ws",http_protocol:"http:",host:"lite.local"};return{ws_protocol:"wss",http_protocol:"https:",host:new URL(e).host}}const xr=e=>{let t=[];return e.split(/,(?=\s*[^\s=;]+=[^\s=;]+)/).forEach(n=>{const[i,o]=n.split(";")[0].split("=");i&&o&&t.push(`${i.trim()}=${o.trim()}`)}),t},At=/^[a-zA-Z0-9_\-\.]+\/[a-zA-Z0-9_\-\.]+$/,Qn=/.*hf\.space\/{0,1}.*$/;async function Xe(e,t){const r={};t&&(r.Authorization=`Bearer ${t}`);const n=e.trim().replace(/\/$/,"");if(At.test(n))try{const o=(await(await fetch(`https://huggingface.co/api/spaces/${n}/${Pn}`,{headers:r})).json()).host;return{space_id:e,...rt(o)}}catch{throw new Error(St)}if(Qn.test(n)){const{ws_protocol:i,http_protocol:o,host:s}=rt(n);return{space_id:s.split("/")[0].replace(".hf.space",""),ws_protocol:i,http_protocol:o,host:s}}return{space_id:!1,...rt(n)}}const Tr=(...e)=>{try{return e.reduce((t,r)=>(t=t.replace(/\/+$/,""),r=r.replace(/^\/+/,""),new URL(r,t+"/").toString()))}catch{throw new Error(Vn)}};function Yn(e,t,r){const n={named_endpoints:{},unnamed_endpoints:{}};return Object.keys(e).forEach(i=>{(i==="named_endpoints"||i==="unnamed_endpoints")&&(n[i]={},Object.entries(e[i]).forEach(([o,{parameters:s,returns:a}])=>{const c=t.dependencies.find(f=>f.api_name===o||f.api_name===o.replace("/",""))?.id||r[o.replace("/","")]||-1,u=c!==-1?t.dependencies.find(f=>f.id==c)?.types:{generator:!1,cancel:!1};if(c!==-1&&t.dependencies.find(f=>f.id==c)?.inputs?.length!==s.length){const f=t.dependencies.find(p=>p.id==c).inputs.map(p=>t.components.find(m=>m.id===p)?.type);try{f.forEach((p,m)=>{if(p==="state"){const _={component:"state",example:null,parameter_default:null,parameter_has_default:!0,parameter_name:null,hidden:!0};s.splice(m,0,_)}})}catch(p){console.error(p)}}const l=(f,p,m,_)=>({...f,description:ei(f?.type,m),type:Kn(f?.type,p,m,_)||""});n[i][o]={parameters:s.map(f=>l(f,f?.component,f?.serializer,"parameter")),returns:a.map(f=>l(f,f?.component,f?.serializer,"return")),type:u}}))}),n}function Kn(e,t,r,n){if(t==="Api")return e.type;switch(e?.type){case"string":return"string";case"boolean":return"boolean";case"number":return"number"}if(r==="JSONSerializable"||r==="StringSerializable")return"any";if(r==="ListStringSerializable")return"string[]";if(t==="Image")return n==="parameter"?"Blob | File | Buffer":"string";if(r==="FileSerializable")return e?.type==="array"?n==="parameter"?"(Blob | File | Buffer)[]":"{ name: string; data: string; size?: number; is_file?: boolean; orig_name?: string}[]":n==="parameter"?"Blob | File | Buffer":"{ name: string; data: string; size?: number; is_file?: boolean; orig_name?: string}";if(r==="GallerySerializable")return n==="parameter"?"[(Blob | File | Buffer), (string | null)][]":"[{ name: string; data: string; size?: number; is_file?: boolean; orig_name?: string}, (string | null))][]"}function ei(e,t){return t==="GallerySerializable"?"array of [file, label] tuples":t==="ListStringSerializable"?"array of strings":t==="FileSerializable"?"array of files or single file":e?.description}function nt(e,t){switch(e.msg){case"send_data":return{type:"data"};case"send_hash":return{type:"hash"};case"queue_full":return{type:"update",status:{queue:!0,message:Er,stage:"error",code:e.code,success:e.success}};case"heartbeat":return{type:"heartbeat"};case"unexpected_error":return{type:"unexpected_error",status:{queue:!0,message:e.message,stage:"error",success:!1}};case"estimation":return{type:"update",status:{queue:!0,stage:t||"pending",code:e.code,size:e.queue_size,position:e.rank,eta:e.rank_eta,success:e.success}};case"progress":return{type:"update",status:{queue:!0,stage:"pending",code:e.code,progress_data:e.progress_data,success:e.success}};case"log":return{type:"log",data:e};case"process_generating":return{type:"generating",status:{queue:!0,message:e.success?null:e.output.error,stage:e.success?"generating":"error",code:e.code,progress_data:e.progress_data,eta:e.average_duration,changed_state_ids:e.success?e.output.changed_state_ids:void 0},data:e.success?e.output:null};case"process_streaming":return{type:"streaming",status:{queue:!0,message:e.output.error,stage:"streaming",time_limit:e.time_limit,code:e.code,progress_data:e.progress_data,eta:e.eta},data:e.output};case"process_completed":return"error"in e.output?{type:"update",status:{queue:!0,title:e.output.title,message:e.output.error,visible:e.output.visible,duration:e.output.duration,stage:"error",code:e.code,success:e.success}}:{type:"complete",status:{queue:!0,message:e.success?void 0:e.output.error,stage:e.success?"complete":"error",code:e.code,progress_data:e.progress_data,changed_state_ids:e.success?e.output.changed_state_ids:void 0},data:e.success?e.output:null};case"process_starts":return{type:"update",status:{queue:!0,stage:"pending",code:e.code,size:e.rank,position:0,success:e.success,eta:e.eta},original_msg:"process_starts"}}return{type:"none",status:{stage:"error",queue:!0}}}const ti=(e=[],t)=>{const r=t?t.parameters:[];if(Array.isArray(e))return t&&r.length>0&&e.length>r.length&&console.warn("Too many arguments provided for the endpoint."),e;const n=[],i=Object.keys(e);return r.forEach((o,s)=>{if(e.hasOwnProperty(o.parameter_name))n[s]=e[o.parameter_name];else if(o.parameter_has_default)n[s]=o.parameter_default;else throw new Error(`No value provided for required parameter: ${o.parameter_name}`)}),i.forEach(o=>{if(!r.some(s=>s.parameter_name===o))throw new Error(`Parameter \`${o}\` is not a valid keyword argument. Please refer to the API for usage.`)}),n.forEach((o,s)=>{if(o===void 0&&!r[s].parameter_has_default)throw new Error(`No value provided for required parameter: ${r[s].parameter_name}`)}),n};async function ri(){if(this.api_info)return this.api_info;const{hf_token:e}=this.options,{config:t}=this,r={"Content-Type":"application/json"};if(e&&(r.Authorization=`Bearer ${e}`),!!t)try{let n,i;if(typeof window<"u"&&window.gradio_api_info)i=window.gradio_api_info;else{if(gr(t?.version||"2.0.0","3.30")<0)n=await this.fetch(kn,{method:"POST",body:JSON.stringify({serialize:!1,config:JSON.stringify(t)}),headers:r,credentials:"include"});else{const o=Tr(t.root,this.api_prefix,In);n=await this.fetch(o,{headers:r,credentials:"include"})}if(!n.ok)throw new Error(me);i=await n.json()}return"api"in i&&(i=i.api),i.named_endpoints["/predict"]&&!i.unnamed_endpoints[0]&&(i.unnamed_endpoints[0]=i.named_endpoints["/predict"]),Yn(i,t,this.api_map)}catch(n){throw new Error("Could not get API info. "+n.message)}}async function ni(e,t,r){const n={};this?.options?.hf_token&&(n.Authorization=`Bearer ${this.options.hf_token}`);const i=1e3,o=[];let s;for(let a=0;a<t.length;a+=i){const c=t.slice(a,a+i),u=new FormData;c.forEach(f=>{u.append("files",f)});try{const f=r?`${e}${this.api_prefix}/${Ut}?upload_id=${r}`:`${e}${this.api_prefix}/${Ut}`;s=await this.fetch(f,{method:"POST",body:u,headers:n,credentials:"include"})}catch(f){throw new Error(me+f.message)}if(!s.ok){const f=await s.text();return{error:`HTTP ${s.status}: ${f}`}}const l=await s.json();l&&o.push(...l)}return{files:o}}async function ii(e,t,r,n){let i=(Array.isArray(e)?e:[e]).map(s=>s.blob);const o=i.filter(s=>s.size>(n??1/0));if(o.length)throw new Error(`File size exceeds the maximum allowed size of ${n} bytes: ${o.map(s=>s.name).join(", ")}`);return await Promise.all(await this.upload_files(t,i,r).then(async s=>{if(s.error)throw new Error(s.error);return s.files?s.files.map((a,c)=>new We({...e[c],path:a,url:`${t}${this.api_prefix}/file=${a}`})):[]}))}async function Js(e,t){return e.map(r=>new We({path:r.name,orig_name:r.name,blob:r,size:r.size,mime_type:r.type,is_stream:t}))}class We{path;url;orig_name;size;blob;is_stream;mime_type;alt_text;b64;meta={_type:"gradio.FileData"};constructor({path:t,url:r,orig_name:n,size:i,blob:o,is_stream:s,mime_type:a,alt_text:c,b64:u}){this.path=t,this.url=r,this.orig_name=n,this.size=i,this.blob=r?void 0:o,this.is_stream=s,this.mime_type=a,this.alt_text=c,this.b64=u}}class oi{type;command;meta;fileData;constructor(t,r){this.type="command",this.command=t,this.meta=r}}typeof process<"u"&&process.versions&&process.versions.node;function $t(e,t,r){for(;r.length>1;){const i=r.shift();if(typeof i=="string"||typeof i=="number")e=e[i];else throw new Error("Invalid key type")}const n=r.shift();if(typeof n=="string"||typeof n=="number")e[n]=t;else throw new Error("Invalid key type")}async function ft(e,t=void 0,r=[],n=!1,i=void 0){if(Array.isArray(e)){let o=[];return await Promise.all(e.map(async(s,a)=>{let c=r.slice();c.push(String(a));const u=await ft(e[a],n?i?.parameters[a]?.component||void 0:t,c,!1,i);o=o.concat(u)})),o}else{if(globalThis.Buffer&&e instanceof globalThis.Buffer||e instanceof Blob)return[{path:r,blob:new Blob([e]),type:t}];if(typeof e=="object"&&e!==null){let o=[];for(const s of Object.keys(e)){const a=[...r,s],c=e[s];o=o.concat(await ft(c,void 0,a,!1,i))}return o}}return[]}function si(e,t){let r=t?.dependencies?.find(n=>n.id==e)?.queue;return r!=null?!r:!t.enable_queue}function ai(e,t){return new Promise((r,n)=>{const i=new MessageChannel;i.port1.onmessage=({data:o})=>{i.port1.close(),r(o)},window.parent.postMessage(e,t,[i.port2])})}function Ie(e,t,r,n,i=!1){if(n==="input"&&!i)throw new Error("Invalid code path. Cannot skip state inputs for input.");if(n==="output"&&i)return e;let o=[],s=0;const a=n==="input"?t.inputs:t.outputs;for(let c=0;c<a.length;c++){const u=a[c];if(r.find(f=>f.id===u)?.type==="state"){if(i)if(e.length===a.length){const f=e[s];o.push(f),s++}else o.push(null);else{s++;continue}continue}else{const f=e[s];o.push(f),s++}}return o}async function ui(e,t,r){const n=this;await li(n,t);const i=await ft(t,void 0,[],!0,r);return(await Promise.all(i.map(async({path:s,blob:a,type:c})=>{if(!a)return{path:s,type:c};const u=await n.upload_files(e,[a]),l=u.files&&u.files[0];return{path:s,file_url:l,type:c,name:typeof File<"u"&&a instanceof File?a?.name:void 0}}))).forEach(({path:s,file_url:a,type:c,name:u})=>{if(c==="Gallery")$t(t,a,s);else if(a){const l=new We({path:a,orig_name:u});$t(t,l,s)}}),t}async function li(e,t){if(!(e.config?.root||e.config?.root_url))throw new Error(vr);await Sr(e,t)}async function Sr(e,t,r=[]){for(const n in t)t[n]instanceof oi?await ci(e,t,n):typeof t[n]=="object"&&t[n]!==null&&await Sr(e,t[n],[...r,n])}async function ci(e,t,r){let n=t[r];const i=e.config?.root||e.config?.root_url;if(!i)throw new Error(vr);try{let o,s;if(typeof process<"u"&&process.versions&&process.versions.node){const l=await h(()=>import("./__vite-browser-external-D7Ct-6yo.js").then(p=>p._),[],import.meta.url);s=(await h(()=>import("./__vite-browser-external-D7Ct-6yo.js").then(p=>p._),[],import.meta.url)).resolve(process.cwd(),n.meta.path),o=await l.readFile(s)}else throw new Error(Fn);const a=new Blob([o],{type:"application/octet-stream"}),c=await e.upload_files(i,[a]),u=c.files&&c.files[0];if(u){const l=new We({path:u,orig_name:n.meta.name||""});t[r]=l}}catch(o){console.error(zn,o)}}async function hi(e,t,r){const n={"Content-Type":"application/json"};this.options.hf_token&&(n.Authorization=`Bearer ${this.options.hf_token}`);try{var i=await this.fetch(e,{method:"POST",body:JSON.stringify(t),headers:{...n,...r},credentials:"include"})}catch{return[{error:me},500]}let o,s;try{o=await i.json(),s=i.status}catch(a){o={error:`Could not parse server response: ${a}`},s=500}return[o,s]}async function fi(e,t={}){let r=!1,n=!1;if(!this.config)throw new Error("Could not resolve app config");if(typeof e=="number")this.config.dependencies.find(i=>i.id==e);else{const i=e.replace(/^\//,"");this.config.dependencies.find(o=>o.id==this.api_map[i])}return new Promise(async(i,o)=>{const s=this.submit(e,t,null,null,!0);let a;for await(const c of s)c.type==="data"&&(n&&i(a),r=!0,a=c),c.type==="status"&&(c.stage==="error"&&o(c),c.stage==="complete"&&(n=!0,r&&i(a)))})}async function Ne(e,t,r){let n=t==="subdomain"?`https://huggingface.co/api/spaces/by-subdomain/${e}`:`https://huggingface.co/api/spaces/${e}`,i,o;try{if(i=await fetch(n),o=i.status,o!==200)throw new Error;i=await i.json()}catch{r({status:"error",load_status:"error",message:Un,detail:"NOT_FOUND"});return}if(!i||o!==200)return;const{runtime:{stage:s},id:a}=i;switch(s){case"STOPPED":case"SLEEPING":r({status:"sleeping",load_status:"pending",message:"Space is asleep. Waking it up...",detail:s}),setTimeout(()=>{Ne(e,t,r)},1e3);break;case"PAUSED":r({status:"paused",load_status:"error",message:"This space has been paused by the author. If you would like to try this demo, consider duplicating the space.",detail:s,discussions_enabled:await Gt(a)});break;case"RUNNING":case"RUNNING_BUILDING":r({status:"running",load_status:"complete",message:"Space is running.",detail:s});break;case"BUILDING":r({status:"building",load_status:"pending",message:"Space is building...",detail:s}),setTimeout(()=>{Ne(e,t,r)},1e3);break;case"APP_STARTING":r({status:"starting",load_status:"pending",message:"Space is starting...",detail:s}),setTimeout(()=>{Ne(e,t,r)},1e3);break;default:r({status:"space_error",load_status:"error",message:"This space is experiencing an issue.",detail:s,discussions_enabled:await Gt(a)});break}}const Ar=async(e,t)=>{let r=0;const n=12,i=5e3;return new Promise(o=>{Ne(e,At.test(e)?"space_name":"subdomain",s=>{t(s),s.status==="running"||s.status==="error"||s.status==="paused"||s.status==="space_error"?o():(s.status==="sleeping"||s.status==="building")&&(r<n?(r++,setTimeout(()=>{Ar(e,t).then(o)},i)):o())})})},pi=/^(?=[^]*\b[dD]iscussions{0,1}\b)(?=[^]*\b[dD]isabled\b)[^]*$/;async function Gt(e){try{const t=await fetch(`https://huggingface.co/api/spaces/${e}/discussions`,{method:"HEAD"}),r=t.headers.get("x-error-message");return!(!t.ok||r&&pi.test(r))}catch{return!1}}async function _i(e,t){const r={};t&&(r.Authorization=`Bearer ${t}`);try{const n=await fetch(`https://huggingface.co/api/spaces/${e}/${Dn}`,{headers:r});if(n.status!==200)throw new Error("Space hardware could not be obtained.");const{hardware:i}=await n.json();return i.current}catch(n){throw new Error(n.message)}}async function di(e,t,r){const n={};r&&(n.Authorization=`Bearer ${r}`);const i={seconds:t};try{const o=await fetch(`https://huggingface.co/api/spaces/${e}/${Hn}`,{method:"POST",headers:{"Content-Type":"application/json",...n},body:JSON.stringify(i)});if(o.status!==200)throw new Error("Could not set sleep timeout on duplicated Space. Please visit *ADD HF LINK TO SETTINGS* to set a timeout manually to reduce billing charges.");return await o.json()}catch(o){throw new Error(o.message)}}const Ft=["cpu-basic","cpu-upgrade","cpu-xl","t4-small","t4-medium","a10g-small","a10g-large","a10g-largex2","a10g-largex4","a100-large","zero-a10g","h100","h100x8"];async function mi(e,t){const{hf_token:r,private:n,hardware:i,timeout:o,auth:s}=t;if(i&&!Ft.includes(i))throw new Error(`Invalid hardware type provided. Valid types are: ${Ft.map(P=>`"${P}"`).join(",")}.`);const{http_protocol:a,host:c}=await Xe(e,r);let u=null;if(s){const P=await wr(a,c,s,fetch);P&&(u=xr(P))}const l={Authorization:`Bearer ${r}`,"Content-Type":"application/json",...u?{Cookie:u.join("; ")}:{}},f=(await(await fetch("https://huggingface.co/api/whoami-v2",{headers:l})).json()).name,p=e.split("/")[1],m={repository:`${f}/${p}`};n&&(m.private=!0);let _;try{i||(_=await _i(e,r))}catch(P){throw Error(St+P.message)}const S=i||_||"cpu-basic";m.hardware=S;try{const P=await fetch(`https://huggingface.co/api/spaces/${e}/duplicate`,{method:"POST",headers:l,body:JSON.stringify(m)});if(P.status===409)try{return await $e.connect(`${f}/${p}`,t)}catch(U){throw console.error("Failed to connect Client instance:",U),U}else if(P.status!==200)throw new Error(P.statusText);const F=await P.json();return await di(`${f}/${p}`,o||300,r),await $e.connect(gi(F.url),t)}catch(P){throw new Error(P)}}function gi(e){const t=/https:\/\/huggingface.co\/spaces\/([^/]+\/[^/]+)/,r=e.match(t);if(r)return r[1]}class bi extends TransformStream{#e="";constructor(t={allowCR:!1}){super({transform:(r,n)=>{for(r=this.#e+r;;){const i=r.indexOf(`
`),o=t.allowCR?r.indexOf("\r"):-1;if(o!==-1&&o!==r.length-1&&(i===-1||i-1>o)){n.enqueue(r.slice(0,o)),r=r.slice(o+1);continue}if(i===-1)break;const s=r[i-1]==="\r"?i-1:i;n.enqueue(r.slice(0,s)),r=r.slice(i+1)}this.#e=r},flush:r=>{if(this.#e==="")return;const n=t.allowCR&&this.#e.endsWith("\r")?this.#e.slice(0,-1):this.#e;r.enqueue(n)}})}}function Ei(e){let t=new TextDecoderStream,r=new bi({allowCR:!0});return e.pipeThrough(t).pipeThrough(r)}function yi(e){let r=/[:]\s*/.exec(e),n=r&&r.index;if(n)return[e.substring(0,n),e.substring(n+r[0].length)]}function zt(e,t,r){e.get(t)||e.set(t,r)}async function*vi(e,t){if(!e.body)return;let r=Ei(e.body),n,i=r.getReader(),o;for(;;){if(t&&t.aborted)return i.cancel();if(n=await i.read(),n.done)return;if(!n.value){o&&(yield o),o=void 0;continue}let[s,a]=yi(n.value)||[];s&&(s==="data"?(o||={},o[s]=o[s]?o[s]+`
`+a:a):s==="event"?(o||={},o[s]=a):s==="id"?(o||={},o[s]=+a||a):s==="retry"&&(o||={},o[s]=+a||void 0))}}async function wi(e,t){let r=new Request(e,t);zt(r.headers,"Accept","text/event-stream"),zt(r.headers,"Content-Type","application/json");let n=await fetch(r);if(!n.ok)throw n;return vi(n,r.signal)}async function xi(){let{event_callbacks:e,unclosed_events:t,pending_stream_messages:r,stream_status:n,config:i,jwt:o}=this;const s=this;if(!i)throw new Error("Could not resolve app config");n.open=!0;let a=null,c=new URLSearchParams({session_hash:this.session_hash}).toString(),u=new URL(`${i.root}${this.api_prefix}/${br}?${c}`);if(o&&u.searchParams.set("__sign",o),a=this.stream(u),!a){console.warn("Cannot connect to SSE endpoint: "+u.toString());return}a.onmessage=async function(l){let f=JSON.parse(l.data);if(f.msg==="close_stream"){Ot(n,s.abort_controller);return}const p=f.event_id;if(!p)await Promise.all(Object.keys(e).map(m=>e[m](f)));else if(e[p]&&i){f.msg==="process_completed"&&["sse","sse_v1","sse_v2","sse_v2.1","sse_v3"].includes(i.protocol)&&t.delete(p);let m=e[p];typeof window<"u"&&typeof document<"u"?setTimeout(m,0,f):m(f)}else r[p]||(r[p]=[]),r[p].push(f)},a.onerror=async function(){await Promise.all(Object.keys(e).map(l=>e[l]({msg:"unexpected_error",message:me})))}}function Ot(e,t){e&&(e.open=!1,t?.abort())}function Ti(e,t,r){!e[t]?(e[t]=[],r.data.forEach((i,o)=>{e[t][o]=i})):r.data.forEach((i,o)=>{let s=Si(e[t][o],i);e[t][o]=s,r.data[o]=s})}function Si(e,t){return t.forEach(([r,n,i])=>{e=Ai(e,n,r,i)}),e}function Ai(e,t,r,n){if(t.length===0){if(r==="replace")return n;if(r==="append")return e+n;throw new Error(`Unsupported action: ${r}`)}let i=e;for(let s=0;s<t.length-1;s++)i=i[t[s]];const o=t[t.length-1];switch(r){case"replace":i[o]=n;break;case"append":i[o]+=n;break;case"add":Array.isArray(i)?i.splice(Number(o),0,n):i[o]=n;break;case"delete":Array.isArray(i)?i.splice(Number(o),1):delete i[o];break;default:throw new Error(`Unknown action: ${r}`)}return e}function Oi(e,t={}){const r={close:()=>{console.warn("Method not implemented.")},onerror:null,onmessage:null,onopen:null,readyState:0,url:e.toString(),withCredentials:!1,CONNECTING:0,OPEN:1,CLOSED:2,addEventListener:()=>{throw new Error("Method not implemented.")},dispatchEvent:()=>{throw new Error("Method not implemented.")},removeEventListener:()=>{throw new Error("Method not implemented.")}};return wi(e,t).then(async n=>{r.readyState=r.OPEN;try{for await(const i of n)r.onmessage&&r.onmessage(i);r.readyState=r.CLOSED}catch(i){r.onerror&&r.onerror(i),r.readyState=r.CLOSED}}).catch(n=>{console.error(n),r.onerror&&r.onerror(n),r.readyState=r.CLOSED}),r}function Pi(e,t={},r,n,i){try{let o=function(I){(i||T[I.type])&&u(I)},s=function(){for(fe=!0;Le.length>0;)Le.shift()({value:void 0,done:!0})},a=function(I){fe||(Le.length>0?Le.shift()(I):pe.push(I))},c=function(I){a(Ri(I)),s()},u=function(I){a({value:I,done:!1})},l=function(){return pe.length>0?Promise.resolve(pe.shift()):fe?Promise.resolve({value:void 0,done:!0}):new Promise(I=>Le.push(I))};const{hf_token:f}=this.options,{fetch:p,app_reference:m,config:_,session_hash:S,api_info:P,api_map:F,stream_status:U,pending_stream_messages:C,pending_diff_streams:M,event_callbacks:j,unclosed_events:H,post_data:k,options:V,api_prefix:z}=this,Pe=this;if(!P)throw new Error("No API found");if(!_)throw new Error("Could not resolve app config");let{fn_index:v,endpoint_info:Ee,dependency:K}=Li(P,e,F,_),Ye=ti(t,Ee),E,y,g=_.protocol??"ws",R="",b=()=>R;const d=typeof e=="number"?"/predict":e;let x,w=null,N=!1,Z={},oe=typeof window<"u"&&typeof document<"u"?new URLSearchParams(window.location.search).toString():"";const T=V?.events?.reduce((I,ne)=>(I[ne]=!0,I),{})||{};async function X(){const I={stage:"complete",queue:!1,time:new Date};N=I,o({...I,type:"status",endpoint:d,fn_index:v});let ne={},_e={};g==="ws"?(E&&E.readyState===0?E.addEventListener("open",()=>{E.close()}):E.close(),ne={fn_index:v,session_hash:S}):(ne={event_id:w},_e={event_id:w,session_hash:S,fn_index:v});try{if(!_)throw new Error("Could not resolve app config");"event_id"in _e&&await p(`${_.root}${z}/${Mn}`,{headers:{"Content-Type":"application/json"},method:"POST",body:JSON.stringify(_e)}),await p(`${_.root}${z}/${Bn}`,{headers:{"Content-Type":"application/json"},method:"POST",body:JSON.stringify(ne)})}catch{console.warn("The `/reset` endpoint could not be called. Subsequent endpoint results may be unreliable.")}}const Re=async I=>{await this._resolve_hearbeat(I)};async function he(I){if(!_)return;let ne=I.render_id;_.components=[..._.components.filter(q=>q.props.rendered_in!==ne),...I.components],_.dependencies=[..._.dependencies.filter(q=>q.rendered_in!==ne),...I.dependencies];const _e=_.components.some(q=>q.type==="state"),B=_.dependencies.some(q=>q.targets.some(ee=>ee[1]==="unload"));_.connect_heartbeat=_e||B,await Re(_),o({type:"render",data:I,endpoint:d,fn_index:v})}this.handle_blob(_.root,Ye,Ee).then(async I=>{if(x={data:Ie(I,K,_.components,"input",!0)||[],event_data:r,fn_index:v,trigger_id:n},si(v,_))o({type:"status",endpoint:d,stage:"pending",queue:!1,fn_index:v,time:new Date}),k(`${_.root}${z}/run${d.startsWith("/")?d:`/${d}`}${oe?"?"+oe:""}`,{...x,session_hash:S}).then(([B,q])=>{const ee=B.data;q==200?(o({type:"data",endpoint:d,fn_index:v,data:Ie(ee,K,_.components,"output",V.with_null_state),time:new Date,event_data:r,trigger_id:n}),B.render_config&&he(B.render_config),o({type:"status",endpoint:d,fn_index:v,stage:"complete",eta:B.average_duration,queue:!1,time:new Date})):o({type:"status",stage:"error",endpoint:d,fn_index:v,message:B.error,queue:!1,time:new Date})}).catch(B=>{o({type:"status",stage:"error",message:B.message,endpoint:d,fn_index:v,queue:!1,time:new Date})});else if(g=="ws"){const{ws_protocol:B,host:q}=await Xe(m,f);o({type:"status",stage:"pending",queue:!0,endpoint:d,fn_index:v,time:new Date});let ee=new URL(`${B}://${qn(q,_.root,!0)}/queue/join${oe?"?"+oe:""}`);this.jwt&&ee.searchParams.set("__sign",this.jwt),E=new WebSocket(ee),E.onclose=Q=>{Q.wasClean||o({type:"status",stage:"error",broken:!0,message:me,queue:!0,endpoint:d,fn_index:v,time:new Date})},E.onmessage=function(Q){const te=JSON.parse(Q.data),{type:$,status:W,data:J}=nt(te,Z[v]);if($==="update"&&W&&!N)o({type:"status",endpoint:d,fn_index:v,time:new Date,...W}),W.stage==="error"&&E.close();else if($==="hash"){E.send(JSON.stringify({fn_index:v,session_hash:S}));return}else $==="data"?E.send(JSON.stringify({...x,session_hash:S})):$==="complete"?N=W:$==="log"?o({type:"log",title:J.title,log:J.log,level:J.level,endpoint:d,duration:J.duration,visible:J.visible,fn_index:v}):$==="generating"&&o({type:"status",time:new Date,...W,stage:W?.stage,queue:!0,endpoint:d,fn_index:v});J&&(o({type:"data",time:new Date,data:Ie(J.data,K,_.components,"output",V.with_null_state),endpoint:d,fn_index:v,event_data:r,trigger_id:n}),N&&(o({type:"status",time:new Date,...N,stage:W?.stage,queue:!0,endpoint:d,fn_index:v}),E.close()))},gr(_.version||"2.0.0","3.6")<0&&addEventListener("open",()=>E.send(JSON.stringify({hash:S})))}else if(g=="sse"){o({type:"status",stage:"pending",queue:!0,endpoint:d,fn_index:v,time:new Date});var _e=new URLSearchParams({fn_index:v.toString(),session_hash:S}).toString();let B=new URL(`${_.root}${z}/${br}?${oe?oe+"&":""}${_e}`);if(this.jwt&&B.searchParams.set("__sign",this.jwt),y=this.stream(B),!y)return Promise.reject(new Error("Cannot connect to SSE endpoint: "+B.toString()));y.onmessage=async function(q){const ee=JSON.parse(q.data),{type:Q,status:te,data:$}=nt(ee,Z[v]);if(Q==="update"&&te&&!N)o({type:"status",endpoint:d,fn_index:v,time:new Date,...te}),te.stage==="error"&&(y?.close(),s());else if(Q==="data"){let[W,J]=await k(`${_.root}${z}/queue/data`,{...x,session_hash:S,event_id:w});J!==200&&(o({type:"status",stage:"error",message:me,queue:!0,endpoint:d,fn_index:v,time:new Date}),y?.close(),s())}else Q==="complete"?N=te:Q==="log"?o({type:"log",title:$.title,log:$.log,level:$.level,endpoint:d,duration:$.duration,visible:$.visible,fn_index:v}):(Q==="generating"||Q==="streaming")&&o({type:"status",time:new Date,...te,stage:te?.stage,queue:!0,endpoint:d,fn_index:v});$&&(o({type:"data",time:new Date,data:Ie($.data,K,_.components,"output",V.with_null_state),endpoint:d,fn_index:v,event_data:r,trigger_id:n}),N&&(o({type:"status",time:new Date,...N,stage:te?.stage,queue:!0,endpoint:d,fn_index:v}),y?.close(),s()))}}else if(g=="sse_v1"||g=="sse_v2"||g=="sse_v2.1"||g=="sse_v3"){o({type:"status",stage:"pending",queue:!0,endpoint:d,fn_index:v,time:new Date});let B="";typeof window<"u"&&typeof document<"u"&&(B=window?.location?.hostname);const ee=B.includes(".dev.")?`https://moon-${B.split(".")[1]}.dev.spaces.huggingface.tech`:"https://huggingface.co";(typeof window<"u"&&typeof document<"u"&&window.parent!=window&&window.supports_zerogpu_headers?ai("zerogpu-headers",ee):Promise.resolve(null)).then(W=>k(`${_.root}${z}/${Rn}?${oe}`,{...x,session_hash:S},W)).then(async([W,J])=>{if(J===503)o({type:"status",stage:"error",message:Er,queue:!0,endpoint:d,fn_index:v,time:new Date});else if(J!==200)o({type:"status",stage:"error",message:me,queue:!0,endpoint:d,fn_index:v,time:new Date});else{w=W.event_id,R=w;let Mt=async function(Ke){try{const{type:ie,status:re,data:Y,original_msg:Sn}=nt(Ke,Z[v]);if(ie=="heartbeat")return;if(ie==="update"&&re&&!N)o({type:"status",endpoint:d,fn_index:v,time:new Date,original_msg:Sn,...re});else if(ie==="complete")N=re;else if(ie=="unexpected_error")console.error("Unexpected error",re?.message),o({type:"status",stage:"error",message:re?.message||"An Unexpected Error Occurred!",queue:!0,endpoint:d,fn_index:v,time:new Date});else if(ie==="log"){o({type:"log",title:Y.title,log:Y.log,level:Y.level,endpoint:d,duration:Y.duration,visible:Y.visible,fn_index:v});return}else(ie==="generating"||ie==="streaming")&&(o({type:"status",time:new Date,...re,stage:re?.stage,queue:!0,endpoint:d,fn_index:v}),Y&&K.connection!=="stream"&&["sse_v2","sse_v2.1","sse_v3"].includes(g)&&Ti(M,w,Y));Y&&(o({type:"data",time:new Date,data:Ie(Y.data,K,_.components,"output",V.with_null_state),endpoint:d,fn_index:v}),Y.render_config&&await he(Y.render_config),N&&(o({type:"status",time:new Date,...N,stage:re?.stage,queue:!0,endpoint:d,fn_index:v}),s())),(re?.stage==="complete"||re?.stage==="error")&&(j[w]&&delete j[w],w in M&&delete M[w])}catch(ie){console.error("Unexpected client exception",ie),o({type:"status",stage:"error",message:"An Unexpected Error Occurred!",queue:!0,endpoint:d,fn_index:v,time:new Date}),["sse_v2","sse_v2.1","sse_v3"].includes(g)&&(Ot(U,Pe.abort_controller),U.open=!1,s())}};w in C&&(C[w].forEach(Ke=>Mt(Ke)),delete C[w]),j[w]=Mt,H.add(w),U.open||await this.open_stream()}})}});let fe=!1;const pe=[],Le=[],Bt={[Symbol.asyncIterator]:()=>Bt,next:l,throw:async I=>(c(I),l()),return:async()=>(s(),l()),cancel:X,event_id:b};return Bt}catch(o){throw console.error("Submit function encountered an error:",o),o}}function Ri(e){return{then:(t,r)=>r(e)}}function Li(e,t,r,n){let i,o,s;if(typeof t=="number")i=t,o=e.unnamed_endpoints[i],s=n.dependencies.find(a=>a.id==t);else{const a=t.replace(/^\//,"");i=r[a],o=e.named_endpoints[t.trim()],s=n.dependencies.find(c=>c.id==r[a])}if(typeof i!="number")throw new Error("There is no endpoint matching that name of fn_index matching that number.");return{fn_index:i,endpoint_info:o,dependency:s}}class $e{app_reference;options;deep_link=null;config;api_prefix="";api_info;api_map={};session_hash=Math.random().toString(36).substring(2);jwt=!1;last_status={};cookies=null;stream_status={open:!1};closed=!1;pending_stream_messages={};pending_diff_streams={};event_callbacks={};unclosed_events=new Set;heartbeat_event=null;abort_controller=null;stream_instance=null;current_payload;ws_map={};get_url_config(t=null){if(!this.config)throw new Error(ae);t===null&&(t=window.location.href);const r=s=>s.replace(/^\/+|\/+$/g,"");let n=r(new URL(this.config.root).pathname),i=r(new URL(t).pathname),o;return i.startsWith(n)?o=r(i.substring(n.length)):o="",this.get_page_config(o)}get_page_config(t){if(!this.config)throw new Error(ae);let r=this.config;return t in r.page||(t=""),{...r,current_page:t,layout:r.page[t].layout,components:r.components.filter(n=>r.page[t].components.includes(n.id)),dependencies:this.config.dependencies.filter(n=>r.page[t].dependencies.includes(n.id))}}fetch(t,r){const n=new Headers(r?.headers||{});if(this&&this.cookies&&n.append("Cookie",this.cookies),this&&this.options.headers)for(const i in this.options.headers)n.append(i,this.options.headers[i]);return fetch(t,{...r,headers:n})}stream(t){const r=new Headers;if(this&&this.cookies&&r.append("Cookie",this.cookies),this&&this.options.headers)for(const n in this.options.headers)r.append(n,this.options.headers[n]);return this&&this.options.hf_token&&r.append("Authorization",`Bearer ${this.options.hf_token}`),this.abort_controller=new AbortController,this.stream_instance=Oi(t.toString(),{credentials:"include",headers:r,signal:this.abort_controller.signal}),this.stream_instance}view_api;upload_files;upload;handle_blob;post_data;submit;predict;open_stream;resolve_config;resolve_cookies;constructor(t,r={events:["data"]}){this.app_reference=t,this.deep_link=r.query_params?.deep_link||null,r.events||(r.events=["data"]),this.options=r,this.current_payload={},this.view_api=ri.bind(this),this.upload_files=ni.bind(this),this.handle_blob=ui.bind(this),this.post_data=hi.bind(this),this.submit=Pi.bind(this),this.predict=fi.bind(this),this.open_stream=xi.bind(this),this.resolve_config=Wn.bind(this),this.resolve_cookies=Zn.bind(this),this.upload=ii.bind(this),this.fetch=this.fetch.bind(this),this.handle_space_success=this.handle_space_success.bind(this),this.stream=this.stream.bind(this)}async init(){this.options.auth&&await this.resolve_cookies(),await this._resolve_config().then(({config:t})=>this._resolve_hearbeat(t)),this.api_info=await this.view_api(),this.api_map=Xn(this.config?.dependencies||[])}async _resolve_hearbeat(t){if(t&&(this.config=t,this.api_prefix=t.api_prefix||"",this.config&&this.config.connect_heartbeat&&this.config.space_id&&this.options.hf_token&&(this.jwt=await Vt(this.config.space_id,this.options.hf_token,this.cookies))),t.space_id&&this.options.hf_token&&(this.jwt=await Vt(t.space_id,this.options.hf_token)),this.config&&this.config.connect_heartbeat){const r=new URL(`${this.config.root}${this.api_prefix}/${Nn}/${this.session_hash}`);this.jwt&&r.searchParams.set("__sign",this.jwt),this.heartbeat_event||(this.heartbeat_event=this.stream(r))}}static async connect(t,r={events:["data"]}){const n=new this(t,r);return r.session_hash&&(n.session_hash=r.session_hash),await n.init(),n}close(){this.closed=!0,Ot(this.stream_status,this.abort_controller)}set_current_payload(t){this.current_payload=t}static async duplicate(t,r={events:["data"]}){return mi(t,r)}async _resolve_config(){const{http_protocol:t,host:r,space_id:n}=await Xe(this.app_reference,this.options.hf_token),{status_callback:i}=this.options;n&&i&&await Ar(n,i);let o;try{let s=`${t}//${r}`;if(o=await this.resolve_config(s),!o)throw new Error(ae);return this.config_success(o)}catch(s){if(n&&i)Ne(n,At.test(n)?"space_name":"subdomain",this.handle_space_success);else throw i&&i({status:"error",message:"Could not load this space.",load_status:"error",detail:"NOT_FOUND"}),Error(s)}}async config_success(t){if(this.config=t,this.api_prefix=t.api_prefix||"",this.config.auth_required)return this.prepare_return_obj();try{this.api_info=await this.view_api()}catch(r){console.error(jn+r.message)}return this.prepare_return_obj()}async handle_space_success(t){if(!this)throw new Error(ae);const{status_callback:r}=this.options;if(r&&r(t),t.status==="running")try{if(this.config=await this._resolve_config(),this.api_prefix=this?.config?.api_prefix||"",!this.config)throw new Error(ae);return await this.config_success(this.config)}catch(n){throw r&&r({status:"error",message:"Could not load this space.",load_status:"error",detail:"NOT_FOUND"}),n}}async component_server(t,r,n){if(!this.config)throw new Error(ae);const i={},{hf_token:o}=this.options,{session_hash:s}=this;o&&(i.Authorization=`Bearer ${this.options.hf_token}`);let a,c=this.config.components.find(l=>l.id===t);c?.props?.root_url?a=c.props.root_url:a=this.config.root;let u;if("binary"in n){u=new FormData;for(const l in n.data)l!=="binary"&&u.append(l,n.data[l]);u.set("component_id",t.toString()),u.set("fn_name",r),u.set("session_hash",s)}else u=JSON.stringify({data:n,component_id:t,fn_name:r,session_hash:s}),i["Content-Type"]="application/json";o&&(i.Authorization=`Bearer ${o}`);try{const l=await this.fetch(`${a}${this.api_prefix}/${Cn}/`,{method:"POST",body:u,headers:i,credentials:"include"});if(!l.ok)throw new Error("Could not connect to component server: "+l.statusText);return await l.json()}catch(l){console.warn(l)}}set_cookies(t){this.cookies=xr(t).join("; ")}prepare_return_obj(){return{config:this.config,predict:this.predict,submit:this.submit,view_api:this.view_api,component_server:this.component_server}}async connect_ws(t){return new Promise((r,n)=>{let i;try{i=new WebSocket(t)}catch{this.ws_map[t]="failed";return}this.ws_map[t]="pending",i.onopen=()=>{this.ws_map[t]=i,r()},i.onerror=o=>{console.error("WebSocket error:",o),this.close_ws(t),this.ws_map[t]="failed",r()},i.onclose=()=>{this.ws_map[t]="closed"},i.onmessage=o=>{}})}async send_ws_message(t,r){if(!(t in this.ws_map))await this.connect_ws(t);else if(this.ws_map[t]==="pending"||this.ws_map[t]==="closed"||this.ws_map[t]==="failed")return;const n=this.ws_map[t];n instanceof WebSocket?n.send(JSON.stringify(r)):this.post_data(t,r)}async close_ws(t){if(t in this.ws_map){const r=this.ws_map[t];r instanceof WebSocket&&(r.close(),delete this.ws_map[t])}}}function ve(){}const Zs=e=>e;function Qs(e,t){for(const r in t)e[r]=t[r];return e}function Ii(e){return e()}function Di(e){e.forEach(Ii)}function Hi(e){return typeof e=="function"}function Ni(e,t){return e!=e?t==t:e!==t||e&&typeof e=="object"||typeof e=="function"}function Or(e,...t){if(e==null){for(const n of t)n(void 0);return ve}const r=e.subscribe(...t);return r.unsubscribe?()=>r.unsubscribe():r}function Pr(e){let t;return Or(e,r=>t=r)(),t}function Ys(e){const t=typeof e=="string"&&e.match(/^\s*(-?[\d.]+)([^\s]*)\s*$/);return t?[parseFloat(t[1]),t[2]||"px"]:[e,"px"]}const ye=[];function Ci(e,t){return{subscribe:le(e,t).subscribe}}function le(e,t=ve){let r;const n=new Set;function i(a){if(Ni(e,a)&&(e=a,r)){const c=!ye.length;for(const u of n)u[1](),ye.push(u,e);if(c){for(let u=0;u<ye.length;u+=2)ye[u][0](ye[u+1]);ye.length=0}}}function o(a){i(a(e))}function s(a,c=ve){const u=[a,c];return n.add(u),n.size===1&&(r=t(i,o)||ve),a(e),()=>{n.delete(u),n.size===0&&r&&(r(),r=null)}}return{set:i,update:o,subscribe:s}}function ge(e,t,r){const n=!Array.isArray(e),i=n?[e]:e;if(!i.every(Boolean))throw new Error("derived() expects stores as input, got a falsy value");const o=t.length<2;return Ci(r,(s,a)=>{let c=!1;const u=[];let l=0,f=ve;const p=()=>{if(l)return;f();const _=t(n?u[0]:u,s,a);o?s(_):f=Hi(_)?_:ve},m=i.map((_,S)=>Or(_,P=>{u[S]=P,l&=~(1<<S),c&&p()},()=>{l|=1<<S}));return c=!0,p(),function(){Di(m),f(),c=!1}})}var Ks=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{};function Bi(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}function ea(e){if(e.__esModule)return e;var t=e.default;if(typeof t=="function"){var r=function n(){return this instanceof n?Reflect.construct(t,arguments,this.constructor):t.apply(this,arguments)};r.prototype=t.prototype}else r={};return Object.defineProperty(r,"__esModule",{value:!0}),Object.keys(e).forEach(function(n){var i=Object.getOwnPropertyDescriptor(e,n);Object.defineProperty(r,n,i.get?i:{enumerable:!0,get:function(){return e[n]}})}),r}var Mi=function(t){return ki(t)&&!Ui(t)};function ki(e){return!!e&&typeof e=="object"}function Ui(e){var t=Object.prototype.toString.call(e);return t==="[object RegExp]"||t==="[object Date]"||$i(e)}var ji=typeof Symbol=="function"&&Symbol.for,Vi=ji?Symbol.for("react.element"):60103;function $i(e){return e.$$typeof===Vi}function Gi(e){return Array.isArray(e)?[]:{}}function Me(e,t){return t.clone!==!1&&t.isMergeableObject(e)?we(Gi(e),e,t):e}function Fi(e,t,r){return e.concat(t).map(function(n){return Me(n,r)})}function zi(e,t){if(!t.customMerge)return we;var r=t.customMerge(e);return typeof r=="function"?r:we}function qi(e){return Object.getOwnPropertySymbols?Object.getOwnPropertySymbols(e).filter(function(t){return Object.propertyIsEnumerable.call(e,t)}):[]}function qt(e){return Object.keys(e).concat(qi(e))}function Rr(e,t){try{return t in e}catch{return!1}}function Xi(e,t){return Rr(e,t)&&!(Object.hasOwnProperty.call(e,t)&&Object.propertyIsEnumerable.call(e,t))}function Wi(e,t,r){var n={};return r.isMergeableObject(e)&&qt(e).forEach(function(i){n[i]=Me(e[i],r)}),qt(t).forEach(function(i){Xi(e,i)||(Rr(e,i)&&r.isMergeableObject(t[i])?n[i]=zi(i,r)(e[i],t[i],r):n[i]=Me(t[i],r))}),n}function we(e,t,r){r=r||{},r.arrayMerge=r.arrayMerge||Fi,r.isMergeableObject=r.isMergeableObject||Mi,r.cloneUnlessOtherwiseSpecified=Me;var n=Array.isArray(t),i=Array.isArray(e),o=n===i;return o?n?r.arrayMerge(e,t,r):Wi(e,t,r):Me(t,r)}we.all=function(t,r){if(!Array.isArray(t))throw new Error("first argument should be an array");return t.reduce(function(n,i){return we(n,i,r)},{})};var Ji=we,Zi=Ji;const Qi=Bi(Zi);var pt=function(e,t){return pt=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,n){r.__proto__=n}||function(r,n){for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(r[i]=n[i])},pt(e,t)};function Je(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");pt(e,t);function r(){this.constructor=e}e.prototype=t===null?Object.create(t):(r.prototype=t.prototype,new r)}var L=function(){return L=Object.assign||function(t){for(var r,n=1,i=arguments.length;n<i;n++){r=arguments[n];for(var o in r)Object.prototype.hasOwnProperty.call(r,o)&&(t[o]=r[o])}return t},L.apply(this,arguments)};function Yi(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(r[n]=e[n]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var i=0,n=Object.getOwnPropertySymbols(e);i<n.length;i++)t.indexOf(n[i])<0&&Object.prototype.propertyIsEnumerable.call(e,n[i])&&(r[n[i]]=e[n[i]]);return r}function it(e,t,r){if(r||arguments.length===2)for(var n=0,i=t.length,o;n<i;n++)(o||!(n in t))&&(o||(o=Array.prototype.slice.call(t,0,n)),o[n]=t[n]);return e.concat(o||Array.prototype.slice.call(t))}var A;(function(e){e[e.EXPECT_ARGUMENT_CLOSING_BRACE=1]="EXPECT_ARGUMENT_CLOSING_BRACE",e[e.EMPTY_ARGUMENT=2]="EMPTY_ARGUMENT",e[e.MALFORMED_ARGUMENT=3]="MALFORMED_ARGUMENT",e[e.EXPECT_ARGUMENT_TYPE=4]="EXPECT_ARGUMENT_TYPE",e[e.INVALID_ARGUMENT_TYPE=5]="INVALID_ARGUMENT_TYPE",e[e.EXPECT_ARGUMENT_STYLE=6]="EXPECT_ARGUMENT_STYLE",e[e.INVALID_NUMBER_SKELETON=7]="INVALID_NUMBER_SKELETON",e[e.INVALID_DATE_TIME_SKELETON=8]="INVALID_DATE_TIME_SKELETON",e[e.EXPECT_NUMBER_SKELETON=9]="EXPECT_NUMBER_SKELETON",e[e.EXPECT_DATE_TIME_SKELETON=10]="EXPECT_DATE_TIME_SKELETON",e[e.UNCLOSED_QUOTE_IN_ARGUMENT_STYLE=11]="UNCLOSED_QUOTE_IN_ARGUMENT_STYLE",e[e.EXPECT_SELECT_ARGUMENT_OPTIONS=12]="EXPECT_SELECT_ARGUMENT_OPTIONS",e[e.EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE=13]="EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE",e[e.INVALID_PLURAL_ARGUMENT_OFFSET_VALUE=14]="INVALID_PLURAL_ARGUMENT_OFFSET_VALUE",e[e.EXPECT_SELECT_ARGUMENT_SELECTOR=15]="EXPECT_SELECT_ARGUMENT_SELECTOR",e[e.EXPECT_PLURAL_ARGUMENT_SELECTOR=16]="EXPECT_PLURAL_ARGUMENT_SELECTOR",e[e.EXPECT_SELECT_ARGUMENT_SELECTOR_FRAGMENT=17]="EXPECT_SELECT_ARGUMENT_SELECTOR_FRAGMENT",e[e.EXPECT_PLURAL_ARGUMENT_SELECTOR_FRAGMENT=18]="EXPECT_PLURAL_ARGUMENT_SELECTOR_FRAGMENT",e[e.INVALID_PLURAL_ARGUMENT_SELECTOR=19]="INVALID_PLURAL_ARGUMENT_SELECTOR",e[e.DUPLICATE_PLURAL_ARGUMENT_SELECTOR=20]="DUPLICATE_PLURAL_ARGUMENT_SELECTOR",e[e.DUPLICATE_SELECT_ARGUMENT_SELECTOR=21]="DUPLICATE_SELECT_ARGUMENT_SELECTOR",e[e.MISSING_OTHER_CLAUSE=22]="MISSING_OTHER_CLAUSE",e[e.INVALID_TAG=23]="INVALID_TAG",e[e.INVALID_TAG_NAME=25]="INVALID_TAG_NAME",e[e.UNMATCHED_CLOSING_TAG=26]="UNMATCHED_CLOSING_TAG",e[e.UNCLOSED_TAG=27]="UNCLOSED_TAG"})(A||(A={}));var D;(function(e){e[e.literal=0]="literal",e[e.argument=1]="argument",e[e.number=2]="number",e[e.date=3]="date",e[e.time=4]="time",e[e.select=5]="select",e[e.plural=6]="plural",e[e.pound=7]="pound",e[e.tag=8]="tag"})(D||(D={}));var xe;(function(e){e[e.number=0]="number",e[e.dateTime=1]="dateTime"})(xe||(xe={}));function Xt(e){return e.type===D.literal}function Ki(e){return e.type===D.argument}function Lr(e){return e.type===D.number}function Ir(e){return e.type===D.date}function Dr(e){return e.type===D.time}function Hr(e){return e.type===D.select}function Nr(e){return e.type===D.plural}function eo(e){return e.type===D.pound}function Cr(e){return e.type===D.tag}function Br(e){return!!(e&&typeof e=="object"&&e.type===xe.number)}function _t(e){return!!(e&&typeof e=="object"&&e.type===xe.dateTime)}var Mr=/[ \xA0\u1680\u2000-\u200A\u202F\u205F\u3000]/,to=/(?:[Eec]{1,6}|G{1,5}|[Qq]{1,5}|(?:[yYur]+|U{1,5})|[ML]{1,5}|d{1,2}|D{1,3}|F{1}|[abB]{1,5}|[hkHK]{1,2}|w{1,2}|W{1}|m{1,2}|s{1,2}|[zZOvVxX]{1,4})(?=([^']*'[^']*')*[^']*$)/g;function ro(e){var t={};return e.replace(to,function(r){var n=r.length;switch(r[0]){case"G":t.era=n===4?"long":n===5?"narrow":"short";break;case"y":t.year=n===2?"2-digit":"numeric";break;case"Y":case"u":case"U":case"r":throw new RangeError("`Y/u/U/r` (year) patterns are not supported, use `y` instead");case"q":case"Q":throw new RangeError("`q/Q` (quarter) patterns are not supported");case"M":case"L":t.month=["numeric","2-digit","short","long","narrow"][n-1];break;case"w":case"W":throw new RangeError("`w/W` (week) patterns are not supported");case"d":t.day=["numeric","2-digit"][n-1];break;case"D":case"F":case"g":throw new RangeError("`D/F/g` (day) patterns are not supported, use `d` instead");case"E":t.weekday=n===4?"long":n===5?"narrow":"short";break;case"e":if(n<4)throw new RangeError("`e..eee` (weekday) patterns are not supported");t.weekday=["short","long","narrow","short"][n-4];break;case"c":if(n<4)throw new RangeError("`c..ccc` (weekday) patterns are not supported");t.weekday=["short","long","narrow","short"][n-4];break;case"a":t.hour12=!0;break;case"b":case"B":throw new RangeError("`b/B` (period) patterns are not supported, use `a` instead");case"h":t.hourCycle="h12",t.hour=["numeric","2-digit"][n-1];break;case"H":t.hourCycle="h23",t.hour=["numeric","2-digit"][n-1];break;case"K":t.hourCycle="h11",t.hour=["numeric","2-digit"][n-1];break;case"k":t.hourCycle="h24",t.hour=["numeric","2-digit"][n-1];break;case"j":case"J":case"C":throw new RangeError("`j/J/C` (hour) patterns are not supported, use `h/H/K/k` instead");case"m":t.minute=["numeric","2-digit"][n-1];break;case"s":t.second=["numeric","2-digit"][n-1];break;case"S":case"A":throw new RangeError("`S/A` (second) patterns are not supported, use `s` instead");case"z":t.timeZoneName=n<4?"short":"long";break;case"Z":case"O":case"v":case"V":case"X":case"x":throw new RangeError("`Z/O/v/V/X/x` (timeZone) patterns are not supported, use `z` instead")}return""}),t}var no=/[\t-\r \x85\u200E\u200F\u2028\u2029]/i;function io(e){if(e.length===0)throw new Error("Number skeleton cannot be empty");for(var t=e.split(no).filter(function(p){return p.length>0}),r=[],n=0,i=t;n<i.length;n++){var o=i[n],s=o.split("/");if(s.length===0)throw new Error("Invalid number skeleton");for(var a=s[0],c=s.slice(1),u=0,l=c;u<l.length;u++){var f=l[u];if(f.length===0)throw new Error("Invalid number skeleton")}r.push({stem:a,options:c})}return r}function oo(e){return e.replace(/^(.*?)-/,"")}var Wt=/^\.(?:(0+)(\*)?|(#+)|(0+)(#+))$/g,kr=/^(@+)?(\+|#+)?[rs]?$/g,so=/(\*)(0+)|(#+)(0+)|(0+)/g,Ur=/^(0+)$/;function Jt(e){var t={};return e[e.length-1]==="r"?t.roundingPriority="morePrecision":e[e.length-1]==="s"&&(t.roundingPriority="lessPrecision"),e.replace(kr,function(r,n,i){return typeof i!="string"?(t.minimumSignificantDigits=n.length,t.maximumSignificantDigits=n.length):i==="+"?t.minimumSignificantDigits=n.length:n[0]==="#"?t.maximumSignificantDigits=n.length:(t.minimumSignificantDigits=n.length,t.maximumSignificantDigits=n.length+(typeof i=="string"?i.length:0)),""}),t}function jr(e){switch(e){case"sign-auto":return{signDisplay:"auto"};case"sign-accounting":case"()":return{currencySign:"accounting"};case"sign-always":case"+!":return{signDisplay:"always"};case"sign-accounting-always":case"()!":return{signDisplay:"always",currencySign:"accounting"};case"sign-except-zero":case"+?":return{signDisplay:"exceptZero"};case"sign-accounting-except-zero":case"()?":return{signDisplay:"exceptZero",currencySign:"accounting"};case"sign-never":case"+_":return{signDisplay:"never"}}}function ao(e){var t;if(e[0]==="E"&&e[1]==="E"?(t={notation:"engineering"},e=e.slice(2)):e[0]==="E"&&(t={notation:"scientific"},e=e.slice(1)),t){var r=e.slice(0,2);if(r==="+!"?(t.signDisplay="always",e=e.slice(2)):r==="+?"&&(t.signDisplay="exceptZero",e=e.slice(2)),!Ur.test(e))throw new Error("Malformed concise eng/scientific notation");t.minimumIntegerDigits=e.length}return t}function Zt(e){var t={},r=jr(e);return r||t}function uo(e){for(var t={},r=0,n=e;r<n.length;r++){var i=n[r];switch(i.stem){case"percent":case"%":t.style="percent";continue;case"%x100":t.style="percent",t.scale=100;continue;case"currency":t.style="currency",t.currency=i.options[0];continue;case"group-off":case",_":t.useGrouping=!1;continue;case"precision-integer":case".":t.maximumFractionDigits=0;continue;case"measure-unit":case"unit":t.style="unit",t.unit=oo(i.options[0]);continue;case"compact-short":case"K":t.notation="compact",t.compactDisplay="short";continue;case"compact-long":case"KK":t.notation="compact",t.compactDisplay="long";continue;case"scientific":t=L(L(L({},t),{notation:"scientific"}),i.options.reduce(function(c,u){return L(L({},c),Zt(u))},{}));continue;case"engineering":t=L(L(L({},t),{notation:"engineering"}),i.options.reduce(function(c,u){return L(L({},c),Zt(u))},{}));continue;case"notation-simple":t.notation="standard";continue;case"unit-width-narrow":t.currencyDisplay="narrowSymbol",t.unitDisplay="narrow";continue;case"unit-width-short":t.currencyDisplay="code",t.unitDisplay="short";continue;case"unit-width-full-name":t.currencyDisplay="name",t.unitDisplay="long";continue;case"unit-width-iso-code":t.currencyDisplay="symbol";continue;case"scale":t.scale=parseFloat(i.options[0]);continue;case"rounding-mode-floor":t.roundingMode="floor";continue;case"rounding-mode-ceiling":t.roundingMode="ceil";continue;case"rounding-mode-down":t.roundingMode="trunc";continue;case"rounding-mode-up":t.roundingMode="expand";continue;case"rounding-mode-half-even":t.roundingMode="halfEven";continue;case"rounding-mode-half-down":t.roundingMode="halfTrunc";continue;case"rounding-mode-half-up":t.roundingMode="halfExpand";continue;case"integer-width":if(i.options.length>1)throw new RangeError("integer-width stems only accept a single optional option");i.options[0].replace(so,function(c,u,l,f,p,m){if(u)t.minimumIntegerDigits=l.length;else{if(f&&p)throw new Error("We currently do not support maximum integer digits");if(m)throw new Error("We currently do not support exact integer digits")}return""});continue}if(Ur.test(i.stem)){t.minimumIntegerDigits=i.stem.length;continue}if(Wt.test(i.stem)){if(i.options.length>1)throw new RangeError("Fraction-precision stems only accept a single optional option");i.stem.replace(Wt,function(c,u,l,f,p,m){return l==="*"?t.minimumFractionDigits=u.length:f&&f[0]==="#"?t.maximumFractionDigits=f.length:p&&m?(t.minimumFractionDigits=p.length,t.maximumFractionDigits=p.length+m.length):(t.minimumFractionDigits=u.length,t.maximumFractionDigits=u.length),""});var o=i.options[0];o==="w"?t=L(L({},t),{trailingZeroDisplay:"stripIfInteger"}):o&&(t=L(L({},t),Jt(o)));continue}if(kr.test(i.stem)){t=L(L({},t),Jt(i.stem));continue}var s=jr(i.stem);s&&(t=L(L({},t),s));var a=ao(i.stem);a&&(t=L(L({},t),a))}return t}var je={"001":["H","h"],AC:["H","h","hb","hB"],AD:["H","hB"],AE:["h","hB","hb","H"],AF:["H","hb","hB","h"],AG:["h","hb","H","hB"],AI:["H","h","hb","hB"],AL:["h","H","hB"],AM:["H","hB"],AO:["H","hB"],AR:["H","h","hB","hb"],AS:["h","H"],AT:["H","hB"],AU:["h","hb","H","hB"],AW:["H","hB"],AX:["H"],AZ:["H","hB","h"],BA:["H","hB","h"],BB:["h","hb","H","hB"],BD:["h","hB","H"],BE:["H","hB"],BF:["H","hB"],BG:["H","hB","h"],BH:["h","hB","hb","H"],BI:["H","h"],BJ:["H","hB"],BL:["H","hB"],BM:["h","hb","H","hB"],BN:["hb","hB","h","H"],BO:["H","hB","h","hb"],BQ:["H"],BR:["H","hB"],BS:["h","hb","H","hB"],BT:["h","H"],BW:["H","h","hb","hB"],BY:["H","h"],BZ:["H","h","hb","hB"],CA:["h","hb","H","hB"],CC:["H","h","hb","hB"],CD:["hB","H"],CF:["H","h","hB"],CG:["H","hB"],CH:["H","hB","h"],CI:["H","hB"],CK:["H","h","hb","hB"],CL:["H","h","hB","hb"],CM:["H","h","hB"],CN:["H","hB","hb","h"],CO:["h","H","hB","hb"],CP:["H"],CR:["H","h","hB","hb"],CU:["H","h","hB","hb"],CV:["H","hB"],CW:["H","hB"],CX:["H","h","hb","hB"],CY:["h","H","hb","hB"],CZ:["H"],DE:["H","hB"],DG:["H","h","hb","hB"],DJ:["h","H"],DK:["H"],DM:["h","hb","H","hB"],DO:["h","H","hB","hb"],DZ:["h","hB","hb","H"],EA:["H","h","hB","hb"],EC:["H","hB","h","hb"],EE:["H","hB"],EG:["h","hB","hb","H"],EH:["h","hB","hb","H"],ER:["h","H"],ES:["H","hB","h","hb"],ET:["hB","hb","h","H"],FI:["H"],FJ:["h","hb","H","hB"],FK:["H","h","hb","hB"],FM:["h","hb","H","hB"],FO:["H","h"],FR:["H","hB"],GA:["H","hB"],GB:["H","h","hb","hB"],GD:["h","hb","H","hB"],GE:["H","hB","h"],GF:["H","hB"],GG:["H","h","hb","hB"],GH:["h","H"],GI:["H","h","hb","hB"],GL:["H","h"],GM:["h","hb","H","hB"],GN:["H","hB"],GP:["H","hB"],GQ:["H","hB","h","hb"],GR:["h","H","hb","hB"],GT:["H","h","hB","hb"],GU:["h","hb","H","hB"],GW:["H","hB"],GY:["h","hb","H","hB"],HK:["h","hB","hb","H"],HN:["H","h","hB","hb"],HR:["H","hB"],HU:["H","h"],IC:["H","h","hB","hb"],ID:["H"],IE:["H","h","hb","hB"],IL:["H","hB"],IM:["H","h","hb","hB"],IN:["h","H"],IO:["H","h","hb","hB"],IQ:["h","hB","hb","H"],IR:["hB","H"],IS:["H"],IT:["H","hB"],JE:["H","h","hb","hB"],JM:["h","hb","H","hB"],JO:["h","hB","hb","H"],JP:["H","K","h"],KE:["hB","hb","H","h"],KG:["H","h","hB","hb"],KH:["hB","h","H","hb"],KI:["h","hb","H","hB"],KM:["H","h","hB","hb"],KN:["h","hb","H","hB"],KP:["h","H","hB","hb"],KR:["h","H","hB","hb"],KW:["h","hB","hb","H"],KY:["h","hb","H","hB"],KZ:["H","hB"],LA:["H","hb","hB","h"],LB:["h","hB","hb","H"],LC:["h","hb","H","hB"],LI:["H","hB","h"],LK:["H","h","hB","hb"],LR:["h","hb","H","hB"],LS:["h","H"],LT:["H","h","hb","hB"],LU:["H","h","hB"],LV:["H","hB","hb","h"],LY:["h","hB","hb","H"],MA:["H","h","hB","hb"],MC:["H","hB"],MD:["H","hB"],ME:["H","hB","h"],MF:["H","hB"],MG:["H","h"],MH:["h","hb","H","hB"],MK:["H","h","hb","hB"],ML:["H"],MM:["hB","hb","H","h"],MN:["H","h","hb","hB"],MO:["h","hB","hb","H"],MP:["h","hb","H","hB"],MQ:["H","hB"],MR:["h","hB","hb","H"],MS:["H","h","hb","hB"],MT:["H","h"],MU:["H","h"],MV:["H","h"],MW:["h","hb","H","hB"],MX:["H","h","hB","hb"],MY:["hb","hB","h","H"],MZ:["H","hB"],NA:["h","H","hB","hb"],NC:["H","hB"],NE:["H"],NF:["H","h","hb","hB"],NG:["H","h","hb","hB"],NI:["H","h","hB","hb"],NL:["H","hB"],NO:["H","h"],NP:["H","h","hB"],NR:["H","h","hb","hB"],NU:["H","h","hb","hB"],NZ:["h","hb","H","hB"],OM:["h","hB","hb","H"],PA:["h","H","hB","hb"],PE:["H","hB","h","hb"],PF:["H","h","hB"],PG:["h","H"],PH:["h","hB","hb","H"],PK:["h","hB","H"],PL:["H","h"],PM:["H","hB"],PN:["H","h","hb","hB"],PR:["h","H","hB","hb"],PS:["h","hB","hb","H"],PT:["H","hB"],PW:["h","H"],PY:["H","h","hB","hb"],QA:["h","hB","hb","H"],RE:["H","hB"],RO:["H","hB"],RS:["H","hB","h"],RU:["H"],RW:["H","h"],SA:["h","hB","hb","H"],SB:["h","hb","H","hB"],SC:["H","h","hB"],SD:["h","hB","hb","H"],SE:["H"],SG:["h","hb","H","hB"],SH:["H","h","hb","hB"],SI:["H","hB"],SJ:["H"],SK:["H"],SL:["h","hb","H","hB"],SM:["H","h","hB"],SN:["H","h","hB"],SO:["h","H"],SR:["H","hB"],SS:["h","hb","H","hB"],ST:["H","hB"],SV:["H","h","hB","hb"],SX:["H","h","hb","hB"],SY:["h","hB","hb","H"],SZ:["h","hb","H","hB"],TA:["H","h","hb","hB"],TC:["h","hb","H","hB"],TD:["h","H","hB"],TF:["H","h","hB"],TG:["H","hB"],TH:["H","h"],TJ:["H","h"],TL:["H","hB","hb","h"],TM:["H","h"],TN:["h","hB","hb","H"],TO:["h","H"],TR:["H","hB"],TT:["h","hb","H","hB"],TW:["hB","hb","h","H"],TZ:["hB","hb","H","h"],UA:["H","hB","h"],UG:["hB","hb","H","h"],UM:["h","hb","H","hB"],US:["h","hb","H","hB"],UY:["H","h","hB","hb"],UZ:["H","hB","h"],VA:["H","h","hB"],VC:["h","hb","H","hB"],VE:["h","H","hB","hb"],VG:["h","hb","H","hB"],VI:["h","hb","H","hB"],VN:["H","h"],VU:["h","H"],WF:["H","hB"],WS:["h","H"],XK:["H","hB","h"],YE:["h","hB","hb","H"],YT:["H","hB"],ZA:["H","h","hb","hB"],ZM:["h","hb","H","hB"],ZW:["H","h"],"af-ZA":["H","h","hB","hb"],"ar-001":["h","hB","hb","H"],"ca-ES":["H","h","hB"],"en-001":["h","hb","H","hB"],"es-BO":["H","h","hB","hb"],"es-BR":["H","h","hB","hb"],"es-EC":["H","h","hB","hb"],"es-ES":["H","h","hB","hb"],"es-GQ":["H","h","hB","hb"],"es-PE":["H","h","hB","hb"],"fr-CA":["H","h","hB"],"gl-ES":["H","h","hB"],"gu-IN":["hB","hb","h","H"],"hi-IN":["hB","h","H"],"it-CH":["H","h","hB"],"it-IT":["H","h","hB"],"kn-IN":["hB","h","H"],"ml-IN":["hB","h","H"],"mr-IN":["hB","hb","h","H"],"pa-IN":["hB","hb","h","H"],"ta-IN":["hB","h","hb","H"],"te-IN":["hB","h","H"],"zu-ZA":["H","hB","hb","h"]};function lo(e,t){for(var r="",n=0;n<e.length;n++){var i=e.charAt(n);if(i==="j"){for(var o=0;n+1<e.length&&e.charAt(n+1)===i;)o++,n++;var s=1+(o&1),a=o<2?1:3+(o>>1),c="a",u=co(t);for((u=="H"||u=="k")&&(a=0);a-- >0;)r+=c;for(;s-- >0;)r=u+r}else i==="J"?r+="H":r+=i}return r}function co(e){var t=e.hourCycle;if(t===void 0&&e.hourCycles&&e.hourCycles.length&&(t=e.hourCycles[0]),t)switch(t){case"h24":return"k";case"h23":return"H";case"h12":return"h";case"h11":return"K";default:throw new Error("Invalid hourCycle")}var r=e.language,n;r!=="root"&&(n=e.maximize().region);var i=je[n||""]||je[r||""]||je["".concat(r,"-001")]||je["001"];return i[0]}var ot,ho=new RegExp("^".concat(Mr.source,"*")),fo=new RegExp("".concat(Mr.source,"*$"));function O(e,t){return{start:e,end:t}}var po=!!String.prototype.startsWith&&"_a".startsWith("a",1),_o=!!String.fromCodePoint,mo=!!Object.fromEntries,go=!!String.prototype.codePointAt,bo=!!String.prototype.trimStart,Eo=!!String.prototype.trimEnd,yo=!!Number.isSafeInteger,vo=yo?Number.isSafeInteger:function(e){return typeof e=="number"&&isFinite(e)&&Math.floor(e)===e&&Math.abs(e)<=9007199254740991},dt=!0;try{var wo=$r("([^\\p{White_Space}\\p{Pattern_Syntax}]*)","yu");dt=((ot=wo.exec("a"))===null||ot===void 0?void 0:ot[0])==="a"}catch{dt=!1}var Qt=po?function(t,r,n){return t.startsWith(r,n)}:function(t,r,n){return t.slice(n,n+r.length)===r},mt=_o?String.fromCodePoint:function(){for(var t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];for(var n="",i=t.length,o=0,s;i>o;){if(s=t[o++],s>1114111)throw RangeError(s+" is not a valid code point");n+=s<65536?String.fromCharCode(s):String.fromCharCode(((s-=65536)>>10)+55296,s%1024+56320)}return n},Yt=mo?Object.fromEntries:function(t){for(var r={},n=0,i=t;n<i.length;n++){var o=i[n],s=o[0],a=o[1];r[s]=a}return r},Vr=go?function(t,r){return t.codePointAt(r)}:function(t,r){var n=t.length;if(!(r<0||r>=n)){var i=t.charCodeAt(r),o;return i<55296||i>56319||r+1===n||(o=t.charCodeAt(r+1))<56320||o>57343?i:(i-55296<<10)+(o-56320)+65536}},xo=bo?function(t){return t.trimStart()}:function(t){return t.replace(ho,"")},To=Eo?function(t){return t.trimEnd()}:function(t){return t.replace(fo,"")};function $r(e,t){return new RegExp(e,t)}var gt;if(dt){var Kt=$r("([^\\p{White_Space}\\p{Pattern_Syntax}]*)","yu");gt=function(t,r){var n;Kt.lastIndex=r;var i=Kt.exec(t);return(n=i[1])!==null&&n!==void 0?n:""}}else gt=function(t,r){for(var n=[];;){var i=Vr(t,r);if(i===void 0||Gr(i)||Po(i))break;n.push(i),r+=i>=65536?2:1}return mt.apply(void 0,n)};var So=function(){function e(t,r){r===void 0&&(r={}),this.message=t,this.position={offset:0,line:1,column:1},this.ignoreTag=!!r.ignoreTag,this.locale=r.locale,this.requiresOtherClause=!!r.requiresOtherClause,this.shouldParseSkeletons=!!r.shouldParseSkeletons}return e.prototype.parse=function(){if(this.offset()!==0)throw Error("parser can only be used once");return this.parseMessage(0,"",!1)},e.prototype.parseMessage=function(t,r,n){for(var i=[];!this.isEOF();){var o=this.char();if(o===123){var s=this.parseArgument(t,n);if(s.err)return s;i.push(s.val)}else{if(o===125&&t>0)break;if(o===35&&(r==="plural"||r==="selectordinal")){var a=this.clonePosition();this.bump(),i.push({type:D.pound,location:O(a,this.clonePosition())})}else if(o===60&&!this.ignoreTag&&this.peek()===47){if(n)break;return this.error(A.UNMATCHED_CLOSING_TAG,O(this.clonePosition(),this.clonePosition()))}else if(o===60&&!this.ignoreTag&&bt(this.peek()||0)){var s=this.parseTag(t,r);if(s.err)return s;i.push(s.val)}else{var s=this.parseLiteral(t,r);if(s.err)return s;i.push(s.val)}}}return{val:i,err:null}},e.prototype.parseTag=function(t,r){var n=this.clonePosition();this.bump();var i=this.parseTagName();if(this.bumpSpace(),this.bumpIf("/>"))return{val:{type:D.literal,value:"<".concat(i,"/>"),location:O(n,this.clonePosition())},err:null};if(this.bumpIf(">")){var o=this.parseMessage(t+1,r,!0);if(o.err)return o;var s=o.val,a=this.clonePosition();if(this.bumpIf("</")){if(this.isEOF()||!bt(this.char()))return this.error(A.INVALID_TAG,O(a,this.clonePosition()));var c=this.clonePosition(),u=this.parseTagName();return i!==u?this.error(A.UNMATCHED_CLOSING_TAG,O(c,this.clonePosition())):(this.bumpSpace(),this.bumpIf(">")?{val:{type:D.tag,value:i,children:s,location:O(n,this.clonePosition())},err:null}:this.error(A.INVALID_TAG,O(a,this.clonePosition())))}else return this.error(A.UNCLOSED_TAG,O(n,this.clonePosition()))}else return this.error(A.INVALID_TAG,O(n,this.clonePosition()))},e.prototype.parseTagName=function(){var t=this.offset();for(this.bump();!this.isEOF()&&Oo(this.char());)this.bump();return this.message.slice(t,this.offset())},e.prototype.parseLiteral=function(t,r){for(var n=this.clonePosition(),i="";;){var o=this.tryParseQuote(r);if(o){i+=o;continue}var s=this.tryParseUnquoted(t,r);if(s){i+=s;continue}var a=this.tryParseLeftAngleBracket();if(a){i+=a;continue}break}var c=O(n,this.clonePosition());return{val:{type:D.literal,value:i,location:c},err:null}},e.prototype.tryParseLeftAngleBracket=function(){return!this.isEOF()&&this.char()===60&&(this.ignoreTag||!Ao(this.peek()||0))?(this.bump(),"<"):null},e.prototype.tryParseQuote=function(t){if(this.isEOF()||this.char()!==39)return null;switch(this.peek()){case 39:return this.bump(),this.bump(),"'";case 123:case 60:case 62:case 125:break;case 35:if(t==="plural"||t==="selectordinal")break;return null;default:return null}this.bump();var r=[this.char()];for(this.bump();!this.isEOF();){var n=this.char();if(n===39)if(this.peek()===39)r.push(39),this.bump();else{this.bump();break}else r.push(n);this.bump()}return mt.apply(void 0,r)},e.prototype.tryParseUnquoted=function(t,r){if(this.isEOF())return null;var n=this.char();return n===60||n===123||n===35&&(r==="plural"||r==="selectordinal")||n===125&&t>0?null:(this.bump(),mt(n))},e.prototype.parseArgument=function(t,r){var n=this.clonePosition();if(this.bump(),this.bumpSpace(),this.isEOF())return this.error(A.EXPECT_ARGUMENT_CLOSING_BRACE,O(n,this.clonePosition()));if(this.char()===125)return this.bump(),this.error(A.EMPTY_ARGUMENT,O(n,this.clonePosition()));var i=this.parseIdentifierIfPossible().value;if(!i)return this.error(A.MALFORMED_ARGUMENT,O(n,this.clonePosition()));if(this.bumpSpace(),this.isEOF())return this.error(A.EXPECT_ARGUMENT_CLOSING_BRACE,O(n,this.clonePosition()));switch(this.char()){case 125:return this.bump(),{val:{type:D.argument,value:i,location:O(n,this.clonePosition())},err:null};case 44:return this.bump(),this.bumpSpace(),this.isEOF()?this.error(A.EXPECT_ARGUMENT_CLOSING_BRACE,O(n,this.clonePosition())):this.parseArgumentOptions(t,r,i,n);default:return this.error(A.MALFORMED_ARGUMENT,O(n,this.clonePosition()))}},e.prototype.parseIdentifierIfPossible=function(){var t=this.clonePosition(),r=this.offset(),n=gt(this.message,r),i=r+n.length;this.bumpTo(i);var o=this.clonePosition(),s=O(t,o);return{value:n,location:s}},e.prototype.parseArgumentOptions=function(t,r,n,i){var o,s=this.clonePosition(),a=this.parseIdentifierIfPossible().value,c=this.clonePosition();switch(a){case"":return this.error(A.EXPECT_ARGUMENT_TYPE,O(s,c));case"number":case"date":case"time":{this.bumpSpace();var u=null;if(this.bumpIf(",")){this.bumpSpace();var l=this.clonePosition(),f=this.parseSimpleArgStyleIfPossible();if(f.err)return f;var p=To(f.val);if(p.length===0)return this.error(A.EXPECT_ARGUMENT_STYLE,O(this.clonePosition(),this.clonePosition()));var m=O(l,this.clonePosition());u={style:p,styleLocation:m}}var _=this.tryParseArgumentClose(i);if(_.err)return _;var S=O(i,this.clonePosition());if(u&&Qt(u?.style,"::",0)){var P=xo(u.style.slice(2));if(a==="number"){var f=this.parseNumberSkeletonFromString(P,u.styleLocation);return f.err?f:{val:{type:D.number,value:n,location:S,style:f.val},err:null}}else{if(P.length===0)return this.error(A.EXPECT_DATE_TIME_SKELETON,S);var F=P;this.locale&&(F=lo(P,this.locale));var p={type:xe.dateTime,pattern:F,location:u.styleLocation,parsedOptions:this.shouldParseSkeletons?ro(F):{}},U=a==="date"?D.date:D.time;return{val:{type:U,value:n,location:S,style:p},err:null}}}return{val:{type:a==="number"?D.number:a==="date"?D.date:D.time,value:n,location:S,style:(o=u?.style)!==null&&o!==void 0?o:null},err:null}}case"plural":case"selectordinal":case"select":{var C=this.clonePosition();if(this.bumpSpace(),!this.bumpIf(","))return this.error(A.EXPECT_SELECT_ARGUMENT_OPTIONS,O(C,L({},C)));this.bumpSpace();var M=this.parseIdentifierIfPossible(),j=0;if(a!=="select"&&M.value==="offset"){if(!this.bumpIf(":"))return this.error(A.EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE,O(this.clonePosition(),this.clonePosition()));this.bumpSpace();var f=this.tryParseDecimalInteger(A.EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE,A.INVALID_PLURAL_ARGUMENT_OFFSET_VALUE);if(f.err)return f;this.bumpSpace(),M=this.parseIdentifierIfPossible(),j=f.val}var H=this.tryParsePluralOrSelectOptions(t,a,r,M);if(H.err)return H;var _=this.tryParseArgumentClose(i);if(_.err)return _;var k=O(i,this.clonePosition());return a==="select"?{val:{type:D.select,value:n,options:Yt(H.val),location:k},err:null}:{val:{type:D.plural,value:n,options:Yt(H.val),offset:j,pluralType:a==="plural"?"cardinal":"ordinal",location:k},err:null}}default:return this.error(A.INVALID_ARGUMENT_TYPE,O(s,c))}},e.prototype.tryParseArgumentClose=function(t){return this.isEOF()||this.char()!==125?this.error(A.EXPECT_ARGUMENT_CLOSING_BRACE,O(t,this.clonePosition())):(this.bump(),{val:!0,err:null})},e.prototype.parseSimpleArgStyleIfPossible=function(){for(var t=0,r=this.clonePosition();!this.isEOF();){var n=this.char();switch(n){case 39:{this.bump();var i=this.clonePosition();if(!this.bumpUntil("'"))return this.error(A.UNCLOSED_QUOTE_IN_ARGUMENT_STYLE,O(i,this.clonePosition()));this.bump();break}case 123:{t+=1,this.bump();break}case 125:{if(t>0)t-=1;else return{val:this.message.slice(r.offset,this.offset()),err:null};break}default:this.bump();break}}return{val:this.message.slice(r.offset,this.offset()),err:null}},e.prototype.parseNumberSkeletonFromString=function(t,r){var n=[];try{n=io(t)}catch{return this.error(A.INVALID_NUMBER_SKELETON,r)}return{val:{type:xe.number,tokens:n,location:r,parsedOptions:this.shouldParseSkeletons?uo(n):{}},err:null}},e.prototype.tryParsePluralOrSelectOptions=function(t,r,n,i){for(var o,s=!1,a=[],c=new Set,u=i.value,l=i.location;;){if(u.length===0){var f=this.clonePosition();if(r!=="select"&&this.bumpIf("=")){var p=this.tryParseDecimalInteger(A.EXPECT_PLURAL_ARGUMENT_SELECTOR,A.INVALID_PLURAL_ARGUMENT_SELECTOR);if(p.err)return p;l=O(f,this.clonePosition()),u=this.message.slice(f.offset,this.offset())}else break}if(c.has(u))return this.error(r==="select"?A.DUPLICATE_SELECT_ARGUMENT_SELECTOR:A.DUPLICATE_PLURAL_ARGUMENT_SELECTOR,l);u==="other"&&(s=!0),this.bumpSpace();var m=this.clonePosition();if(!this.bumpIf("{"))return this.error(r==="select"?A.EXPECT_SELECT_ARGUMENT_SELECTOR_FRAGMENT:A.EXPECT_PLURAL_ARGUMENT_SELECTOR_FRAGMENT,O(this.clonePosition(),this.clonePosition()));var _=this.parseMessage(t+1,r,n);if(_.err)return _;var S=this.tryParseArgumentClose(m);if(S.err)return S;a.push([u,{value:_.val,location:O(m,this.clonePosition())}]),c.add(u),this.bumpSpace(),o=this.parseIdentifierIfPossible(),u=o.value,l=o.location}return a.length===0?this.error(r==="select"?A.EXPECT_SELECT_ARGUMENT_SELECTOR:A.EXPECT_PLURAL_ARGUMENT_SELECTOR,O(this.clonePosition(),this.clonePosition())):this.requiresOtherClause&&!s?this.error(A.MISSING_OTHER_CLAUSE,O(this.clonePosition(),this.clonePosition())):{val:a,err:null}},e.prototype.tryParseDecimalInteger=function(t,r){var n=1,i=this.clonePosition();this.bumpIf("+")||this.bumpIf("-")&&(n=-1);for(var o=!1,s=0;!this.isEOF();){var a=this.char();if(a>=48&&a<=57)o=!0,s=s*10+(a-48),this.bump();else break}var c=O(i,this.clonePosition());return o?(s*=n,vo(s)?{val:s,err:null}:this.error(r,c)):this.error(t,c)},e.prototype.offset=function(){return this.position.offset},e.prototype.isEOF=function(){return this.offset()===this.message.length},e.prototype.clonePosition=function(){return{offset:this.position.offset,line:this.position.line,column:this.position.column}},e.prototype.char=function(){var t=this.position.offset;if(t>=this.message.length)throw Error("out of bound");var r=Vr(this.message,t);if(r===void 0)throw Error("Offset ".concat(t," is at invalid UTF-16 code unit boundary"));return r},e.prototype.error=function(t,r){return{val:null,err:{kind:t,message:this.message,location:r}}},e.prototype.bump=function(){if(!this.isEOF()){var t=this.char();t===10?(this.position.line+=1,this.position.column=1,this.position.offset+=1):(this.position.column+=1,this.position.offset+=t<65536?1:2)}},e.prototype.bumpIf=function(t){if(Qt(this.message,t,this.offset())){for(var r=0;r<t.length;r++)this.bump();return!0}return!1},e.prototype.bumpUntil=function(t){var r=this.offset(),n=this.message.indexOf(t,r);return n>=0?(this.bumpTo(n),!0):(this.bumpTo(this.message.length),!1)},e.prototype.bumpTo=function(t){if(this.offset()>t)throw Error("targetOffset ".concat(t," must be greater than or equal to the current offset ").concat(this.offset()));for(t=Math.min(t,this.message.length);;){var r=this.offset();if(r===t)break;if(r>t)throw Error("targetOffset ".concat(t," is at invalid UTF-16 code unit boundary"));if(this.bump(),this.isEOF())break}},e.prototype.bumpSpace=function(){for(;!this.isEOF()&&Gr(this.char());)this.bump()},e.prototype.peek=function(){if(this.isEOF())return null;var t=this.char(),r=this.offset(),n=this.message.charCodeAt(r+(t>=65536?2:1));return n??null},e}();function bt(e){return e>=97&&e<=122||e>=65&&e<=90}function Ao(e){return bt(e)||e===47}function Oo(e){return e===45||e===46||e>=48&&e<=57||e===95||e>=97&&e<=122||e>=65&&e<=90||e==183||e>=192&&e<=214||e>=216&&e<=246||e>=248&&e<=893||e>=895&&e<=8191||e>=8204&&e<=8205||e>=8255&&e<=8256||e>=8304&&e<=8591||e>=11264&&e<=12271||e>=12289&&e<=55295||e>=63744&&e<=64975||e>=65008&&e<=65533||e>=65536&&e<=983039}function Gr(e){return e>=9&&e<=13||e===32||e===133||e>=8206&&e<=8207||e===8232||e===8233}function Po(e){return e>=33&&e<=35||e===36||e>=37&&e<=39||e===40||e===41||e===42||e===43||e===44||e===45||e>=46&&e<=47||e>=58&&e<=59||e>=60&&e<=62||e>=63&&e<=64||e===91||e===92||e===93||e===94||e===96||e===123||e===124||e===125||e===126||e===161||e>=162&&e<=165||e===166||e===167||e===169||e===171||e===172||e===174||e===176||e===177||e===182||e===187||e===191||e===215||e===247||e>=8208&&e<=8213||e>=8214&&e<=8215||e===8216||e===8217||e===8218||e>=8219&&e<=8220||e===8221||e===8222||e===8223||e>=8224&&e<=8231||e>=8240&&e<=8248||e===8249||e===8250||e>=8251&&e<=8254||e>=8257&&e<=8259||e===8260||e===8261||e===8262||e>=8263&&e<=8273||e===8274||e===8275||e>=8277&&e<=8286||e>=8592&&e<=8596||e>=8597&&e<=8601||e>=8602&&e<=8603||e>=8604&&e<=8607||e===8608||e>=8609&&e<=8610||e===8611||e>=8612&&e<=8613||e===8614||e>=8615&&e<=8621||e===8622||e>=8623&&e<=8653||e>=8654&&e<=8655||e>=8656&&e<=8657||e===8658||e===8659||e===8660||e>=8661&&e<=8691||e>=8692&&e<=8959||e>=8960&&e<=8967||e===8968||e===8969||e===8970||e===8971||e>=8972&&e<=8991||e>=8992&&e<=8993||e>=8994&&e<=9e3||e===9001||e===9002||e>=9003&&e<=9083||e===9084||e>=9085&&e<=9114||e>=9115&&e<=9139||e>=9140&&e<=9179||e>=9180&&e<=9185||e>=9186&&e<=9254||e>=9255&&e<=9279||e>=9280&&e<=9290||e>=9291&&e<=9311||e>=9472&&e<=9654||e===9655||e>=9656&&e<=9664||e===9665||e>=9666&&e<=9719||e>=9720&&e<=9727||e>=9728&&e<=9838||e===9839||e>=9840&&e<=10087||e===10088||e===10089||e===10090||e===10091||e===10092||e===10093||e===10094||e===10095||e===10096||e===10097||e===10098||e===10099||e===10100||e===10101||e>=10132&&e<=10175||e>=10176&&e<=10180||e===10181||e===10182||e>=10183&&e<=10213||e===10214||e===10215||e===10216||e===10217||e===10218||e===10219||e===10220||e===10221||e===10222||e===10223||e>=10224&&e<=10239||e>=10240&&e<=10495||e>=10496&&e<=10626||e===10627||e===10628||e===10629||e===10630||e===10631||e===10632||e===10633||e===10634||e===10635||e===10636||e===10637||e===10638||e===10639||e===10640||e===10641||e===10642||e===10643||e===10644||e===10645||e===10646||e===10647||e===10648||e>=10649&&e<=10711||e===10712||e===10713||e===10714||e===10715||e>=10716&&e<=10747||e===10748||e===10749||e>=10750&&e<=11007||e>=11008&&e<=11055||e>=11056&&e<=11076||e>=11077&&e<=11078||e>=11079&&e<=11084||e>=11085&&e<=11123||e>=11124&&e<=11125||e>=11126&&e<=11157||e===11158||e>=11159&&e<=11263||e>=11776&&e<=11777||e===11778||e===11779||e===11780||e===11781||e>=11782&&e<=11784||e===11785||e===11786||e===11787||e===11788||e===11789||e>=11790&&e<=11798||e===11799||e>=11800&&e<=11801||e===11802||e===11803||e===11804||e===11805||e>=11806&&e<=11807||e===11808||e===11809||e===11810||e===11811||e===11812||e===11813||e===11814||e===11815||e===11816||e===11817||e>=11818&&e<=11822||e===11823||e>=11824&&e<=11833||e>=11834&&e<=11835||e>=11836&&e<=11839||e===11840||e===11841||e===11842||e>=11843&&e<=11855||e>=11856&&e<=11857||e===11858||e>=11859&&e<=11903||e>=12289&&e<=12291||e===12296||e===12297||e===12298||e===12299||e===12300||e===12301||e===12302||e===12303||e===12304||e===12305||e>=12306&&e<=12307||e===12308||e===12309||e===12310||e===12311||e===12312||e===12313||e===12314||e===12315||e===12316||e===12317||e>=12318&&e<=12319||e===12320||e===12336||e===64830||e===64831||e>=65093&&e<=65094}function Et(e){e.forEach(function(t){if(delete t.location,Hr(t)||Nr(t))for(var r in t.options)delete t.options[r].location,Et(t.options[r].value);else Lr(t)&&Br(t.style)||(Ir(t)||Dr(t))&&_t(t.style)?delete t.style.location:Cr(t)&&Et(t.children)})}function Ro(e,t){t===void 0&&(t={}),t=L({shouldParseSkeletons:!0,requiresOtherClause:!0},t);var r=new So(e,t).parse();if(r.err){var n=SyntaxError(A[r.err.kind]);throw n.location=r.err.location,n.originalMessage=r.err.message,n}return t?.captureLocation||Et(r.val),r.val}function st(e,t){var r=t&&t.cache?t.cache:Co,n=t&&t.serializer?t.serializer:No,i=t&&t.strategy?t.strategy:Io;return i(e,{cache:r,serializer:n})}function Lo(e){return e==null||typeof e=="number"||typeof e=="boolean"}function Fr(e,t,r,n){var i=Lo(n)?n:r(n),o=t.get(i);return typeof o>"u"&&(o=e.call(this,n),t.set(i,o)),o}function zr(e,t,r){var n=Array.prototype.slice.call(arguments,3),i=r(n),o=t.get(i);return typeof o>"u"&&(o=e.apply(this,n),t.set(i,o)),o}function Pt(e,t,r,n,i){return r.bind(t,e,n,i)}function Io(e,t){var r=e.length===1?Fr:zr;return Pt(e,this,r,t.cache.create(),t.serializer)}function Do(e,t){return Pt(e,this,zr,t.cache.create(),t.serializer)}function Ho(e,t){return Pt(e,this,Fr,t.cache.create(),t.serializer)}var No=function(){return JSON.stringify(arguments)};function Rt(){this.cache=Object.create(null)}Rt.prototype.get=function(e){return this.cache[e]};Rt.prototype.set=function(e,t){this.cache[e]=t};var Co={create:function(){return new Rt}},at={variadic:Do,monadic:Ho},Te;(function(e){e.MISSING_VALUE="MISSING_VALUE",e.INVALID_VALUE="INVALID_VALUE",e.MISSING_INTL_API="MISSING_INTL_API"})(Te||(Te={}));var Ze=function(e){Je(t,e);function t(r,n,i){var o=e.call(this,r)||this;return o.code=n,o.originalMessage=i,o}return t.prototype.toString=function(){return"[formatjs Error: ".concat(this.code,"] ").concat(this.message)},t}(Error),er=function(e){Je(t,e);function t(r,n,i,o){return e.call(this,'Invalid values for "'.concat(r,'": "').concat(n,'". Options are "').concat(Object.keys(i).join('", "'),'"'),Te.INVALID_VALUE,o)||this}return t}(Ze),Bo=function(e){Je(t,e);function t(r,n,i){return e.call(this,'Value for "'.concat(r,'" must be of type ').concat(n),Te.INVALID_VALUE,i)||this}return t}(Ze),Mo=function(e){Je(t,e);function t(r,n){return e.call(this,'The intl string context variable "'.concat(r,'" was not provided to the string "').concat(n,'"'),Te.MISSING_VALUE,n)||this}return t}(Ze),G;(function(e){e[e.literal=0]="literal",e[e.object=1]="object"})(G||(G={}));function ko(e){return e.length<2?e:e.reduce(function(t,r){var n=t[t.length-1];return!n||n.type!==G.literal||r.type!==G.literal?t.push(r):n.value+=r.value,t},[])}function Uo(e){return typeof e=="function"}function Ve(e,t,r,n,i,o,s){if(e.length===1&&Xt(e[0]))return[{type:G.literal,value:e[0].value}];for(var a=[],c=0,u=e;c<u.length;c++){var l=u[c];if(Xt(l)){a.push({type:G.literal,value:l.value});continue}if(eo(l)){typeof o=="number"&&a.push({type:G.literal,value:r.getNumberFormat(t).format(o)});continue}var f=l.value;if(!(i&&f in i))throw new Mo(f,s);var p=i[f];if(Ki(l)){(!p||typeof p=="string"||typeof p=="number")&&(p=typeof p=="string"||typeof p=="number"?String(p):""),a.push({type:typeof p=="string"?G.literal:G.object,value:p});continue}if(Ir(l)){var m=typeof l.style=="string"?n.date[l.style]:_t(l.style)?l.style.parsedOptions:void 0;a.push({type:G.literal,value:r.getDateTimeFormat(t,m).format(p)});continue}if(Dr(l)){var m=typeof l.style=="string"?n.time[l.style]:_t(l.style)?l.style.parsedOptions:n.time.medium;a.push({type:G.literal,value:r.getDateTimeFormat(t,m).format(p)});continue}if(Lr(l)){var m=typeof l.style=="string"?n.number[l.style]:Br(l.style)?l.style.parsedOptions:void 0;m&&m.scale&&(p=p*(m.scale||1)),a.push({type:G.literal,value:r.getNumberFormat(t,m).format(p)});continue}if(Cr(l)){var _=l.children,S=l.value,P=i[S];if(!Uo(P))throw new Bo(S,"function",s);var F=Ve(_,t,r,n,i,o),U=P(F.map(function(j){return j.value}));Array.isArray(U)||(U=[U]),a.push.apply(a,U.map(function(j){return{type:typeof j=="string"?G.literal:G.object,value:j}}))}if(Hr(l)){var C=l.options[p]||l.options.other;if(!C)throw new er(l.value,p,Object.keys(l.options),s);a.push.apply(a,Ve(C.value,t,r,n,i));continue}if(Nr(l)){var C=l.options["=".concat(p)];if(!C){if(!Intl.PluralRules)throw new Ze(`Intl.PluralRules is not available in this environment.
Try polyfilling it using "@formatjs/intl-pluralrules"
`,Te.MISSING_INTL_API,s);var M=r.getPluralRules(t,{type:l.pluralType}).select(p-(l.offset||0));C=l.options[M]||l.options.other}if(!C)throw new er(l.value,p,Object.keys(l.options),s);a.push.apply(a,Ve(C.value,t,r,n,i,p-(l.offset||0)));continue}}return ko(a)}function jo(e,t){return t?L(L(L({},e||{}),t||{}),Object.keys(e).reduce(function(r,n){return r[n]=L(L({},e[n]),t[n]||{}),r},{})):e}function Vo(e,t){return t?Object.keys(e).reduce(function(r,n){return r[n]=jo(e[n],t[n]),r},L({},e)):e}function ut(e){return{create:function(){return{get:function(t){return e[t]},set:function(t,r){e[t]=r}}}}}function $o(e){return e===void 0&&(e={number:{},dateTime:{},pluralRules:{}}),{getNumberFormat:st(function(){for(var t,r=[],n=0;n<arguments.length;n++)r[n]=arguments[n];return new((t=Intl.NumberFormat).bind.apply(t,it([void 0],r,!1)))},{cache:ut(e.number),strategy:at.variadic}),getDateTimeFormat:st(function(){for(var t,r=[],n=0;n<arguments.length;n++)r[n]=arguments[n];return new((t=Intl.DateTimeFormat).bind.apply(t,it([void 0],r,!1)))},{cache:ut(e.dateTime),strategy:at.variadic}),getPluralRules:st(function(){for(var t,r=[],n=0;n<arguments.length;n++)r[n]=arguments[n];return new((t=Intl.PluralRules).bind.apply(t,it([void 0],r,!1)))},{cache:ut(e.pluralRules),strategy:at.variadic})}}var qr=function(){function e(t,r,n,i){var o=this;if(r===void 0&&(r=e.defaultLocale),this.formatterCache={number:{},dateTime:{},pluralRules:{}},this.format=function(c){var u=o.formatToParts(c);if(u.length===1)return u[0].value;var l=u.reduce(function(f,p){return!f.length||p.type!==G.literal||typeof f[f.length-1]!="string"?f.push(p.value):f[f.length-1]+=p.value,f},[]);return l.length<=1?l[0]||"":l},this.formatToParts=function(c){return Ve(o.ast,o.locales,o.formatters,o.formats,c,void 0,o.message)},this.resolvedOptions=function(){var c;return{locale:((c=o.resolvedLocale)===null||c===void 0?void 0:c.toString())||Intl.NumberFormat.supportedLocalesOf(o.locales)[0]}},this.getAst=function(){return o.ast},this.locales=r,this.resolvedLocale=e.resolveLocale(r),typeof t=="string"){if(this.message=t,!e.__parse)throw new TypeError("IntlMessageFormat.__parse must be set to process `message` of type `string`");var s=i||{};s.formatters;var a=Yi(s,["formatters"]);this.ast=e.__parse(t,L(L({},a),{locale:this.resolvedLocale}))}else this.ast=t;if(!Array.isArray(this.ast))throw new TypeError("A message must be provided as a String or AST.");this.formats=Vo(e.formats,n),this.formatters=i&&i.formatters||$o(this.formatterCache)}return Object.defineProperty(e,"defaultLocale",{get:function(){return e.memoizedDefaultLocale||(e.memoizedDefaultLocale=new Intl.NumberFormat().resolvedOptions().locale),e.memoizedDefaultLocale},enumerable:!1,configurable:!0}),e.memoizedDefaultLocale=null,e.resolveLocale=function(t){if(!(typeof Intl.Locale>"u")){var r=Intl.NumberFormat.supportedLocalesOf(t);return r.length>0?new Intl.Locale(r[0]):new Intl.Locale(typeof t=="string"?t:t[0])}},e.__parse=Ro,e.formats={number:{integer:{maximumFractionDigits:0},currency:{style:"currency"},percent:{style:"percent"}},date:{short:{month:"numeric",day:"numeric",year:"2-digit"},medium:{month:"short",day:"numeric",year:"numeric"},long:{month:"long",day:"numeric",year:"numeric"},full:{weekday:"long",month:"long",day:"numeric",year:"numeric"}},time:{short:{hour:"numeric",minute:"numeric"},medium:{hour:"numeric",minute:"numeric",second:"numeric"},long:{hour:"numeric",minute:"numeric",second:"numeric",timeZoneName:"short"},full:{hour:"numeric",minute:"numeric",second:"numeric",timeZoneName:"short"}}},e}();function Go(e,t){if(t==null)return;if(t in e)return e[t];const r=t.split(".");let n=e;for(let i=0;i<r.length;i++)if(typeof n=="object"){if(i>0){const o=r.slice(i,r.length).join(".");if(o in n){n=n[o];break}}n=n[r[i]]}else n=void 0;return n}const ue={},Fo=(e,t,r)=>r&&(t in ue||(ue[t]={}),e in ue[t]||(ue[t][e]=r),r),Xr=(e,t)=>{if(t==null)return;if(t in ue&&e in ue[t])return ue[t][e];const r=Ue(t);for(let n=0;n<r.length;n++){const i=r[n],o=qo(i,e);if(o)return Fo(e,t,o)}};let Lt;const Ae=le({});function zo(e){return Lt[e]||null}function It(e){return e in Lt}function qo(e,t){if(!It(e))return null;const r=zo(e);return Go(r,t)}function Xo(e){if(e==null)return;const t=Ue(e);for(let r=0;r<t.length;r++){const n=t[r];if(It(n))return n}}function yt(e,...t){delete ue[e],Ae.update(r=>(r[e]=Qi.all([r[e]||{},...t]),r))}ge([Ae],([e])=>Object.keys(e));Ae.subscribe(e=>Lt=e);const Ce={};function Wo(e){Ce[e]=new Set}function Jo(e,t){Ce[e].delete(t),Ce[e].size===0&&delete Ce[e]}function Be(e){return Ce[e]}function Zo(e){return Ue(e).map(t=>{const r=Be(t);return[t,r?[...r]:[]]}).filter(([,t])=>t.length>0)}function Ge(e){return e==null?!1:Ue(e).some(t=>{var r;return(r=Be(t))==null?void 0:r.size})}function Qo(e,t){return Promise.all(t.map(n=>(Jo(e,n),n().then(i=>i.default||i)))).then(n=>yt(e,...n))}const De={};function Wr(e){if(!Ge(e))return e in De?De[e]:Promise.resolve();const t=Zo(e);return De[e]=Promise.all(t.map(([r,n])=>Qo(r,n))).then(()=>{if(Ge(e))return Wr(e);delete De[e]}),De[e]}function Yo(e,t){Be(e)||Wo(e);const r=Be(e);Be(e).has(t)||(It(e)||Ae.update(n=>(n[e]={},n)),r.add(t))}var tr=Object.getOwnPropertySymbols,Ko=Object.prototype.hasOwnProperty,es=Object.prototype.propertyIsEnumerable,ts=(e,t)=>{var r={};for(var n in e)Ko.call(e,n)&&t.indexOf(n)<0&&(r[n]=e[n]);if(e!=null&&tr)for(var n of tr(e))t.indexOf(n)<0&&es.call(e,n)&&(r[n]=e[n]);return r};const rs={number:{scientific:{notation:"scientific"},engineering:{notation:"engineering"},compactLong:{notation:"compact",compactDisplay:"long"},compactShort:{notation:"compact",compactDisplay:"short"}},date:{short:{month:"numeric",day:"numeric",year:"2-digit"},medium:{month:"short",day:"numeric",year:"numeric"},long:{month:"long",day:"numeric",year:"numeric"},full:{weekday:"long",month:"long",day:"numeric",year:"numeric"}},time:{short:{hour:"numeric",minute:"numeric"},medium:{hour:"numeric",minute:"numeric",second:"numeric"},long:{hour:"numeric",minute:"numeric",second:"numeric",timeZoneName:"short"},full:{hour:"numeric",minute:"numeric",second:"numeric",timeZoneName:"short"}}};function ns({locale:e,id:t}){console.warn(`[svelte-i18n] The message "${t}" was not found in "${Ue(e).join('", "')}".${Ge(ce())?`

Note: there are at least one loader still registered to this locale that wasn't executed.`:""}`)}const is={fallbackLocale:null,loadingDelay:200,formats:rs,warnOnMissingMessages:!0,handleMissingMessage:void 0,ignoreTag:!0},He=is;function Se(){return He}function os(e){const t=e,{formats:r}=t,n=ts(t,["formats"]);let i=e.fallbackLocale;if(e.initialLocale)try{qr.resolveLocale(e.initialLocale)&&(i=e.initialLocale)}catch{console.warn(`[svelte-i18n] The initial locale "${e.initialLocale}" is not a valid locale.`)}return n.warnOnMissingMessages&&(delete n.warnOnMissingMessages,n.handleMissingMessage==null?n.handleMissingMessage=ns:console.warn('[svelte-i18n] The "warnOnMissingMessages" option is deprecated. Please use the "handleMissingMessage" option instead.')),Object.assign(He,n,{initialLocale:i}),r&&("number"in r&&Object.assign(He.formats.number,r.number),"date"in r&&Object.assign(He.formats.date,r.date),"time"in r&&Object.assign(He.formats.time,r.time)),be.set(i)}const lt=le(!1);var ss=Object.defineProperty,as=Object.defineProperties,us=Object.getOwnPropertyDescriptors,rr=Object.getOwnPropertySymbols,ls=Object.prototype.hasOwnProperty,cs=Object.prototype.propertyIsEnumerable,nr=(e,t,r)=>t in e?ss(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,hs=(e,t)=>{for(var r in t||(t={}))ls.call(t,r)&&nr(e,r,t[r]);if(rr)for(var r of rr(t))cs.call(t,r)&&nr(e,r,t[r]);return e},fs=(e,t)=>as(e,us(t));let vt;const Fe=le(null);function ir(e){return e.split("-").map((t,r,n)=>n.slice(0,r+1).join("-")).reverse()}function Ue(e,t=Se().fallbackLocale){const r=ir(e);return t?[...new Set([...r,...ir(t)])]:r}function ce(){return vt??void 0}Fe.subscribe(e=>{vt=e??void 0,typeof window<"u"&&e!=null&&document.documentElement.setAttribute("lang",e)});const ps=e=>{if(e&&Xo(e)&&Ge(e)){const{loadingDelay:t}=Se();let r;return typeof window<"u"&&ce()!=null&&t?r=window.setTimeout(()=>lt.set(!0),t):lt.set(!0),Wr(e).then(()=>{Fe.set(e)}).finally(()=>{clearTimeout(r),lt.set(!1)})}return Fe.set(e)},be=fs(hs({},Fe),{set:ps}),_s=()=>typeof window>"u"?null:window.navigator.language||window.navigator.languages[0],Qe=e=>{const t=Object.create(null);return n=>{const i=JSON.stringify(n);return i in t?t[i]:t[i]=e(n)}};var ds=Object.defineProperty,ze=Object.getOwnPropertySymbols,Jr=Object.prototype.hasOwnProperty,Zr=Object.prototype.propertyIsEnumerable,or=(e,t,r)=>t in e?ds(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,Dt=(e,t)=>{for(var r in t||(t={}))Jr.call(t,r)&&or(e,r,t[r]);if(ze)for(var r of ze(t))Zr.call(t,r)&&or(e,r,t[r]);return e},Oe=(e,t)=>{var r={};for(var n in e)Jr.call(e,n)&&t.indexOf(n)<0&&(r[n]=e[n]);if(e!=null&&ze)for(var n of ze(e))t.indexOf(n)<0&&Zr.call(e,n)&&(r[n]=e[n]);return r};const ke=(e,t)=>{const{formats:r}=Se();if(e in r&&t in r[e])return r[e][t];throw new Error(`[svelte-i18n] Unknown "${t}" ${e} format.`)},ms=Qe(e=>{var t=e,{locale:r,format:n}=t,i=Oe(t,["locale","format"]);if(r==null)throw new Error('[svelte-i18n] A "locale" must be set to format numbers');return n&&(i=ke("number",n)),new Intl.NumberFormat(r,i)}),gs=Qe(e=>{var t=e,{locale:r,format:n}=t,i=Oe(t,["locale","format"]);if(r==null)throw new Error('[svelte-i18n] A "locale" must be set to format dates');return n?i=ke("date",n):Object.keys(i).length===0&&(i=ke("date","short")),new Intl.DateTimeFormat(r,i)}),bs=Qe(e=>{var t=e,{locale:r,format:n}=t,i=Oe(t,["locale","format"]);if(r==null)throw new Error('[svelte-i18n] A "locale" must be set to format time values');return n?i=ke("time",n):Object.keys(i).length===0&&(i=ke("time","short")),new Intl.DateTimeFormat(r,i)}),Es=(e={})=>{var t=e,{locale:r=ce()}=t,n=Oe(t,["locale"]);return ms(Dt({locale:r},n))},ys=(e={})=>{var t=e,{locale:r=ce()}=t,n=Oe(t,["locale"]);return gs(Dt({locale:r},n))},vs=(e={})=>{var t=e,{locale:r=ce()}=t,n=Oe(t,["locale"]);return bs(Dt({locale:r},n))},ws=Qe((e,t=ce())=>new qr(e,t,Se().formats,{ignoreTag:Se().ignoreTag})),xs=(e,t={})=>{var r,n,i,o;let s=t;typeof e=="object"&&(s=e,e=s.id);const{values:a,locale:c=ce(),default:u}=s;if(c==null)throw new Error("[svelte-i18n] Cannot format a message without first setting the initial locale.");let l=Xr(e,c);if(!l)l=(o=(i=(n=(r=Se()).handleMissingMessage)==null?void 0:n.call(r,{locale:c,id:e,defaultValue:u}))!=null?i:u)!=null?o:e;else if(typeof l!="string")return console.warn(`[svelte-i18n] Message with id "${e}" must be of type "string", found: "${typeof l}". Gettin its value through the "$format" method is deprecated; use the "json" method instead.`),l;if(!a)return l;let f=l;try{f=ws(l,c).format(a)}catch(p){p instanceof Error&&console.warn(`[svelte-i18n] Message "${e}" has syntax error:`,p.message)}return f},Ts=(e,t)=>vs(t).format(e),Ss=(e,t)=>ys(t).format(e),As=(e,t)=>Es(t).format(e),Os=(e,t=ce())=>Xr(e,t),Qr=ge([be,Ae],()=>xs);ge([be],()=>Ts);ge([be],()=>Ss);ge([be],()=>As);ge([be,Ae],()=>Os);let Yr=!1;typeof window<"u"&&"attachShadow"in Element.prototype&&"adoptedStyleSheets"in Document.prototype&&(Yr="adoptedStyleSheets"in document.createElement("div").attachShadow({mode:"open"}));function sr(e,t){const r=new URL(import.meta.url).origin;var n=e;if(window.location.origin!==r&&(n=new URL(e,r).href),document.querySelector(`link[href='${n}']`))return Promise.resolve();const o=document.createElement("link");return o.rel="stylesheet",o.href=n,new Promise((s,a)=>{o.addEventListener("load",()=>s()),o.addEventListener("error",()=>{console.error(`Unable to preload CSS for ${n}`),s()}),t.appendChild(o)})}function ta(e,t,r){if(!Yr)return e;r||(r=document.createElement("style")),r.remove();const n=new CSSStyleSheet;n.replaceSync(e);let i="";e=e.replace(/@import\s+url\((.*?)\);\s*/g,(c,u)=>(i+=`@import url(${u});
`,""));const o=n.cssRules;let s="",a=`.gradio-container.gradio-container-${t} .contain `;for(let c=0;c<o.length;c++){const u=o[c];let l=u.cssText.includes(".dark");if(u instanceof CSSStyleRule){const f=u.selectorText;if(f){const p=f.replace(".dark","").split(",").map(m=>`${l?".dark":""} ${a} ${m.trim()} `).join(",");s+=u.cssText,s+=u.cssText.replace(f,p)}}else if(u instanceof CSSMediaRule){let f=`@media ${u.media.mediaText} {`;for(let p=0;p<u.cssRules.length;p++){const m=u.cssRules[p];if(m instanceof CSSStyleRule){let _=m.cssText.includes(".dark ");const S=m.selectorText,P=S.replace(".dark","").split(",").map(F=>`${_?".dark":""} ${a} ${F.trim()} `).join(",");f+=m.cssText.replace(S,P)}}f+="}",s+=f}else if(u instanceof CSSKeyframesRule){s+=`@keyframes ${u.name} {`;for(let f=0;f<u.cssRules.length;f++){const p=u.cssRules[f];p instanceof CSSKeyframeRule&&(s+=`${p.keyText} { ${p.style.cssText} }`)}s+="}"}else u instanceof CSSFontFaceRule&&(s+=`@font-face { ${u.style.cssText} }`)}return i+s}const Ps={accordion:{component:()=>h(()=>import("./Index-CaC1GRz3.js"),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13]),import.meta.url)},annotatedimage:{component:()=>h(()=>import("./Index-GuD1Tw7E.js"),__vite__mapDeps([14,1,2,3,4,5,15,16,17,18,19,9,6,7,8,10,20,21,22,23]),import.meta.url)},audio:{base:()=>h(()=>import("./StaticAudio-DqD2VoK2.js"),__vite__mapDeps([24,25,3,4,5,15,2,9,16,26,27,28,29,18,30,31,32,33,20,21,22,34,35,36]),import.meta.url),example:()=>h(()=>import("./Example-BQyGztrG.js"),__vite__mapDeps([37,38]),import.meta.url),component:()=>h(()=>import("./index-BAVjOiyx.js"),__vite__mapDeps([39,24,25,3,4,5,15,2,9,16,26,27,28,29,18,30,31,32,33,20,21,22,34,35,36,40,41,42,43,10,44,45,7,8,46,47,6,1,48,37,38]),import.meta.url)},box:{component:()=>h(()=>import("./Index-BedFhM2U.js"),__vite__mapDeps([49,1,2,3,4,5]),import.meta.url)},browserstate:{component:()=>h(()=>import("./Index-DHm3X9_J.js"),__vite__mapDeps([50,51]),import.meta.url)},button:{component:()=>h(()=>import("./Index-DXQ3WLFM.js"),__vite__mapDeps([52,53,54,20,21,22,55,3,4,5,56,2,57]),import.meta.url)},chatbot:{component:()=>h(()=>import("./Index-PzJAyUHf.js"),__vite__mapDeps([58,25,54,20,21,22,55,3,4,5,9,2,59,60,10,44,33,18,61,62,63,64,27,65,29,1,15,6,7,8,66,57]),import.meta.url)},checkbox:{example:()=>h(()=>import("./Example-CZ-iEz1g.js"),__vite__mapDeps([67,38]),import.meta.url),component:()=>h(()=>import("./Index-oVWU9m6e.js"),__vite__mapDeps([68,1,2,69,62,3,4,5,6,7,8,9,10,70,71]),import.meta.url)},checkboxgroup:{example:()=>h(()=>import("./Example-DccrJI--.js"),__vite__mapDeps([72,38]),import.meta.url),component:()=>h(()=>import("./Index-DMs26Evw.js"),__vite__mapDeps([73,1,2,74,69,62,3,4,5,6,7,8,9,10,75]),import.meta.url)},code:{example:()=>h(()=>import("./Example-DQvZIwBt.js"),__vite__mapDeps([76,77]),import.meta.url),component:()=>h(()=>import("./Index-BMproeh5.js").then(e=>e.K),__vite__mapDeps([78,21,22,59,60,3,4,5,9,2,28,36,20,18,6,7,8,10,79,1,15,16,76,77,80]),import.meta.url)},colorpicker:{example:()=>h(()=>import("./Example-BaLyJYAe.js"),__vite__mapDeps([81,82]),import.meta.url),component:()=>h(()=>import("./Index-DQ3_4eWv.js"),__vite__mapDeps([83,84,74,69,62,3,4,5,2,1,6,7,8,9,10,81,82,85]),import.meta.url)},column:{component:()=>h(()=>import("./Index-BrpXEgHX.js"),__vite__mapDeps([11,6,7,8,3,4,5,9,2,10,12]),import.meta.url)},core:{component:()=>h(()=>import("./index-Bp4yBqQw.js"),__vite__mapDeps([86,87]),import.meta.url)},dataframe:{example:()=>h(()=>import("./Example-CqPGqNav.js"),__vite__mapDeps([88,89]),import.meta.url),component:()=>h(()=>import("./Index-ALdaZ21I.js"),__vite__mapDeps([90,1,2,3,4,5,91,92,64,41,42,21,22,62,7,8,70,71,59,93,60,19,9,6,10,94,95,25,15,16,26,27,28,17,18,96,54,20,55,36,57,97,45,98,63,46,99,48,100,101,88,89,102]),import.meta.url)},dataset:{component:()=>h(()=>import("./Index-Dvl64pLq.js"),__vite__mapDeps([103,1,2,3,4,5,7,8,104,105,106,107]),import.meta.url)},datetime:{example:()=>h(()=>import("./Example-BBLMS951.js"),[],import.meta.url),component:()=>h(()=>import("./Index-D2QAi4I9.js"),__vite__mapDeps([108,1,2,74,69,62,3,4,5,109,110]),import.meta.url)},downloadbutton:{component:()=>h(()=>import("./Index-CybTFp6a.js"),__vite__mapDeps([111,53,54,20,21,22,55,3,4,5,56,2,57,112]),import.meta.url)},dropdown:{example:()=>h(()=>import("./Example-BgQNfMWT.js"),__vite__mapDeps([113,38]),import.meta.url),component:()=>h(()=>import("./Index-DR-TuqpS.js"),__vite__mapDeps([114,74,69,62,3,4,5,2,93,115,63,116,1,6,7,8,9,10,113,38]),import.meta.url)},file:{example:()=>h(()=>import("./Example-DrmWnoSo.js"),__vite__mapDeps([117,118]),import.meta.url),component:()=>h(()=>import("./Index-DfTFkCtq.js"),__vite__mapDeps([119,120,3,4,5,15,2,16,61,41,42,9,10,18,21,22,36,20,121,1,48,6,7,8,117,118]),import.meta.url)},fileexplorer:{example:()=>h(()=>import("./Example-CIFMxn5c.js"),__vite__mapDeps([122,123]),import.meta.url),component:()=>h(()=>import("./Index-DtzkFa5_.js"),__vite__mapDeps([124,61,1,2,3,4,5,15,6,7,8,9,10,125]),import.meta.url)},form:{component:()=>h(()=>import("./Index-DE1Sah7F.js"),__vite__mapDeps([126,127]),import.meta.url)},gallery:{base:()=>h(()=>import("./Gallery-GP9wgZoX.js"),__vite__mapDeps([128,3,4,5,15,2,9,16,26,27,25,10,28,17,32,18,19,43,44,33,36,21,22,20,54,55,129,34,130,91,131,42,57]),import.meta.url),component:()=>h(()=>import("./Index-BzsA-dBW.js"),__vite__mapDeps([132,1,2,3,4,5,48,41,42,128,15,9,16,26,27,25,10,28,17,32,18,19,43,44,33,36,21,22,20,54,55,129,34,130,91,131,57,6,7,8,120,61,121,118]),import.meta.url)},group:{component:()=>h(()=>import("./Index-WEzAIkMk.js"),__vite__mapDeps([133,134]),import.meta.url)},highlightedtext:{component:()=>h(()=>import("./Index-B14Vg3Db.js"),__vite__mapDeps([135,136,1,2,3,4,5,15,16,6,7,8,9,10,137]),import.meta.url)},html:{base:()=>h(()=>import("./Index-C0bxGwtU.js"),__vite__mapDeps([138,6,7,8,3,4,5,9,2,10,79,1,15,25,139]),import.meta.url),example:()=>h(()=>import("./Example-C2a4WxRl.js"),__vite__mapDeps([140,141]),import.meta.url),component:()=>h(()=>import("./Index-C0bxGwtU.js"),__vite__mapDeps([138,6,7,8,3,4,5,9,2,10,79,1,15,25,139]),import.meta.url)},image:{base:()=>h(()=>import("./ImagePreview-CpxjYXeK.js"),__vite__mapDeps([95,25,3,4,5,15,2,9,16,26,27,28,17,18,19,96,54,20,21,22,55,36,57]),import.meta.url),example:()=>h(()=>import("./Example-CC8yxxGn.js"),__vite__mapDeps([100,54,20,21,22,55,101]),import.meta.url),component:()=>h(()=>import("./Index-B6-B2pPO.js"),__vite__mapDeps([94,95,25,3,4,5,15,2,9,16,26,27,28,17,18,19,96,54,20,21,22,55,36,57,97,10,45,41,42,93,98,7,8,63,46,99,1,48,6,100,101]),import.meta.url)},imageeditor:{example:()=>h(()=>import("./Example-BHKzkkLr.js"),__vite__mapDeps([142,3,4,5,54,20,21,22,55,97,15,2,9,10,17,45,41,42,18,19,96,93,98,7,8,63,46,99,143,57,101]),import.meta.url),component:()=>h(()=>import("./Index-DCEUuTr0.js").then(e=>e.as),__vite__mapDeps([144,95,25,3,4,5,15,2,9,16,26,27,28,17,18,19,96,54,20,21,22,55,36,57,97,10,45,41,42,93,98,7,8,63,46,99,1,6,84,59,65,33,145,101]),import.meta.url)},imageslider:{example:()=>h(()=>import("./Example-j_9MW44b.js"),__vite__mapDeps([146,147]),import.meta.url),component:()=>h(()=>import("./Index-CVEjRJ8U.js"),__vite__mapDeps([148,149,150,20,21,22,3,4,5,15,2,9,16,10,28,17,33,18,19,36,6,7,8,41,42,1,48,151]),import.meta.url)},json:{example:()=>h(()=>import("./Example-D_6QaySw.js"),__vite__mapDeps([152,153,59,60,3,4,5,9,2,16,18,154,155]),import.meta.url),component:()=>h(()=>import("./Index-5i89YVR8.js"),__vite__mapDeps([156,153,59,60,3,4,5,9,2,16,18,154,1,15,6,7,8,10]),import.meta.url)},label:{component:()=>h(()=>import("./Index-DeQezhBd.js"),__vite__mapDeps([157,158,1,2,3,4,5,15,16,6,7,8,9,10,159]),import.meta.url)},markdown:{example:()=>h(()=>import("./Example-Dijoz-Uw.js"),__vite__mapDeps([160,62,3,4,5,38]),import.meta.url),component:()=>h(()=>import("./Index-DTliZ1wA.js"),__vite__mapDeps([161,25,59,60,62,3,4,5,9,2,18,6,7,8,10,1,160,38,162]),import.meta.url)},model3d:{example:()=>h(()=>import("./Example-uQ8MuYg6.js"),__vite__mapDeps([163,38]),import.meta.url),component:()=>h(()=>import("./Index-CxIzfvY9.js"),__vite__mapDeps([164,3,4,5,15,2,9,28,61,33,18,41,42,43,10,44,36,21,22,20,1,16,48,6,7,8,163,38,165]),import.meta.url)},multimodaltextbox:{example:()=>h(()=>import("./Example-BfnVJ_3N.js"),__vite__mapDeps([166,54,20,21,22,55,3,4,5,129,34,130,167,2,57]),import.meta.url),component:()=>h(()=>import("./Index-7wV0551G.js"),__vite__mapDeps([168,74,69,62,3,4,5,2,10,61,45,41,42,29,169,98,170,21,22,54,20,55,40,43,9,28,44,33,18,36,15,7,8,46,30,25,31,32,16,34,35,47,1,6,166,129,130,167,57,171]),import.meta.url)},nativeplot:{example:()=>h(()=>import("./Example-Creifpe8.js"),[],import.meta.url),component:()=>h(()=>import("./Index-BmjPKxr3.js"),__vite__mapDeps([172,1,2,74,69,62,3,4,5,16,158,18,19,9,6,7,8,10,173]),import.meta.url)},number:{example:()=>h(()=>import("./Example-CqL1e7EB.js"),__vite__mapDeps([174,38]),import.meta.url),component:()=>h(()=>import("./Index-DlaooGe8.js"),__vite__mapDeps([175,1,2,74,69,62,3,4,5,6,7,8,9,10,176]),import.meta.url)},paramviewer:{example:()=>h(()=>import("./Example-C9__vDgN.js"),__vite__mapDeps([177,38]),import.meta.url),component:()=>h(()=>import("./Index-BSKlTVUx.js"),__vite__mapDeps([178,4,179]),import.meta.url)},plot:{base:()=>h(()=>import("./Plot-DPlFUnzo.js").then(e=>e.b),__vite__mapDeps([180,3,4,5,16,2]),import.meta.url),component:()=>h(()=>import("./Index-BR1sVb0F.js"),__vite__mapDeps([181,180,3,4,5,16,2,1,15,18,19,9,6,7,8,10]),import.meta.url)},radio:{example:()=>h(()=>import("./Example-BoMLuz1A.js"),__vite__mapDeps([182,38]),import.meta.url),component:()=>h(()=>import("./Index-DurZmyFC.js"),__vite__mapDeps([183,1,2,74,69,62,3,4,5,6,7,8,9,10,182,38,184]),import.meta.url)},row:{component:()=>h(()=>import("./Index-BFah9pAF.js"),__vite__mapDeps([185,6,7,8,3,4,5,9,2,10,186]),import.meta.url)},sidebar:{component:()=>h(()=>import("./Index-Gj0WTAu3.js"),__vite__mapDeps([187,6,7,8,3,4,5,9,2,10,11,12,188]),import.meta.url)},sketchbox:{component:()=>h(()=>import("./Index-BlWK1-fD.js"),__vite__mapDeps([189,190]),import.meta.url)},slider:{example:()=>h(()=>import("./Example-BrizabXh.js"),__vite__mapDeps([191,38]),import.meta.url),component:()=>h(()=>import("./Index-3JI__9QL.js"),__vite__mapDeps([192,1,2,74,69,62,3,4,5,6,7,8,9,10,193]),import.meta.url)},state:{component:()=>h(()=>import("./Index-uRgjJb4U.js"),[],import.meta.url)},statustracker:{component:()=>h(()=>import("./index-DOyaaCjr.js"),__vite__mapDeps([194,6,7,8,3,4,5,9,2,10,195,63,46]),import.meta.url)},tabitem:{component:()=>h(()=>import("./Index-CBYcBFye.js"),__vite__mapDeps([196,197,198,11,6,7,8,3,4,5,9,2,10,12,199]),import.meta.url)},tabs:{component:()=>h(()=>import("./Index-CiKJIoEh.js"),__vite__mapDeps([200,197,198]),import.meta.url)},textbox:{example:()=>h(()=>import("./Example-BdAjEacD.js"),__vite__mapDeps([104,105]),import.meta.url),component:()=>h(()=>import("./Index-BFGcUqmi.js"),__vite__mapDeps([201,202,74,69,62,3,4,5,2,59,60,169,98,63,107,1,6,7,8,9,10,104,105]),import.meta.url)},timer:{component:()=>h(()=>import("./Index-BMLc4VxK.js"),[],import.meta.url)},uploadbutton:{component:()=>h(()=>import("./Index-B6Hnnfyx.js"),__vite__mapDeps([203,53,54,20,21,22,55,3,4,5,56,2,57,204]),import.meta.url)},video:{base:()=>h(()=>import("./VideoPreview-31w_cOLN.js").then(e=>e.a),__vite__mapDeps([205,3,4,5,15,2,9,16,26,27,25,28,170,18,36,21,22,20,31,32,33,129,34,130,43,10,44,206,42]),import.meta.url),example:()=>h(()=>import("./Example-q2WHRP-V.js"),__vite__mapDeps([207,129,20,21,22,34,130,208]),import.meta.url),component:()=>h(()=>import("./index-D0nAiVXY.js"),__vite__mapDeps([209,41,42,3,4,5,15,2,170,45,21,22,54,20,55,97,9,10,17,18,19,96,93,98,7,8,63,46,99,129,34,130,205,16,26,27,25,28,36,31,32,33,43,44,206,207,208,1,48,6,210,57,101]),import.meta.url)}},se={},Ht=typeof window<"u";function ar({api_url:e,name:t,id:r,variant:n}){const i=Ht&&window.__GRADIO__CC__,o={...Ps,...i||{}};let s=r||t;if(se[`${s}-${n}`])return{component:se[`${s}-${n}`],name:t};try{if(!o?.[s]?.[n]&&!o?.[t]?.[n])throw new Error;return se[`${s}-${n}`]=(o?.[s]?.[n]||o?.[t]?.[n])(),{name:t,component:se[`${s}-${n}`]}}catch{if(!s)throw new Error(`Component not found: ${t}`);try{return se[`${s}-${n}`]=Rs(e,s,n),{name:t,component:se[`${s}-${n}`]}}catch(c){if(n==="example")return se[`${s}-${n}`]=h(()=>import("./Example-DxdiEFS_.js"),__vite__mapDeps([211,38]),import.meta.url),{name:t,component:se[`${s}-${n}`]};throw console.error(`failed to load: ${t}`),console.error(c),c}}}function ur(e){return Ht?new Promise((t,r)=>{const n=document.createElement("link");n.rel="stylesheet",n.href=e,document.head.appendChild(n),n.onload=()=>t(),n.onerror=()=>r()}):Promise.resolve()}function Rs(e,t,r){const n=Ht?"client":"server";let i;return n==="server"?Promise.all([ur(`${e}/custom_component/${t}/${r}/style.css`),h(()=>import("./Index-Bh2vG2zW.js"),__vite__mapDeps([212,1,2,3,4,5,6,7,8,9,10,213]),import.meta.url)]).then(([o,s])=>s):(i=`${e}/custom_component/${t}/${n}/${r}/index.js`,Promise.all([ur(`${e}/custom_component/${t}/${n}/${r}/style.css`),import(i)]).then(([o,s])=>s))}function Ls(){const e=le({}),t={},r={},n=new Map,i=new Map,o=new Map,s={};function a({fn_index:u,status:l,queue:f=!0,size:p,position:m=null,eta:_=null,message:S=null,progress:P,time_limit:F=null}){const U=r[u],C=t[u],M=s[u],j=U.map(H=>{let k;const V=n.get(H)||0;if(M==="pending"&&l!=="pending"){let z=V-1;n.set(H,z<0?0:z),k=z>0?"pending":l}else M==="pending"&&l==="pending"?k="pending":M!=="pending"&&l==="pending"?(k="pending",n.set(H,V+1)):k=l;return{id:H,queue_position:m,queue_size:p,eta:_,status:k,message:S,progress:P}});C.forEach(H=>{const k=i.get(H)||0;if(M==="pending"&&l!=="pending"){let V=k-1;i.set(H,V<0?0:V),o.set(H,l)}else M!=="pending"&&l==="pending"?(i.set(H,k+1),o.set(H,l)):o.delete(H)}),e.update(H=>(j.forEach(({id:k,queue_position:V,queue_size:z,eta:Pe,status:v,message:Ee,progress:K})=>{H[k]={queue:f,queue_size:z,queue_position:V,eta:Pe,message:Ee,progress:K,status:v,fn_index:u}}),H)),s[u]=l}function c(u,l,f){t[u]=l,r[u]=f}return{update:a,register:c,subscribe:e.subscribe,get_status_for_fn(u){return s[u]},get_inputs_to_update(){return o}}}let de=[];const Is=typeof window<"u",lr=Is?requestAnimationFrame:async e=>await e();function ra(e){let t,r=le({}),n={},i,o,s,a,c=Ls();const u=le(e);let l=[],f,p={},m,_,S;function P(E){E.forEach(y=>{y.targets.forEach(g=>{const R=a[g[0]];R&&y.event_specific_args?.length>0&&y.event_specific_args?.forEach(b=>{R.props[b]=y[b]})})})}async function F({app:E,components:y,layout:g,dependencies:R,root:b,options:d}){V(),f=E,a&&y.forEach(x=>{x.props.value==null&&x.id in a&&(x.props.value=a[x.id].props.value)}),l=y,i=new Set,o=new Set,de=[],s=new Map,t=new Map,a={},_=g,S=b,m={id:g.id,type:"column",props:{interactive:!1,scale:d.fill_height?1:null},has_modes:!1,instance:null,component:null,component_class_id:"",key:null},y.push(m),R.forEach(x=>{c.register(x.id,x.inputs,x.show_progress_on||x.outputs),x.frontend_fn=cr(x.js,!!x.backend_fn,x.inputs.length,x.outputs.length),hr(x.targets,x.id,n),fr(x,i,o)}),r.set(n),s=pr(y,g,b),a=y.reduce((x,w)=>(x[w.id]=w,x),{}),await C(g,b,l),u.set(m),P(R)}function U({render_id:E,components:y,layout:g,root:R,dependencies:b}){_=g,S=R,y.forEach(T=>{for(const X in T.props)T.props[X]===null&&(T.props[X]=void 0)});let d=[],x=[];y.forEach(T=>{T.key==null||!p[E]?.includes(T.key)?x.push(T):d.push(T)}),pr(x,g,R).forEach((T,X)=>{s.set(X,T)}),n={},b.forEach(T=>{c.register(T.id,T.inputs,T.outputs),T.frontend_fn=cr(T.js,!!T.backend_fn,T.inputs.length,T.outputs.length),hr(T.targets,T.id,n),fr(T,i,o)}),r.set(n);let N=a[g.id];const Z=T=>{T.children&&T.children.forEach(X=>{Z(X)})};Z(N),Object.entries(a).forEach(([T,X])=>{let Re=Number(T);if(X.rendered_in===E){let he=d.find(fe=>fe.key===X.key);if(X.key!=null&&he!==void 0){const fe=a[X.id];for(const pe in he.props)he.props.preserved_by_key?.includes(pe)||(fe.props[pe]=he.props[pe])}else delete a[Re],t.has(Re)&&t.delete(Re)}}),x.concat(d.filter(T=>!a[T.id])).forEach(T=>{a[T.id]=T,t.set(T.id,T)}),N.parent&&(N.parent.children[N.parent.children.indexOf(N)]=a[g.id]),C(g,R,l.concat(y),N.parent).then(()=>{u.set(m),p[E]=y.map(T=>T.key).filter(T=>T!=null)}),P(b)}async function C(E,y,g,R){const b=a[E.id];if(!b.component){const d=b.component_class_id||b.type;let x=s.get(d);x&&(b.component=(await x)?.default)}if(b.parent=R,b.type==="dataset"&&(b.props.component_map=wt(b.type,b.component_class_id,y,g,b.props.components).example_components),n[b.id]&&(b.props.attached_events=Object.keys(n[b.id])),b.props.interactive=Ns(b.id,b.props.interactive,b.props.value,i,o),b.props.server=Cs(b.id,b.props.server_fns,f),t.set(b.id,b),E.children&&(b.children=await Promise.all(E.children.map(d=>C(d,y,g,b)))),b.type==="tabs"&&!b.props.initial_tabs){const x=(E.children?.map((w,N)=>{const Z=a[w.id];return Z.props.id??=w.id,{type:Z.type,props:{...Z.props,id:Z.props.id,order:N}}})||[]).filter(w=>w.type==="tabitem");b.props.initial_tabs=x?.map(w=>({label:w.props.label,id:w.props.id,visible:typeof w.props.visible=="boolean"?w.props.visible:!0,interactive:w.props.interactive,order:w.props.order}))}return b.type==="tabs"&&E.children?.forEach((d,x)=>{const w=a[d.id];w.props.order=x}),b}let M=!1,j=le(!1);async function H(E){if(E.size===0)return;const y=l.filter(g=>E.has(g.id));for(const g of y){const R=g.component_class_id||g.type;if(s.has(R))g.component=(await s.get(R))?.default??g.component;else{const{component:b,example_components:d}=wt(g.type,g.component_class_id,S,l);if(s.set(R,b),d)for(const[x,w]of d)s.set(x,w);g.component||(g.component=(await b)?.default)}}}function k(E){return E.some(y=>y.some(g=>{const R=a[g.id];return R?g.prop==="visible"||g.prop==="selected"&&R.type==="tabs":!1}))}function V(){const E=k(de);let y;E&&_&&(y=qe(_,l)),u.update(g=>{for(let R=0;R<de.length;R++)for(let b=0;b<de[R].length;b++){const d=de[R][b];if(!d)continue;const x=a[d.id];if(!x)continue;let w;d.value instanceof Map?w=new Map(d.value):d.value instanceof Set?w=new Set(d.value):Array.isArray(d.value)?w=[...d.value]:d.value==null?w=null:typeof d.value=="object"?w={...d.value}:w=d.value,x.props[d.prop]=w}return g}),E&&_&&y&&lr(async()=>{const g=qe(_,l),R=new Set;for(const b of g)y.has(b)||R.add(b);await H(R),R.size>0&&u.update(b=>b)}),de=[],M=!1,j.set(!1)}function z(E){E&&(de.push(E),M||(M=!0,j.set(!0),lr(V)))}function Pe(E){let y=t.get(E);if(!y){const g=Pr(u);y=v(g,E)}return y?y.instance?.get_value?y.instance.get_value():y.props.value:null}function v(E,y){if(E.id===y)return E;if(E.children)for(const g of E.children){const R=v(g,y);if(R)return R}}function Ee(E,y){const g=t.get(E);g&&g.instance?.modify_stream_state&&g.instance.modify_stream_state(y)}function K(E){const y=t.get(E);return y?.instance?.get_stream_state?y.instance.get_stream_state():"not_set"}function Ye(E,y){const g=t.get(E);g?.instance?.set_time_limit&&g.instance.set_time_limit(y)}return{layout:u,targets:r,update_value:z,get_data:Pe,modify_stream:Ee,get_stream_state:K,set_time_limit:Ye,loading_status:c,scheduled_updates:j,create_layout:F,rerender_layout:U}}const Ds=Object.getPrototypeOf(async function(){}).constructor;function cr(e,t,r,n){if(!e||e===!0)return null;const i=t?r===1:n===1;try{return new Ds("__fn_args",`  let result = await (${e})(...__fn_args);
  if (typeof result === "undefined") return [];
  return (${i} && !Array.isArray(result)) ? [result] : result;`)}catch(o){return console.error("Could not parse custom js method."),console.error(o),null}}function hr(e,t,r){return e.forEach(([n,i])=>{r[n]||(r[n]={}),r[n]?.[i]&&!r[n]?.[i].includes(t)?r[n][i].push(t):r[n][i]=[t]}),r}function fr(e,t,r){return e.inputs.forEach(n=>t.add(n)),e.outputs.forEach(n=>r.add(n)),[t,r]}function Hs(e){return Array.isArray(e)&&e.length===0||e===""||e===0||!e}function Ns(e,t,r,n,i){return t===!1?!1:t===!0?!0:!!(n.has(e)||!i.has(e)&&Hs(r))}function Cs(e,t,r){return t?t.reduce((n,i)=>(n[i]=async(...o)=>(o.length===1&&(o=o[0]),await r.component_server(e,i,o)),n),{}):{}}function wt(e,t,r,n,i){let o=new Map;e==="api"&&(e="state"),e==="dataset"&&i&&i.forEach(a=>{if(o.has(a))return;let c;const u=n.find(l=>l.type===a);u&&(c=ar({api_url:r,name:a,id:u.component_class_id,variant:"example"}),o.set(a,c.component))});const s=ar({api_url:r,name:e,id:t,variant:"component"});return{component:s.component,name:s.name,example_components:o.size>0?o:void 0}}function Bs(e,t,r){const n=r?.selected_tab_id===e.id||r?.selected_tab_id===e.props.id;return t&&n}function Ms(e,t,r){const n=e.props.selected;if(typeof n=="string"||typeof n=="number")return n;if(t.children)for(const i of t.children){const o=r.find(s=>s.id===i.id);if(o?.type==="tabitem"&&o.props.visible!==!1&&o.props.interactive!==!1)return o.id||o.props.id}}function ct(e,t,r){const n=new Set;if(e.children)for(const i of e.children)qe(i,t,!0,r).forEach(s=>n.add(s));return n}function qe(e,t,r=!0,n){const i=new Set,o=t.find(a=>a.id===e.id);if(!o)return i;const s=r&&(typeof o.props.visible=="boolean"?o.props.visible:!0);if(o.type==="tabitem")return Bs(o,s,n)&&(i.add(o.id),ct(e,t,n).forEach(c=>i.add(c))),i;if(o.type==="tabs"){if(s){i.add(o.id);const a=Ms(o,e,t);ct(e,t,{selected_tab_id:a}).forEach(u=>i.add(u))}return i}return s&&(i.add(o.id),ct(e,t,n).forEach(c=>i.add(c))),i}function pr(e,t,r){let n=new Map;const i=qe(t,e);return e.forEach(o=>{if(i.has(o.id)){const{component:s,example_components:a}=wt(o.type,o.component_class_id,r,e);if(n.set(o.component_class_id||o.type,s),a)for(const[c,u]of a)n.set(c,u)}}),n}const na=["red","green","blue","yellow","purple","teal","orange","cyan","lime","pink"],ks=[{color:"red",primary:600,secondary:100},{color:"green",primary:600,secondary:100},{color:"blue",primary:600,secondary:100},{color:"yellow",primary:500,secondary:100},{color:"purple",primary:600,secondary:100},{color:"teal",primary:600,secondary:100},{color:"orange",primary:600,secondary:100},{color:"cyan",primary:600,secondary:100},{color:"lime",primary:500,secondary:100},{color:"pink",primary:600,secondary:100}],_r={inherit:"inherit",current:"currentColor",transparent:"transparent",black:"#000",white:"#fff",slate:{50:"#f8fafc",100:"#f1f5f9",200:"#e2e8f0",300:"#cbd5e1",400:"#94a3b8",500:"#64748b",600:"#475569",700:"#334155",800:"#1e293b",900:"#0f172a",950:"#020617"},gray:{50:"#f9fafb",100:"#f3f4f6",200:"#e5e7eb",300:"#d1d5db",400:"#9ca3af",500:"#6b7280",600:"#4b5563",700:"#374151",800:"#1f2937",900:"#111827",950:"#030712"},zinc:{50:"#fafafa",100:"#f4f4f5",200:"#e4e4e7",300:"#d4d4d8",400:"#a1a1aa",500:"#71717a",600:"#52525b",700:"#3f3f46",800:"#27272a",900:"#18181b",950:"#09090b"},neutral:{50:"#fafafa",100:"#f5f5f5",200:"#e5e5e5",300:"#d4d4d4",400:"#a3a3a3",500:"#737373",600:"#525252",700:"#404040",800:"#262626",900:"#171717",950:"#0a0a0a"},stone:{50:"#fafaf9",100:"#f5f5f4",200:"#e7e5e4",300:"#d6d3d1",400:"#a8a29e",500:"#78716c",600:"#57534e",700:"#44403c",800:"#292524",900:"#1c1917",950:"#0c0a09"},red:{50:"#fef2f2",100:"#fee2e2",200:"#fecaca",300:"#fca5a5",400:"#f87171",500:"#ef4444",600:"#dc2626",700:"#b91c1c",800:"#991b1b",900:"#7f1d1d",950:"#450a0a"},orange:{50:"#fff7ed",100:"#ffedd5",200:"#fed7aa",300:"#fdba74",400:"#fb923c",500:"#f97316",600:"#ea580c",700:"#c2410c",800:"#9a3412",900:"#7c2d12",950:"#431407"},amber:{50:"#fffbeb",100:"#fef3c7",200:"#fde68a",300:"#fcd34d",400:"#fbbf24",500:"#f59e0b",600:"#d97706",700:"#b45309",800:"#92400e",900:"#78350f",950:"#451a03"},yellow:{50:"#fefce8",100:"#fef9c3",200:"#fef08a",300:"#fde047",400:"#facc15",500:"#eab308",600:"#ca8a04",700:"#a16207",800:"#854d0e",900:"#713f12",950:"#422006"},lime:{50:"#f7fee7",100:"#ecfccb",200:"#d9f99d",300:"#bef264",400:"#a3e635",500:"#84cc16",600:"#65a30d",700:"#4d7c0f",800:"#3f6212",900:"#365314",950:"#1a2e05"},green:{50:"#f0fdf4",100:"#dcfce7",200:"#bbf7d0",300:"#86efac",400:"#4ade80",500:"#22c55e",600:"#16a34a",700:"#15803d",800:"#166534",900:"#14532d",950:"#052e16"},emerald:{50:"#ecfdf5",100:"#d1fae5",200:"#a7f3d0",300:"#6ee7b7",400:"#34d399",500:"#10b981",600:"#059669",700:"#047857",800:"#065f46",900:"#064e3b",950:"#022c22"},teal:{50:"#f0fdfa",100:"#ccfbf1",200:"#99f6e4",300:"#5eead4",400:"#2dd4bf",500:"#14b8a6",600:"#0d9488",700:"#0f766e",800:"#115e59",900:"#134e4a",950:"#042f2e"},cyan:{50:"#ecfeff",100:"#cffafe",200:"#a5f3fc",300:"#67e8f9",400:"#22d3ee",500:"#06b6d4",600:"#0891b2",700:"#0e7490",800:"#155e75",900:"#164e63",950:"#083344"},sky:{50:"#f0f9ff",100:"#e0f2fe",200:"#bae6fd",300:"#7dd3fc",400:"#38bdf8",500:"#0ea5e9",600:"#0284c7",700:"#0369a1",800:"#075985",900:"#0c4a6e",950:"#082f49"},blue:{50:"#eff6ff",100:"#dbeafe",200:"#bfdbfe",300:"#93c5fd",400:"#60a5fa",500:"#3b82f6",600:"#2563eb",700:"#1d4ed8",800:"#1e40af",900:"#1e3a8a",950:"#172554"},indigo:{50:"#eef2ff",100:"#e0e7ff",200:"#c7d2fe",300:"#a5b4fc",400:"#818cf8",500:"#6366f1",600:"#4f46e5",700:"#4338ca",800:"#3730a3",900:"#312e81",950:"#1e1b4b"},violet:{50:"#f5f3ff",100:"#ede9fe",200:"#ddd6fe",300:"#c4b5fd",400:"#a78bfa",500:"#8b5cf6",600:"#7c3aed",700:"#6d28d9",800:"#5b21b6",900:"#4c1d95",950:"#2e1065"},purple:{50:"#faf5ff",100:"#f3e8ff",200:"#e9d5ff",300:"#d8b4fe",400:"#c084fc",500:"#a855f7",600:"#9333ea",700:"#7e22ce",800:"#6b21a8",900:"#581c87",950:"#3b0764"},fuchsia:{50:"#fdf4ff",100:"#fae8ff",200:"#f5d0fe",300:"#f0abfc",400:"#e879f9",500:"#d946ef",600:"#c026d3",700:"#a21caf",800:"#86198f",900:"#701a75",950:"#4a044e"},pink:{50:"#fdf2f8",100:"#fce7f3",200:"#fbcfe8",300:"#f9a8d4",400:"#f472b6",500:"#ec4899",600:"#db2777",700:"#be185d",800:"#9d174d",900:"#831843",950:"#500724"},rose:{50:"#fff1f2",100:"#ffe4e6",200:"#fecdd3",300:"#fda4af",400:"#fb7185",500:"#f43f5e",600:"#e11d48",700:"#be123c",800:"#9f1239",900:"#881337",950:"#4c0519"}},ia=ks.reduce((e,{color:t,primary:r,secondary:n})=>({...e,[t]:{primary:_r[t][r],secondary:_r[t][n]}}),{});function Kr(e){if(e==null)return"";const t=String(e),r=Pr(Qr);let n=r(t);if(n!==t)return n;const i=t.toLowerCase();for(const o of Gs){const s=o.substring(o.indexOf(".")+1);if(i===s){const a=r(o);if(a!==o)return a;break}}return t}const oa=ge(Qr,()=>Kr),en="English",tn={annotated_image:"Annotated Image"},rn={allow_recording_access:"Please allow access to the microphone for recording.",audio:"Audio",drop_to_upload:"Drop an audio file here to upload",record_from_microphone:"Record from microphone",stop_recording:"Stop recording",no_device_support:"Media devices could not be accessed. Check that you are running on a secure origin (https) or localhost (or you have passed a valid SSL certificate to ssl_verify), and you have allowed browser access to your device.",stop:"Stop",resume:"Resume",record:"Record",no_microphone:"No microphone found",pause:"Pause",play:"Play",waiting:"Waiting"},nn={connection_can_break:"On mobile, the connection can break if this tab is unfocused or the device sleeps, losing your position in queue.",long_requests_queue:"There is a long queue of requests pending. Duplicate this Space to skip.",lost_connection:"Lost connection due to leaving page. Rejoining queue...",waiting_for_inputs:"Waiting for file(s) to finish uploading, please retry."},on={edit:"Edit",retry:"Retry",undo:"Undo",submit:"Submit",cancel:"Cancel",like:"Like",dislike:"Dislike",clear:"Clear"},sn={checkbox:"Checkbox",checkbox_group:"Checkbox Group"},an={code:"Code"},un={color_picker:"Color Picker"},ln={built_with:"built with",built_with_gradio:"Built with Gradio",clear:"Clear",download:"Download",edit:"Edit",empty:"Empty",error:"Error",hosted_on:"Hosted on",loading:"Loading",logo:"logo",or:"or",remove:"Remove",settings:"Settings",share:"Share",submit:"Submit",undo:"Undo",no_devices:"No devices found",language:"Language",display_theme:"Display Theme",pwa:"Progressive Web App",record:"Record",stop_recording:"Stop Recording",screen_studio:"Screen Studio",share_gradio_tab:"[Sharing] Gradio Tab",run:"Run"},cn={incorrect_format:"Incorrect format, only CSV and TSV files are supported",new_column:"Add column",new_row:"New row",add_row_above:"Add row above",add_row_below:"Add row below",delete_row:"Delete row",delete_column:"Delete column",add_column_left:"Add column to the left",add_column_right:"Add column to the right",sort_column:"Sort column",sort_ascending:"Sort ascending",sort_descending:"Sort descending",drop_to_upload:"Drop CSV or TSV files here to import data into dataframe",clear_sort:"Clear sort",filter:"Filter",clear_filter:"Clear filters"},hn={dropdown:"Dropdown"},fn={build_error:"there is a build error",config_error:"there is a config error",contact_page_author:"Please contact the author of the page to let them know.",no_app_file:"there is no app file",runtime_error:"there is a runtime error",space_not_working:`"Space isn't working because" {0}`,space_paused:"the space is paused",use_via_api:"Use via API",use_via_api_or_mcp:"Use via API or MCP"},pn={uploading:"Uploading..."},_n={highlighted_text:"Highlighted Text"},dn={allow_webcam_access:"Please allow access to the webcam for recording.",brush_color:"Brush color",brush_radius:"Brush radius",image:"Image",remove_image:"Remove Image",select_brush_color:"Select brush color",start_drawing:"Start drawing",use_brush:"Use brush",drop_to_upload:"Drop an image file here to upload"},mn={label:"Label"},gn={enable_cookies:"If you are visiting a HuggingFace Space in Incognito mode, you must enable third party cookies.",incorrect_credentials:"Incorrect Credentials",username:"username",password:"password",login:"Login"},bn={number:"Number"},En={plot:"Plot"},yn={radio:"Radio"},vn={slider:"Slider"},wn={click_to_upload:"Click to Upload",drop_audio:"Drop Audio Here",drop_csv:"Drop CSV Here",drop_file:"Drop File Here",drop_image:"Drop Image Here",drop_video:"Drop Video Here",drop_gallery:"Drop Media Here",paste_clipboard:"Paste from Clipboard"},xn={drop_to_upload:"Drop a video file here to upload"},Nt={_name:en,"3D_model":{"3d_model":"3D Model",drop_to_upload:"Drop a 3D model (.obj, .glb, .stl, .gltf, .splat, or .ply) file here to upload"},annotated_image:tn,audio:rn,blocks:nn,chatbot:on,checkbox:sn,code:an,color_picker:un,common:ln,dataframe:cn,dropdown:hn,errors:fn,file:pn,highlighted_text:_n,image:dn,label:mn,login:gn,number:bn,plot:En,radio:yn,slider:vn,upload_text:wn,video:xn},Us=Object.freeze(Object.defineProperty({__proto__:null,_name:en,annotated_image:tn,audio:rn,blocks:nn,chatbot:on,checkbox:sn,code:an,color_picker:un,common:ln,dataframe:cn,default:Nt,dropdown:hn,errors:fn,file:pn,highlighted_text:_n,image:dn,label:mn,login:gn,number:bn,plot:En,radio:yn,slider:vn,upload_text:wn,video:xn},Symbol.toStringTag,{value:"Module"})),js={ar:"العربية",ca:"Català",ckb:"کوردی",de:"Deutsch",en:"English",es:"Español",eu:"Euskara",fa:"فارسی",fi:"Suomi",fr:"Français",he:"עברית",hi:"हिंदी",ja:"日本語",ko:"한국어",lt:"Lietuvių",nb:"Norsk bokmål",nl:"Nederlands",pl:"Polski","pt-BR":"Português do Brasil",pt:"Português",ro:"Română",ru:"Русский",sv:"Svenska",ta:"தமிழ்",th:"ภาษาไทย",tr:"Türkçe",uk:"Українська",ur:"اردو",uz:"O'zbek","zh-CN":"简体中文","zh-TW":"繁體中文"},Vs=Object.assign({"./lang/ar.json":()=>h(()=>import("./ar-C14WhJAs.js"),[],import.meta.url),"./lang/ca.json":()=>h(()=>import("./ca-BawhR5IW.js"),[],import.meta.url),"./lang/ckb.json":()=>h(()=>import("./ckb-DJglGH6y.js"),[],import.meta.url),"./lang/de.json":()=>h(()=>import("./de-1aCSmaRd.js"),[],import.meta.url),"./lang/en.json":()=>h(()=>Promise.resolve().then(()=>Us),void 0,import.meta.url),"./lang/es.json":()=>h(()=>import("./es-DnL3K9UL.js"),[],import.meta.url),"./lang/eu.json":()=>h(()=>import("./eu-CVD4vzPg.js"),[],import.meta.url),"./lang/fa.json":()=>h(()=>import("./fa-EPplD9mS.js"),[],import.meta.url),"./lang/fi.json":()=>h(()=>import("./fi-B30SrT2N.js"),[],import.meta.url),"./lang/fr.json":()=>h(()=>import("./fr-BtMjEOuI.js"),[],import.meta.url),"./lang/he.json":()=>h(()=>import("./he-C03Xfbr8.js"),[],import.meta.url),"./lang/hi.json":()=>h(()=>import("./hi-DaWfWT5u.js"),[],import.meta.url),"./lang/ja.json":()=>h(()=>import("./ja-BTBiRAkN.js"),[],import.meta.url),"./lang/ko.json":()=>h(()=>import("./ko-C9Hiv2KW.js"),[],import.meta.url),"./lang/lt.json":()=>h(()=>import("./lt-Z_MxAT26.js"),[],import.meta.url),"./lang/nb.json":()=>h(()=>import("./nb-CGxYPyHM.js"),[],import.meta.url),"./lang/nl.json":()=>h(()=>import("./nl-Dw1IiFHs.js"),[],import.meta.url),"./lang/pl.json":()=>h(()=>import("./pl-CewrFAug.js"),[],import.meta.url),"./lang/pt-BR.json":()=>h(()=>import("./pt-BR-DZjK3tkX.js"),[],import.meta.url),"./lang/pt.json":()=>h(()=>import("./pt-DH1R3DI2.js"),[],import.meta.url),"./lang/ro.json":()=>h(()=>import("./ro-boSZHd3M.js"),[],import.meta.url),"./lang/ru.json":()=>h(()=>import("./ru-BgQGKwpu.js"),[],import.meta.url),"./lang/sv.json":()=>h(()=>import("./sv-D1nuPeJf.js"),[],import.meta.url),"./lang/ta.json":()=>h(()=>import("./ta-Ex4E4CrJ.js"),[],import.meta.url),"./lang/th.json":()=>h(()=>import("./th-DGNowuDO.js"),[],import.meta.url),"./lang/tr.json":()=>h(()=>import("./tr-Cez09yqM.js"),[],import.meta.url),"./lang/uk.json":()=>h(()=>import("./uk-C_6kyB1E.js"),[],import.meta.url),"./lang/ur.json":()=>h(()=>import("./ur-1gg1Zkwj.js"),[],import.meta.url),"./lang/uz.json":()=>h(()=>import("./uz-CVKNjEjc.js"),[],import.meta.url),"./lang/zh-CN.json":()=>h(()=>import("./zh-CN-C4t77Jdq.js"),[],import.meta.url),"./lang/zh-TW.json":()=>h(()=>import("./zh-TW-DnQkjK12.js"),[],import.meta.url)});function sa(e){return e&&typeof e=="object"&&e.__type__==="translation_metadata"&&typeof e.key=="string"}function aa(e){if(typeof e!="string")return e;const t="__i18n__",r=e.indexOf(t);if(r===-1)return e;try{const n=r>0?e.substring(0,r):"",i=r+t.length,o=e.indexOf("{",i);let s=-1,a=0;for(let l=o;l<e.length;l++)if(e[l]==="{"&&a++,e[l]==="}"&&a--,a===0){s=l+1;break}if(s===-1)return console.error("Could not find end of JSON in i18n string"),e;const c=e.substring(o,s),u=s<e.length?e.substring(s):"";try{const l=JSON.parse(c);if(l&&l.key){const f=Kr(l.key);return n+f+u}}catch(l){console.error("Error parsing i18n JSON:",l)}return e}catch(n){return console.error("Error processing translation:",n),e}}function $s(){return{...Object.fromEntries(Object.entries(Vs).map(([t,r])=>[t.split("/").pop().split(".")[0],{type:"lazy",data:r}])),en:{type:"static",data:Nt}}}const Ct=$s(),dr=Object.keys(Ct),ua=Object.entries(Ct).map(([e])=>[js[e]||e,e]);let Gs=new Set,ht=!1,mr;async function la(e){if(ht&&!(ht&&e!==mr))return;mr=e,Fs({processed_langs:Ct,custom_translations:e??{}});const r=_s();let n=r&&dr.includes(r)?r:null;if(!n){const i=r?.split("-")[0];n=i&&dr.includes(i)?i:"en"}await os({fallbackLocale:"en",initialLocale:n}),ht=!0}function ca(e){be.set(e)}function ha(e,t,r="en"){return e&&t.includes(e)?e:r}function Fs(e){if(e)try{for(const t in e.custom_translations)yt(t,e.custom_translations[t]);for(const t in e.processed_langs)t==="en"&&e.processed_langs[t].type==="static"?yt(t,Nt):e.processed_langs[t].type==="lazy"&&Yo(t,e.processed_langs[t].data)}catch(t){console.error("Error loading translations:",t)}}const zs="./assets/index-rsZ55Oi2.css";let xt;xt=[];let Tt,Tn,qs=new Promise(e=>{Tn=e});async function Xs(){Tt=(await h(()=>import("./Index-BgyqPiU2.js"),__vite__mapDeps([214,87,6,7,8,3,4,5,9,2,10,21,22,215]),import.meta.url)).default,Tn()}function Ws(){const e={SvelteComponent:et.SvelteComponent};for(const r in et)r!=="SvelteComponent"&&(r==="SvelteComponentDev"?e[r]=e.SvelteComponent:e[r]=et[r]);window.__gradio__svelte__internal=e;class t extends HTMLElement{control_page_title;initial_height;is_embed;container;info;autoscroll;eager;theme_mode;host;space;src;app;loading;updating;constructor(){super(),this.host=this.getAttribute("host"),this.space=this.getAttribute("space"),this.src=this.getAttribute("src"),this.control_page_title=this.getAttribute("control_page_title"),this.initial_height=this.getAttribute("initial_height")??"300px",this.is_embed=this.getAttribute("embed")??"true",this.container=this.getAttribute("container")??"true",this.info=this.getAttribute("info")??!0,this.autoscroll=this.getAttribute("autoscroll"),this.eager=this.getAttribute("eager"),this.theme_mode=this.getAttribute("theme_mode"),this.updating=!1,this.loading=!1}async connectedCallback(){await Xs(),this.loading=!0,this.app&&this.app.$destroy(),typeof xt!="string"&&xt.forEach(o=>sr(o,document.head)),await sr(zs,document.head);const n=new CustomEvent("domchange",{bubbles:!0,cancelable:!1,composed:!0});new MutationObserver(o=>{this.dispatchEvent(n)}).observe(this,{childList:!0}),this.app=new Tt({target:this,props:{space:this.space?this.space.trim():this.space,src:this.src?this.src.trim():this.src,info:this.info!=="false",container:this.container!=="false",is_embed:this.is_embed!=="false",initial_height:this.initial_height,eager:this.eager==="true",version:"5-36-2",theme_mode:this.theme_mode,autoscroll:this.autoscroll==="true",control_page_title:this.control_page_title==="true",Client:$e,app_mode:window.__gradio_mode__==="app"}}),this.updating&&this.setAttribute(this.updating.name,this.updating.value),this.loading=!1}static get observedAttributes(){return["src","space","host"]}async attributeChangedCallback(n,i,o){if(await qs,(n==="host"||n==="space"||n==="src")&&o!==i){if(this.updating={name:n,value:o},this.loading)return;this.app&&this.app.$destroy(),this.space=null,this.host=null,this.src=null,n==="host"?this.host=o:n==="space"?this.space=o:n==="src"&&(this.src=o),this.app=new Tt({target:this,props:{space:this.space?this.space.trim():this.space,src:this.src?this.src.trim():this.src,info:this.info!=="false",container:this.container!=="false",is_embed:this.is_embed!=="false",initial_height:this.initial_height,eager:this.eager==="true",version:"5-36-2",theme_mode:this.theme_mode,autoscroll:this.autoscroll==="true",control_page_title:this.control_page_title==="true",Client:$e,app_mode:window.__gradio_mode__==="app"}}),this.updating=!1}}}customElements.get("gradio-app")||customElements.define("gradio-app",t)}Ws();export{Qr as $,Ds as A,Pr as B,ea as C,We as F,h as _,$s as a,Gs as b,ra as c,ca as d,Fs as e,Zs as f,ha as g,Ys as h,sa as i,Ks as j,Bi as k,ua as l,sr as m,Js as n,na as o,ta as p,be as q,ve as r,la as s,aa as t,Hi as u,ia as v,le as w,oa as x,ar as y,Qs as z};
//# sourceMappingURL=index-DJ2rNx9E.js.map
