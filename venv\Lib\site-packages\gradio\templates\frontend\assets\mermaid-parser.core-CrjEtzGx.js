const __vite__fileDeps=["./info-46DW6VJ7-T6H96B96.js","./index-DJ2rNx9E.js","./index-rsZ55Oi2.css","./mermaid.core-iWqruL5K.js","./dispatch-kxCwF96_.js","./step-Ce-xBr2D.js","./select-BigU4G0v.js","./_baseUniq-Dlm_GEmK.js","./_basePickBy-Db_w_ffx.js","./clone-Di4df7I0.js","./packet-W2GHVCYJ-Dn1z_8ol.js","./pie-BEWT4RHE-hOQ3iOqr.js","./architecture-I3QFYML2-B1cwIXOR.js","./gitGraph-YCYPL57B-BCpxlQQm.js"],__vite__mapDeps=i=>i.map(i=>__vite__fileDeps[i]);
import{_ as Bt}from"./index-DJ2rNx9E.js";import{b1 as pc,b2 as mc,av as Ha,aN as gc,az as yc,aw as Z,a9 as Tc,aa as Di,aD as Rc,aG as Ka,aH as za,aS as Fi,aE as Ac,ac as pt,ad as D,ax as Gi,ar as Ec}from"./mermaid.core-iWqruL5K.js";import{k as vt,j as ii,g as $t,S as vc,w as kc,x as Sc,c as qa,v as K,y as Ya,l as Ic,z as xc,A as Nc,B as Cc,C as wc,a as Xa,d as C,i as ze,r as oe,f as Ee,D as q}from"./_baseUniq-Dlm_GEmK.js";import{j as si,m as k,d as _c,f as Ne,g as kt,i as ai,h as w,l as St,e as Lc}from"./_basePickBy-Db_w_ffx.js";import{c as te}from"./clone-Di4df7I0.js";var $c=Object.prototype,Oc=$c.hasOwnProperty,Ae=pc(function(n,e){if(mc(e)||Ha(e)){gc(e,vt(e),n);return}for(var t in e)Oc.call(e,t)&&yc(n,t,e[t])});function Ja(n,e,t){var r=-1,i=n.length;e<0&&(e=-e>i?0:i+e),t=t>i?i:t,t<0&&(t+=i),i=e>t?0:t-e>>>0,e>>>=0;for(var s=Array(i);++r<i;)s[r]=n[r+e];return s}function Qt(n){for(var e=-1,t=n==null?0:n.length,r=0,i=[];++e<t;){var s=n[e];s&&(i[r++]=s)}return i}function bc(n,e,t,r){for(var i=-1,s=n==null?0:n.length;++i<s;){var a=n[i];e(r,a,t(a),n)}return r}function Pc(n,e,t,r){return ii(n,function(i,s,a){e(r,i,t(i),a)}),r}function Mc(n,e){return function(t,r){var i=Z(t)?bc:Pc,s=e?e():{};return i(t,n,$t(r),s)}}var Dc=200;function Fc(n,e,t,r){var i=-1,s=kc,a=!0,o=n.length,l=[],c=e.length;if(!o)return l;e.length>=Dc&&(s=Sc,a=!1,e=new vc(e));e:for(;++i<o;){var u=n[i],d=u;if(u=u!==0?u:0,a&&d===d){for(var f=c;f--;)if(e[f]===d)continue e;l.push(u)}else s(e,d,r)||l.push(u)}return l}var Vn=Tc(function(n,e){return Di(n)?Fc(n,qa(e,1,Di,!0)):[]});function J(n,e,t){var r=n==null?0:n.length;return r?(e=e===void 0?1:si(e),Ja(n,e<0?0:e,r)):[]}function qt(n,e,t){var r=n==null?0:n.length;return r?(e=e===void 0?1:si(e),e=r-e,Ja(n,0,e<0?0:e)):[]}function Gc(n,e){for(var t=-1,r=n==null?0:n.length;++t<r;)if(!e(n[t],t,n))return!1;return!0}function Uc(n,e){var t=!0;return ii(n,function(r,i,s){return t=!!e(r,i,s),t}),t}function $e(n,e,t){var r=Z(n)?Gc:Uc;return r(n,$t(e))}function be(n){return n&&n.length?n[0]:void 0}function Re(n,e){return qa(k(n,e))}var Bc=Object.prototype,Vc=Bc.hasOwnProperty,Wc=Mc(function(n,e,t){Vc.call(n,t)?n[t].push(e):Rc(n,t,[e])}),jc="[object String]";function fe(n){return typeof n=="string"||!Z(n)&&Ka(n)&&za(n)==jc}var Hc=Math.max;function ue(n,e,t,r){n=Ha(n)?n:K(n),t=t&&!r?si(t):0;var i=n.length;return t<0&&(t=Hc(i+t,0)),fe(n)?t<=i&&n.indexOf(e,t)>-1:!!i&&Ya(n,e,t)>-1}function Ui(n,e,t){var r=n==null?0:n.length;if(!r)return-1;var i=0;return Ya(n,e,i)}var Kc="[object RegExp]";function zc(n){return Ka(n)&&za(n)==Kc}var Bi=Fi&&Fi.isRegExp,qe=Bi?Ac(Bi):zc,qc="Expected a function";function Yc(n){if(typeof n!="function")throw new TypeError(qc);return function(){var e=arguments;switch(e.length){case 0:return!n.call(this);case 1:return!n.call(this,e[0]);case 2:return!n.call(this,e[0],e[1]);case 3:return!n.call(this,e[0],e[1],e[2])}return!n.apply(this,e)}}function Pe(n,e){if(n==null)return{};var t=Ic(xc(n),function(r){return[r]});return e=$t(e),_c(n,t,function(r,i){return e(r,i[0])})}function Wn(n,e){var t=Z(n)?Nc:Cc;return t(n,Yc($t(e)))}function Xc(n,e){var t;return ii(n,function(r,i,s){return t=e(r,i,s),!t}),!!t}function Qa(n,e,t){var r=Z(n)?wc:Xc;return r(n,$t(e))}function oi(n){return n&&n.length?Xa(n):[]}function Jc(n,e){return n&&n.length?Xa(n,$t(e)):[]}function ae(n){return typeof n=="object"&&n!==null&&typeof n.$type=="string"}function Ge(n){return typeof n=="object"&&n!==null&&typeof n.$refText=="string"}function Qc(n){return typeof n=="object"&&n!==null&&typeof n.name=="string"&&typeof n.type=="string"&&typeof n.path=="string"}function fn(n){return typeof n=="object"&&n!==null&&ae(n.container)&&Ge(n.reference)&&typeof n.message=="string"}class Za{constructor(){this.subtypes={},this.allSubtypes={}}isInstance(e,t){return ae(e)&&this.isSubtype(e.$type,t)}isSubtype(e,t){if(e===t)return!0;let r=this.subtypes[e];r||(r=this.subtypes[e]={});const i=r[t];if(i!==void 0)return i;{const s=this.computeIsSubtype(e,t);return r[t]=s,s}}getAllSubTypes(e){const t=this.allSubtypes[e];if(t)return t;{const r=this.getAllTypes(),i=[];for(const s of r)this.isSubtype(s,e)&&i.push(s);return this.allSubtypes[e]=i,i}}}function It(n){return typeof n=="object"&&n!==null&&Array.isArray(n.content)}function eo(n){return typeof n=="object"&&n!==null&&typeof n.tokenType=="object"}function to(n){return It(n)&&typeof n.fullText=="string"}class ie{constructor(e,t){this.startFn=e,this.nextFn=t}iterator(){const e={state:this.startFn(),next:()=>this.nextFn(e.state),[Symbol.iterator]:()=>e};return e}[Symbol.iterator](){return this.iterator()}isEmpty(){return!!this.iterator().next().done}count(){const e=this.iterator();let t=0,r=e.next();for(;!r.done;)t++,r=e.next();return t}toArray(){const e=[],t=this.iterator();let r;do r=t.next(),r.value!==void 0&&e.push(r.value);while(!r.done);return e}toSet(){return new Set(this)}toMap(e,t){const r=this.map(i=>[e?e(i):i,t?t(i):i]);return new Map(r)}toString(){return this.join()}concat(e){const t=e[Symbol.iterator]();return new ie(()=>({first:this.startFn(),firstDone:!1}),r=>{let i;if(!r.firstDone){do if(i=this.nextFn(r.first),!i.done)return i;while(!i.done);r.firstDone=!0}do if(i=t.next(),!i.done)return i;while(!i.done);return xe})}join(e=","){const t=this.iterator();let r="",i,s=!1;do i=t.next(),i.done||(s&&(r+=e),r+=Zc(i.value)),s=!0;while(!i.done);return r}indexOf(e,t=0){const r=this.iterator();let i=0,s=r.next();for(;!s.done;){if(i>=t&&s.value===e)return i;s=r.next(),i++}return-1}every(e){const t=this.iterator();let r=t.next();for(;!r.done;){if(!e(r.value))return!1;r=t.next()}return!0}some(e){const t=this.iterator();let r=t.next();for(;!r.done;){if(e(r.value))return!0;r=t.next()}return!1}forEach(e){const t=this.iterator();let r=0,i=t.next();for(;!i.done;)e(i.value,r),i=t.next(),r++}map(e){return new ie(this.startFn,t=>{const{done:r,value:i}=this.nextFn(t);return r?xe:{done:!1,value:e(i)}})}filter(e){return new ie(this.startFn,t=>{let r;do if(r=this.nextFn(t),!r.done&&e(r.value))return r;while(!r.done);return xe})}nonNullable(){return this.filter(e=>e!=null)}reduce(e,t){const r=this.iterator();let i=t,s=r.next();for(;!s.done;)i===void 0?i=s.value:i=e(i,s.value),s=r.next();return i}reduceRight(e,t){return this.recursiveReduce(this.iterator(),e,t)}recursiveReduce(e,t,r){const i=e.next();if(i.done)return r;const s=this.recursiveReduce(e,t,r);return s===void 0?i.value:t(s,i.value)}find(e){const t=this.iterator();let r=t.next();for(;!r.done;){if(e(r.value))return r.value;r=t.next()}}findIndex(e){const t=this.iterator();let r=0,i=t.next();for(;!i.done;){if(e(i.value))return r;i=t.next(),r++}return-1}includes(e){const t=this.iterator();let r=t.next();for(;!r.done;){if(r.value===e)return!0;r=t.next()}return!1}flatMap(e){return new ie(()=>({this:this.startFn()}),t=>{do{if(t.iterator){const s=t.iterator.next();if(s.done)t.iterator=void 0;else return s}const{done:r,value:i}=this.nextFn(t.this);if(!r){const s=e(i);if(Tn(s))t.iterator=s[Symbol.iterator]();else return{done:!1,value:s}}}while(t.iterator);return xe})}flat(e){if(e===void 0&&(e=1),e<=0)return this;const t=e>1?this.flat(e-1):this;return new ie(()=>({this:t.startFn()}),r=>{do{if(r.iterator){const a=r.iterator.next();if(a.done)r.iterator=void 0;else return a}const{done:i,value:s}=t.nextFn(r.this);if(!i)if(Tn(s))r.iterator=s[Symbol.iterator]();else return{done:!1,value:s}}while(r.iterator);return xe})}head(){const t=this.iterator().next();if(!t.done)return t.value}tail(e=1){return new ie(()=>{const t=this.startFn();for(let r=0;r<e;r++)if(this.nextFn(t).done)return t;return t},this.nextFn)}limit(e){return new ie(()=>({size:0,state:this.startFn()}),t=>(t.size++,t.size>e?xe:this.nextFn(t.state)))}distinct(e){const t=new Set;return this.filter(r=>{const i=e?e(r):r;return t.has(i)?!1:(t.add(i),!0)})}exclude(e,t){const r=new Set;for(const i of e){const s=t?t(i):i;r.add(s)}return this.filter(i=>{const s=t?t(i):i;return!r.has(s)})}}function Zc(n){return typeof n=="string"?n:typeof n>"u"?"undefined":typeof n.toString=="function"?n.toString():Object.prototype.toString.call(n)}function Tn(n){return!!n&&typeof n[Symbol.iterator]=="function"}const eu=new ie(()=>{},()=>xe),xe=Object.freeze({done:!0,value:void 0});function Q(...n){if(n.length===1){const e=n[0];if(e instanceof ie)return e;if(Tn(e))return new ie(()=>e[Symbol.iterator](),t=>t.next());if(typeof e.length=="number")return new ie(()=>({index:0}),t=>t.index<e.length?{done:!1,value:e[t.index++]}:xe)}return n.length>1?new ie(()=>({collIndex:0,arrIndex:0}),e=>{do{if(e.iterator){const t=e.iterator.next();if(!t.done)return t;e.iterator=void 0}if(e.array){if(e.arrIndex<e.array.length)return{done:!1,value:e.array[e.arrIndex++]};e.array=void 0,e.arrIndex=0}if(e.collIndex<n.length){const t=n[e.collIndex++];Tn(t)?e.iterator=t[Symbol.iterator]():t&&typeof t.length=="number"&&(e.array=t)}}while(e.iterator||e.array||e.collIndex<n.length);return xe}):eu}class li extends ie{constructor(e,t,r){super(()=>({iterators:r?.includeRoot?[[e][Symbol.iterator]()]:[t(e)[Symbol.iterator]()],pruned:!1}),i=>{for(i.pruned&&(i.iterators.pop(),i.pruned=!1);i.iterators.length>0;){const a=i.iterators[i.iterators.length-1].next();if(a.done)i.iterators.pop();else return i.iterators.push(t(a.value)[Symbol.iterator]()),a}return xe})}iterator(){const e={state:this.startFn(),next:()=>this.nextFn(e.state),prune:()=>{e.state.pruned=!0},[Symbol.iterator]:()=>e};return e}}var Ar;(function(n){function e(s){return s.reduce((a,o)=>a+o,0)}n.sum=e;function t(s){return s.reduce((a,o)=>a*o,0)}n.product=t;function r(s){return s.reduce((a,o)=>Math.min(a,o))}n.min=r;function i(s){return s.reduce((a,o)=>Math.max(a,o))}n.max=i})(Ar||(Ar={}));function Er(n){return new li(n,e=>It(e)?e.content:[],{includeRoot:!0})}function tu(n,e){for(;n.container;)if(n=n.container,n===e)return!0;return!1}function vr(n){return{start:{character:n.startColumn-1,line:n.startLine-1},end:{character:n.endColumn,line:n.endLine-1}}}function Rn(n){if(!n)return;const{offset:e,end:t,range:r}=n;return{range:r,offset:e,end:t,length:t-e}}var Qe;(function(n){n[n.Before=0]="Before",n[n.After=1]="After",n[n.OverlapFront=2]="OverlapFront",n[n.OverlapBack=3]="OverlapBack",n[n.Inside=4]="Inside"})(Qe||(Qe={}));function nu(n,e){if(n.end.line<e.start.line||n.end.line===e.start.line&&n.end.character<n.start.character)return Qe.Before;if(n.start.line>e.end.line||n.start.line===e.end.line&&n.start.character>e.end.character)return Qe.After;const t=n.start.line>e.start.line||n.start.line===e.start.line&&n.start.character>=e.start.character,r=n.end.line<e.end.line||n.end.line===e.end.line&&n.end.character<=e.end.character;return t&&r?Qe.Inside:t?Qe.OverlapBack:Qe.OverlapFront}function ru(n,e){return nu(n,e)>Qe.After}const iu=/^[\w\p{L}]$/u;function su(n,e){if(n){const t=au(n,!0);if(t&&Vi(t,e))return t;if(to(n)){const r=n.content.findIndex(i=>!i.hidden);for(let i=r-1;i>=0;i--){const s=n.content[i];if(Vi(s,e))return s}}}}function Vi(n,e){return eo(n)&&e.includes(n.tokenType.name)}function au(n,e=!0){for(;n.container;){const t=n.container;let r=t.content.indexOf(n);for(;r>0;){r--;const i=t.content[r];if(e||!i.hidden)return i}n=t}}class no extends Error{constructor(e,t){super(e?`${t} at ${e.range.start.line}:${e.range.start.character}`:t)}}function jn(n){throw new Error("Error! The input value was not handled.")}const ar="AbstractRule",or="AbstractType",Wi="Condition",ou="TypeDefinition",ji="ValueLiteral",ro="AbstractElement";function lu(n){return M.isInstance(n,ro)}const cu="ArrayLiteral",uu="ArrayType",io="BooleanLiteral";function du(n){return M.isInstance(n,io)}const so="Conjunction";function fu(n){return M.isInstance(n,so)}const ao="Disjunction";function hu(n){return M.isInstance(n,ao)}const pu="Grammar",oo="InferredType";function lo(n){return M.isInstance(n,oo)}const co="Interface";function uo(n){return M.isInstance(n,co)}const fo="Negation";function mu(n){return M.isInstance(n,fo)}const gu="NumberLiteral",yu="Parameter",ho="ParameterReference";function Tu(n){return M.isInstance(n,ho)}const po="ParserRule";function Ce(n){return M.isInstance(n,po)}const Ru="ReferenceType",Au="ReturnType";function Eu(n){return M.isInstance(n,Au)}const mo="SimpleType";function vu(n){return M.isInstance(n,mo)}const ku="StringLiteral",kr="TerminalRule";function mt(n){return M.isInstance(n,kr)}const go="Type";function yo(n){return M.isInstance(n,go)}const Su="UnionType",To="Action";function Hn(n){return M.isInstance(n,To)}const Ro="Alternatives";function Ao(n){return M.isInstance(n,Ro)}const Eo="Assignment";function ct(n){return M.isInstance(n,Eo)}const vo="CharacterRange";function Iu(n){return M.isInstance(n,vo)}const ko="CrossReference";function ci(n){return M.isInstance(n,ko)}const So="EndOfFile";function xu(n){return M.isInstance(n,So)}const Io="Group";function ui(n){return M.isInstance(n,Io)}const xo="Keyword";function ut(n){return M.isInstance(n,xo)}const No="NegatedToken";function Nu(n){return M.isInstance(n,No)}const Co="RegexToken";function Cu(n){return M.isInstance(n,Co)}const wo="RuleCall";function dt(n){return M.isInstance(n,wo)}const _o="TerminalAlternatives";function wu(n){return M.isInstance(n,_o)}const Lo="TerminalGroup";function _u(n){return M.isInstance(n,Lo)}const $o="TerminalRuleCall";function Lu(n){return M.isInstance(n,$o)}const Oo="UnorderedGroup";function bo(n){return M.isInstance(n,Oo)}const Po="UntilToken";function $u(n){return M.isInstance(n,Po)}const Mo="Wildcard";function Ou(n){return M.isInstance(n,Mo)}class Do extends Za{getAllTypes(){return["AbstractElement","AbstractRule","AbstractType","Action","Alternatives","ArrayLiteral","ArrayType","Assignment","BooleanLiteral","CharacterRange","Condition","Conjunction","CrossReference","Disjunction","EndOfFile","Grammar","GrammarImport","Group","InferredType","Interface","Keyword","NamedArgument","NegatedToken","Negation","NumberLiteral","Parameter","ParameterReference","ParserRule","ReferenceType","RegexToken","ReturnType","RuleCall","SimpleType","StringLiteral","TerminalAlternatives","TerminalGroup","TerminalRule","TerminalRuleCall","Type","TypeAttribute","TypeDefinition","UnionType","UnorderedGroup","UntilToken","ValueLiteral","Wildcard"]}computeIsSubtype(e,t){switch(e){case To:case Ro:case Eo:case vo:case ko:case So:case Io:case xo:case No:case Co:case wo:case _o:case Lo:case $o:case Oo:case Po:case Mo:return this.isSubtype(ro,t);case cu:case gu:case ku:return this.isSubtype(ji,t);case uu:case Ru:case mo:case Su:return this.isSubtype(ou,t);case io:return this.isSubtype(Wi,t)||this.isSubtype(ji,t);case so:case ao:case fo:case ho:return this.isSubtype(Wi,t);case oo:case co:case go:return this.isSubtype(or,t);case po:return this.isSubtype(ar,t)||this.isSubtype(or,t);case kr:return this.isSubtype(ar,t);default:return!1}}getReferenceType(e){const t=`${e.container.$type}:${e.property}`;switch(t){case"Action:type":case"CrossReference:type":case"Interface:superTypes":case"ParserRule:returnType":case"SimpleType:typeRef":return or;case"Grammar:hiddenTokens":case"ParserRule:hiddenTokens":case"RuleCall:rule":return ar;case"Grammar:usedGrammars":return pu;case"NamedArgument:parameter":case"ParameterReference:parameter":return yu;case"TerminalRuleCall:rule":return kr;default:throw new Error(`${t} is not a valid reference id.`)}}getTypeMetaData(e){switch(e){case"AbstractElement":return{name:"AbstractElement",properties:[{name:"cardinality"},{name:"lookahead"}]};case"ArrayLiteral":return{name:"ArrayLiteral",properties:[{name:"elements",defaultValue:[]}]};case"ArrayType":return{name:"ArrayType",properties:[{name:"elementType"}]};case"BooleanLiteral":return{name:"BooleanLiteral",properties:[{name:"true",defaultValue:!1}]};case"Conjunction":return{name:"Conjunction",properties:[{name:"left"},{name:"right"}]};case"Disjunction":return{name:"Disjunction",properties:[{name:"left"},{name:"right"}]};case"Grammar":return{name:"Grammar",properties:[{name:"definesHiddenTokens",defaultValue:!1},{name:"hiddenTokens",defaultValue:[]},{name:"imports",defaultValue:[]},{name:"interfaces",defaultValue:[]},{name:"isDeclared",defaultValue:!1},{name:"name"},{name:"rules",defaultValue:[]},{name:"types",defaultValue:[]},{name:"usedGrammars",defaultValue:[]}]};case"GrammarImport":return{name:"GrammarImport",properties:[{name:"path"}]};case"InferredType":return{name:"InferredType",properties:[{name:"name"}]};case"Interface":return{name:"Interface",properties:[{name:"attributes",defaultValue:[]},{name:"name"},{name:"superTypes",defaultValue:[]}]};case"NamedArgument":return{name:"NamedArgument",properties:[{name:"calledByName",defaultValue:!1},{name:"parameter"},{name:"value"}]};case"Negation":return{name:"Negation",properties:[{name:"value"}]};case"NumberLiteral":return{name:"NumberLiteral",properties:[{name:"value"}]};case"Parameter":return{name:"Parameter",properties:[{name:"name"}]};case"ParameterReference":return{name:"ParameterReference",properties:[{name:"parameter"}]};case"ParserRule":return{name:"ParserRule",properties:[{name:"dataType"},{name:"definesHiddenTokens",defaultValue:!1},{name:"definition"},{name:"entry",defaultValue:!1},{name:"fragment",defaultValue:!1},{name:"hiddenTokens",defaultValue:[]},{name:"inferredType"},{name:"name"},{name:"parameters",defaultValue:[]},{name:"returnType"},{name:"wildcard",defaultValue:!1}]};case"ReferenceType":return{name:"ReferenceType",properties:[{name:"referenceType"}]};case"ReturnType":return{name:"ReturnType",properties:[{name:"name"}]};case"SimpleType":return{name:"SimpleType",properties:[{name:"primitiveType"},{name:"stringType"},{name:"typeRef"}]};case"StringLiteral":return{name:"StringLiteral",properties:[{name:"value"}]};case"TerminalRule":return{name:"TerminalRule",properties:[{name:"definition"},{name:"fragment",defaultValue:!1},{name:"hidden",defaultValue:!1},{name:"name"},{name:"type"}]};case"Type":return{name:"Type",properties:[{name:"name"},{name:"type"}]};case"TypeAttribute":return{name:"TypeAttribute",properties:[{name:"defaultValue"},{name:"isOptional",defaultValue:!1},{name:"name"},{name:"type"}]};case"UnionType":return{name:"UnionType",properties:[{name:"types",defaultValue:[]}]};case"Action":return{name:"Action",properties:[{name:"cardinality"},{name:"feature"},{name:"inferredType"},{name:"lookahead"},{name:"operator"},{name:"type"}]};case"Alternatives":return{name:"Alternatives",properties:[{name:"cardinality"},{name:"elements",defaultValue:[]},{name:"lookahead"}]};case"Assignment":return{name:"Assignment",properties:[{name:"cardinality"},{name:"feature"},{name:"lookahead"},{name:"operator"},{name:"terminal"}]};case"CharacterRange":return{name:"CharacterRange",properties:[{name:"cardinality"},{name:"left"},{name:"lookahead"},{name:"right"}]};case"CrossReference":return{name:"CrossReference",properties:[{name:"cardinality"},{name:"deprecatedSyntax",defaultValue:!1},{name:"lookahead"},{name:"terminal"},{name:"type"}]};case"EndOfFile":return{name:"EndOfFile",properties:[{name:"cardinality"},{name:"lookahead"}]};case"Group":return{name:"Group",properties:[{name:"cardinality"},{name:"elements",defaultValue:[]},{name:"guardCondition"},{name:"lookahead"}]};case"Keyword":return{name:"Keyword",properties:[{name:"cardinality"},{name:"lookahead"},{name:"value"}]};case"NegatedToken":return{name:"NegatedToken",properties:[{name:"cardinality"},{name:"lookahead"},{name:"terminal"}]};case"RegexToken":return{name:"RegexToken",properties:[{name:"cardinality"},{name:"lookahead"},{name:"regex"}]};case"RuleCall":return{name:"RuleCall",properties:[{name:"arguments",defaultValue:[]},{name:"cardinality"},{name:"lookahead"},{name:"rule"}]};case"TerminalAlternatives":return{name:"TerminalAlternatives",properties:[{name:"cardinality"},{name:"elements",defaultValue:[]},{name:"lookahead"}]};case"TerminalGroup":return{name:"TerminalGroup",properties:[{name:"cardinality"},{name:"elements",defaultValue:[]},{name:"lookahead"}]};case"TerminalRuleCall":return{name:"TerminalRuleCall",properties:[{name:"cardinality"},{name:"lookahead"},{name:"rule"}]};case"UnorderedGroup":return{name:"UnorderedGroup",properties:[{name:"cardinality"},{name:"elements",defaultValue:[]},{name:"lookahead"}]};case"UntilToken":return{name:"UntilToken",properties:[{name:"cardinality"},{name:"lookahead"},{name:"terminal"}]};case"Wildcard":return{name:"Wildcard",properties:[{name:"cardinality"},{name:"lookahead"}]};default:return{name:e,properties:[]}}}}const M=new Do;function bu(n){for(const[e,t]of Object.entries(n))e.startsWith("$")||(Array.isArray(t)?t.forEach((r,i)=>{ae(r)&&(r.$container=n,r.$containerProperty=e,r.$containerIndex=i)}):ae(t)&&(t.$container=n,t.$containerProperty=e))}function Kn(n,e){let t=n;for(;t;){if(e(t))return t;t=t.$container}}function Ue(n){const t=Pu(n).$document;if(!t)throw new Error("AST node has no document.");return t}function Pu(n){for(;n.$container;)n=n.$container;return n}function di(n,e){if(!n)throw new Error("Node must be an AstNode.");const t=e?.range;return new ie(()=>({keys:Object.keys(n),keyIndex:0,arrayIndex:0}),r=>{for(;r.keyIndex<r.keys.length;){const i=r.keys[r.keyIndex];if(!i.startsWith("$")){const s=n[i];if(ae(s)){if(r.keyIndex++,Hi(s,t))return{done:!1,value:s}}else if(Array.isArray(s)){for(;r.arrayIndex<s.length;){const a=r.arrayIndex++,o=s[a];if(ae(o)&&Hi(o,t))return{done:!1,value:o}}r.arrayIndex=0}}r.keyIndex++}return xe})}function Zt(n,e){if(!n)throw new Error("Root node must be an AstNode.");return new li(n,t=>di(t,e))}function At(n,e){if(!n)throw new Error("Root node must be an AstNode.");return new li(n,t=>di(t,e),{includeRoot:!0})}function Hi(n,e){var t;if(!e)return!0;const r=(t=n.$cstNode)===null||t===void 0?void 0:t.range;return r?ru(r,e):!1}function Fo(n){return new ie(()=>({keys:Object.keys(n),keyIndex:0,arrayIndex:0}),e=>{for(;e.keyIndex<e.keys.length;){const t=e.keys[e.keyIndex];if(!t.startsWith("$")){const r=n[t];if(Ge(r))return e.keyIndex++,{done:!1,value:{reference:r,container:n,property:t}};if(Array.isArray(r)){for(;e.arrayIndex<r.length;){const i=e.arrayIndex++,s=r[i];if(Ge(s))return{done:!1,value:{reference:s,container:n,property:t,index:i}}}e.arrayIndex=0}}e.keyIndex++}return xe})}function Mu(n,e){const t=n.getTypeMetaData(e.$type),r=e;for(const i of t.properties)i.defaultValue!==void 0&&r[i.name]===void 0&&(r[i.name]=Go(i.defaultValue))}function Go(n){return Array.isArray(n)?[...n.map(Go)]:n}function _(n){return n.charCodeAt(0)}function lr(n,e){Array.isArray(n)?n.forEach(function(t){e.push(t)}):e.push(n)}function Vt(n,e){if(n[e]===!0)throw"duplicate flag "+e;n[e],n[e]=!0}function Tt(n){if(n===void 0)throw Error("Internal Error - Should never get here!");return!0}function Du(){throw Error("Internal Error - Should never get here!")}function Ki(n){return n.type==="Character"}const An=[];for(let n=_("0");n<=_("9");n++)An.push(n);const En=[_("_")].concat(An);for(let n=_("a");n<=_("z");n++)En.push(n);for(let n=_("A");n<=_("Z");n++)En.push(n);const zi=[_(" "),_("\f"),_(`
`),_("\r"),_("	"),_("\v"),_("	"),_(" "),_(" "),_(" "),_(" "),_(" "),_(" "),_(" "),_(" "),_(" "),_(" "),_(" "),_(" "),_(" "),_("\u2028"),_("\u2029"),_(" "),_(" "),_("　"),_("\uFEFF")],Fu=/[0-9a-fA-F]/,ln=/[0-9]/,Gu=/[1-9]/;class Uo{constructor(){this.idx=0,this.input="",this.groupIdx=0}saveState(){return{idx:this.idx,input:this.input,groupIdx:this.groupIdx}}restoreState(e){this.idx=e.idx,this.input=e.input,this.groupIdx=e.groupIdx}pattern(e){this.idx=0,this.input=e,this.groupIdx=0,this.consumeChar("/");const t=this.disjunction();this.consumeChar("/");const r={type:"Flags",loc:{begin:this.idx,end:e.length},global:!1,ignoreCase:!1,multiLine:!1,unicode:!1,sticky:!1};for(;this.isRegExpFlag();)switch(this.popChar()){case"g":Vt(r,"global");break;case"i":Vt(r,"ignoreCase");break;case"m":Vt(r,"multiLine");break;case"u":Vt(r,"unicode");break;case"y":Vt(r,"sticky");break}if(this.idx!==this.input.length)throw Error("Redundant input: "+this.input.substring(this.idx));return{type:"Pattern",flags:r,value:t,loc:this.loc(0)}}disjunction(){const e=[],t=this.idx;for(e.push(this.alternative());this.peekChar()==="|";)this.consumeChar("|"),e.push(this.alternative());return{type:"Disjunction",value:e,loc:this.loc(t)}}alternative(){const e=[],t=this.idx;for(;this.isTerm();)e.push(this.term());return{type:"Alternative",value:e,loc:this.loc(t)}}term(){return this.isAssertion()?this.assertion():this.atom()}assertion(){const e=this.idx;switch(this.popChar()){case"^":return{type:"StartAnchor",loc:this.loc(e)};case"$":return{type:"EndAnchor",loc:this.loc(e)};case"\\":switch(this.popChar()){case"b":return{type:"WordBoundary",loc:this.loc(e)};case"B":return{type:"NonWordBoundary",loc:this.loc(e)}}throw Error("Invalid Assertion Escape");case"(":this.consumeChar("?");let t;switch(this.popChar()){case"=":t="Lookahead";break;case"!":t="NegativeLookahead";break}Tt(t);const r=this.disjunction();return this.consumeChar(")"),{type:t,value:r,loc:this.loc(e)}}return Du()}quantifier(e=!1){let t;const r=this.idx;switch(this.popChar()){case"*":t={atLeast:0,atMost:1/0};break;case"+":t={atLeast:1,atMost:1/0};break;case"?":t={atLeast:0,atMost:1};break;case"{":const i=this.integerIncludingZero();switch(this.popChar()){case"}":t={atLeast:i,atMost:i};break;case",":let s;this.isDigit()?(s=this.integerIncludingZero(),t={atLeast:i,atMost:s}):t={atLeast:i,atMost:1/0},this.consumeChar("}");break}if(e===!0&&t===void 0)return;Tt(t);break}if(!(e===!0&&t===void 0)&&Tt(t))return this.peekChar(0)==="?"?(this.consumeChar("?"),t.greedy=!1):t.greedy=!0,t.type="Quantifier",t.loc=this.loc(r),t}atom(){let e;const t=this.idx;switch(this.peekChar()){case".":e=this.dotAll();break;case"\\":e=this.atomEscape();break;case"[":e=this.characterClass();break;case"(":e=this.group();break}if(e===void 0&&this.isPatternCharacter()&&(e=this.patternCharacter()),Tt(e))return e.loc=this.loc(t),this.isQuantifier()&&(e.quantifier=this.quantifier()),e}dotAll(){return this.consumeChar("."),{type:"Set",complement:!0,value:[_(`
`),_("\r"),_("\u2028"),_("\u2029")]}}atomEscape(){switch(this.consumeChar("\\"),this.peekChar()){case"1":case"2":case"3":case"4":case"5":case"6":case"7":case"8":case"9":return this.decimalEscapeAtom();case"d":case"D":case"s":case"S":case"w":case"W":return this.characterClassEscape();case"f":case"n":case"r":case"t":case"v":return this.controlEscapeAtom();case"c":return this.controlLetterEscapeAtom();case"0":return this.nulCharacterAtom();case"x":return this.hexEscapeSequenceAtom();case"u":return this.regExpUnicodeEscapeSequenceAtom();default:return this.identityEscapeAtom()}}decimalEscapeAtom(){return{type:"GroupBackReference",value:this.positiveInteger()}}characterClassEscape(){let e,t=!1;switch(this.popChar()){case"d":e=An;break;case"D":e=An,t=!0;break;case"s":e=zi;break;case"S":e=zi,t=!0;break;case"w":e=En;break;case"W":e=En,t=!0;break}if(Tt(e))return{type:"Set",value:e,complement:t}}controlEscapeAtom(){let e;switch(this.popChar()){case"f":e=_("\f");break;case"n":e=_(`
`);break;case"r":e=_("\r");break;case"t":e=_("	");break;case"v":e=_("\v");break}if(Tt(e))return{type:"Character",value:e}}controlLetterEscapeAtom(){this.consumeChar("c");const e=this.popChar();if(/[a-zA-Z]/.test(e)===!1)throw Error("Invalid ");return{type:"Character",value:e.toUpperCase().charCodeAt(0)-64}}nulCharacterAtom(){return this.consumeChar("0"),{type:"Character",value:_("\0")}}hexEscapeSequenceAtom(){return this.consumeChar("x"),this.parseHexDigits(2)}regExpUnicodeEscapeSequenceAtom(){return this.consumeChar("u"),this.parseHexDigits(4)}identityEscapeAtom(){const e=this.popChar();return{type:"Character",value:_(e)}}classPatternCharacterAtom(){switch(this.peekChar()){case`
`:case"\r":case"\u2028":case"\u2029":case"\\":case"]":throw Error("TBD");default:const e=this.popChar();return{type:"Character",value:_(e)}}}characterClass(){const e=[];let t=!1;for(this.consumeChar("["),this.peekChar(0)==="^"&&(this.consumeChar("^"),t=!0);this.isClassAtom();){const r=this.classAtom();if(r.type,Ki(r)&&this.isRangeDash()){this.consumeChar("-");const i=this.classAtom();if(i.type,Ki(i)){if(i.value<r.value)throw Error("Range out of order in character class");e.push({from:r.value,to:i.value})}else lr(r.value,e),e.push(_("-")),lr(i.value,e)}else lr(r.value,e)}return this.consumeChar("]"),{type:"Set",complement:t,value:e}}classAtom(){switch(this.peekChar()){case"]":case`
`:case"\r":case"\u2028":case"\u2029":throw Error("TBD");case"\\":return this.classEscape();default:return this.classPatternCharacterAtom()}}classEscape(){switch(this.consumeChar("\\"),this.peekChar()){case"b":return this.consumeChar("b"),{type:"Character",value:_("\b")};case"d":case"D":case"s":case"S":case"w":case"W":return this.characterClassEscape();case"f":case"n":case"r":case"t":case"v":return this.controlEscapeAtom();case"c":return this.controlLetterEscapeAtom();case"0":return this.nulCharacterAtom();case"x":return this.hexEscapeSequenceAtom();case"u":return this.regExpUnicodeEscapeSequenceAtom();default:return this.identityEscapeAtom()}}group(){let e=!0;switch(this.consumeChar("("),this.peekChar(0)){case"?":this.consumeChar("?"),this.consumeChar(":"),e=!1;break;default:this.groupIdx++;break}const t=this.disjunction();this.consumeChar(")");const r={type:"Group",capturing:e,value:t};return e&&(r.idx=this.groupIdx),r}positiveInteger(){let e=this.popChar();if(Gu.test(e)===!1)throw Error("Expecting a positive integer");for(;ln.test(this.peekChar(0));)e+=this.popChar();return parseInt(e,10)}integerIncludingZero(){let e=this.popChar();if(ln.test(e)===!1)throw Error("Expecting an integer");for(;ln.test(this.peekChar(0));)e+=this.popChar();return parseInt(e,10)}patternCharacter(){const e=this.popChar();switch(e){case`
`:case"\r":case"\u2028":case"\u2029":case"^":case"$":case"\\":case".":case"*":case"+":case"?":case"(":case")":case"[":case"|":throw Error("TBD");default:return{type:"Character",value:_(e)}}}isRegExpFlag(){switch(this.peekChar(0)){case"g":case"i":case"m":case"u":case"y":return!0;default:return!1}}isRangeDash(){return this.peekChar()==="-"&&this.isClassAtom(1)}isDigit(){return ln.test(this.peekChar(0))}isClassAtom(e=0){switch(this.peekChar(e)){case"]":case`
`:case"\r":case"\u2028":case"\u2029":return!1;default:return!0}}isTerm(){return this.isAtom()||this.isAssertion()}isAtom(){if(this.isPatternCharacter())return!0;switch(this.peekChar(0)){case".":case"\\":case"[":case"(":return!0;default:return!1}}isAssertion(){switch(this.peekChar(0)){case"^":case"$":return!0;case"\\":switch(this.peekChar(1)){case"b":case"B":return!0;default:return!1}case"(":return this.peekChar(1)==="?"&&(this.peekChar(2)==="="||this.peekChar(2)==="!");default:return!1}}isQuantifier(){const e=this.saveState();try{return this.quantifier(!0)!==void 0}catch{return!1}finally{this.restoreState(e)}}isPatternCharacter(){switch(this.peekChar()){case"^":case"$":case"\\":case".":case"*":case"+":case"?":case"(":case")":case"[":case"|":case"/":case`
`:case"\r":case"\u2028":case"\u2029":return!1;default:return!0}}parseHexDigits(e){let t="";for(let i=0;i<e;i++){const s=this.popChar();if(Fu.test(s)===!1)throw Error("Expecting a HexDecimal digits");t+=s}return{type:"Character",value:parseInt(t,16)}}peekChar(e=0){return this.input[this.idx+e]}popChar(){const e=this.peekChar(0);return this.consumeChar(void 0),e}consumeChar(e){if(e!==void 0&&this.input[this.idx]!==e)throw Error("Expected: '"+e+"' but found: '"+this.input[this.idx]+"' at offset: "+this.idx);if(this.idx>=this.input.length)throw Error("Unexpected end of input");this.idx++}loc(e){return{begin:e,end:this.idx}}}class zn{visitChildren(e){for(const t in e){const r=e[t];e.hasOwnProperty(t)&&(r.type!==void 0?this.visit(r):Array.isArray(r)&&r.forEach(i=>{this.visit(i)},this))}}visit(e){switch(e.type){case"Pattern":this.visitPattern(e);break;case"Flags":this.visitFlags(e);break;case"Disjunction":this.visitDisjunction(e);break;case"Alternative":this.visitAlternative(e);break;case"StartAnchor":this.visitStartAnchor(e);break;case"EndAnchor":this.visitEndAnchor(e);break;case"WordBoundary":this.visitWordBoundary(e);break;case"NonWordBoundary":this.visitNonWordBoundary(e);break;case"Lookahead":this.visitLookahead(e);break;case"NegativeLookahead":this.visitNegativeLookahead(e);break;case"Character":this.visitCharacter(e);break;case"Set":this.visitSet(e);break;case"Group":this.visitGroup(e);break;case"GroupBackReference":this.visitGroupBackReference(e);break;case"Quantifier":this.visitQuantifier(e);break}this.visitChildren(e)}visitPattern(e){}visitFlags(e){}visitDisjunction(e){}visitAlternative(e){}visitStartAnchor(e){}visitEndAnchor(e){}visitWordBoundary(e){}visitNonWordBoundary(e){}visitLookahead(e){}visitNegativeLookahead(e){}visitCharacter(e){}visitSet(e){}visitGroup(e){}visitGroupBackReference(e){}visitQuantifier(e){}}const Uu=/\r?\n/gm,Bu=new Uo;class Vu extends zn{constructor(){super(...arguments),this.isStarting=!0,this.endRegexpStack=[],this.multiline=!1}get endRegex(){return this.endRegexpStack.join("")}reset(e){this.multiline=!1,this.regex=e,this.startRegexp="",this.isStarting=!0,this.endRegexpStack=[]}visitGroup(e){e.quantifier&&(this.isStarting=!1,this.endRegexpStack=[])}visitCharacter(e){const t=String.fromCharCode(e.value);if(!this.multiline&&t===`
`&&(this.multiline=!0),e.quantifier)this.isStarting=!1,this.endRegexpStack=[];else{const r=qn(t);this.endRegexpStack.push(r),this.isStarting&&(this.startRegexp+=r)}}visitSet(e){if(!this.multiline){const t=this.regex.substring(e.loc.begin,e.loc.end),r=new RegExp(t);this.multiline=!!`
`.match(r)}if(e.quantifier)this.isStarting=!1,this.endRegexpStack=[];else{const t=this.regex.substring(e.loc.begin,e.loc.end);this.endRegexpStack.push(t),this.isStarting&&(this.startRegexp+=t)}}visitChildren(e){e.type==="Group"&&e.quantifier||super.visitChildren(e)}}const cr=new Vu;function Wu(n){try{return typeof n=="string"&&(n=new RegExp(n)),n=n.toString(),cr.reset(n),cr.visit(Bu.pattern(n)),cr.multiline}catch{return!1}}function qi(n){return(typeof n=="string"?new RegExp(n):n).test(" ")}function qn(n){return n.replace(/[.*+?^${}()|[\]\\]/g,"\\$&")}function ju(n){return Array.prototype.map.call(n,e=>/\w/.test(e)?`[${e.toLowerCase()}${e.toUpperCase()}]`:qn(e)).join("")}function Hu(n,e){const t=Ku(n),r=e.match(t);return!!r&&r[0].length>0}function Ku(n){typeof n=="string"&&(n=new RegExp(n));const e=n,t=n.source;let r=0;function i(){let s="",a;function o(c){s+=t.substr(r,c),r+=c}function l(c){s+="(?:"+t.substr(r,c)+"|$)",r+=c}for(;r<t.length;)switch(t[r]){case"\\":switch(t[r+1]){case"c":l(3);break;case"x":l(4);break;case"u":e.unicode?t[r+2]==="{"?l(t.indexOf("}",r)-r+1):l(6):l(2);break;case"p":case"P":e.unicode?l(t.indexOf("}",r)-r+1):l(2);break;case"k":l(t.indexOf(">",r)-r+1);break;default:l(2);break}break;case"[":a=/\[(?:\\.|.)*?\]/g,a.lastIndex=r,a=a.exec(t)||[],l(a[0].length);break;case"|":case"^":case"$":case"*":case"+":case"?":o(1);break;case"{":a=/\{\d+,?\d*\}/g,a.lastIndex=r,a=a.exec(t),a?o(a[0].length):l(1);break;case"(":if(t[r+1]==="?")switch(t[r+2]){case":":s+="(?:",r+=3,s+=i()+"|$)";break;case"=":s+="(?=",r+=3,s+=i()+")";break;case"!":a=r,r+=3,i(),s+=t.substr(a,r-a);break;case"<":switch(t[r+3]){case"=":case"!":a=r,r+=4,i(),s+=t.substr(a,r-a);break;default:o(t.indexOf(">",r)-r+1),s+=i()+"|$)";break}break}else o(1),s+=i()+"|$)";break;case")":return++r,s;default:l(1);break}return s}return new RegExp(i(),n.flags)}function zu(n){return n.rules.find(e=>Ce(e)&&e.entry)}function qu(n){return n.rules.filter(e=>mt(e)&&e.hidden)}function Bo(n,e){const t=new Set,r=zu(n);if(!r)return new Set(n.rules);const i=[r].concat(qu(n));for(const a of i)Vo(a,t,e);const s=new Set;for(const a of n.rules)(t.has(a.name)||mt(a)&&a.hidden)&&s.add(a);return s}function Vo(n,e,t){e.add(n.name),Zt(n).forEach(r=>{if(dt(r)||t){const i=r.rule.ref;i&&!e.has(i.name)&&Vo(i,e,t)}})}function Yu(n){if(n.terminal)return n.terminal;if(n.type.ref){const e=jo(n.type.ref);return e?.terminal}}function Xu(n){return n.hidden&&!pi(n).test(" ")}function Ju(n,e){return!n||!e?[]:fi(n,e,n.astNode,!0)}function Wo(n,e,t){if(!n||!e)return;const r=fi(n,e,n.astNode,!0);if(r.length!==0)return t!==void 0?t=Math.max(0,Math.min(t,r.length-1)):t=0,r[t]}function fi(n,e,t,r){if(!r){const i=Kn(n.grammarSource,ct);if(i&&i.feature===e)return[n]}return It(n)&&n.astNode===t?n.content.flatMap(i=>fi(i,e,t,!1)):[]}function Qu(n,e,t){if(!n)return;const r=Zu(n,e,n?.astNode);if(r.length!==0)return t!==void 0?t=Math.max(0,Math.min(t,r.length-1)):t=0,r[t]}function Zu(n,e,t){if(n.astNode!==t)return[];if(ut(n.grammarSource)&&n.grammarSource.value===e)return[n];const r=Er(n).iterator();let i;const s=[];do if(i=r.next(),!i.done){const a=i.value;a.astNode===t?ut(a.grammarSource)&&a.grammarSource.value===e&&s.push(a):r.prune()}while(!i.done);return s}function ed(n){var e;const t=n.astNode;for(;t===((e=n.container)===null||e===void 0?void 0:e.astNode);){const r=Kn(n.grammarSource,ct);if(r)return r;n=n.container}}function jo(n){let e=n;return lo(e)&&(Hn(e.$container)?e=e.$container.$container:Ce(e.$container)?e=e.$container:jn(e.$container)),Ho(n,e,new Map)}function Ho(n,e,t){var r;function i(s,a){let o;return Kn(s,ct)||(o=Ho(a,a,t)),t.set(n,o),o}if(t.has(n))return t.get(n);t.set(n,void 0);for(const s of Zt(e)){if(ct(s)&&s.feature.toLowerCase()==="name")return t.set(n,s),s;if(dt(s)&&Ce(s.rule.ref))return i(s,s.rule.ref);if(vu(s)&&(!((r=s.typeRef)===null||r===void 0)&&r.ref))return i(s,s.typeRef.ref)}}function hi(n){return Ko(n,new Set)}function Ko(n,e){if(e.has(n))return!0;e.add(n);for(const t of Zt(n))if(dt(t)){if(!t.rule.ref||Ce(t.rule.ref)&&!Ko(t.rule.ref,e))return!1}else{if(ct(t))return!1;if(Hn(t))return!1}return!!n.definition}function zo(n){if(n.inferredType)return n.inferredType.name;if(n.dataType)return n.dataType;if(n.returnType){const e=n.returnType.ref;if(e){if(Ce(e))return e.name;if(uo(e)||yo(e))return e.name}}}function Yn(n){var e;if(Ce(n))return hi(n)?n.name:(e=zo(n))!==null&&e!==void 0?e:n.name;if(uo(n)||yo(n)||Eu(n))return n.name;if(Hn(n)){const t=td(n);if(t)return t}else if(lo(n))return n.name;throw new Error("Cannot get name of Unknown Type")}function td(n){var e;if(n.inferredType)return n.inferredType.name;if(!((e=n.type)===null||e===void 0)&&e.ref)return Yn(n.type.ref)}function nd(n){var e,t,r;return mt(n)?(t=(e=n.type)===null||e===void 0?void 0:e.name)!==null&&t!==void 0?t:"string":hi(n)?n.name:(r=zo(n))!==null&&r!==void 0?r:n.name}function pi(n){const e={s:!1,i:!1,u:!1},t=Ot(n.definition,e),r=Object.entries(e).filter(([,i])=>i).map(([i])=>i).join("");return new RegExp(t,r)}const mi=/[\s\S]/.source;function Ot(n,e){if(wu(n))return rd(n);if(_u(n))return id(n);if(Iu(n))return od(n);if(Lu(n)){const t=n.rule.ref;if(!t)throw new Error("Missing rule reference.");return Ke(Ot(t.definition),{cardinality:n.cardinality,lookahead:n.lookahead})}else{if(Nu(n))return ad(n);if($u(n))return sd(n);if(Cu(n)){const t=n.regex.lastIndexOf("/"),r=n.regex.substring(1,t),i=n.regex.substring(t+1);return e&&(e.i=i.includes("i"),e.s=i.includes("s"),e.u=i.includes("u")),Ke(r,{cardinality:n.cardinality,lookahead:n.lookahead,wrap:!1})}else{if(Ou(n))return Ke(mi,{cardinality:n.cardinality,lookahead:n.lookahead});throw new Error(`Invalid terminal element: ${n?.$type}`)}}}function rd(n){return Ke(n.elements.map(e=>Ot(e)).join("|"),{cardinality:n.cardinality,lookahead:n.lookahead})}function id(n){return Ke(n.elements.map(e=>Ot(e)).join(""),{cardinality:n.cardinality,lookahead:n.lookahead})}function sd(n){return Ke(`${mi}*?${Ot(n.terminal)}`,{cardinality:n.cardinality,lookahead:n.lookahead})}function ad(n){return Ke(`(?!${Ot(n.terminal)})${mi}*?`,{cardinality:n.cardinality,lookahead:n.lookahead})}function od(n){return n.right?Ke(`[${ur(n.left)}-${ur(n.right)}]`,{cardinality:n.cardinality,lookahead:n.lookahead,wrap:!1}):Ke(ur(n.left),{cardinality:n.cardinality,lookahead:n.lookahead,wrap:!1})}function ur(n){return qn(n.value)}function Ke(n,e){var t;return(e.wrap!==!1||e.lookahead)&&(n=`(${(t=e.lookahead)!==null&&t!==void 0?t:""}${n})`),e.cardinality?`${n}${e.cardinality}`:n}function ld(n){const e=[],t=n.Grammar;for(const r of t.rules)mt(r)&&Xu(r)&&Wu(pi(r))&&e.push(r.name);return{multilineCommentRules:e,nameRegexp:iu}}function Sr(n){console&&console.error&&console.error(`Error: ${n}`)}function qo(n){console&&console.warn&&console.warn(`Warning: ${n}`)}function Yo(n){const e=new Date().getTime(),t=n();return{time:new Date().getTime()-e,value:t}}function Xo(n){function e(){}e.prototype=n;const t=new e;function r(){return typeof t.bar}return r(),r(),n}function cd(n){return ud(n)?n.LABEL:n.name}function ud(n){return fe(n.LABEL)&&n.LABEL!==""}class Be{get definition(){return this._definition}set definition(e){this._definition=e}constructor(e){this._definition=e}accept(e){e.visit(this),C(this.definition,t=>{t.accept(e)})}}class le extends Be{constructor(e){super([]),this.idx=1,Ae(this,Pe(e,t=>t!==void 0))}set definition(e){}get definition(){return this.referencedRule!==void 0?this.referencedRule.definition:[]}accept(e){e.visit(this)}}class bt extends Be{constructor(e){super(e.definition),this.orgText="",Ae(this,Pe(e,t=>t!==void 0))}}class he extends Be{constructor(e){super(e.definition),this.ignoreAmbiguities=!1,Ae(this,Pe(e,t=>t!==void 0))}}class ee extends Be{constructor(e){super(e.definition),this.idx=1,Ae(this,Pe(e,t=>t!==void 0))}}class ve extends Be{constructor(e){super(e.definition),this.idx=1,Ae(this,Pe(e,t=>t!==void 0))}}class ke extends Be{constructor(e){super(e.definition),this.idx=1,Ae(this,Pe(e,t=>t!==void 0))}}class W extends Be{constructor(e){super(e.definition),this.idx=1,Ae(this,Pe(e,t=>t!==void 0))}}class pe extends Be{constructor(e){super(e.definition),this.idx=1,Ae(this,Pe(e,t=>t!==void 0))}}class me extends Be{get definition(){return this._definition}set definition(e){this._definition=e}constructor(e){super(e.definition),this.idx=1,this.ignoreAmbiguities=!1,this.hasPredicates=!1,Ae(this,Pe(e,t=>t!==void 0))}}class G{constructor(e){this.idx=1,Ae(this,Pe(e,t=>t!==void 0))}accept(e){e.visit(this)}}function dd(n){return k(n,hn)}function hn(n){function e(t){return k(t,hn)}if(n instanceof le){const t={type:"NonTerminal",name:n.nonTerminalName,idx:n.idx};return fe(n.label)&&(t.label=n.label),t}else{if(n instanceof he)return{type:"Alternative",definition:e(n.definition)};if(n instanceof ee)return{type:"Option",idx:n.idx,definition:e(n.definition)};if(n instanceof ve)return{type:"RepetitionMandatory",idx:n.idx,definition:e(n.definition)};if(n instanceof ke)return{type:"RepetitionMandatoryWithSeparator",idx:n.idx,separator:hn(new G({terminalType:n.separator})),definition:e(n.definition)};if(n instanceof pe)return{type:"RepetitionWithSeparator",idx:n.idx,separator:hn(new G({terminalType:n.separator})),definition:e(n.definition)};if(n instanceof W)return{type:"Repetition",idx:n.idx,definition:e(n.definition)};if(n instanceof me)return{type:"Alternation",idx:n.idx,definition:e(n.definition)};if(n instanceof G){const t={type:"Terminal",name:n.terminalType.name,label:cd(n.terminalType),idx:n.idx};fe(n.label)&&(t.terminalLabel=n.label);const r=n.terminalType.PATTERN;return n.terminalType.PATTERN&&(t.pattern=qe(r)?r.source:r),t}else{if(n instanceof bt)return{type:"Rule",name:n.name,orgText:n.orgText,definition:e(n.definition)};throw Error("non exhaustive match")}}}class Pt{visit(e){const t=e;switch(t.constructor){case le:return this.visitNonTerminal(t);case he:return this.visitAlternative(t);case ee:return this.visitOption(t);case ve:return this.visitRepetitionMandatory(t);case ke:return this.visitRepetitionMandatoryWithSeparator(t);case pe:return this.visitRepetitionWithSeparator(t);case W:return this.visitRepetition(t);case me:return this.visitAlternation(t);case G:return this.visitTerminal(t);case bt:return this.visitRule(t);default:throw Error("non exhaustive match")}}visitNonTerminal(e){}visitAlternative(e){}visitOption(e){}visitRepetition(e){}visitRepetitionMandatory(e){}visitRepetitionMandatoryWithSeparator(e){}visitRepetitionWithSeparator(e){}visitAlternation(e){}visitTerminal(e){}visitRule(e){}}function fd(n){return n instanceof he||n instanceof ee||n instanceof W||n instanceof ve||n instanceof ke||n instanceof pe||n instanceof G||n instanceof bt}function vn(n,e=[]){return n instanceof ee||n instanceof W||n instanceof pe?!0:n instanceof me?Qa(n.definition,r=>vn(r,e)):n instanceof le&&ue(e,n)?!1:n instanceof Be?(n instanceof le&&e.push(n),$e(n.definition,r=>vn(r,e))):!1}function hd(n){return n instanceof me}function Fe(n){if(n instanceof le)return"SUBRULE";if(n instanceof ee)return"OPTION";if(n instanceof me)return"OR";if(n instanceof ve)return"AT_LEAST_ONE";if(n instanceof ke)return"AT_LEAST_ONE_SEP";if(n instanceof pe)return"MANY_SEP";if(n instanceof W)return"MANY";if(n instanceof G)return"CONSUME";throw Error("non exhaustive match")}class Xn{walk(e,t=[]){C(e.definition,(r,i)=>{const s=J(e.definition,i+1);if(r instanceof le)this.walkProdRef(r,s,t);else if(r instanceof G)this.walkTerminal(r,s,t);else if(r instanceof he)this.walkFlat(r,s,t);else if(r instanceof ee)this.walkOption(r,s,t);else if(r instanceof ve)this.walkAtLeastOne(r,s,t);else if(r instanceof ke)this.walkAtLeastOneSep(r,s,t);else if(r instanceof pe)this.walkManySep(r,s,t);else if(r instanceof W)this.walkMany(r,s,t);else if(r instanceof me)this.walkOr(r,s,t);else throw Error("non exhaustive match")})}walkTerminal(e,t,r){}walkProdRef(e,t,r){}walkFlat(e,t,r){const i=t.concat(r);this.walk(e,i)}walkOption(e,t,r){const i=t.concat(r);this.walk(e,i)}walkAtLeastOne(e,t,r){const i=[new ee({definition:e.definition})].concat(t,r);this.walk(e,i)}walkAtLeastOneSep(e,t,r){const i=Yi(e,t,r);this.walk(e,i)}walkMany(e,t,r){const i=[new ee({definition:e.definition})].concat(t,r);this.walk(e,i)}walkManySep(e,t,r){const i=Yi(e,t,r);this.walk(e,i)}walkOr(e,t,r){const i=t.concat(r);C(e.definition,s=>{const a=new he({definition:[s]});this.walk(a,i)})}}function Yi(n,e,t){return[new ee({definition:[new G({terminalType:n.separator})].concat(n.definition)})].concat(e,t)}function en(n){if(n instanceof le)return en(n.referencedRule);if(n instanceof G)return gd(n);if(fd(n))return pd(n);if(hd(n))return md(n);throw Error("non exhaustive match")}function pd(n){let e=[];const t=n.definition;let r=0,i=t.length>r,s,a=!0;for(;i&&a;)s=t[r],a=vn(s),e=e.concat(en(s)),r=r+1,i=t.length>r;return oi(e)}function md(n){const e=k(n.definition,t=>en(t));return oi(Ne(e))}function gd(n){return[n.terminalType]}const Jo="_~IN~_";class yd extends Xn{constructor(e){super(),this.topProd=e,this.follows={}}startWalking(){return this.walk(this.topProd),this.follows}walkTerminal(e,t,r){}walkProdRef(e,t,r){const i=Rd(e.referencedRule,e.idx)+this.topProd.name,s=t.concat(r),a=new he({definition:s}),o=en(a);this.follows[i]=o}}function Td(n){const e={};return C(n,t=>{const r=new yd(t).startWalking();Ae(e,r)}),e}function Rd(n,e){return n.name+e+Jo}let pn={};const Ad=new Uo;function Jn(n){const e=n.toString();if(pn.hasOwnProperty(e))return pn[e];{const t=Ad.pattern(e);return pn[e]=t,t}}function Ed(){pn={}}const Qo="Complement Sets are not supported for first char optimization",kn=`Unable to use "first char" lexer optimizations:
`;function vd(n,e=!1){try{const t=Jn(n);return Ir(t.value,{},t.flags.ignoreCase)}catch(t){if(t.message===Qo)e&&qo(`${kn}	Unable to optimize: < ${n.toString()} >
	Complement Sets cannot be automatically optimized.
	This will disable the lexer's first char optimizations.
	See: https://chevrotain.io/docs/guide/resolving_lexer_errors.html#COMPLEMENT for details.`);else{let r="";e&&(r=`
	This will disable the lexer's first char optimizations.
	See: https://chevrotain.io/docs/guide/resolving_lexer_errors.html#REGEXP_PARSING for details.`),Sr(`${kn}
	Failed parsing: < ${n.toString()} >
	Using the @chevrotain/regexp-to-ast library
	Please open an issue at: https://github.com/chevrotain/chevrotain/issues`+r)}}return[]}function Ir(n,e,t){switch(n.type){case"Disjunction":for(let i=0;i<n.value.length;i++)Ir(n.value[i],e,t);break;case"Alternative":const r=n.value;for(let i=0;i<r.length;i++){const s=r[i];switch(s.type){case"EndAnchor":case"GroupBackReference":case"Lookahead":case"NegativeLookahead":case"StartAnchor":case"WordBoundary":case"NonWordBoundary":continue}const a=s;switch(a.type){case"Character":cn(a.value,e,t);break;case"Set":if(a.complement===!0)throw Error(Qo);C(a.value,l=>{if(typeof l=="number")cn(l,e,t);else{const c=l;if(t===!0)for(let u=c.from;u<=c.to;u++)cn(u,e,t);else{for(let u=c.from;u<=c.to&&u<Kt;u++)cn(u,e,t);if(c.to>=Kt){const u=c.from>=Kt?c.from:Kt,d=c.to,f=Ze(u),h=Ze(d);for(let m=f;m<=h;m++)e[m]=m}}}});break;case"Group":Ir(a.value,e,t);break;default:throw Error("Non Exhaustive Match")}const o=a.quantifier!==void 0&&a.quantifier.atLeast===0;if(a.type==="Group"&&xr(a)===!1||a.type!=="Group"&&o===!1)break}break;default:throw Error("non exhaustive match!")}return K(e)}function cn(n,e,t){const r=Ze(n);e[r]=r,t===!0&&kd(n,e)}function kd(n,e){const t=String.fromCharCode(n),r=t.toUpperCase();if(r!==t){const i=Ze(r.charCodeAt(0));e[i]=i}else{const i=t.toLowerCase();if(i!==t){const s=Ze(i.charCodeAt(0));e[s]=s}}}function Xi(n,e){return kt(n.value,t=>{if(typeof t=="number")return ue(e,t);{const r=t;return kt(e,i=>r.from<=i&&i<=r.to)!==void 0}})}function xr(n){const e=n.quantifier;return e&&e.atLeast===0?!0:n.value?Z(n.value)?$e(n.value,xr):xr(n.value):!1}class Sd extends zn{constructor(e){super(),this.targetCharCodes=e,this.found=!1}visitChildren(e){if(this.found!==!0){switch(e.type){case"Lookahead":this.visitLookahead(e);return;case"NegativeLookahead":this.visitNegativeLookahead(e);return}super.visitChildren(e)}}visitCharacter(e){ue(this.targetCharCodes,e.value)&&(this.found=!0)}visitSet(e){e.complement?Xi(e,this.targetCharCodes)===void 0&&(this.found=!0):Xi(e,this.targetCharCodes)!==void 0&&(this.found=!0)}}function gi(n,e){if(e instanceof RegExp){const t=Jn(e),r=new Sd(n);return r.visit(t),r.found}else return kt(e,t=>ue(n,t.charCodeAt(0)))!==void 0}const ft="PATTERN",Ht="defaultMode",un="modes";let Zo=typeof new RegExp("(?:)").sticky=="boolean";function Id(n,e){e=ai(e,{useSticky:Zo,debug:!1,safeMode:!1,positionTracking:"full",lineTerminatorCharacters:["\r",`
`],tracer:(E,R)=>R()});const t=e.tracer;t("initCharCodeToOptimizedIndexMap",()=>{qd()});let r;t("Reject Lexer.NA",()=>{r=Wn(n,E=>E[ft]===de.NA)});let i=!1,s;t("Transform Patterns",()=>{i=!1,s=k(r,E=>{const R=E[ft];if(qe(R)){const N=R.source;return N.length===1&&N!=="^"&&N!=="$"&&N!=="."&&!R.ignoreCase?N:N.length===2&&N[0]==="\\"&&!ue(["d","D","s","S","t","r","n","t","0","c","b","B","f","v","w","W"],N[1])?N[1]:e.useSticky?Qi(R):Ji(R)}else{if(pt(R))return i=!0,{exec:R};if(typeof R=="object")return i=!0,R;if(typeof R=="string"){if(R.length===1)return R;{const N=R.replace(/[\\^$.*+?()[\]{}|]/g,"\\$&"),F=new RegExp(N);return e.useSticky?Qi(F):Ji(F)}}else throw Error("non exhaustive match")}})});let a,o,l,c,u;t("misc mapping",()=>{a=k(r,E=>E.tokenTypeIdx),o=k(r,E=>{const R=E.GROUP;if(R!==de.SKIPPED){if(fe(R))return R;if(ze(R))return!1;throw Error("non exhaustive match")}}),l=k(r,E=>{const R=E.LONGER_ALT;if(R)return Z(R)?k(R,F=>Ui(r,F)):[Ui(r,R)]}),c=k(r,E=>E.PUSH_MODE),u=k(r,E=>w(E,"POP_MODE"))});let d;t("Line Terminator Handling",()=>{const E=nl(e.lineTerminatorCharacters);d=k(r,R=>!1),e.positionTracking!=="onlyOffset"&&(d=k(r,R=>w(R,"LINE_BREAKS")?!!R.LINE_BREAKS:tl(R,E)===!1&&gi(E,R.PATTERN)))});let f,h,m,g;t("Misc Mapping #2",()=>{f=k(r,el),h=k(s,Hd),m=oe(r,(E,R)=>{const N=R.GROUP;return fe(N)&&N!==de.SKIPPED&&(E[N]=[]),E},{}),g=k(s,(E,R)=>({pattern:s[R],longerAlt:l[R],canLineTerminator:d[R],isCustom:f[R],short:h[R],group:o[R],push:c[R],pop:u[R],tokenTypeIdx:a[R],tokenType:r[R]}))});let A=!0,T=[];return e.safeMode||t("First Char Optimization",()=>{T=oe(r,(E,R,N)=>{if(typeof R.PATTERN=="string"){const F=R.PATTERN.charCodeAt(0),ne=Ze(F);dr(E,ne,g[N])}else if(Z(R.START_CHARS_HINT)){let F;C(R.START_CHARS_HINT,ne=>{const _e=typeof ne=="string"?ne.charCodeAt(0):ne,ge=Ze(_e);F!==ge&&(F=ge,dr(E,ge,g[N]))})}else if(qe(R.PATTERN))if(R.PATTERN.unicode)A=!1,e.ensureOptimizations&&Sr(`${kn}	Unable to analyze < ${R.PATTERN.toString()} > pattern.
	The regexp unicode flag is not currently supported by the regexp-to-ast library.
	This will disable the lexer's first char optimizations.
	For details See: https://chevrotain.io/docs/guide/resolving_lexer_errors.html#UNICODE_OPTIMIZE`);else{const F=vd(R.PATTERN,e.ensureOptimizations);D(F)&&(A=!1),C(F,ne=>{dr(E,ne,g[N])})}else e.ensureOptimizations&&Sr(`${kn}	TokenType: <${R.name}> is using a custom token pattern without providing <start_chars_hint> parameter.
	This will disable the lexer's first char optimizations.
	For details See: https://chevrotain.io/docs/guide/resolving_lexer_errors.html#CUSTOM_OPTIMIZE`),A=!1;return E},[])}),{emptyGroups:m,patternIdxToConfig:g,charCodeToPatternIdxToConfig:T,hasCustom:i,canBeOptimized:A}}function xd(n,e){let t=[];const r=Cd(n);t=t.concat(r.errors);const i=wd(r.valid),s=i.valid;return t=t.concat(i.errors),t=t.concat(Nd(s)),t=t.concat(Dd(s)),t=t.concat(Fd(s,e)),t=t.concat(Gd(s)),t}function Nd(n){let e=[];const t=Ee(n,r=>qe(r[ft]));return e=e.concat(Ld(t)),e=e.concat(bd(t)),e=e.concat(Pd(t)),e=e.concat(Md(t)),e=e.concat($d(t)),e}function Cd(n){const e=Ee(n,i=>!w(i,ft)),t=k(e,i=>({message:"Token Type: ->"+i.name+"<- missing static 'PATTERN' property",type:j.MISSING_PATTERN,tokenTypes:[i]})),r=Vn(n,e);return{errors:t,valid:r}}function wd(n){const e=Ee(n,i=>{const s=i[ft];return!qe(s)&&!pt(s)&&!w(s,"exec")&&!fe(s)}),t=k(e,i=>({message:"Token Type: ->"+i.name+"<- static 'PATTERN' can only be a RegExp, a Function matching the {CustomPatternMatcherFunc} type or an Object matching the {ICustomPattern} interface.",type:j.INVALID_PATTERN,tokenTypes:[i]})),r=Vn(n,e);return{errors:t,valid:r}}const _d=/[^\\][$]/;function Ld(n){class e extends zn{constructor(){super(...arguments),this.found=!1}visitEndAnchor(s){this.found=!0}}const t=Ee(n,i=>{const s=i.PATTERN;try{const a=Jn(s),o=new e;return o.visit(a),o.found}catch{return _d.test(s.source)}});return k(t,i=>({message:`Unexpected RegExp Anchor Error:
	Token Type: ->`+i.name+`<- static 'PATTERN' cannot contain end of input anchor '$'
	See chevrotain.io/docs/guide/resolving_lexer_errors.html#ANCHORS	for details.`,type:j.EOI_ANCHOR_FOUND,tokenTypes:[i]}))}function $d(n){const e=Ee(n,r=>r.PATTERN.test(""));return k(e,r=>({message:"Token Type: ->"+r.name+"<- static 'PATTERN' must not match an empty string",type:j.EMPTY_MATCH_PATTERN,tokenTypes:[r]}))}const Od=/[^\\[][\^]|^\^/;function bd(n){class e extends zn{constructor(){super(...arguments),this.found=!1}visitStartAnchor(s){this.found=!0}}const t=Ee(n,i=>{const s=i.PATTERN;try{const a=Jn(s),o=new e;return o.visit(a),o.found}catch{return Od.test(s.source)}});return k(t,i=>({message:`Unexpected RegExp Anchor Error:
	Token Type: ->`+i.name+`<- static 'PATTERN' cannot contain start of input anchor '^'
	See https://chevrotain.io/docs/guide/resolving_lexer_errors.html#ANCHORS	for details.`,type:j.SOI_ANCHOR_FOUND,tokenTypes:[i]}))}function Pd(n){const e=Ee(n,r=>{const i=r[ft];return i instanceof RegExp&&(i.multiline||i.global)});return k(e,r=>({message:"Token Type: ->"+r.name+"<- static 'PATTERN' may NOT contain global('g') or multiline('m')",type:j.UNSUPPORTED_FLAGS_FOUND,tokenTypes:[r]}))}function Md(n){const e=[];let t=k(n,s=>oe(n,(a,o)=>(s.PATTERN.source===o.PATTERN.source&&!ue(e,o)&&o.PATTERN!==de.NA&&(e.push(o),a.push(o)),a),[]));t=Qt(t);const r=Ee(t,s=>s.length>1);return k(r,s=>{const a=k(s,l=>l.name);return{message:`The same RegExp pattern ->${be(s).PATTERN}<-has been used in all of the following Token Types: ${a.join(", ")} <-`,type:j.DUPLICATE_PATTERNS_FOUND,tokenTypes:s}})}function Dd(n){const e=Ee(n,r=>{if(!w(r,"GROUP"))return!1;const i=r.GROUP;return i!==de.SKIPPED&&i!==de.NA&&!fe(i)});return k(e,r=>({message:"Token Type: ->"+r.name+"<- static 'GROUP' can only be Lexer.SKIPPED/Lexer.NA/A String",type:j.INVALID_GROUP_TYPE_FOUND,tokenTypes:[r]}))}function Fd(n,e){const t=Ee(n,i=>i.PUSH_MODE!==void 0&&!ue(e,i.PUSH_MODE));return k(t,i=>({message:`Token Type: ->${i.name}<- static 'PUSH_MODE' value cannot refer to a Lexer Mode ->${i.PUSH_MODE}<-which does not exist`,type:j.PUSH_MODE_DOES_NOT_EXIST,tokenTypes:[i]}))}function Gd(n){const e=[],t=oe(n,(r,i,s)=>{const a=i.PATTERN;return a===de.NA||(fe(a)?r.push({str:a,idx:s,tokenType:i}):qe(a)&&Bd(a)&&r.push({str:a.source,idx:s,tokenType:i})),r},[]);return C(n,(r,i)=>{C(t,({str:s,idx:a,tokenType:o})=>{if(i<a&&Ud(s,r.PATTERN)){const l=`Token: ->${o.name}<- can never be matched.
Because it appears AFTER the Token Type ->${r.name}<-in the lexer's definition.
See https://chevrotain.io/docs/guide/resolving_lexer_errors.html#UNREACHABLE`;e.push({message:l,type:j.UNREACHABLE_PATTERN,tokenTypes:[r,o]})}})}),e}function Ud(n,e){if(qe(e)){const t=e.exec(n);return t!==null&&t.index===0}else{if(pt(e))return e(n,0,[],{});if(w(e,"exec"))return e.exec(n,0,[],{});if(typeof e=="string")return e===n;throw Error("non exhaustive match")}}function Bd(n){return kt([".","\\","[","]","|","^","$","(",")","?","*","+","{"],t=>n.source.indexOf(t)!==-1)===void 0}function Ji(n){const e=n.ignoreCase?"i":"";return new RegExp(`^(?:${n.source})`,e)}function Qi(n){const e=n.ignoreCase?"iy":"y";return new RegExp(`${n.source}`,e)}function Vd(n,e,t){const r=[];return w(n,Ht)||r.push({message:"A MultiMode Lexer cannot be initialized without a <"+Ht+`> property in its definition
`,type:j.MULTI_MODE_LEXER_WITHOUT_DEFAULT_MODE}),w(n,un)||r.push({message:"A MultiMode Lexer cannot be initialized without a <"+un+`> property in its definition
`,type:j.MULTI_MODE_LEXER_WITHOUT_MODES_PROPERTY}),w(n,un)&&w(n,Ht)&&!w(n.modes,n.defaultMode)&&r.push({message:`A MultiMode Lexer cannot be initialized with a ${Ht}: <${n.defaultMode}>which does not exist
`,type:j.MULTI_MODE_LEXER_DEFAULT_MODE_VALUE_DOES_NOT_EXIST}),w(n,un)&&C(n.modes,(i,s)=>{C(i,(a,o)=>{if(ze(a))r.push({message:`A Lexer cannot be initialized using an undefined Token Type. Mode:<${s}> at index: <${o}>
`,type:j.LEXER_DEFINITION_CANNOT_CONTAIN_UNDEFINED});else if(w(a,"LONGER_ALT")){const l=Z(a.LONGER_ALT)?a.LONGER_ALT:[a.LONGER_ALT];C(l,c=>{!ze(c)&&!ue(i,c)&&r.push({message:`A MultiMode Lexer cannot be initialized with a longer_alt <${c.name}> on token <${a.name}> outside of mode <${s}>
`,type:j.MULTI_MODE_LEXER_LONGER_ALT_NOT_IN_CURRENT_MODE})})}})}),r}function Wd(n,e,t){const r=[];let i=!1;const s=Qt(Ne(K(n.modes))),a=Wn(s,l=>l[ft]===de.NA),o=nl(t);return e&&C(a,l=>{const c=tl(l,o);if(c!==!1){const d={message:zd(l,c),type:c.issue,tokenType:l};r.push(d)}else w(l,"LINE_BREAKS")?l.LINE_BREAKS===!0&&(i=!0):gi(o,l.PATTERN)&&(i=!0)}),e&&!i&&r.push({message:`Warning: No LINE_BREAKS Found.
	This Lexer has been defined to track line and column information,
	But none of the Token Types can be identified as matching a line terminator.
	See https://chevrotain.io/docs/guide/resolving_lexer_errors.html#LINE_BREAKS 
	for details.`,type:j.NO_LINE_BREAKS_FLAGS}),r}function jd(n){const e={},t=vt(n);return C(t,r=>{const i=n[r];if(Z(i))e[r]=[];else throw Error("non exhaustive match")}),e}function el(n){const e=n.PATTERN;if(qe(e))return!1;if(pt(e))return!0;if(w(e,"exec"))return!0;if(fe(e))return!1;throw Error("non exhaustive match")}function Hd(n){return fe(n)&&n.length===1?n.charCodeAt(0):!1}const Kd={test:function(n){const e=n.length;for(let t=this.lastIndex;t<e;t++){const r=n.charCodeAt(t);if(r===10)return this.lastIndex=t+1,!0;if(r===13)return n.charCodeAt(t+1)===10?this.lastIndex=t+2:this.lastIndex=t+1,!0}return!1},lastIndex:0};function tl(n,e){if(w(n,"LINE_BREAKS"))return!1;if(qe(n.PATTERN)){try{gi(e,n.PATTERN)}catch(t){return{issue:j.IDENTIFY_TERMINATOR,errMsg:t.message}}return!1}else{if(fe(n.PATTERN))return!1;if(el(n))return{issue:j.CUSTOM_LINE_BREAK};throw Error("non exhaustive match")}}function zd(n,e){if(e.issue===j.IDENTIFY_TERMINATOR)return`Warning: unable to identify line terminator usage in pattern.
	The problem is in the <${n.name}> Token Type
	 Root cause: ${e.errMsg}.
	For details See: https://chevrotain.io/docs/guide/resolving_lexer_errors.html#IDENTIFY_TERMINATOR`;if(e.issue===j.CUSTOM_LINE_BREAK)return`Warning: A Custom Token Pattern should specify the <line_breaks> option.
	The problem is in the <${n.name}> Token Type
	For details See: https://chevrotain.io/docs/guide/resolving_lexer_errors.html#CUSTOM_LINE_BREAK`;throw Error("non exhaustive match")}function nl(n){return k(n,t=>fe(t)?t.charCodeAt(0):t)}function dr(n,e,t){n[e]===void 0?n[e]=[t]:n[e].push(t)}const Kt=256;let mn=[];function Ze(n){return n<Kt?n:mn[n]}function qd(){if(D(mn)){mn=new Array(65536);for(let n=0;n<65536;n++)mn[n]=n>255?255+~~(n/255):n}}function tn(n,e){const t=n.tokenTypeIdx;return t===e.tokenTypeIdx?!0:e.isParent===!0&&e.categoryMatchesMap[t]===!0}function Sn(n,e){return n.tokenTypeIdx===e.tokenTypeIdx}let Zi=1;const rl={};function nn(n){const e=Yd(n);Xd(e),Qd(e),Jd(e),C(e,t=>{t.isParent=t.categoryMatches.length>0})}function Yd(n){let e=te(n),t=n,r=!0;for(;r;){t=Qt(Ne(k(t,s=>s.CATEGORIES)));const i=Vn(t,e);e=e.concat(i),D(i)?r=!1:t=i}return e}function Xd(n){C(n,e=>{sl(e)||(rl[Zi]=e,e.tokenTypeIdx=Zi++),es(e)&&!Z(e.CATEGORIES)&&(e.CATEGORIES=[e.CATEGORIES]),es(e)||(e.CATEGORIES=[]),Zd(e)||(e.categoryMatches=[]),ef(e)||(e.categoryMatchesMap={})})}function Jd(n){C(n,e=>{e.categoryMatches=[],C(e.categoryMatchesMap,(t,r)=>{e.categoryMatches.push(rl[r].tokenTypeIdx)})})}function Qd(n){C(n,e=>{il([],e)})}function il(n,e){C(n,t=>{e.categoryMatchesMap[t.tokenTypeIdx]=!0}),C(e.CATEGORIES,t=>{const r=n.concat(e);ue(r,t)||il(r,t)})}function sl(n){return w(n,"tokenTypeIdx")}function es(n){return w(n,"CATEGORIES")}function Zd(n){return w(n,"categoryMatches")}function ef(n){return w(n,"categoryMatchesMap")}function tf(n){return w(n,"tokenTypeIdx")}const nf={buildUnableToPopLexerModeMessage(n){return`Unable to pop Lexer Mode after encountering Token ->${n.image}<- The Mode Stack is empty`},buildUnexpectedCharactersMessage(n,e,t,r,i){return`unexpected character: ->${n.charAt(e)}<- at offset: ${e}, skipped ${t} characters.`}};var j;(function(n){n[n.MISSING_PATTERN=0]="MISSING_PATTERN",n[n.INVALID_PATTERN=1]="INVALID_PATTERN",n[n.EOI_ANCHOR_FOUND=2]="EOI_ANCHOR_FOUND",n[n.UNSUPPORTED_FLAGS_FOUND=3]="UNSUPPORTED_FLAGS_FOUND",n[n.DUPLICATE_PATTERNS_FOUND=4]="DUPLICATE_PATTERNS_FOUND",n[n.INVALID_GROUP_TYPE_FOUND=5]="INVALID_GROUP_TYPE_FOUND",n[n.PUSH_MODE_DOES_NOT_EXIST=6]="PUSH_MODE_DOES_NOT_EXIST",n[n.MULTI_MODE_LEXER_WITHOUT_DEFAULT_MODE=7]="MULTI_MODE_LEXER_WITHOUT_DEFAULT_MODE",n[n.MULTI_MODE_LEXER_WITHOUT_MODES_PROPERTY=8]="MULTI_MODE_LEXER_WITHOUT_MODES_PROPERTY",n[n.MULTI_MODE_LEXER_DEFAULT_MODE_VALUE_DOES_NOT_EXIST=9]="MULTI_MODE_LEXER_DEFAULT_MODE_VALUE_DOES_NOT_EXIST",n[n.LEXER_DEFINITION_CANNOT_CONTAIN_UNDEFINED=10]="LEXER_DEFINITION_CANNOT_CONTAIN_UNDEFINED",n[n.SOI_ANCHOR_FOUND=11]="SOI_ANCHOR_FOUND",n[n.EMPTY_MATCH_PATTERN=12]="EMPTY_MATCH_PATTERN",n[n.NO_LINE_BREAKS_FLAGS=13]="NO_LINE_BREAKS_FLAGS",n[n.UNREACHABLE_PATTERN=14]="UNREACHABLE_PATTERN",n[n.IDENTIFY_TERMINATOR=15]="IDENTIFY_TERMINATOR",n[n.CUSTOM_LINE_BREAK=16]="CUSTOM_LINE_BREAK",n[n.MULTI_MODE_LEXER_LONGER_ALT_NOT_IN_CURRENT_MODE=17]="MULTI_MODE_LEXER_LONGER_ALT_NOT_IN_CURRENT_MODE"})(j||(j={}));const zt={deferDefinitionErrorsHandling:!1,positionTracking:"full",lineTerminatorsPattern:/\n|\r\n?/g,lineTerminatorCharacters:[`
`,"\r"],ensureOptimizations:!1,safeMode:!1,errorMessageProvider:nf,traceInitPerf:!1,skipValidations:!1,recoveryEnabled:!0};Object.freeze(zt);class de{constructor(e,t=zt){if(this.lexerDefinition=e,this.lexerDefinitionErrors=[],this.lexerDefinitionWarning=[],this.patternIdxToConfig={},this.charCodeToPatternIdxToConfig={},this.modes=[],this.emptyGroups={},this.trackStartLines=!0,this.trackEndLines=!0,this.hasCustom=!1,this.canModeBeOptimized={},this.TRACE_INIT=(i,s)=>{if(this.traceInitPerf===!0){this.traceInitIndent++;const a=new Array(this.traceInitIndent+1).join("	");this.traceInitIndent<this.traceInitMaxIdent&&console.log(`${a}--> <${i}>`);const{time:o,value:l}=Yo(s),c=o>10?console.warn:console.log;return this.traceInitIndent<this.traceInitMaxIdent&&c(`${a}<-- <${i}> time: ${o}ms`),this.traceInitIndent--,l}else return s()},typeof t=="boolean")throw Error(`The second argument to the Lexer constructor is now an ILexerConfig Object.
a boolean 2nd argument is no longer supported`);this.config=Ae({},zt,t);const r=this.config.traceInitPerf;r===!0?(this.traceInitMaxIdent=1/0,this.traceInitPerf=!0):typeof r=="number"&&(this.traceInitMaxIdent=r,this.traceInitPerf=!0),this.traceInitIndent=-1,this.TRACE_INIT("Lexer Constructor",()=>{let i,s=!0;this.TRACE_INIT("Lexer Config handling",()=>{if(this.config.lineTerminatorsPattern===zt.lineTerminatorsPattern)this.config.lineTerminatorsPattern=Kd;else if(this.config.lineTerminatorCharacters===zt.lineTerminatorCharacters)throw Error(`Error: Missing <lineTerminatorCharacters> property on the Lexer config.
	For details See: https://chevrotain.io/docs/guide/resolving_lexer_errors.html#MISSING_LINE_TERM_CHARS`);if(t.safeMode&&t.ensureOptimizations)throw Error('"safeMode" and "ensureOptimizations" flags are mutually exclusive.');this.trackStartLines=/full|onlyStart/i.test(this.config.positionTracking),this.trackEndLines=/full/i.test(this.config.positionTracking),Z(e)?i={modes:{defaultMode:te(e)},defaultMode:Ht}:(s=!1,i=te(e))}),this.config.skipValidations===!1&&(this.TRACE_INIT("performRuntimeChecks",()=>{this.lexerDefinitionErrors=this.lexerDefinitionErrors.concat(Vd(i,this.trackStartLines,this.config.lineTerminatorCharacters))}),this.TRACE_INIT("performWarningRuntimeChecks",()=>{this.lexerDefinitionWarning=this.lexerDefinitionWarning.concat(Wd(i,this.trackStartLines,this.config.lineTerminatorCharacters))})),i.modes=i.modes?i.modes:{},C(i.modes,(o,l)=>{i.modes[l]=Wn(o,c=>ze(c))});const a=vt(i.modes);if(C(i.modes,(o,l)=>{this.TRACE_INIT(`Mode: <${l}> processing`,()=>{if(this.modes.push(l),this.config.skipValidations===!1&&this.TRACE_INIT("validatePatterns",()=>{this.lexerDefinitionErrors=this.lexerDefinitionErrors.concat(xd(o,a))}),D(this.lexerDefinitionErrors)){nn(o);let c;this.TRACE_INIT("analyzeTokenTypes",()=>{c=Id(o,{lineTerminatorCharacters:this.config.lineTerminatorCharacters,positionTracking:t.positionTracking,ensureOptimizations:t.ensureOptimizations,safeMode:t.safeMode,tracer:this.TRACE_INIT})}),this.patternIdxToConfig[l]=c.patternIdxToConfig,this.charCodeToPatternIdxToConfig[l]=c.charCodeToPatternIdxToConfig,this.emptyGroups=Ae({},this.emptyGroups,c.emptyGroups),this.hasCustom=c.hasCustom||this.hasCustom,this.canModeBeOptimized[l]=c.canBeOptimized}})}),this.defaultMode=i.defaultMode,!D(this.lexerDefinitionErrors)&&!this.config.deferDefinitionErrorsHandling){const l=k(this.lexerDefinitionErrors,c=>c.message).join(`-----------------------
`);throw new Error(`Errors detected in definition of Lexer:
`+l)}C(this.lexerDefinitionWarning,o=>{qo(o.message)}),this.TRACE_INIT("Choosing sub-methods implementations",()=>{if(Zo?(this.chopInput=Gi,this.match=this.matchWithTest):(this.updateLastIndex=q,this.match=this.matchWithExec),s&&(this.handleModes=q),this.trackStartLines===!1&&(this.computeNewColumn=Gi),this.trackEndLines===!1&&(this.updateTokenEndLineColumnLocation=q),/full/i.test(this.config.positionTracking))this.createTokenInstance=this.createFullToken;else if(/onlyStart/i.test(this.config.positionTracking))this.createTokenInstance=this.createStartOnlyToken;else if(/onlyOffset/i.test(this.config.positionTracking))this.createTokenInstance=this.createOffsetOnlyToken;else throw Error(`Invalid <positionTracking> config option: "${this.config.positionTracking}"`);this.hasCustom?(this.addToken=this.addTokenUsingPush,this.handlePayload=this.handlePayloadWithCustom):(this.addToken=this.addTokenUsingMemberAccess,this.handlePayload=this.handlePayloadNoCustom)}),this.TRACE_INIT("Failed Optimization Warnings",()=>{const o=oe(this.canModeBeOptimized,(l,c,u)=>(c===!1&&l.push(u),l),[]);if(t.ensureOptimizations&&!D(o))throw Error(`Lexer Modes: < ${o.join(", ")} > cannot be optimized.
	 Disable the "ensureOptimizations" lexer config flag to silently ignore this and run the lexer in an un-optimized mode.
	 Or inspect the console log for details on how to resolve these issues.`)}),this.TRACE_INIT("clearRegExpParserCache",()=>{Ed()}),this.TRACE_INIT("toFastProperties",()=>{Xo(this)})})}tokenize(e,t=this.defaultMode){if(!D(this.lexerDefinitionErrors)){const i=k(this.lexerDefinitionErrors,s=>s.message).join(`-----------------------
`);throw new Error(`Unable to Tokenize because Errors detected in definition of Lexer:
`+i)}return this.tokenizeInternal(e,t)}tokenizeInternal(e,t){let r,i,s,a,o,l,c,u,d,f,h,m,g,A,T;const E=e,R=E.length;let N=0,F=0;const ne=this.hasCustom?0:Math.floor(e.length/10),_e=new Array(ne),ge=[];let De=this.trackStartLines?1:void 0,Se=this.trackStartLines?1:void 0;const v=jd(this.emptyGroups),y=this.trackStartLines,x=this.config.lineTerminatorsPattern;let S=0,O=[],$=[];const L=[],ye=[];Object.freeze(ye);let z;function V(){return O}function st(re){const Ie=Ze(re),yt=$[Ie];return yt===void 0?ye:yt}const hc=re=>{if(L.length===1&&re.tokenType.PUSH_MODE===void 0){const Ie=this.config.errorMessageProvider.buildUnableToPopLexerModeMessage(re);ge.push({offset:re.startOffset,line:re.startLine,column:re.startColumn,length:re.image.length,message:Ie})}else{L.pop();const Ie=St(L);O=this.patternIdxToConfig[Ie],$=this.charCodeToPatternIdxToConfig[Ie],S=O.length;const yt=this.canModeBeOptimized[Ie]&&this.config.safeMode===!1;$&&yt?z=st:z=V}};function bi(re){L.push(re),$=this.charCodeToPatternIdxToConfig[re],O=this.patternIdxToConfig[re],S=O.length,S=O.length;const Ie=this.canModeBeOptimized[re]&&this.config.safeMode===!1;$&&Ie?z=st:z=V}bi.call(this,t);let Le;const Pi=this.config.recoveryEnabled;for(;N<R;){l=null;const re=E.charCodeAt(N),Ie=z(re),yt=Ie.length;for(r=0;r<yt;r++){Le=Ie[r];const Te=Le.pattern;c=null;const Ve=Le.short;if(Ve!==!1?re===Ve&&(l=Te):Le.isCustom===!0?(T=Te.exec(E,N,_e,v),T!==null?(l=T[0],T.payload!==void 0&&(c=T.payload)):l=null):(this.updateLastIndex(Te,N),l=this.match(Te,e,N)),l!==null){if(o=Le.longerAlt,o!==void 0){const Xe=o.length;for(s=0;s<Xe;s++){const We=O[o[s]],at=We.pattern;if(u=null,We.isCustom===!0?(T=at.exec(E,N,_e,v),T!==null?(a=T[0],T.payload!==void 0&&(u=T.payload)):a=null):(this.updateLastIndex(at,N),a=this.match(at,e,N)),a&&a.length>l.length){l=a,c=u,Le=We;break}}}break}}if(l!==null){if(d=l.length,f=Le.group,f!==void 0&&(h=Le.tokenTypeIdx,m=this.createTokenInstance(l,N,h,Le.tokenType,De,Se,d),this.handlePayload(m,c),f===!1?F=this.addToken(_e,F,m):v[f].push(m)),e=this.chopInput(e,d),N=N+d,Se=this.computeNewColumn(Se,d),y===!0&&Le.canLineTerminator===!0){let Te=0,Ve,Xe;x.lastIndex=0;do Ve=x.test(l),Ve===!0&&(Xe=x.lastIndex-1,Te++);while(Ve===!0);Te!==0&&(De=De+Te,Se=d-Xe,this.updateTokenEndLineColumnLocation(m,f,Xe,Te,De,Se,d))}this.handleModes(Le,hc,bi,m)}else{const Te=N,Ve=De,Xe=Se;let We=Pi===!1;for(;We===!1&&N<R;)for(e=this.chopInput(e,1),N++,i=0;i<S;i++){const at=O[i],sr=at.pattern,Mi=at.short;if(Mi!==!1?E.charCodeAt(N)===Mi&&(We=!0):at.isCustom===!0?We=sr.exec(E,N,_e,v)!==null:(this.updateLastIndex(sr,N),We=sr.exec(e)!==null),We===!0)break}if(g=N-Te,Se=this.computeNewColumn(Se,g),A=this.config.errorMessageProvider.buildUnexpectedCharactersMessage(E,Te,g,Ve,Xe),ge.push({offset:Te,line:Ve,column:Xe,length:g,message:A}),Pi===!1)break}}return this.hasCustom||(_e.length=F),{tokens:_e,groups:v,errors:ge}}handleModes(e,t,r,i){if(e.pop===!0){const s=e.push;t(i),s!==void 0&&r.call(this,s)}else e.push!==void 0&&r.call(this,e.push)}chopInput(e,t){return e.substring(t)}updateLastIndex(e,t){e.lastIndex=t}updateTokenEndLineColumnLocation(e,t,r,i,s,a,o){let l,c;t!==void 0&&(l=r===o-1,c=l?-1:0,i===1&&l===!0||(e.endLine=s+c,e.endColumn=a-1+-c))}computeNewColumn(e,t){return e+t}createOffsetOnlyToken(e,t,r,i){return{image:e,startOffset:t,tokenTypeIdx:r,tokenType:i}}createStartOnlyToken(e,t,r,i,s,a){return{image:e,startOffset:t,startLine:s,startColumn:a,tokenTypeIdx:r,tokenType:i}}createFullToken(e,t,r,i,s,a,o){return{image:e,startOffset:t,endOffset:t+o-1,startLine:s,endLine:s,startColumn:a,endColumn:a+o-1,tokenTypeIdx:r,tokenType:i}}addTokenUsingPush(e,t,r){return e.push(r),t}addTokenUsingMemberAccess(e,t,r){return e[t]=r,t++,t}handlePayloadNoCustom(e,t){}handlePayloadWithCustom(e,t){t!==null&&(e.payload=t)}matchWithTest(e,t,r){return e.test(t)===!0?t.substring(r,e.lastIndex):null}matchWithExec(e,t){const r=e.exec(t);return r!==null?r[0]:null}}de.SKIPPED="This marks a skipped Token pattern, this means each token identified by it willbe consumed and then thrown into oblivion, this can be used to for example to completely ignore whitespace.";de.NA=/NOT_APPLICABLE/;function Et(n){return al(n)?n.LABEL:n.name}function al(n){return fe(n.LABEL)&&n.LABEL!==""}const rf="parent",ts="categories",ns="label",rs="group",is="push_mode",ss="pop_mode",as="longer_alt",os="line_breaks",ls="start_chars_hint";function ol(n){return sf(n)}function sf(n){const e=n.pattern,t={};if(t.name=n.name,ze(e)||(t.PATTERN=e),w(n,rf))throw`The parent property is no longer supported.
See: https://github.com/chevrotain/chevrotain/issues/564#issuecomment-349062346 for details.`;return w(n,ts)&&(t.CATEGORIES=n[ts]),nn([t]),w(n,ns)&&(t.LABEL=n[ns]),w(n,rs)&&(t.GROUP=n[rs]),w(n,ss)&&(t.POP_MODE=n[ss]),w(n,is)&&(t.PUSH_MODE=n[is]),w(n,as)&&(t.LONGER_ALT=n[as]),w(n,os)&&(t.LINE_BREAKS=n[os]),w(n,ls)&&(t.START_CHARS_HINT=n[ls]),t}const et=ol({name:"EOF",pattern:de.NA});nn([et]);function yi(n,e,t,r,i,s,a,o){return{image:e,startOffset:t,endOffset:r,startLine:i,endLine:s,startColumn:a,endColumn:o,tokenTypeIdx:n.tokenTypeIdx,tokenType:n}}function ll(n,e){return tn(n,e)}const Rt={buildMismatchTokenMessage({expected:n,actual:e,previous:t,ruleName:r}){return`Expecting ${al(n)?`--> ${Et(n)} <--`:`token of type --> ${n.name} <--`} but found --> '${e.image}' <--`},buildNotAllInputParsedMessage({firstRedundant:n,ruleName:e}){return"Redundant input, expecting EOF but found: "+n.image},buildNoViableAltMessage({expectedPathsPerAlt:n,actual:e,previous:t,customUserDescription:r,ruleName:i}){const s="Expecting: ",o=`
but found: '`+be(e).image+"'";if(r)return s+r+o;{const l=oe(n,(f,h)=>f.concat(h),[]),c=k(l,f=>`[${k(f,h=>Et(h)).join(", ")}]`),d=`one of these possible Token sequences:
${k(c,(f,h)=>`  ${h+1}. ${f}`).join(`
`)}`;return s+d+o}},buildEarlyExitMessage({expectedIterationPaths:n,actual:e,customUserDescription:t,ruleName:r}){const i="Expecting: ",a=`
but found: '`+be(e).image+"'";if(t)return i+t+a;{const l=`expecting at least one iteration which starts with one of these possible Token sequences::
  <${k(n,c=>`[${k(c,u=>Et(u)).join(",")}]`).join(" ,")}>`;return i+l+a}}};Object.freeze(Rt);const af={buildRuleNotFoundError(n,e){return"Invalid grammar, reference to a rule which is not defined: ->"+e.nonTerminalName+`<-
inside top level rule: ->`+n.name+"<-"}},lt={buildDuplicateFoundError(n,e){function t(u){return u instanceof G?u.terminalType.name:u instanceof le?u.nonTerminalName:""}const r=n.name,i=be(e),s=i.idx,a=Fe(i),o=t(i),l=s>0;let c=`->${a}${l?s:""}<- ${o?`with argument: ->${o}<-`:""}
                  appears more than once (${e.length} times) in the top level rule: ->${r}<-.                  
                  For further details see: https://chevrotain.io/docs/FAQ.html#NUMERICAL_SUFFIXES 
                  `;return c=c.replace(/[ \t]+/g," "),c=c.replace(/\s\s+/g,`
`),c},buildNamespaceConflictError(n){return`Namespace conflict found in grammar.
The grammar has both a Terminal(Token) and a Non-Terminal(Rule) named: <${n.name}>.
To resolve this make sure each Terminal and Non-Terminal names are unique
This is easy to accomplish by using the convention that Terminal names start with an uppercase letter
and Non-Terminal names start with a lower case letter.`},buildAlternationPrefixAmbiguityError(n){const e=k(n.prefixPath,i=>Et(i)).join(", "),t=n.alternation.idx===0?"":n.alternation.idx;return`Ambiguous alternatives: <${n.ambiguityIndices.join(" ,")}> due to common lookahead prefix
in <OR${t}> inside <${n.topLevelRule.name}> Rule,
<${e}> may appears as a prefix path in all these alternatives.
See: https://chevrotain.io/docs/guide/resolving_grammar_errors.html#COMMON_PREFIX
For Further details.`},buildAlternationAmbiguityError(n){const e=k(n.prefixPath,i=>Et(i)).join(", "),t=n.alternation.idx===0?"":n.alternation.idx;let r=`Ambiguous Alternatives Detected: <${n.ambiguityIndices.join(" ,")}> in <OR${t}> inside <${n.topLevelRule.name}> Rule,
<${e}> may appears as a prefix path in all these alternatives.
`;return r=r+`See: https://chevrotain.io/docs/guide/resolving_grammar_errors.html#AMBIGUOUS_ALTERNATIVES
For Further details.`,r},buildEmptyRepetitionError(n){let e=Fe(n.repetition);return n.repetition.idx!==0&&(e+=n.repetition.idx),`The repetition <${e}> within Rule <${n.topLevelRule.name}> can never consume any tokens.
This could lead to an infinite loop.`},buildTokenNameError(n){return"deprecated"},buildEmptyAlternationError(n){return`Ambiguous empty alternative: <${n.emptyChoiceIdx+1}> in <OR${n.alternation.idx}> inside <${n.topLevelRule.name}> Rule.
Only the last alternative may be an empty alternative.`},buildTooManyAlternativesError(n){return`An Alternation cannot have more than 256 alternatives:
<OR${n.alternation.idx}> inside <${n.topLevelRule.name}> Rule.
 has ${n.alternation.definition.length+1} alternatives.`},buildLeftRecursionError(n){const e=n.topLevelRule.name,t=k(n.leftRecursionPath,s=>s.name),r=`${e} --> ${t.concat([e]).join(" --> ")}`;return`Left Recursion found in grammar.
rule: <${e}> can be invoked from itself (directly or indirectly)
without consuming any Tokens. The grammar path that causes this is: 
 ${r}
 To fix this refactor your grammar to remove the left recursion.
see: https://en.wikipedia.org/wiki/LL_parser#Left_factoring.`},buildInvalidRuleNameError(n){return"deprecated"},buildDuplicateRuleNameError(n){let e;return n.topLevelRule instanceof bt?e=n.topLevelRule.name:e=n.topLevelRule,`Duplicate definition, rule: ->${e}<- is already defined in the grammar: ->${n.grammarName}<-`}};function of(n,e){const t=new lf(n,e);return t.resolveRefs(),t.errors}class lf extends Pt{constructor(e,t){super(),this.nameToTopRule=e,this.errMsgProvider=t,this.errors=[]}resolveRefs(){C(K(this.nameToTopRule),e=>{this.currTopLevel=e,e.accept(this)})}visitNonTerminal(e){const t=this.nameToTopRule[e.nonTerminalName];if(t)e.referencedRule=t;else{const r=this.errMsgProvider.buildRuleNotFoundError(this.currTopLevel,e);this.errors.push({message:r,type:ce.UNRESOLVED_SUBRULE_REF,ruleName:this.currTopLevel.name,unresolvedRefName:e.nonTerminalName})}}}class cf extends Xn{constructor(e,t){super(),this.topProd=e,this.path=t,this.possibleTokTypes=[],this.nextProductionName="",this.nextProductionOccurrence=0,this.found=!1,this.isAtEndOfPath=!1}startWalking(){if(this.found=!1,this.path.ruleStack[0]!==this.topProd.name)throw Error("The path does not start with the walker's top Rule!");return this.ruleStack=te(this.path.ruleStack).reverse(),this.occurrenceStack=te(this.path.occurrenceStack).reverse(),this.ruleStack.pop(),this.occurrenceStack.pop(),this.updateExpectedNext(),this.walk(this.topProd),this.possibleTokTypes}walk(e,t=[]){this.found||super.walk(e,t)}walkProdRef(e,t,r){if(e.referencedRule.name===this.nextProductionName&&e.idx===this.nextProductionOccurrence){const i=t.concat(r);this.updateExpectedNext(),this.walk(e.referencedRule,i)}}updateExpectedNext(){D(this.ruleStack)?(this.nextProductionName="",this.nextProductionOccurrence=0,this.isAtEndOfPath=!0):(this.nextProductionName=this.ruleStack.pop(),this.nextProductionOccurrence=this.occurrenceStack.pop())}}class uf extends cf{constructor(e,t){super(e,t),this.path=t,this.nextTerminalName="",this.nextTerminalOccurrence=0,this.nextTerminalName=this.path.lastTok.name,this.nextTerminalOccurrence=this.path.lastTokOccurrence}walkTerminal(e,t,r){if(this.isAtEndOfPath&&e.terminalType.name===this.nextTerminalName&&e.idx===this.nextTerminalOccurrence&&!this.found){const i=t.concat(r),s=new he({definition:i});this.possibleTokTypes=en(s),this.found=!0}}}class Qn extends Xn{constructor(e,t){super(),this.topRule=e,this.occurrence=t,this.result={token:void 0,occurrence:void 0,isEndOfRule:void 0}}startWalking(){return this.walk(this.topRule),this.result}}class df extends Qn{walkMany(e,t,r){if(e.idx===this.occurrence){const i=be(t.concat(r));this.result.isEndOfRule=i===void 0,i instanceof G&&(this.result.token=i.terminalType,this.result.occurrence=i.idx)}else super.walkMany(e,t,r)}}class cs extends Qn{walkManySep(e,t,r){if(e.idx===this.occurrence){const i=be(t.concat(r));this.result.isEndOfRule=i===void 0,i instanceof G&&(this.result.token=i.terminalType,this.result.occurrence=i.idx)}else super.walkManySep(e,t,r)}}class ff extends Qn{walkAtLeastOne(e,t,r){if(e.idx===this.occurrence){const i=be(t.concat(r));this.result.isEndOfRule=i===void 0,i instanceof G&&(this.result.token=i.terminalType,this.result.occurrence=i.idx)}else super.walkAtLeastOne(e,t,r)}}class us extends Qn{walkAtLeastOneSep(e,t,r){if(e.idx===this.occurrence){const i=be(t.concat(r));this.result.isEndOfRule=i===void 0,i instanceof G&&(this.result.token=i.terminalType,this.result.occurrence=i.idx)}else super.walkAtLeastOneSep(e,t,r)}}function Nr(n,e,t=[]){t=te(t);let r=[],i=0;function s(o){return o.concat(J(n,i+1))}function a(o){const l=Nr(s(o),e,t);return r.concat(l)}for(;t.length<e&&i<n.length;){const o=n[i];if(o instanceof he)return a(o.definition);if(o instanceof le)return a(o.definition);if(o instanceof ee)r=a(o.definition);else if(o instanceof ve){const l=o.definition.concat([new W({definition:o.definition})]);return a(l)}else if(o instanceof ke){const l=[new he({definition:o.definition}),new W({definition:[new G({terminalType:o.separator})].concat(o.definition)})];return a(l)}else if(o instanceof pe){const l=o.definition.concat([new W({definition:[new G({terminalType:o.separator})].concat(o.definition)})]);r=a(l)}else if(o instanceof W){const l=o.definition.concat([new W({definition:o.definition})]);r=a(l)}else{if(o instanceof me)return C(o.definition,l=>{D(l.definition)===!1&&(r=a(l.definition))}),r;if(o instanceof G)t.push(o.terminalType);else throw Error("non exhaustive match")}i++}return r.push({partialPath:t,suffixDef:J(n,i)}),r}function cl(n,e,t,r){const i="EXIT_NONE_TERMINAL",s=[i],a="EXIT_ALTERNATIVE";let o=!1;const l=e.length,c=l-r-1,u=[],d=[];for(d.push({idx:-1,def:n,ruleStack:[],occurrenceStack:[]});!D(d);){const f=d.pop();if(f===a){o&&St(d).idx<=c&&d.pop();continue}const h=f.def,m=f.idx,g=f.ruleStack,A=f.occurrenceStack;if(D(h))continue;const T=h[0];if(T===i){const E={idx:m,def:J(h),ruleStack:qt(g),occurrenceStack:qt(A)};d.push(E)}else if(T instanceof G)if(m<l-1){const E=m+1,R=e[E];if(t(R,T.terminalType)){const N={idx:E,def:J(h),ruleStack:g,occurrenceStack:A};d.push(N)}}else if(m===l-1)u.push({nextTokenType:T.terminalType,nextTokenOccurrence:T.idx,ruleStack:g,occurrenceStack:A}),o=!0;else throw Error("non exhaustive match");else if(T instanceof le){const E=te(g);E.push(T.nonTerminalName);const R=te(A);R.push(T.idx);const N={idx:m,def:T.definition.concat(s,J(h)),ruleStack:E,occurrenceStack:R};d.push(N)}else if(T instanceof ee){const E={idx:m,def:J(h),ruleStack:g,occurrenceStack:A};d.push(E),d.push(a);const R={idx:m,def:T.definition.concat(J(h)),ruleStack:g,occurrenceStack:A};d.push(R)}else if(T instanceof ve){const E=new W({definition:T.definition,idx:T.idx}),R=T.definition.concat([E],J(h)),N={idx:m,def:R,ruleStack:g,occurrenceStack:A};d.push(N)}else if(T instanceof ke){const E=new G({terminalType:T.separator}),R=new W({definition:[E].concat(T.definition),idx:T.idx}),N=T.definition.concat([R],J(h)),F={idx:m,def:N,ruleStack:g,occurrenceStack:A};d.push(F)}else if(T instanceof pe){const E={idx:m,def:J(h),ruleStack:g,occurrenceStack:A};d.push(E),d.push(a);const R=new G({terminalType:T.separator}),N=new W({definition:[R].concat(T.definition),idx:T.idx}),F=T.definition.concat([N],J(h)),ne={idx:m,def:F,ruleStack:g,occurrenceStack:A};d.push(ne)}else if(T instanceof W){const E={idx:m,def:J(h),ruleStack:g,occurrenceStack:A};d.push(E),d.push(a);const R=new W({definition:T.definition,idx:T.idx}),N=T.definition.concat([R],J(h)),F={idx:m,def:N,ruleStack:g,occurrenceStack:A};d.push(F)}else if(T instanceof me)for(let E=T.definition.length-1;E>=0;E--){const R=T.definition[E],N={idx:m,def:R.definition.concat(J(h)),ruleStack:g,occurrenceStack:A};d.push(N),d.push(a)}else if(T instanceof he)d.push({idx:m,def:T.definition.concat(J(h)),ruleStack:g,occurrenceStack:A});else if(T instanceof bt)d.push(hf(T,m,g,A));else throw Error("non exhaustive match")}return u}function hf(n,e,t,r){const i=te(t);i.push(n.name);const s=te(r);return s.push(1),{idx:e,def:n.definition,ruleStack:i,occurrenceStack:s}}var B;(function(n){n[n.OPTION=0]="OPTION",n[n.REPETITION=1]="REPETITION",n[n.REPETITION_MANDATORY=2]="REPETITION_MANDATORY",n[n.REPETITION_MANDATORY_WITH_SEPARATOR=3]="REPETITION_MANDATORY_WITH_SEPARATOR",n[n.REPETITION_WITH_SEPARATOR=4]="REPETITION_WITH_SEPARATOR",n[n.ALTERNATION=5]="ALTERNATION"})(B||(B={}));function Ti(n){if(n instanceof ee||n==="Option")return B.OPTION;if(n instanceof W||n==="Repetition")return B.REPETITION;if(n instanceof ve||n==="RepetitionMandatory")return B.REPETITION_MANDATORY;if(n instanceof ke||n==="RepetitionMandatoryWithSeparator")return B.REPETITION_MANDATORY_WITH_SEPARATOR;if(n instanceof pe||n==="RepetitionWithSeparator")return B.REPETITION_WITH_SEPARATOR;if(n instanceof me||n==="Alternation")return B.ALTERNATION;throw Error("non exhaustive match")}function ds(n){const{occurrence:e,rule:t,prodType:r,maxLookahead:i}=n,s=Ti(r);return s===B.ALTERNATION?Zn(e,t,i):er(e,t,s,i)}function pf(n,e,t,r,i,s){const a=Zn(n,e,t),o=fl(a)?Sn:tn;return s(a,r,o,i)}function mf(n,e,t,r,i,s){const a=er(n,e,i,t),o=fl(a)?Sn:tn;return s(a[0],o,r)}function gf(n,e,t,r){const i=n.length,s=$e(n,a=>$e(a,o=>o.length===1));if(e)return function(a){const o=k(a,l=>l.GATE);for(let l=0;l<i;l++){const c=n[l],u=c.length,d=o[l];if(!(d!==void 0&&d.call(this)===!1))e:for(let f=0;f<u;f++){const h=c[f],m=h.length;for(let g=0;g<m;g++){const A=this.LA(g+1);if(t(A,h[g])===!1)continue e}return l}}};if(s&&!r){const a=k(n,l=>Ne(l)),o=oe(a,(l,c,u)=>(C(c,d=>{w(l,d.tokenTypeIdx)||(l[d.tokenTypeIdx]=u),C(d.categoryMatches,f=>{w(l,f)||(l[f]=u)})}),l),{});return function(){const l=this.LA(1);return o[l.tokenTypeIdx]}}else return function(){for(let a=0;a<i;a++){const o=n[a],l=o.length;e:for(let c=0;c<l;c++){const u=o[c],d=u.length;for(let f=0;f<d;f++){const h=this.LA(f+1);if(t(h,u[f])===!1)continue e}return a}}}}function yf(n,e,t){const r=$e(n,s=>s.length===1),i=n.length;if(r&&!t){const s=Ne(n);if(s.length===1&&D(s[0].categoryMatches)){const o=s[0].tokenTypeIdx;return function(){return this.LA(1).tokenTypeIdx===o}}else{const a=oe(s,(o,l,c)=>(o[l.tokenTypeIdx]=!0,C(l.categoryMatches,u=>{o[u]=!0}),o),[]);return function(){const o=this.LA(1);return a[o.tokenTypeIdx]===!0}}}else return function(){e:for(let s=0;s<i;s++){const a=n[s],o=a.length;for(let l=0;l<o;l++){const c=this.LA(l+1);if(e(c,a[l])===!1)continue e}return!0}return!1}}class Tf extends Xn{constructor(e,t,r){super(),this.topProd=e,this.targetOccurrence=t,this.targetProdType=r}startWalking(){return this.walk(this.topProd),this.restDef}checkIsTarget(e,t,r,i){return e.idx===this.targetOccurrence&&this.targetProdType===t?(this.restDef=r.concat(i),!0):!1}walkOption(e,t,r){this.checkIsTarget(e,B.OPTION,t,r)||super.walkOption(e,t,r)}walkAtLeastOne(e,t,r){this.checkIsTarget(e,B.REPETITION_MANDATORY,t,r)||super.walkOption(e,t,r)}walkAtLeastOneSep(e,t,r){this.checkIsTarget(e,B.REPETITION_MANDATORY_WITH_SEPARATOR,t,r)||super.walkOption(e,t,r)}walkMany(e,t,r){this.checkIsTarget(e,B.REPETITION,t,r)||super.walkOption(e,t,r)}walkManySep(e,t,r){this.checkIsTarget(e,B.REPETITION_WITH_SEPARATOR,t,r)||super.walkOption(e,t,r)}}class ul extends Pt{constructor(e,t,r){super(),this.targetOccurrence=e,this.targetProdType=t,this.targetRef=r,this.result=[]}checkIsTarget(e,t){e.idx===this.targetOccurrence&&this.targetProdType===t&&(this.targetRef===void 0||e===this.targetRef)&&(this.result=e.definition)}visitOption(e){this.checkIsTarget(e,B.OPTION)}visitRepetition(e){this.checkIsTarget(e,B.REPETITION)}visitRepetitionMandatory(e){this.checkIsTarget(e,B.REPETITION_MANDATORY)}visitRepetitionMandatoryWithSeparator(e){this.checkIsTarget(e,B.REPETITION_MANDATORY_WITH_SEPARATOR)}visitRepetitionWithSeparator(e){this.checkIsTarget(e,B.REPETITION_WITH_SEPARATOR)}visitAlternation(e){this.checkIsTarget(e,B.ALTERNATION)}}function fs(n){const e=new Array(n);for(let t=0;t<n;t++)e[t]=[];return e}function fr(n){let e=[""];for(let t=0;t<n.length;t++){const r=n[t],i=[];for(let s=0;s<e.length;s++){const a=e[s];i.push(a+"_"+r.tokenTypeIdx);for(let o=0;o<r.categoryMatches.length;o++){const l="_"+r.categoryMatches[o];i.push(a+l)}}e=i}return e}function Rf(n,e,t){for(let r=0;r<n.length;r++){if(r===t)continue;const i=n[r];for(let s=0;s<e.length;s++){const a=e[s];if(i[a]===!0)return!1}}return!0}function dl(n,e){const t=k(n,a=>Nr([a],1)),r=fs(t.length),i=k(t,a=>{const o={};return C(a,l=>{const c=fr(l.partialPath);C(c,u=>{o[u]=!0})}),o});let s=t;for(let a=1;a<=e;a++){const o=s;s=fs(o.length);for(let l=0;l<o.length;l++){const c=o[l];for(let u=0;u<c.length;u++){const d=c[u].partialPath,f=c[u].suffixDef,h=fr(d);if(Rf(i,h,l)||D(f)||d.length===e){const g=r[l];if(Cr(g,d)===!1){g.push(d);for(let A=0;A<h.length;A++){const T=h[A];i[l][T]=!0}}}else{const g=Nr(f,a+1,d);s[l]=s[l].concat(g),C(g,A=>{const T=fr(A.partialPath);C(T,E=>{i[l][E]=!0})})}}}}return r}function Zn(n,e,t,r){const i=new ul(n,B.ALTERNATION,r);return e.accept(i),dl(i.result,t)}function er(n,e,t,r){const i=new ul(n,t);e.accept(i);const s=i.result,o=new Tf(e,n,t).startWalking(),l=new he({definition:s}),c=new he({definition:o});return dl([l,c],r)}function Cr(n,e){e:for(let t=0;t<n.length;t++){const r=n[t];if(r.length===e.length){for(let i=0;i<r.length;i++){const s=e[i],a=r[i];if((s===a||a.categoryMatchesMap[s.tokenTypeIdx]!==void 0)===!1)continue e}return!0}}return!1}function Af(n,e){return n.length<e.length&&$e(n,(t,r)=>{const i=e[r];return t===i||i.categoryMatchesMap[t.tokenTypeIdx]})}function fl(n){return $e(n,e=>$e(e,t=>$e(t,r=>D(r.categoryMatches))))}function Ef(n){const e=n.lookaheadStrategy.validate({rules:n.rules,tokenTypes:n.tokenTypes,grammarName:n.grammarName});return k(e,t=>Object.assign({type:ce.CUSTOM_LOOKAHEAD_VALIDATION},t))}function vf(n,e,t,r){const i=Re(n,l=>kf(l,t)),s=Pf(n,e,t),a=Re(n,l=>Lf(l,t)),o=Re(n,l=>xf(l,n,r,t));return i.concat(s,a,o)}function kf(n,e){const t=new If;n.accept(t);const r=t.allProductions,i=Wc(r,Sf),s=Pe(i,o=>o.length>1);return k(K(s),o=>{const l=be(o),c=e.buildDuplicateFoundError(n,o),u=Fe(l),d={message:c,type:ce.DUPLICATE_PRODUCTIONS,ruleName:n.name,dslName:u,occurrence:l.idx},f=hl(l);return f&&(d.parameter=f),d})}function Sf(n){return`${Fe(n)}_#_${n.idx}_#_${hl(n)}`}function hl(n){return n instanceof G?n.terminalType.name:n instanceof le?n.nonTerminalName:""}class If extends Pt{constructor(){super(...arguments),this.allProductions=[]}visitNonTerminal(e){this.allProductions.push(e)}visitOption(e){this.allProductions.push(e)}visitRepetitionWithSeparator(e){this.allProductions.push(e)}visitRepetitionMandatory(e){this.allProductions.push(e)}visitRepetitionMandatoryWithSeparator(e){this.allProductions.push(e)}visitRepetition(e){this.allProductions.push(e)}visitAlternation(e){this.allProductions.push(e)}visitTerminal(e){this.allProductions.push(e)}}function xf(n,e,t,r){const i=[];if(oe(e,(a,o)=>o.name===n.name?a+1:a,0)>1){const a=r.buildDuplicateRuleNameError({topLevelRule:n,grammarName:t});i.push({message:a,type:ce.DUPLICATE_RULE_NAME,ruleName:n.name})}return i}function Nf(n,e,t){const r=[];let i;return ue(e,n)||(i=`Invalid rule override, rule: ->${n}<- cannot be overridden in the grammar: ->${t}<-as it is not defined in any of the super grammars `,r.push({message:i,type:ce.INVALID_RULE_OVERRIDE,ruleName:n})),r}function pl(n,e,t,r=[]){const i=[],s=gn(e.definition);if(D(s))return[];{const a=n.name;ue(s,n)&&i.push({message:t.buildLeftRecursionError({topLevelRule:n,leftRecursionPath:r}),type:ce.LEFT_RECURSION,ruleName:a});const l=Vn(s,r.concat([n])),c=Re(l,u=>{const d=te(r);return d.push(u),pl(n,u,t,d)});return i.concat(c)}}function gn(n){let e=[];if(D(n))return e;const t=be(n);if(t instanceof le)e.push(t.referencedRule);else if(t instanceof he||t instanceof ee||t instanceof ve||t instanceof ke||t instanceof pe||t instanceof W)e=e.concat(gn(t.definition));else if(t instanceof me)e=Ne(k(t.definition,s=>gn(s.definition)));else if(!(t instanceof G))throw Error("non exhaustive match");const r=vn(t),i=n.length>1;if(r&&i){const s=J(n);return e.concat(gn(s))}else return e}class Ri extends Pt{constructor(){super(...arguments),this.alternations=[]}visitAlternation(e){this.alternations.push(e)}}function Cf(n,e){const t=new Ri;n.accept(t);const r=t.alternations;return Re(r,s=>{const a=qt(s.definition);return Re(a,(o,l)=>{const c=cl([o],[],tn,1);return D(c)?[{message:e.buildEmptyAlternationError({topLevelRule:n,alternation:s,emptyChoiceIdx:l}),type:ce.NONE_LAST_EMPTY_ALT,ruleName:n.name,occurrence:s.idx,alternative:l+1}]:[]})})}function wf(n,e,t){const r=new Ri;n.accept(r);let i=r.alternations;return i=Wn(i,a=>a.ignoreAmbiguities===!0),Re(i,a=>{const o=a.idx,l=a.maxLookahead||e,c=Zn(o,n,l,a),u=Of(c,a,n,t),d=bf(c,a,n,t);return u.concat(d)})}class _f extends Pt{constructor(){super(...arguments),this.allProductions=[]}visitRepetitionWithSeparator(e){this.allProductions.push(e)}visitRepetitionMandatory(e){this.allProductions.push(e)}visitRepetitionMandatoryWithSeparator(e){this.allProductions.push(e)}visitRepetition(e){this.allProductions.push(e)}}function Lf(n,e){const t=new Ri;n.accept(t);const r=t.alternations;return Re(r,s=>s.definition.length>255?[{message:e.buildTooManyAlternativesError({topLevelRule:n,alternation:s}),type:ce.TOO_MANY_ALTS,ruleName:n.name,occurrence:s.idx}]:[])}function $f(n,e,t){const r=[];return C(n,i=>{const s=new _f;i.accept(s);const a=s.allProductions;C(a,o=>{const l=Ti(o),c=o.maxLookahead||e,u=o.idx,f=er(u,i,l,c)[0];if(D(Ne(f))){const h=t.buildEmptyRepetitionError({topLevelRule:i,repetition:o});r.push({message:h,type:ce.NO_NON_EMPTY_LOOKAHEAD,ruleName:i.name})}})}),r}function Of(n,e,t,r){const i=[],s=oe(n,(o,l,c)=>(e.definition[c].ignoreAmbiguities===!0||C(l,u=>{const d=[c];C(n,(f,h)=>{c!==h&&Cr(f,u)&&e.definition[h].ignoreAmbiguities!==!0&&d.push(h)}),d.length>1&&!Cr(i,u)&&(i.push(u),o.push({alts:d,path:u}))}),o),[]);return k(s,o=>{const l=k(o.alts,u=>u+1);return{message:r.buildAlternationAmbiguityError({topLevelRule:t,alternation:e,ambiguityIndices:l,prefixPath:o.path}),type:ce.AMBIGUOUS_ALTS,ruleName:t.name,occurrence:e.idx,alternatives:o.alts}})}function bf(n,e,t,r){const i=oe(n,(a,o,l)=>{const c=k(o,u=>({idx:l,path:u}));return a.concat(c)},[]);return Qt(Re(i,a=>{if(e.definition[a.idx].ignoreAmbiguities===!0)return[];const l=a.idx,c=a.path,u=Ee(i,f=>e.definition[f.idx].ignoreAmbiguities!==!0&&f.idx<l&&Af(f.path,c));return k(u,f=>{const h=[f.idx+1,l+1],m=e.idx===0?"":e.idx;return{message:r.buildAlternationPrefixAmbiguityError({topLevelRule:t,alternation:e,ambiguityIndices:h,prefixPath:f.path}),type:ce.AMBIGUOUS_PREFIX_ALTS,ruleName:t.name,occurrence:m,alternatives:h}})}))}function Pf(n,e,t){const r=[],i=k(e,s=>s.name);return C(n,s=>{const a=s.name;if(ue(i,a)){const o=t.buildNamespaceConflictError(s);r.push({message:o,type:ce.CONFLICT_TOKENS_RULES_NAMESPACE,ruleName:a})}}),r}function Mf(n){const e=ai(n,{errMsgProvider:af}),t={};return C(n.rules,r=>{t[r.name]=r}),of(t,e.errMsgProvider)}function Df(n){return n=ai(n,{errMsgProvider:lt}),vf(n.rules,n.tokenTypes,n.errMsgProvider,n.grammarName)}const ml="MismatchedTokenException",gl="NoViableAltException",yl="EarlyExitException",Tl="NotAllInputParsedException",Rl=[ml,gl,yl,Tl];Object.freeze(Rl);function In(n){return ue(Rl,n.name)}class tr extends Error{constructor(e,t){super(e),this.token=t,this.resyncedTokens=[],Object.setPrototypeOf(this,new.target.prototype),Error.captureStackTrace&&Error.captureStackTrace(this,this.constructor)}}class Al extends tr{constructor(e,t,r){super(e,t),this.previousToken=r,this.name=ml}}class Ff extends tr{constructor(e,t,r){super(e,t),this.previousToken=r,this.name=gl}}class Gf extends tr{constructor(e,t){super(e,t),this.name=Tl}}class Uf extends tr{constructor(e,t,r){super(e,t),this.previousToken=r,this.name=yl}}const hr={},El="InRuleRecoveryException";class Bf extends Error{constructor(e){super(e),this.name=El}}class Vf{initRecoverable(e){this.firstAfterRepMap={},this.resyncFollows={},this.recoveryEnabled=w(e,"recoveryEnabled")?e.recoveryEnabled:Ye.recoveryEnabled,this.recoveryEnabled&&(this.attemptInRepetitionRecovery=Wf)}getTokenToInsert(e){const t=yi(e,"",NaN,NaN,NaN,NaN,NaN,NaN);return t.isInsertedInRecovery=!0,t}canTokenTypeBeInsertedInRecovery(e){return!0}canTokenTypeBeDeletedInRecovery(e){return!0}tryInRepetitionRecovery(e,t,r,i){const s=this.findReSyncTokenType(),a=this.exportLexerState(),o=[];let l=!1;const c=this.LA(1);let u=this.LA(1);const d=()=>{const f=this.LA(0),h=this.errorMessageProvider.buildMismatchTokenMessage({expected:i,actual:c,previous:f,ruleName:this.getCurrRuleFullName()}),m=new Al(h,c,this.LA(0));m.resyncedTokens=qt(o),this.SAVE_ERROR(m)};for(;!l;)if(this.tokenMatcher(u,i)){d();return}else if(r.call(this)){d(),e.apply(this,t);return}else this.tokenMatcher(u,s)?l=!0:(u=this.SKIP_TOKEN(),this.addToResyncTokens(u,o));this.importLexerState(a)}shouldInRepetitionRecoveryBeTried(e,t,r){return!(r===!1||this.tokenMatcher(this.LA(1),e)||this.isBackTracking()||this.canPerformInRuleRecovery(e,this.getFollowsForInRuleRecovery(e,t)))}getFollowsForInRuleRecovery(e,t){const r=this.getCurrentGrammarPath(e,t);return this.getNextPossibleTokenTypes(r)}tryInRuleRecovery(e,t){if(this.canRecoverWithSingleTokenInsertion(e,t))return this.getTokenToInsert(e);if(this.canRecoverWithSingleTokenDeletion(e)){const r=this.SKIP_TOKEN();return this.consumeToken(),r}throw new Bf("sad sad panda")}canPerformInRuleRecovery(e,t){return this.canRecoverWithSingleTokenInsertion(e,t)||this.canRecoverWithSingleTokenDeletion(e)}canRecoverWithSingleTokenInsertion(e,t){if(!this.canTokenTypeBeInsertedInRecovery(e)||D(t))return!1;const r=this.LA(1);return kt(t,s=>this.tokenMatcher(r,s))!==void 0}canRecoverWithSingleTokenDeletion(e){return this.canTokenTypeBeDeletedInRecovery(e)?this.tokenMatcher(this.LA(2),e):!1}isInCurrentRuleReSyncSet(e){const t=this.getCurrFollowKey(),r=this.getFollowSetFromFollowKey(t);return ue(r,e)}findReSyncTokenType(){const e=this.flattenFollowSet();let t=this.LA(1),r=2;for(;;){const i=kt(e,s=>ll(t,s));if(i!==void 0)return i;t=this.LA(r),r++}}getCurrFollowKey(){if(this.RULE_STACK.length===1)return hr;const e=this.getLastExplicitRuleShortName(),t=this.getLastExplicitRuleOccurrenceIndex(),r=this.getPreviousExplicitRuleShortName();return{ruleName:this.shortRuleNameToFullName(e),idxInCallingRule:t,inRule:this.shortRuleNameToFullName(r)}}buildFullFollowKeyStack(){const e=this.RULE_STACK,t=this.RULE_OCCURRENCE_STACK;return k(e,(r,i)=>i===0?hr:{ruleName:this.shortRuleNameToFullName(r),idxInCallingRule:t[i],inRule:this.shortRuleNameToFullName(e[i-1])})}flattenFollowSet(){const e=k(this.buildFullFollowKeyStack(),t=>this.getFollowSetFromFollowKey(t));return Ne(e)}getFollowSetFromFollowKey(e){if(e===hr)return[et];const t=e.ruleName+e.idxInCallingRule+Jo+e.inRule;return this.resyncFollows[t]}addToResyncTokens(e,t){return this.tokenMatcher(e,et)||t.push(e),t}reSyncTo(e){const t=[];let r=this.LA(1);for(;this.tokenMatcher(r,e)===!1;)r=this.SKIP_TOKEN(),this.addToResyncTokens(r,t);return qt(t)}attemptInRepetitionRecovery(e,t,r,i,s,a,o){}getCurrentGrammarPath(e,t){const r=this.getHumanReadableRuleStack(),i=te(this.RULE_OCCURRENCE_STACK);return{ruleStack:r,occurrenceStack:i,lastTok:e,lastTokOccurrence:t}}getHumanReadableRuleStack(){return k(this.RULE_STACK,e=>this.shortRuleNameToFullName(e))}}function Wf(n,e,t,r,i,s,a){const o=this.getKeyForAutomaticLookahead(r,i);let l=this.firstAfterRepMap[o];if(l===void 0){const f=this.getCurrRuleFullName(),h=this.getGAstProductions()[f];l=new s(h,i).startWalking(),this.firstAfterRepMap[o]=l}let c=l.token,u=l.occurrence;const d=l.isEndOfRule;this.RULE_STACK.length===1&&d&&c===void 0&&(c=et,u=1),!(c===void 0||u===void 0)&&this.shouldInRepetitionRecoveryBeTried(c,u,a)&&this.tryInRepetitionRecovery(n,e,t,c)}const jf=4,rt=8,vl=1<<rt,kl=2<<rt,wr=3<<rt,_r=4<<rt,Lr=5<<rt,yn=6<<rt;function pr(n,e,t){return t|e|n}class Ai{constructor(e){var t;this.maxLookahead=(t=e?.maxLookahead)!==null&&t!==void 0?t:Ye.maxLookahead}validate(e){const t=this.validateNoLeftRecursion(e.rules);if(D(t)){const r=this.validateEmptyOrAlternatives(e.rules),i=this.validateAmbiguousAlternationAlternatives(e.rules,this.maxLookahead),s=this.validateSomeNonEmptyLookaheadPath(e.rules,this.maxLookahead);return[...t,...r,...i,...s]}return t}validateNoLeftRecursion(e){return Re(e,t=>pl(t,t,lt))}validateEmptyOrAlternatives(e){return Re(e,t=>Cf(t,lt))}validateAmbiguousAlternationAlternatives(e,t){return Re(e,r=>wf(r,t,lt))}validateSomeNonEmptyLookaheadPath(e,t){return $f(e,t,lt)}buildLookaheadForAlternation(e){return pf(e.prodOccurrence,e.rule,e.maxLookahead,e.hasPredicates,e.dynamicTokensEnabled,gf)}buildLookaheadForOptional(e){return mf(e.prodOccurrence,e.rule,e.maxLookahead,e.dynamicTokensEnabled,Ti(e.prodType),yf)}}class Hf{initLooksAhead(e){this.dynamicTokensEnabled=w(e,"dynamicTokensEnabled")?e.dynamicTokensEnabled:Ye.dynamicTokensEnabled,this.maxLookahead=w(e,"maxLookahead")?e.maxLookahead:Ye.maxLookahead,this.lookaheadStrategy=w(e,"lookaheadStrategy")?e.lookaheadStrategy:new Ai({maxLookahead:this.maxLookahead}),this.lookAheadFuncsCache=new Map}preComputeLookaheadFunctions(e){C(e,t=>{this.TRACE_INIT(`${t.name} Rule Lookahead`,()=>{const{alternation:r,repetition:i,option:s,repetitionMandatory:a,repetitionMandatoryWithSeparator:o,repetitionWithSeparator:l}=zf(t);C(r,c=>{const u=c.idx===0?"":c.idx;this.TRACE_INIT(`${Fe(c)}${u}`,()=>{const d=this.lookaheadStrategy.buildLookaheadForAlternation({prodOccurrence:c.idx,rule:t,maxLookahead:c.maxLookahead||this.maxLookahead,hasPredicates:c.hasPredicates,dynamicTokensEnabled:this.dynamicTokensEnabled}),f=pr(this.fullRuleNameToShort[t.name],vl,c.idx);this.setLaFuncCache(f,d)})}),C(i,c=>{this.computeLookaheadFunc(t,c.idx,wr,"Repetition",c.maxLookahead,Fe(c))}),C(s,c=>{this.computeLookaheadFunc(t,c.idx,kl,"Option",c.maxLookahead,Fe(c))}),C(a,c=>{this.computeLookaheadFunc(t,c.idx,_r,"RepetitionMandatory",c.maxLookahead,Fe(c))}),C(o,c=>{this.computeLookaheadFunc(t,c.idx,yn,"RepetitionMandatoryWithSeparator",c.maxLookahead,Fe(c))}),C(l,c=>{this.computeLookaheadFunc(t,c.idx,Lr,"RepetitionWithSeparator",c.maxLookahead,Fe(c))})})})}computeLookaheadFunc(e,t,r,i,s,a){this.TRACE_INIT(`${a}${t===0?"":t}`,()=>{const o=this.lookaheadStrategy.buildLookaheadForOptional({prodOccurrence:t,rule:e,maxLookahead:s||this.maxLookahead,dynamicTokensEnabled:this.dynamicTokensEnabled,prodType:i}),l=pr(this.fullRuleNameToShort[e.name],r,t);this.setLaFuncCache(l,o)})}getKeyForAutomaticLookahead(e,t){const r=this.getLastExplicitRuleShortName();return pr(r,e,t)}getLaFuncFromCache(e){return this.lookAheadFuncsCache.get(e)}setLaFuncCache(e,t){this.lookAheadFuncsCache.set(e,t)}}class Kf extends Pt{constructor(){super(...arguments),this.dslMethods={option:[],alternation:[],repetition:[],repetitionWithSeparator:[],repetitionMandatory:[],repetitionMandatoryWithSeparator:[]}}reset(){this.dslMethods={option:[],alternation:[],repetition:[],repetitionWithSeparator:[],repetitionMandatory:[],repetitionMandatoryWithSeparator:[]}}visitOption(e){this.dslMethods.option.push(e)}visitRepetitionWithSeparator(e){this.dslMethods.repetitionWithSeparator.push(e)}visitRepetitionMandatory(e){this.dslMethods.repetitionMandatory.push(e)}visitRepetitionMandatoryWithSeparator(e){this.dslMethods.repetitionMandatoryWithSeparator.push(e)}visitRepetition(e){this.dslMethods.repetition.push(e)}visitAlternation(e){this.dslMethods.alternation.push(e)}}const dn=new Kf;function zf(n){dn.reset(),n.accept(dn);const e=dn.dslMethods;return dn.reset(),e}function hs(n,e){isNaN(n.startOffset)===!0?(n.startOffset=e.startOffset,n.endOffset=e.endOffset):n.endOffset<e.endOffset&&(n.endOffset=e.endOffset)}function ps(n,e){isNaN(n.startOffset)===!0?(n.startOffset=e.startOffset,n.startColumn=e.startColumn,n.startLine=e.startLine,n.endOffset=e.endOffset,n.endColumn=e.endColumn,n.endLine=e.endLine):n.endOffset<e.endOffset&&(n.endOffset=e.endOffset,n.endColumn=e.endColumn,n.endLine=e.endLine)}function qf(n,e,t){n.children[t]===void 0?n.children[t]=[e]:n.children[t].push(e)}function Yf(n,e,t){n.children[e]===void 0?n.children[e]=[t]:n.children[e].push(t)}const Xf="name";function Sl(n,e){Object.defineProperty(n,Xf,{enumerable:!1,configurable:!0,writable:!1,value:e})}function Jf(n,e){const t=vt(n),r=t.length;for(let i=0;i<r;i++){const s=t[i],a=n[s],o=a.length;for(let l=0;l<o;l++){const c=a[l];c.tokenTypeIdx===void 0&&this[c.name](c.children,e)}}}function Qf(n,e){const t=function(){};Sl(t,n+"BaseSemantics");const r={visit:function(i,s){if(Z(i)&&(i=i[0]),!ze(i))return this[i.name](i.children,s)},validateVisitor:function(){const i=eh(this,e);if(!D(i)){const s=k(i,a=>a.msg);throw Error(`Errors Detected in CST Visitor <${this.constructor.name}>:
	${s.join(`

`).replace(/\n/g,`
	`)}`)}}};return t.prototype=r,t.prototype.constructor=t,t._RULE_NAMES=e,t}function Zf(n,e,t){const r=function(){};Sl(r,n+"BaseSemanticsWithDefaults");const i=Object.create(t.prototype);return C(e,s=>{i[s]=Jf}),r.prototype=i,r.prototype.constructor=r,r}var $r;(function(n){n[n.REDUNDANT_METHOD=0]="REDUNDANT_METHOD",n[n.MISSING_METHOD=1]="MISSING_METHOD"})($r||($r={}));function eh(n,e){return th(n,e)}function th(n,e){const t=Ee(e,i=>pt(n[i])===!1),r=k(t,i=>({msg:`Missing visitor method: <${i}> on ${n.constructor.name} CST Visitor.`,type:$r.MISSING_METHOD,methodName:i}));return Qt(r)}class nh{initTreeBuilder(e){if(this.CST_STACK=[],this.outputCst=e.outputCst,this.nodeLocationTracking=w(e,"nodeLocationTracking")?e.nodeLocationTracking:Ye.nodeLocationTracking,!this.outputCst)this.cstInvocationStateUpdate=q,this.cstFinallyStateUpdate=q,this.cstPostTerminal=q,this.cstPostNonTerminal=q,this.cstPostRule=q;else if(/full/i.test(this.nodeLocationTracking))this.recoveryEnabled?(this.setNodeLocationFromToken=ps,this.setNodeLocationFromNode=ps,this.cstPostRule=q,this.setInitialNodeLocation=this.setInitialNodeLocationFullRecovery):(this.setNodeLocationFromToken=q,this.setNodeLocationFromNode=q,this.cstPostRule=this.cstPostRuleFull,this.setInitialNodeLocation=this.setInitialNodeLocationFullRegular);else if(/onlyOffset/i.test(this.nodeLocationTracking))this.recoveryEnabled?(this.setNodeLocationFromToken=hs,this.setNodeLocationFromNode=hs,this.cstPostRule=q,this.setInitialNodeLocation=this.setInitialNodeLocationOnlyOffsetRecovery):(this.setNodeLocationFromToken=q,this.setNodeLocationFromNode=q,this.cstPostRule=this.cstPostRuleOnlyOffset,this.setInitialNodeLocation=this.setInitialNodeLocationOnlyOffsetRegular);else if(/none/i.test(this.nodeLocationTracking))this.setNodeLocationFromToken=q,this.setNodeLocationFromNode=q,this.cstPostRule=q,this.setInitialNodeLocation=q;else throw Error(`Invalid <nodeLocationTracking> config option: "${e.nodeLocationTracking}"`)}setInitialNodeLocationOnlyOffsetRecovery(e){e.location={startOffset:NaN,endOffset:NaN}}setInitialNodeLocationOnlyOffsetRegular(e){e.location={startOffset:this.LA(1).startOffset,endOffset:NaN}}setInitialNodeLocationFullRecovery(e){e.location={startOffset:NaN,startLine:NaN,startColumn:NaN,endOffset:NaN,endLine:NaN,endColumn:NaN}}setInitialNodeLocationFullRegular(e){const t=this.LA(1);e.location={startOffset:t.startOffset,startLine:t.startLine,startColumn:t.startColumn,endOffset:NaN,endLine:NaN,endColumn:NaN}}cstInvocationStateUpdate(e){const t={name:e,children:Object.create(null)};this.setInitialNodeLocation(t),this.CST_STACK.push(t)}cstFinallyStateUpdate(){this.CST_STACK.pop()}cstPostRuleFull(e){const t=this.LA(0),r=e.location;r.startOffset<=t.startOffset?(r.endOffset=t.endOffset,r.endLine=t.endLine,r.endColumn=t.endColumn):(r.startOffset=NaN,r.startLine=NaN,r.startColumn=NaN)}cstPostRuleOnlyOffset(e){const t=this.LA(0),r=e.location;r.startOffset<=t.startOffset?r.endOffset=t.endOffset:r.startOffset=NaN}cstPostTerminal(e,t){const r=this.CST_STACK[this.CST_STACK.length-1];qf(r,t,e),this.setNodeLocationFromToken(r.location,t)}cstPostNonTerminal(e,t){const r=this.CST_STACK[this.CST_STACK.length-1];Yf(r,t,e),this.setNodeLocationFromNode(r.location,e.location)}getBaseCstVisitorConstructor(){if(ze(this.baseCstVisitorConstructor)){const e=Qf(this.className,vt(this.gastProductionsCache));return this.baseCstVisitorConstructor=e,e}return this.baseCstVisitorConstructor}getBaseCstVisitorConstructorWithDefaults(){if(ze(this.baseCstVisitorWithDefaultsConstructor)){const e=Zf(this.className,vt(this.gastProductionsCache),this.getBaseCstVisitorConstructor());return this.baseCstVisitorWithDefaultsConstructor=e,e}return this.baseCstVisitorWithDefaultsConstructor}getLastExplicitRuleShortName(){const e=this.RULE_STACK;return e[e.length-1]}getPreviousExplicitRuleShortName(){const e=this.RULE_STACK;return e[e.length-2]}getLastExplicitRuleOccurrenceIndex(){const e=this.RULE_OCCURRENCE_STACK;return e[e.length-1]}}class rh{initLexerAdapter(){this.tokVector=[],this.tokVectorLength=0,this.currIdx=-1}set input(e){if(this.selfAnalysisDone!==!0)throw Error("Missing <performSelfAnalysis> invocation at the end of the Parser's constructor.");this.reset(),this.tokVector=e,this.tokVectorLength=e.length}get input(){return this.tokVector}SKIP_TOKEN(){return this.currIdx<=this.tokVector.length-2?(this.consumeToken(),this.LA(1)):Nn}LA(e){const t=this.currIdx+e;return t<0||this.tokVectorLength<=t?Nn:this.tokVector[t]}consumeToken(){this.currIdx++}exportLexerState(){return this.currIdx}importLexerState(e){this.currIdx=e}resetLexerState(){this.currIdx=-1}moveToTerminatedState(){this.currIdx=this.tokVector.length-1}getLexerPosition(){return this.exportLexerState()}}class ih{ACTION(e){return e.call(this)}consume(e,t,r){return this.consumeInternal(t,e,r)}subrule(e,t,r){return this.subruleInternal(t,e,r)}option(e,t){return this.optionInternal(t,e)}or(e,t){return this.orInternal(t,e)}many(e,t){return this.manyInternal(e,t)}atLeastOne(e,t){return this.atLeastOneInternal(e,t)}CONSUME(e,t){return this.consumeInternal(e,0,t)}CONSUME1(e,t){return this.consumeInternal(e,1,t)}CONSUME2(e,t){return this.consumeInternal(e,2,t)}CONSUME3(e,t){return this.consumeInternal(e,3,t)}CONSUME4(e,t){return this.consumeInternal(e,4,t)}CONSUME5(e,t){return this.consumeInternal(e,5,t)}CONSUME6(e,t){return this.consumeInternal(e,6,t)}CONSUME7(e,t){return this.consumeInternal(e,7,t)}CONSUME8(e,t){return this.consumeInternal(e,8,t)}CONSUME9(e,t){return this.consumeInternal(e,9,t)}SUBRULE(e,t){return this.subruleInternal(e,0,t)}SUBRULE1(e,t){return this.subruleInternal(e,1,t)}SUBRULE2(e,t){return this.subruleInternal(e,2,t)}SUBRULE3(e,t){return this.subruleInternal(e,3,t)}SUBRULE4(e,t){return this.subruleInternal(e,4,t)}SUBRULE5(e,t){return this.subruleInternal(e,5,t)}SUBRULE6(e,t){return this.subruleInternal(e,6,t)}SUBRULE7(e,t){return this.subruleInternal(e,7,t)}SUBRULE8(e,t){return this.subruleInternal(e,8,t)}SUBRULE9(e,t){return this.subruleInternal(e,9,t)}OPTION(e){return this.optionInternal(e,0)}OPTION1(e){return this.optionInternal(e,1)}OPTION2(e){return this.optionInternal(e,2)}OPTION3(e){return this.optionInternal(e,3)}OPTION4(e){return this.optionInternal(e,4)}OPTION5(e){return this.optionInternal(e,5)}OPTION6(e){return this.optionInternal(e,6)}OPTION7(e){return this.optionInternal(e,7)}OPTION8(e){return this.optionInternal(e,8)}OPTION9(e){return this.optionInternal(e,9)}OR(e){return this.orInternal(e,0)}OR1(e){return this.orInternal(e,1)}OR2(e){return this.orInternal(e,2)}OR3(e){return this.orInternal(e,3)}OR4(e){return this.orInternal(e,4)}OR5(e){return this.orInternal(e,5)}OR6(e){return this.orInternal(e,6)}OR7(e){return this.orInternal(e,7)}OR8(e){return this.orInternal(e,8)}OR9(e){return this.orInternal(e,9)}MANY(e){this.manyInternal(0,e)}MANY1(e){this.manyInternal(1,e)}MANY2(e){this.manyInternal(2,e)}MANY3(e){this.manyInternal(3,e)}MANY4(e){this.manyInternal(4,e)}MANY5(e){this.manyInternal(5,e)}MANY6(e){this.manyInternal(6,e)}MANY7(e){this.manyInternal(7,e)}MANY8(e){this.manyInternal(8,e)}MANY9(e){this.manyInternal(9,e)}MANY_SEP(e){this.manySepFirstInternal(0,e)}MANY_SEP1(e){this.manySepFirstInternal(1,e)}MANY_SEP2(e){this.manySepFirstInternal(2,e)}MANY_SEP3(e){this.manySepFirstInternal(3,e)}MANY_SEP4(e){this.manySepFirstInternal(4,e)}MANY_SEP5(e){this.manySepFirstInternal(5,e)}MANY_SEP6(e){this.manySepFirstInternal(6,e)}MANY_SEP7(e){this.manySepFirstInternal(7,e)}MANY_SEP8(e){this.manySepFirstInternal(8,e)}MANY_SEP9(e){this.manySepFirstInternal(9,e)}AT_LEAST_ONE(e){this.atLeastOneInternal(0,e)}AT_LEAST_ONE1(e){return this.atLeastOneInternal(1,e)}AT_LEAST_ONE2(e){this.atLeastOneInternal(2,e)}AT_LEAST_ONE3(e){this.atLeastOneInternal(3,e)}AT_LEAST_ONE4(e){this.atLeastOneInternal(4,e)}AT_LEAST_ONE5(e){this.atLeastOneInternal(5,e)}AT_LEAST_ONE6(e){this.atLeastOneInternal(6,e)}AT_LEAST_ONE7(e){this.atLeastOneInternal(7,e)}AT_LEAST_ONE8(e){this.atLeastOneInternal(8,e)}AT_LEAST_ONE9(e){this.atLeastOneInternal(9,e)}AT_LEAST_ONE_SEP(e){this.atLeastOneSepFirstInternal(0,e)}AT_LEAST_ONE_SEP1(e){this.atLeastOneSepFirstInternal(1,e)}AT_LEAST_ONE_SEP2(e){this.atLeastOneSepFirstInternal(2,e)}AT_LEAST_ONE_SEP3(e){this.atLeastOneSepFirstInternal(3,e)}AT_LEAST_ONE_SEP4(e){this.atLeastOneSepFirstInternal(4,e)}AT_LEAST_ONE_SEP5(e){this.atLeastOneSepFirstInternal(5,e)}AT_LEAST_ONE_SEP6(e){this.atLeastOneSepFirstInternal(6,e)}AT_LEAST_ONE_SEP7(e){this.atLeastOneSepFirstInternal(7,e)}AT_LEAST_ONE_SEP8(e){this.atLeastOneSepFirstInternal(8,e)}AT_LEAST_ONE_SEP9(e){this.atLeastOneSepFirstInternal(9,e)}RULE(e,t,r=Cn){if(ue(this.definedRulesNames,e)){const a={message:lt.buildDuplicateRuleNameError({topLevelRule:e,grammarName:this.className}),type:ce.DUPLICATE_RULE_NAME,ruleName:e};this.definitionErrors.push(a)}this.definedRulesNames.push(e);const i=this.defineRule(e,t,r);return this[e]=i,i}OVERRIDE_RULE(e,t,r=Cn){const i=Nf(e,this.definedRulesNames,this.className);this.definitionErrors=this.definitionErrors.concat(i);const s=this.defineRule(e,t,r);return this[e]=s,s}BACKTRACK(e,t){return function(){this.isBackTrackingStack.push(1);const r=this.saveRecogState();try{return e.apply(this,t),!0}catch(i){if(In(i))return!1;throw i}finally{this.reloadRecogState(r),this.isBackTrackingStack.pop()}}}getGAstProductions(){return this.gastProductionsCache}getSerializedGastProductions(){return dd(K(this.gastProductionsCache))}}class sh{initRecognizerEngine(e,t){if(this.className=this.constructor.name,this.shortRuleNameToFull={},this.fullRuleNameToShort={},this.ruleShortNameIdx=256,this.tokenMatcher=Sn,this.subruleIdx=0,this.definedRulesNames=[],this.tokensMap={},this.isBackTrackingStack=[],this.RULE_STACK=[],this.RULE_OCCURRENCE_STACK=[],this.gastProductionsCache={},w(t,"serializedGrammar"))throw Error(`The Parser's configuration can no longer contain a <serializedGrammar> property.
	See: https://chevrotain.io/docs/changes/BREAKING_CHANGES.html#_6-0-0
	For Further details.`);if(Z(e)){if(D(e))throw Error(`A Token Vocabulary cannot be empty.
	Note that the first argument for the parser constructor
	is no longer a Token vector (since v4.0).`);if(typeof e[0].startOffset=="number")throw Error(`The Parser constructor no longer accepts a token vector as the first argument.
	See: https://chevrotain.io/docs/changes/BREAKING_CHANGES.html#_4-0-0
	For Further details.`)}if(Z(e))this.tokensMap=oe(e,(s,a)=>(s[a.name]=a,s),{});else if(w(e,"modes")&&$e(Ne(K(e.modes)),tf)){const s=Ne(K(e.modes)),a=oi(s);this.tokensMap=oe(a,(o,l)=>(o[l.name]=l,o),{})}else if(Ec(e))this.tokensMap=te(e);else throw new Error("<tokensDictionary> argument must be An Array of Token constructors, A dictionary of Token constructors or an IMultiModeLexerDefinition");this.tokensMap.EOF=et;const r=w(e,"modes")?Ne(K(e.modes)):K(e),i=$e(r,s=>D(s.categoryMatches));this.tokenMatcher=i?Sn:tn,nn(K(this.tokensMap))}defineRule(e,t,r){if(this.selfAnalysisDone)throw Error(`Grammar rule <${e}> may not be defined after the 'performSelfAnalysis' method has been called'
Make sure that all grammar rule definitions are done before 'performSelfAnalysis' is called.`);const i=w(r,"resyncEnabled")?r.resyncEnabled:Cn.resyncEnabled,s=w(r,"recoveryValueFunc")?r.recoveryValueFunc:Cn.recoveryValueFunc,a=this.ruleShortNameIdx<<jf+rt;this.ruleShortNameIdx++,this.shortRuleNameToFull[a]=e,this.fullRuleNameToShort[e]=a;let o;return this.outputCst===!0?o=function(...u){try{this.ruleInvocationStateUpdate(a,e,this.subruleIdx),t.apply(this,u);const d=this.CST_STACK[this.CST_STACK.length-1];return this.cstPostRule(d),d}catch(d){return this.invokeRuleCatch(d,i,s)}finally{this.ruleFinallyStateUpdate()}}:o=function(...u){try{return this.ruleInvocationStateUpdate(a,e,this.subruleIdx),t.apply(this,u)}catch(d){return this.invokeRuleCatch(d,i,s)}finally{this.ruleFinallyStateUpdate()}},Object.assign(o,{ruleName:e,originalGrammarAction:t})}invokeRuleCatch(e,t,r){const i=this.RULE_STACK.length===1,s=t&&!this.isBackTracking()&&this.recoveryEnabled;if(In(e)){const a=e;if(s){const o=this.findReSyncTokenType();if(this.isInCurrentRuleReSyncSet(o))if(a.resyncedTokens=this.reSyncTo(o),this.outputCst){const l=this.CST_STACK[this.CST_STACK.length-1];return l.recoveredNode=!0,l}else return r(e);else{if(this.outputCst){const l=this.CST_STACK[this.CST_STACK.length-1];l.recoveredNode=!0,a.partialCstResult=l}throw a}}else{if(i)return this.moveToTerminatedState(),r(e);throw a}}else throw e}optionInternal(e,t){const r=this.getKeyForAutomaticLookahead(kl,t);return this.optionInternalLogic(e,t,r)}optionInternalLogic(e,t,r){let i=this.getLaFuncFromCache(r),s;if(typeof e!="function"){s=e.DEF;const a=e.GATE;if(a!==void 0){const o=i;i=()=>a.call(this)&&o.call(this)}}else s=e;if(i.call(this)===!0)return s.call(this)}atLeastOneInternal(e,t){const r=this.getKeyForAutomaticLookahead(_r,e);return this.atLeastOneInternalLogic(e,t,r)}atLeastOneInternalLogic(e,t,r){let i=this.getLaFuncFromCache(r),s;if(typeof t!="function"){s=t.DEF;const a=t.GATE;if(a!==void 0){const o=i;i=()=>a.call(this)&&o.call(this)}}else s=t;if(i.call(this)===!0){let a=this.doSingleRepetition(s);for(;i.call(this)===!0&&a===!0;)a=this.doSingleRepetition(s)}else throw this.raiseEarlyExitException(e,B.REPETITION_MANDATORY,t.ERR_MSG);this.attemptInRepetitionRecovery(this.atLeastOneInternal,[e,t],i,_r,e,ff)}atLeastOneSepFirstInternal(e,t){const r=this.getKeyForAutomaticLookahead(yn,e);this.atLeastOneSepFirstInternalLogic(e,t,r)}atLeastOneSepFirstInternalLogic(e,t,r){const i=t.DEF,s=t.SEP;if(this.getLaFuncFromCache(r).call(this)===!0){i.call(this);const o=()=>this.tokenMatcher(this.LA(1),s);for(;this.tokenMatcher(this.LA(1),s)===!0;)this.CONSUME(s),i.call(this);this.attemptInRepetitionRecovery(this.repetitionSepSecondInternal,[e,s,o,i,us],o,yn,e,us)}else throw this.raiseEarlyExitException(e,B.REPETITION_MANDATORY_WITH_SEPARATOR,t.ERR_MSG)}manyInternal(e,t){const r=this.getKeyForAutomaticLookahead(wr,e);return this.manyInternalLogic(e,t,r)}manyInternalLogic(e,t,r){let i=this.getLaFuncFromCache(r),s;if(typeof t!="function"){s=t.DEF;const o=t.GATE;if(o!==void 0){const l=i;i=()=>o.call(this)&&l.call(this)}}else s=t;let a=!0;for(;i.call(this)===!0&&a===!0;)a=this.doSingleRepetition(s);this.attemptInRepetitionRecovery(this.manyInternal,[e,t],i,wr,e,df,a)}manySepFirstInternal(e,t){const r=this.getKeyForAutomaticLookahead(Lr,e);this.manySepFirstInternalLogic(e,t,r)}manySepFirstInternalLogic(e,t,r){const i=t.DEF,s=t.SEP;if(this.getLaFuncFromCache(r).call(this)===!0){i.call(this);const o=()=>this.tokenMatcher(this.LA(1),s);for(;this.tokenMatcher(this.LA(1),s)===!0;)this.CONSUME(s),i.call(this);this.attemptInRepetitionRecovery(this.repetitionSepSecondInternal,[e,s,o,i,cs],o,Lr,e,cs)}}repetitionSepSecondInternal(e,t,r,i,s){for(;r();)this.CONSUME(t),i.call(this);this.attemptInRepetitionRecovery(this.repetitionSepSecondInternal,[e,t,r,i,s],r,yn,e,s)}doSingleRepetition(e){const t=this.getLexerPosition();return e.call(this),this.getLexerPosition()>t}orInternal(e,t){const r=this.getKeyForAutomaticLookahead(vl,t),i=Z(e)?e:e.DEF,a=this.getLaFuncFromCache(r).call(this,i);if(a!==void 0)return i[a].ALT.call(this);this.raiseNoAltException(t,e.ERR_MSG)}ruleFinallyStateUpdate(){if(this.RULE_STACK.pop(),this.RULE_OCCURRENCE_STACK.pop(),this.cstFinallyStateUpdate(),this.RULE_STACK.length===0&&this.isAtEndOfInput()===!1){const e=this.LA(1),t=this.errorMessageProvider.buildNotAllInputParsedMessage({firstRedundant:e,ruleName:this.getCurrRuleFullName()});this.SAVE_ERROR(new Gf(t,e))}}subruleInternal(e,t,r){let i;try{const s=r!==void 0?r.ARGS:void 0;return this.subruleIdx=t,i=e.apply(this,s),this.cstPostNonTerminal(i,r!==void 0&&r.LABEL!==void 0?r.LABEL:e.ruleName),i}catch(s){throw this.subruleInternalError(s,r,e.ruleName)}}subruleInternalError(e,t,r){throw In(e)&&e.partialCstResult!==void 0&&(this.cstPostNonTerminal(e.partialCstResult,t!==void 0&&t.LABEL!==void 0?t.LABEL:r),delete e.partialCstResult),e}consumeInternal(e,t,r){let i;try{const s=this.LA(1);this.tokenMatcher(s,e)===!0?(this.consumeToken(),i=s):this.consumeInternalError(e,s,r)}catch(s){i=this.consumeInternalRecovery(e,t,s)}return this.cstPostTerminal(r!==void 0&&r.LABEL!==void 0?r.LABEL:e.name,i),i}consumeInternalError(e,t,r){let i;const s=this.LA(0);throw r!==void 0&&r.ERR_MSG?i=r.ERR_MSG:i=this.errorMessageProvider.buildMismatchTokenMessage({expected:e,actual:t,previous:s,ruleName:this.getCurrRuleFullName()}),this.SAVE_ERROR(new Al(i,t,s))}consumeInternalRecovery(e,t,r){if(this.recoveryEnabled&&r.name==="MismatchedTokenException"&&!this.isBackTracking()){const i=this.getFollowsForInRuleRecovery(e,t);try{return this.tryInRuleRecovery(e,i)}catch(s){throw s.name===El?r:s}}else throw r}saveRecogState(){const e=this.errors,t=te(this.RULE_STACK);return{errors:e,lexerState:this.exportLexerState(),RULE_STACK:t,CST_STACK:this.CST_STACK}}reloadRecogState(e){this.errors=e.errors,this.importLexerState(e.lexerState),this.RULE_STACK=e.RULE_STACK}ruleInvocationStateUpdate(e,t,r){this.RULE_OCCURRENCE_STACK.push(r),this.RULE_STACK.push(e),this.cstInvocationStateUpdate(t)}isBackTracking(){return this.isBackTrackingStack.length!==0}getCurrRuleFullName(){const e=this.getLastExplicitRuleShortName();return this.shortRuleNameToFull[e]}shortRuleNameToFullName(e){return this.shortRuleNameToFull[e]}isAtEndOfInput(){return this.tokenMatcher(this.LA(1),et)}reset(){this.resetLexerState(),this.subruleIdx=0,this.isBackTrackingStack=[],this.errors=[],this.RULE_STACK=[],this.CST_STACK=[],this.RULE_OCCURRENCE_STACK=[]}}class ah{initErrorHandler(e){this._errors=[],this.errorMessageProvider=w(e,"errorMessageProvider")?e.errorMessageProvider:Ye.errorMessageProvider}SAVE_ERROR(e){if(In(e))return e.context={ruleStack:this.getHumanReadableRuleStack(),ruleOccurrenceStack:te(this.RULE_OCCURRENCE_STACK)},this._errors.push(e),e;throw Error("Trying to save an Error which is not a RecognitionException")}get errors(){return te(this._errors)}set errors(e){this._errors=e}raiseEarlyExitException(e,t,r){const i=this.getCurrRuleFullName(),s=this.getGAstProductions()[i],o=er(e,s,t,this.maxLookahead)[0],l=[];for(let u=1;u<=this.maxLookahead;u++)l.push(this.LA(u));const c=this.errorMessageProvider.buildEarlyExitMessage({expectedIterationPaths:o,actual:l,previous:this.LA(0),customUserDescription:r,ruleName:i});throw this.SAVE_ERROR(new Uf(c,this.LA(1),this.LA(0)))}raiseNoAltException(e,t){const r=this.getCurrRuleFullName(),i=this.getGAstProductions()[r],s=Zn(e,i,this.maxLookahead),a=[];for(let c=1;c<=this.maxLookahead;c++)a.push(this.LA(c));const o=this.LA(0),l=this.errorMessageProvider.buildNoViableAltMessage({expectedPathsPerAlt:s,actual:a,previous:o,customUserDescription:t,ruleName:this.getCurrRuleFullName()});throw this.SAVE_ERROR(new Ff(l,this.LA(1),o))}}class oh{initContentAssist(){}computeContentAssist(e,t){const r=this.gastProductionsCache[e];if(ze(r))throw Error(`Rule ->${e}<- does not exist in this grammar.`);return cl([r],t,this.tokenMatcher,this.maxLookahead)}getNextPossibleTokenTypes(e){const t=be(e.ruleStack),i=this.getGAstProductions()[t];return new uf(i,e).startWalking()}}const nr={description:"This Object indicates the Parser is during Recording Phase"};Object.freeze(nr);const ms=!0,gs=Math.pow(2,rt)-1,Il=ol({name:"RECORDING_PHASE_TOKEN",pattern:de.NA});nn([Il]);const xl=yi(Il,`This IToken indicates the Parser is in Recording Phase
	See: https://chevrotain.io/docs/guide/internals.html#grammar-recording for details`,-1,-1,-1,-1,-1,-1);Object.freeze(xl);const lh={name:`This CSTNode indicates the Parser is in Recording Phase
	See: https://chevrotain.io/docs/guide/internals.html#grammar-recording for details`,children:{}};class ch{initGastRecorder(e){this.recordingProdStack=[],this.RECORDING_PHASE=!1}enableRecording(){this.RECORDING_PHASE=!0,this.TRACE_INIT("Enable Recording",()=>{for(let e=0;e<10;e++){const t=e>0?e:"";this[`CONSUME${t}`]=function(r,i){return this.consumeInternalRecord(r,e,i)},this[`SUBRULE${t}`]=function(r,i){return this.subruleInternalRecord(r,e,i)},this[`OPTION${t}`]=function(r){return this.optionInternalRecord(r,e)},this[`OR${t}`]=function(r){return this.orInternalRecord(r,e)},this[`MANY${t}`]=function(r){this.manyInternalRecord(e,r)},this[`MANY_SEP${t}`]=function(r){this.manySepFirstInternalRecord(e,r)},this[`AT_LEAST_ONE${t}`]=function(r){this.atLeastOneInternalRecord(e,r)},this[`AT_LEAST_ONE_SEP${t}`]=function(r){this.atLeastOneSepFirstInternalRecord(e,r)}}this.consume=function(e,t,r){return this.consumeInternalRecord(t,e,r)},this.subrule=function(e,t,r){return this.subruleInternalRecord(t,e,r)},this.option=function(e,t){return this.optionInternalRecord(t,e)},this.or=function(e,t){return this.orInternalRecord(t,e)},this.many=function(e,t){this.manyInternalRecord(e,t)},this.atLeastOne=function(e,t){this.atLeastOneInternalRecord(e,t)},this.ACTION=this.ACTION_RECORD,this.BACKTRACK=this.BACKTRACK_RECORD,this.LA=this.LA_RECORD})}disableRecording(){this.RECORDING_PHASE=!1,this.TRACE_INIT("Deleting Recording methods",()=>{const e=this;for(let t=0;t<10;t++){const r=t>0?t:"";delete e[`CONSUME${r}`],delete e[`SUBRULE${r}`],delete e[`OPTION${r}`],delete e[`OR${r}`],delete e[`MANY${r}`],delete e[`MANY_SEP${r}`],delete e[`AT_LEAST_ONE${r}`],delete e[`AT_LEAST_ONE_SEP${r}`]}delete e.consume,delete e.subrule,delete e.option,delete e.or,delete e.many,delete e.atLeastOne,delete e.ACTION,delete e.BACKTRACK,delete e.LA})}ACTION_RECORD(e){}BACKTRACK_RECORD(e,t){return()=>!0}LA_RECORD(e){return Nn}topLevelRuleRecord(e,t){try{const r=new bt({definition:[],name:e});return r.name=e,this.recordingProdStack.push(r),t.call(this),this.recordingProdStack.pop(),r}catch(r){if(r.KNOWN_RECORDER_ERROR!==!0)try{r.message=r.message+`
	 This error was thrown during the "grammar recording phase" For more info see:
	https://chevrotain.io/docs/guide/internals.html#grammar-recording`}catch{throw r}throw r}}optionInternalRecord(e,t){return Wt.call(this,ee,e,t)}atLeastOneInternalRecord(e,t){Wt.call(this,ve,t,e)}atLeastOneSepFirstInternalRecord(e,t){Wt.call(this,ke,t,e,ms)}manyInternalRecord(e,t){Wt.call(this,W,t,e)}manySepFirstInternalRecord(e,t){Wt.call(this,pe,t,e,ms)}orInternalRecord(e,t){return uh.call(this,e,t)}subruleInternalRecord(e,t,r){if(xn(t),!e||w(e,"ruleName")===!1){const o=new Error(`<SUBRULE${ys(t)}> argument is invalid expecting a Parser method reference but got: <${JSON.stringify(e)}>
 inside top level rule: <${this.recordingProdStack[0].name}>`);throw o.KNOWN_RECORDER_ERROR=!0,o}const i=St(this.recordingProdStack),s=e.ruleName,a=new le({idx:t,nonTerminalName:s,label:r?.LABEL,referencedRule:void 0});return i.definition.push(a),this.outputCst?lh:nr}consumeInternalRecord(e,t,r){if(xn(t),!sl(e)){const a=new Error(`<CONSUME${ys(t)}> argument is invalid expecting a TokenType reference but got: <${JSON.stringify(e)}>
 inside top level rule: <${this.recordingProdStack[0].name}>`);throw a.KNOWN_RECORDER_ERROR=!0,a}const i=St(this.recordingProdStack),s=new G({idx:t,terminalType:e,label:r?.LABEL});return i.definition.push(s),xl}}function Wt(n,e,t,r=!1){xn(t);const i=St(this.recordingProdStack),s=pt(e)?e:e.DEF,a=new n({definition:[],idx:t});return r&&(a.separator=e.SEP),w(e,"MAX_LOOKAHEAD")&&(a.maxLookahead=e.MAX_LOOKAHEAD),this.recordingProdStack.push(a),s.call(this),i.definition.push(a),this.recordingProdStack.pop(),nr}function uh(n,e){xn(e);const t=St(this.recordingProdStack),r=Z(n)===!1,i=r===!1?n:n.DEF,s=new me({definition:[],idx:e,ignoreAmbiguities:r&&n.IGNORE_AMBIGUITIES===!0});w(n,"MAX_LOOKAHEAD")&&(s.maxLookahead=n.MAX_LOOKAHEAD);const a=Qa(i,o=>pt(o.GATE));return s.hasPredicates=a,t.definition.push(s),C(i,o=>{const l=new he({definition:[]});s.definition.push(l),w(o,"IGNORE_AMBIGUITIES")?l.ignoreAmbiguities=o.IGNORE_AMBIGUITIES:w(o,"GATE")&&(l.ignoreAmbiguities=!0),this.recordingProdStack.push(l),o.ALT.call(this),this.recordingProdStack.pop()}),nr}function ys(n){return n===0?"":`${n}`}function xn(n){if(n<0||n>gs){const e=new Error(`Invalid DSL Method idx value: <${n}>
	Idx value must be a none negative value smaller than ${gs+1}`);throw e.KNOWN_RECORDER_ERROR=!0,e}}class dh{initPerformanceTracer(e){if(w(e,"traceInitPerf")){const t=e.traceInitPerf,r=typeof t=="number";this.traceInitMaxIdent=r?t:1/0,this.traceInitPerf=r?t>0:t}else this.traceInitMaxIdent=0,this.traceInitPerf=Ye.traceInitPerf;this.traceInitIndent=-1}TRACE_INIT(e,t){if(this.traceInitPerf===!0){this.traceInitIndent++;const r=new Array(this.traceInitIndent+1).join("	");this.traceInitIndent<this.traceInitMaxIdent&&console.log(`${r}--> <${e}>`);const{time:i,value:s}=Yo(t),a=i>10?console.warn:console.log;return this.traceInitIndent<this.traceInitMaxIdent&&a(`${r}<-- <${e}> time: ${i}ms`),this.traceInitIndent--,s}else return t()}}function fh(n,e){e.forEach(t=>{const r=t.prototype;Object.getOwnPropertyNames(r).forEach(i=>{if(i==="constructor")return;const s=Object.getOwnPropertyDescriptor(r,i);s&&(s.get||s.set)?Object.defineProperty(n.prototype,i,s):n.prototype[i]=t.prototype[i]})})}const Nn=yi(et,"",NaN,NaN,NaN,NaN,NaN,NaN);Object.freeze(Nn);const Ye=Object.freeze({recoveryEnabled:!1,maxLookahead:3,dynamicTokensEnabled:!1,outputCst:!0,errorMessageProvider:Rt,nodeLocationTracking:"none",traceInitPerf:!1,skipValidations:!1}),Cn=Object.freeze({recoveryValueFunc:()=>{},resyncEnabled:!0});var ce;(function(n){n[n.INVALID_RULE_NAME=0]="INVALID_RULE_NAME",n[n.DUPLICATE_RULE_NAME=1]="DUPLICATE_RULE_NAME",n[n.INVALID_RULE_OVERRIDE=2]="INVALID_RULE_OVERRIDE",n[n.DUPLICATE_PRODUCTIONS=3]="DUPLICATE_PRODUCTIONS",n[n.UNRESOLVED_SUBRULE_REF=4]="UNRESOLVED_SUBRULE_REF",n[n.LEFT_RECURSION=5]="LEFT_RECURSION",n[n.NONE_LAST_EMPTY_ALT=6]="NONE_LAST_EMPTY_ALT",n[n.AMBIGUOUS_ALTS=7]="AMBIGUOUS_ALTS",n[n.CONFLICT_TOKENS_RULES_NAMESPACE=8]="CONFLICT_TOKENS_RULES_NAMESPACE",n[n.INVALID_TOKEN_NAME=9]="INVALID_TOKEN_NAME",n[n.NO_NON_EMPTY_LOOKAHEAD=10]="NO_NON_EMPTY_LOOKAHEAD",n[n.AMBIGUOUS_PREFIX_ALTS=11]="AMBIGUOUS_PREFIX_ALTS",n[n.TOO_MANY_ALTS=12]="TOO_MANY_ALTS",n[n.CUSTOM_LOOKAHEAD_VALIDATION=13]="CUSTOM_LOOKAHEAD_VALIDATION"})(ce||(ce={}));function Ts(n=void 0){return function(){return n}}class rn{static performSelfAnalysis(e){throw Error("The **static** `performSelfAnalysis` method has been deprecated.	\nUse the **instance** method with the same name instead.")}performSelfAnalysis(){this.TRACE_INIT("performSelfAnalysis",()=>{let e;this.selfAnalysisDone=!0;const t=this.className;this.TRACE_INIT("toFastProps",()=>{Xo(this)}),this.TRACE_INIT("Grammar Recording",()=>{try{this.enableRecording(),C(this.definedRulesNames,i=>{const a=this[i].originalGrammarAction;let o;this.TRACE_INIT(`${i} Rule`,()=>{o=this.topLevelRuleRecord(i,a)}),this.gastProductionsCache[i]=o})}finally{this.disableRecording()}});let r=[];if(this.TRACE_INIT("Grammar Resolving",()=>{r=Mf({rules:K(this.gastProductionsCache)}),this.definitionErrors=this.definitionErrors.concat(r)}),this.TRACE_INIT("Grammar Validations",()=>{if(D(r)&&this.skipValidations===!1){const i=Df({rules:K(this.gastProductionsCache),tokenTypes:K(this.tokensMap),errMsgProvider:lt,grammarName:t}),s=Ef({lookaheadStrategy:this.lookaheadStrategy,rules:K(this.gastProductionsCache),tokenTypes:K(this.tokensMap),grammarName:t});this.definitionErrors=this.definitionErrors.concat(i,s)}}),D(this.definitionErrors)&&(this.recoveryEnabled&&this.TRACE_INIT("computeAllProdsFollows",()=>{const i=Td(K(this.gastProductionsCache));this.resyncFollows=i}),this.TRACE_INIT("ComputeLookaheadFunctions",()=>{var i,s;(s=(i=this.lookaheadStrategy).initialize)===null||s===void 0||s.call(i,{rules:K(this.gastProductionsCache)}),this.preComputeLookaheadFunctions(K(this.gastProductionsCache))})),!rn.DEFER_DEFINITION_ERRORS_HANDLING&&!D(this.definitionErrors))throw e=k(this.definitionErrors,i=>i.message),new Error(`Parser Definition Errors detected:
 ${e.join(`
-------------------------------
`)}`)})}constructor(e,t){this.definitionErrors=[],this.selfAnalysisDone=!1;const r=this;if(r.initErrorHandler(t),r.initLexerAdapter(),r.initLooksAhead(t),r.initRecognizerEngine(e,t),r.initRecoverable(t),r.initTreeBuilder(t),r.initContentAssist(),r.initGastRecorder(t),r.initPerformanceTracer(t),w(t,"ignoredIssues"))throw new Error(`The <ignoredIssues> IParserConfig property has been deprecated.
	Please use the <IGNORE_AMBIGUITIES> flag on the relevant DSL method instead.
	See: https://chevrotain.io/docs/guide/resolving_grammar_errors.html#IGNORING_AMBIGUITIES
	For further details.`);this.skipValidations=w(t,"skipValidations")?t.skipValidations:Ye.skipValidations}}rn.DEFER_DEFINITION_ERRORS_HANDLING=!1;fh(rn,[Vf,Hf,nh,rh,sh,ih,ah,oh,ch,dh]);class hh extends rn{constructor(e,t=Ye){const r=te(t);r.outputCst=!1,super(e,r)}}function xt(n,e,t){return`${n.name}_${e}_${t}`}const tt=1,ph=2,Nl=4,Cl=5,sn=7,mh=8,gh=9,yh=10,Th=11,wl=12;class Ei{constructor(e){this.target=e}isEpsilon(){return!1}}class vi extends Ei{constructor(e,t){super(e),this.tokenType=t}}class _l extends Ei{constructor(e){super(e)}isEpsilon(){return!0}}class ki extends Ei{constructor(e,t,r){super(e),this.rule=t,this.followState=r}isEpsilon(){return!0}}function Rh(n){const e={decisionMap:{},decisionStates:[],ruleToStartState:new Map,ruleToStopState:new Map,states:[]};Ah(e,n);const t=n.length;for(let r=0;r<t;r++){const i=n[r],s=gt(e,i,i);s!==void 0&&Lh(e,i,s)}return e}function Ah(n,e){const t=e.length;for(let r=0;r<t;r++){const i=e[r],s=Y(n,i,void 0,{type:ph}),a=Y(n,i,void 0,{type:sn});s.stop=a,n.ruleToStartState.set(i,s),n.ruleToStopState.set(i,a)}}function Ll(n,e,t){return t instanceof G?Si(n,e,t.terminalType,t):t instanceof le?_h(n,e,t):t instanceof me?Ih(n,e,t):t instanceof ee?xh(n,e,t):t instanceof W?Eh(n,e,t):t instanceof pe?vh(n,e,t):t instanceof ve?kh(n,e,t):t instanceof ke?Sh(n,e,t):gt(n,e,t)}function Eh(n,e,t){const r=Y(n,e,t,{type:Cl});it(n,r);const i=Mt(n,e,r,t,gt(n,e,t));return Ol(n,e,t,i)}function vh(n,e,t){const r=Y(n,e,t,{type:Cl});it(n,r);const i=Mt(n,e,r,t,gt(n,e,t)),s=Si(n,e,t.separator,t);return Ol(n,e,t,i,s)}function kh(n,e,t){const r=Y(n,e,t,{type:Nl});it(n,r);const i=Mt(n,e,r,t,gt(n,e,t));return $l(n,e,t,i)}function Sh(n,e,t){const r=Y(n,e,t,{type:Nl});it(n,r);const i=Mt(n,e,r,t,gt(n,e,t)),s=Si(n,e,t.separator,t);return $l(n,e,t,i,s)}function Ih(n,e,t){const r=Y(n,e,t,{type:tt});it(n,r);const i=k(t.definition,a=>Ll(n,e,a));return Mt(n,e,r,t,...i)}function xh(n,e,t){const r=Y(n,e,t,{type:tt});it(n,r);const i=Mt(n,e,r,t,gt(n,e,t));return Nh(n,e,t,i)}function gt(n,e,t){const r=Ee(k(t.definition,i=>Ll(n,e,i)),i=>i!==void 0);return r.length===1?r[0]:r.length===0?void 0:wh(n,r)}function $l(n,e,t,r,i){const s=r.left,a=r.right,o=Y(n,e,t,{type:Th});it(n,o);const l=Y(n,e,t,{type:wl});return s.loopback=o,l.loopback=o,n.decisionMap[xt(e,i?"RepetitionMandatoryWithSeparator":"RepetitionMandatory",t.idx)]=o,H(a,o),i===void 0?(H(o,s),H(o,l)):(H(o,l),H(o,i.left),H(i.right,s)),{left:s,right:l}}function Ol(n,e,t,r,i){const s=r.left,a=r.right,o=Y(n,e,t,{type:yh});it(n,o);const l=Y(n,e,t,{type:wl}),c=Y(n,e,t,{type:gh});return o.loopback=c,l.loopback=c,H(o,s),H(o,l),H(a,c),i!==void 0?(H(c,l),H(c,i.left),H(i.right,s)):H(c,o),n.decisionMap[xt(e,i?"RepetitionWithSeparator":"Repetition",t.idx)]=o,{left:o,right:l}}function Nh(n,e,t,r){const i=r.left,s=r.right;return H(i,s),n.decisionMap[xt(e,"Option",t.idx)]=i,r}function it(n,e){return n.decisionStates.push(e),e.decision=n.decisionStates.length-1,e.decision}function Mt(n,e,t,r,...i){const s=Y(n,e,r,{type:mh,start:t});t.end=s;for(const o of i)o!==void 0?(H(t,o.left),H(o.right,s)):H(t,s);const a={left:t,right:s};return n.decisionMap[xt(e,Ch(r),r.idx)]=t,a}function Ch(n){if(n instanceof me)return"Alternation";if(n instanceof ee)return"Option";if(n instanceof W)return"Repetition";if(n instanceof pe)return"RepetitionWithSeparator";if(n instanceof ve)return"RepetitionMandatory";if(n instanceof ke)return"RepetitionMandatoryWithSeparator";throw new Error("Invalid production type encountered")}function wh(n,e){const t=e.length;for(let s=0;s<t-1;s++){const a=e[s];let o;a.left.transitions.length===1&&(o=a.left.transitions[0]);const l=o instanceof ki,c=o,u=e[s+1].left;a.left.type===tt&&a.right.type===tt&&o!==void 0&&(l&&c.followState===a.right||o.target===a.right)?(l?c.followState=u:o.target=u,$h(n,a.right)):H(a.right,u)}const r=e[0],i=e[t-1];return{left:r.left,right:i.right}}function Si(n,e,t,r){const i=Y(n,e,r,{type:tt}),s=Y(n,e,r,{type:tt});return Ii(i,new vi(s,t)),{left:i,right:s}}function _h(n,e,t){const r=t.referencedRule,i=n.ruleToStartState.get(r),s=Y(n,e,t,{type:tt}),a=Y(n,e,t,{type:tt}),o=new ki(i,r,a);return Ii(s,o),{left:s,right:a}}function Lh(n,e,t){const r=n.ruleToStartState.get(e);H(r,t.left);const i=n.ruleToStopState.get(e);return H(t.right,i),{left:r,right:i}}function H(n,e){const t=new _l(e);Ii(n,t)}function Y(n,e,t,r){const i=Object.assign({atn:n,production:t,epsilonOnlyTransitions:!1,rule:e,transitions:[],nextTokenWithinRule:[],stateNumber:n.states.length},r);return n.states.push(i),i}function Ii(n,e){n.transitions.length===0&&(n.epsilonOnlyTransitions=e.isEpsilon()),n.transitions.push(e)}function $h(n,e){n.states.splice(n.states.indexOf(e),1)}const wn={};class Or{constructor(){this.map={},this.configs=[]}get size(){return this.configs.length}finalize(){this.map={}}add(e){const t=bl(e);t in this.map||(this.map[t]=this.configs.length,this.configs.push(e))}get elements(){return this.configs}get alts(){return k(this.configs,e=>e.alt)}get key(){let e="";for(const t in this.map)e+=t+":";return e}}function bl(n,e=!0){return`${e?`a${n.alt}`:""}s${n.state.stateNumber}:${n.stack.map(t=>t.stateNumber.toString()).join("_")}`}function Oh(n,e){const t={};return r=>{const i=r.toString();let s=t[i];return s!==void 0||(s={atnStartState:n,decision:e,states:{}},t[i]=s),s}}class Pl{constructor(){this.predicates=[]}is(e){return e>=this.predicates.length||this.predicates[e]}set(e,t){this.predicates[e]=t}toString(){let e="";const t=this.predicates.length;for(let r=0;r<t;r++)e+=this.predicates[r]===!0?"1":"0";return e}}const Rs=new Pl;class bh extends Ai{constructor(e){var t;super(),this.logging=(t=e?.logging)!==null&&t!==void 0?t:r=>console.log(r)}initialize(e){this.atn=Rh(e.rules),this.dfas=Ph(this.atn)}validateAmbiguousAlternationAlternatives(){return[]}validateEmptyOrAlternatives(){return[]}buildLookaheadForAlternation(e){const{prodOccurrence:t,rule:r,hasPredicates:i,dynamicTokensEnabled:s}=e,a=this.dfas,o=this.logging,l=xt(r,"Alternation",t),u=this.atn.decisionMap[l].decision,d=k(ds({maxLookahead:1,occurrence:t,prodType:"Alternation",rule:r}),f=>k(f,h=>h[0]));if(As(d,!1)&&!s){const f=oe(d,(h,m,g)=>(C(m,A=>{A&&(h[A.tokenTypeIdx]=g,C(A.categoryMatches,T=>{h[T]=g}))}),h),{});return i?function(h){var m;const g=this.LA(1),A=f[g.tokenTypeIdx];if(h!==void 0&&A!==void 0){const T=(m=h[A])===null||m===void 0?void 0:m.GATE;if(T!==void 0&&T.call(this)===!1)return}return A}:function(){const h=this.LA(1);return f[h.tokenTypeIdx]}}else return i?function(f){const h=new Pl,m=f===void 0?0:f.length;for(let A=0;A<m;A++){const T=f?.[A].GATE;h.set(A,T===void 0||T.call(this))}const g=mr.call(this,a,u,h,o);return typeof g=="number"?g:void 0}:function(){const f=mr.call(this,a,u,Rs,o);return typeof f=="number"?f:void 0}}buildLookaheadForOptional(e){const{prodOccurrence:t,rule:r,prodType:i,dynamicTokensEnabled:s}=e,a=this.dfas,o=this.logging,l=xt(r,i,t),u=this.atn.decisionMap[l].decision,d=k(ds({maxLookahead:1,occurrence:t,prodType:i,rule:r}),f=>k(f,h=>h[0]));if(As(d)&&d[0][0]&&!s){const f=d[0],h=Ne(f);if(h.length===1&&D(h[0].categoryMatches)){const g=h[0].tokenTypeIdx;return function(){return this.LA(1).tokenTypeIdx===g}}else{const m=oe(h,(g,A)=>(A!==void 0&&(g[A.tokenTypeIdx]=!0,C(A.categoryMatches,T=>{g[T]=!0})),g),{});return function(){const g=this.LA(1);return m[g.tokenTypeIdx]===!0}}}return function(){const f=mr.call(this,a,u,Rs,o);return typeof f=="object"?!1:f===0}}}function As(n,e=!0){const t=new Set;for(const r of n){const i=new Set;for(const s of r){if(s===void 0){if(e)break;return!1}const a=[s.tokenTypeIdx].concat(s.categoryMatches);for(const o of a)if(t.has(o)){if(!i.has(o))return!1}else t.add(o),i.add(o)}}return!0}function Ph(n){const e=n.decisionStates.length,t=Array(e);for(let r=0;r<e;r++)t[r]=Oh(n.decisionStates[r],r);return t}function mr(n,e,t,r){const i=n[e](t);let s=i.start;if(s===void 0){const o=Kh(i.atnStartState);s=Dl(i,Ml(o)),i.start=s}return Mh.apply(this,[i,s,t,r])}function Mh(n,e,t,r){let i=e,s=1;const a=[];let o=this.LA(s++);for(;;){let l=Vh(i,o);if(l===void 0&&(l=Dh.apply(this,[n,i,o,s,t,r])),l===wn)return Bh(a,i,o);if(l.isAcceptState===!0)return l.prediction;i=l,a.push(o),o=this.LA(s++)}}function Dh(n,e,t,r,i,s){const a=Wh(e.configs,t,i);if(a.size===0)return Es(n,e,t,wn),wn;let o=Ml(a);const l=Hh(a,i);if(l!==void 0)o.isAcceptState=!0,o.prediction=l,o.configs.uniqueAlt=l;else if(Xh(a)){const c=Lc(a.alts);o.isAcceptState=!0,o.prediction=c,o.configs.uniqueAlt=c,Fh.apply(this,[n,r,a.alts,s])}return o=Es(n,e,t,o),o}function Fh(n,e,t,r){const i=[];for(let c=1;c<=e;c++)i.push(this.LA(c).tokenType);const s=n.atnStartState,a=s.rule,o=s.production,l=Gh({topLevelRule:a,ambiguityIndices:t,production:o,prefixPath:i});r(l)}function Gh(n){const e=k(n.prefixPath,i=>Et(i)).join(", "),t=n.production.idx===0?"":n.production.idx;let r=`Ambiguous Alternatives Detected: <${n.ambiguityIndices.join(", ")}> in <${Uh(n.production)}${t}> inside <${n.topLevelRule.name}> Rule,
<${e}> may appears as a prefix path in all these alternatives.
`;return r=r+`See: https://chevrotain.io/docs/guide/resolving_grammar_errors.html#AMBIGUOUS_ALTERNATIVES
For Further details.`,r}function Uh(n){if(n instanceof le)return"SUBRULE";if(n instanceof ee)return"OPTION";if(n instanceof me)return"OR";if(n instanceof ve)return"AT_LEAST_ONE";if(n instanceof ke)return"AT_LEAST_ONE_SEP";if(n instanceof pe)return"MANY_SEP";if(n instanceof W)return"MANY";if(n instanceof G)return"CONSUME";throw Error("non exhaustive match")}function Bh(n,e,t){const r=Re(e.configs.elements,s=>s.state.transitions),i=Jc(r.filter(s=>s instanceof vi).map(s=>s.tokenType),s=>s.tokenTypeIdx);return{actualToken:t,possibleTokenTypes:i,tokenPath:n}}function Vh(n,e){return n.edges[e.tokenTypeIdx]}function Wh(n,e,t){const r=new Or,i=[];for(const a of n.elements){if(t.is(a.alt)===!1)continue;if(a.state.type===sn){i.push(a);continue}const o=a.state.transitions.length;for(let l=0;l<o;l++){const c=a.state.transitions[l],u=jh(c,e);u!==void 0&&r.add({state:u,alt:a.alt,stack:a.stack})}}let s;if(i.length===0&&r.size===1&&(s=r),s===void 0){s=new Or;for(const a of r.elements)_n(a,s)}if(i.length>0&&!qh(s))for(const a of i)s.add(a);return s}function jh(n,e){if(n instanceof vi&&ll(e,n.tokenType))return n.target}function Hh(n,e){let t;for(const r of n.elements)if(e.is(r.alt)===!0){if(t===void 0)t=r.alt;else if(t!==r.alt)return}return t}function Ml(n){return{configs:n,edges:{},isAcceptState:!1,prediction:-1}}function Es(n,e,t,r){return r=Dl(n,r),e.edges[t.tokenTypeIdx]=r,r}function Dl(n,e){if(e===wn)return e;const t=e.configs.key,r=n.states[t];return r!==void 0?r:(e.configs.finalize(),n.states[t]=e,e)}function Kh(n){const e=new Or,t=n.transitions.length;for(let r=0;r<t;r++){const s={state:n.transitions[r].target,alt:r,stack:[]};_n(s,e)}return e}function _n(n,e){const t=n.state;if(t.type===sn){if(n.stack.length>0){const i=[...n.stack],a={state:i.pop(),alt:n.alt,stack:i};_n(a,e)}else e.add(n);return}t.epsilonOnlyTransitions||e.add(n);const r=t.transitions.length;for(let i=0;i<r;i++){const s=t.transitions[i],a=zh(n,s);a!==void 0&&_n(a,e)}}function zh(n,e){if(e instanceof _l)return{state:e.target,alt:n.alt,stack:n.stack};if(e instanceof ki){const t=[...n.stack,e.followState];return{state:e.target,alt:n.alt,stack:t}}}function qh(n){for(const e of n.elements)if(e.state.type===sn)return!0;return!1}function Yh(n){for(const e of n.elements)if(e.state.type!==sn)return!1;return!0}function Xh(n){if(Yh(n))return!0;const e=Jh(n.elements);return Qh(e)&&!Zh(e)}function Jh(n){const e=new Map;for(const t of n){const r=bl(t,!1);let i=e.get(r);i===void 0&&(i={},e.set(r,i)),i[t.alt]=!0}return e}function Qh(n){for(const e of Array.from(n.values()))if(Object.keys(e).length>1)return!0;return!1}function Zh(n){for(const e of Array.from(n.values()))if(Object.keys(e).length===1)return!0;return!1}var vs;(function(n){function e(t){return typeof t=="string"}n.is=e})(vs||(vs={}));var br;(function(n){function e(t){return typeof t=="string"}n.is=e})(br||(br={}));var ks;(function(n){n.MIN_VALUE=-2147483648,n.MAX_VALUE=2147483647;function e(t){return typeof t=="number"&&n.MIN_VALUE<=t&&t<=n.MAX_VALUE}n.is=e})(ks||(ks={}));var Ln;(function(n){n.MIN_VALUE=0,n.MAX_VALUE=2147483647;function e(t){return typeof t=="number"&&n.MIN_VALUE<=t&&t<=n.MAX_VALUE}n.is=e})(Ln||(Ln={}));var P;(function(n){function e(r,i){return r===Number.MAX_VALUE&&(r=Ln.MAX_VALUE),i===Number.MAX_VALUE&&(i=Ln.MAX_VALUE),{line:r,character:i}}n.create=e;function t(r){let i=r;return p.objectLiteral(i)&&p.uinteger(i.line)&&p.uinteger(i.character)}n.is=t})(P||(P={}));var b;(function(n){function e(r,i,s,a){if(p.uinteger(r)&&p.uinteger(i)&&p.uinteger(s)&&p.uinteger(a))return{start:P.create(r,i),end:P.create(s,a)};if(P.is(r)&&P.is(i))return{start:r,end:i};throw new Error(`Range#create called with invalid arguments[${r}, ${i}, ${s}, ${a}]`)}n.create=e;function t(r){let i=r;return p.objectLiteral(i)&&P.is(i.start)&&P.is(i.end)}n.is=t})(b||(b={}));var $n;(function(n){function e(r,i){return{uri:r,range:i}}n.create=e;function t(r){let i=r;return p.objectLiteral(i)&&b.is(i.range)&&(p.string(i.uri)||p.undefined(i.uri))}n.is=t})($n||($n={}));var Ss;(function(n){function e(r,i,s,a){return{targetUri:r,targetRange:i,targetSelectionRange:s,originSelectionRange:a}}n.create=e;function t(r){let i=r;return p.objectLiteral(i)&&b.is(i.targetRange)&&p.string(i.targetUri)&&b.is(i.targetSelectionRange)&&(b.is(i.originSelectionRange)||p.undefined(i.originSelectionRange))}n.is=t})(Ss||(Ss={}));var Pr;(function(n){function e(r,i,s,a){return{red:r,green:i,blue:s,alpha:a}}n.create=e;function t(r){const i=r;return p.objectLiteral(i)&&p.numberRange(i.red,0,1)&&p.numberRange(i.green,0,1)&&p.numberRange(i.blue,0,1)&&p.numberRange(i.alpha,0,1)}n.is=t})(Pr||(Pr={}));var Is;(function(n){function e(r,i){return{range:r,color:i}}n.create=e;function t(r){const i=r;return p.objectLiteral(i)&&b.is(i.range)&&Pr.is(i.color)}n.is=t})(Is||(Is={}));var xs;(function(n){function e(r,i,s){return{label:r,textEdit:i,additionalTextEdits:s}}n.create=e;function t(r){const i=r;return p.objectLiteral(i)&&p.string(i.label)&&(p.undefined(i.textEdit)||Ct.is(i))&&(p.undefined(i.additionalTextEdits)||p.typedArray(i.additionalTextEdits,Ct.is))}n.is=t})(xs||(xs={}));var Ns;(function(n){n.Comment="comment",n.Imports="imports",n.Region="region"})(Ns||(Ns={}));var Cs;(function(n){function e(r,i,s,a,o,l){const c={startLine:r,endLine:i};return p.defined(s)&&(c.startCharacter=s),p.defined(a)&&(c.endCharacter=a),p.defined(o)&&(c.kind=o),p.defined(l)&&(c.collapsedText=l),c}n.create=e;function t(r){const i=r;return p.objectLiteral(i)&&p.uinteger(i.startLine)&&p.uinteger(i.startLine)&&(p.undefined(i.startCharacter)||p.uinteger(i.startCharacter))&&(p.undefined(i.endCharacter)||p.uinteger(i.endCharacter))&&(p.undefined(i.kind)||p.string(i.kind))}n.is=t})(Cs||(Cs={}));var Mr;(function(n){function e(r,i){return{location:r,message:i}}n.create=e;function t(r){let i=r;return p.defined(i)&&$n.is(i.location)&&p.string(i.message)}n.is=t})(Mr||(Mr={}));var ws;(function(n){n.Error=1,n.Warning=2,n.Information=3,n.Hint=4})(ws||(ws={}));var _s;(function(n){n.Unnecessary=1,n.Deprecated=2})(_s||(_s={}));var Ls;(function(n){function e(t){const r=t;return p.objectLiteral(r)&&p.string(r.href)}n.is=e})(Ls||(Ls={}));var On;(function(n){function e(r,i,s,a,o,l){let c={range:r,message:i};return p.defined(s)&&(c.severity=s),p.defined(a)&&(c.code=a),p.defined(o)&&(c.source=o),p.defined(l)&&(c.relatedInformation=l),c}n.create=e;function t(r){var i;let s=r;return p.defined(s)&&b.is(s.range)&&p.string(s.message)&&(p.number(s.severity)||p.undefined(s.severity))&&(p.integer(s.code)||p.string(s.code)||p.undefined(s.code))&&(p.undefined(s.codeDescription)||p.string((i=s.codeDescription)===null||i===void 0?void 0:i.href))&&(p.string(s.source)||p.undefined(s.source))&&(p.undefined(s.relatedInformation)||p.typedArray(s.relatedInformation,Mr.is))}n.is=t})(On||(On={}));var Nt;(function(n){function e(r,i,...s){let a={title:r,command:i};return p.defined(s)&&s.length>0&&(a.arguments=s),a}n.create=e;function t(r){let i=r;return p.defined(i)&&p.string(i.title)&&p.string(i.command)}n.is=t})(Nt||(Nt={}));var Ct;(function(n){function e(s,a){return{range:s,newText:a}}n.replace=e;function t(s,a){return{range:{start:s,end:s},newText:a}}n.insert=t;function r(s){return{range:s,newText:""}}n.del=r;function i(s){const a=s;return p.objectLiteral(a)&&p.string(a.newText)&&b.is(a.range)}n.is=i})(Ct||(Ct={}));var Dr;(function(n){function e(r,i,s){const a={label:r};return i!==void 0&&(a.needsConfirmation=i),s!==void 0&&(a.description=s),a}n.create=e;function t(r){const i=r;return p.objectLiteral(i)&&p.string(i.label)&&(p.boolean(i.needsConfirmation)||i.needsConfirmation===void 0)&&(p.string(i.description)||i.description===void 0)}n.is=t})(Dr||(Dr={}));var wt;(function(n){function e(t){const r=t;return p.string(r)}n.is=e})(wt||(wt={}));var $s;(function(n){function e(s,a,o){return{range:s,newText:a,annotationId:o}}n.replace=e;function t(s,a,o){return{range:{start:s,end:s},newText:a,annotationId:o}}n.insert=t;function r(s,a){return{range:s,newText:"",annotationId:a}}n.del=r;function i(s){const a=s;return Ct.is(a)&&(Dr.is(a.annotationId)||wt.is(a.annotationId))}n.is=i})($s||($s={}));var Fr;(function(n){function e(r,i){return{textDocument:r,edits:i}}n.create=e;function t(r){let i=r;return p.defined(i)&&Wr.is(i.textDocument)&&Array.isArray(i.edits)}n.is=t})(Fr||(Fr={}));var Gr;(function(n){function e(r,i,s){let a={kind:"create",uri:r};return i!==void 0&&(i.overwrite!==void 0||i.ignoreIfExists!==void 0)&&(a.options=i),s!==void 0&&(a.annotationId=s),a}n.create=e;function t(r){let i=r;return i&&i.kind==="create"&&p.string(i.uri)&&(i.options===void 0||(i.options.overwrite===void 0||p.boolean(i.options.overwrite))&&(i.options.ignoreIfExists===void 0||p.boolean(i.options.ignoreIfExists)))&&(i.annotationId===void 0||wt.is(i.annotationId))}n.is=t})(Gr||(Gr={}));var Ur;(function(n){function e(r,i,s,a){let o={kind:"rename",oldUri:r,newUri:i};return s!==void 0&&(s.overwrite!==void 0||s.ignoreIfExists!==void 0)&&(o.options=s),a!==void 0&&(o.annotationId=a),o}n.create=e;function t(r){let i=r;return i&&i.kind==="rename"&&p.string(i.oldUri)&&p.string(i.newUri)&&(i.options===void 0||(i.options.overwrite===void 0||p.boolean(i.options.overwrite))&&(i.options.ignoreIfExists===void 0||p.boolean(i.options.ignoreIfExists)))&&(i.annotationId===void 0||wt.is(i.annotationId))}n.is=t})(Ur||(Ur={}));var Br;(function(n){function e(r,i,s){let a={kind:"delete",uri:r};return i!==void 0&&(i.recursive!==void 0||i.ignoreIfNotExists!==void 0)&&(a.options=i),s!==void 0&&(a.annotationId=s),a}n.create=e;function t(r){let i=r;return i&&i.kind==="delete"&&p.string(i.uri)&&(i.options===void 0||(i.options.recursive===void 0||p.boolean(i.options.recursive))&&(i.options.ignoreIfNotExists===void 0||p.boolean(i.options.ignoreIfNotExists)))&&(i.annotationId===void 0||wt.is(i.annotationId))}n.is=t})(Br||(Br={}));var Vr;(function(n){function e(t){let r=t;return r&&(r.changes!==void 0||r.documentChanges!==void 0)&&(r.documentChanges===void 0||r.documentChanges.every(i=>p.string(i.kind)?Gr.is(i)||Ur.is(i)||Br.is(i):Fr.is(i)))}n.is=e})(Vr||(Vr={}));var Os;(function(n){function e(r){return{uri:r}}n.create=e;function t(r){let i=r;return p.defined(i)&&p.string(i.uri)}n.is=t})(Os||(Os={}));var bs;(function(n){function e(r,i){return{uri:r,version:i}}n.create=e;function t(r){let i=r;return p.defined(i)&&p.string(i.uri)&&p.integer(i.version)}n.is=t})(bs||(bs={}));var Wr;(function(n){function e(r,i){return{uri:r,version:i}}n.create=e;function t(r){let i=r;return p.defined(i)&&p.string(i.uri)&&(i.version===null||p.integer(i.version))}n.is=t})(Wr||(Wr={}));var Ps;(function(n){function e(r,i,s,a){return{uri:r,languageId:i,version:s,text:a}}n.create=e;function t(r){let i=r;return p.defined(i)&&p.string(i.uri)&&p.string(i.languageId)&&p.integer(i.version)&&p.string(i.text)}n.is=t})(Ps||(Ps={}));var jr;(function(n){n.PlainText="plaintext",n.Markdown="markdown";function e(t){const r=t;return r===n.PlainText||r===n.Markdown}n.is=e})(jr||(jr={}));var Yt;(function(n){function e(t){const r=t;return p.objectLiteral(t)&&jr.is(r.kind)&&p.string(r.value)}n.is=e})(Yt||(Yt={}));var Ms;(function(n){n.Text=1,n.Method=2,n.Function=3,n.Constructor=4,n.Field=5,n.Variable=6,n.Class=7,n.Interface=8,n.Module=9,n.Property=10,n.Unit=11,n.Value=12,n.Enum=13,n.Keyword=14,n.Snippet=15,n.Color=16,n.File=17,n.Reference=18,n.Folder=19,n.EnumMember=20,n.Constant=21,n.Struct=22,n.Event=23,n.Operator=24,n.TypeParameter=25})(Ms||(Ms={}));var Ds;(function(n){n.PlainText=1,n.Snippet=2})(Ds||(Ds={}));var Fs;(function(n){n.Deprecated=1})(Fs||(Fs={}));var Gs;(function(n){function e(r,i,s){return{newText:r,insert:i,replace:s}}n.create=e;function t(r){const i=r;return i&&p.string(i.newText)&&b.is(i.insert)&&b.is(i.replace)}n.is=t})(Gs||(Gs={}));var Us;(function(n){n.asIs=1,n.adjustIndentation=2})(Us||(Us={}));var Bs;(function(n){function e(t){const r=t;return r&&(p.string(r.detail)||r.detail===void 0)&&(p.string(r.description)||r.description===void 0)}n.is=e})(Bs||(Bs={}));var Vs;(function(n){function e(t){return{label:t}}n.create=e})(Vs||(Vs={}));var Ws;(function(n){function e(t,r){return{items:t||[],isIncomplete:!!r}}n.create=e})(Ws||(Ws={}));var bn;(function(n){function e(r){return r.replace(/[\\`*_{}[\]()#+\-.!]/g,"\\$&")}n.fromPlainText=e;function t(r){const i=r;return p.string(i)||p.objectLiteral(i)&&p.string(i.language)&&p.string(i.value)}n.is=t})(bn||(bn={}));var js;(function(n){function e(t){let r=t;return!!r&&p.objectLiteral(r)&&(Yt.is(r.contents)||bn.is(r.contents)||p.typedArray(r.contents,bn.is))&&(t.range===void 0||b.is(t.range))}n.is=e})(js||(js={}));var Hs;(function(n){function e(t,r){return r?{label:t,documentation:r}:{label:t}}n.create=e})(Hs||(Hs={}));var Ks;(function(n){function e(t,r,...i){let s={label:t};return p.defined(r)&&(s.documentation=r),p.defined(i)?s.parameters=i:s.parameters=[],s}n.create=e})(Ks||(Ks={}));var zs;(function(n){n.Text=1,n.Read=2,n.Write=3})(zs||(zs={}));var qs;(function(n){function e(t,r){let i={range:t};return p.number(r)&&(i.kind=r),i}n.create=e})(qs||(qs={}));var Ys;(function(n){n.File=1,n.Module=2,n.Namespace=3,n.Package=4,n.Class=5,n.Method=6,n.Property=7,n.Field=8,n.Constructor=9,n.Enum=10,n.Interface=11,n.Function=12,n.Variable=13,n.Constant=14,n.String=15,n.Number=16,n.Boolean=17,n.Array=18,n.Object=19,n.Key=20,n.Null=21,n.EnumMember=22,n.Struct=23,n.Event=24,n.Operator=25,n.TypeParameter=26})(Ys||(Ys={}));var Xs;(function(n){n.Deprecated=1})(Xs||(Xs={}));var Js;(function(n){function e(t,r,i,s,a){let o={name:t,kind:r,location:{uri:s,range:i}};return a&&(o.containerName=a),o}n.create=e})(Js||(Js={}));var Qs;(function(n){function e(t,r,i,s){return s!==void 0?{name:t,kind:r,location:{uri:i,range:s}}:{name:t,kind:r,location:{uri:i}}}n.create=e})(Qs||(Qs={}));var Zs;(function(n){function e(r,i,s,a,o,l){let c={name:r,detail:i,kind:s,range:a,selectionRange:o};return l!==void 0&&(c.children=l),c}n.create=e;function t(r){let i=r;return i&&p.string(i.name)&&p.number(i.kind)&&b.is(i.range)&&b.is(i.selectionRange)&&(i.detail===void 0||p.string(i.detail))&&(i.deprecated===void 0||p.boolean(i.deprecated))&&(i.children===void 0||Array.isArray(i.children))&&(i.tags===void 0||Array.isArray(i.tags))}n.is=t})(Zs||(Zs={}));var ea;(function(n){n.Empty="",n.QuickFix="quickfix",n.Refactor="refactor",n.RefactorExtract="refactor.extract",n.RefactorInline="refactor.inline",n.RefactorRewrite="refactor.rewrite",n.Source="source",n.SourceOrganizeImports="source.organizeImports",n.SourceFixAll="source.fixAll"})(ea||(ea={}));var Pn;(function(n){n.Invoked=1,n.Automatic=2})(Pn||(Pn={}));var ta;(function(n){function e(r,i,s){let a={diagnostics:r};return i!=null&&(a.only=i),s!=null&&(a.triggerKind=s),a}n.create=e;function t(r){let i=r;return p.defined(i)&&p.typedArray(i.diagnostics,On.is)&&(i.only===void 0||p.typedArray(i.only,p.string))&&(i.triggerKind===void 0||i.triggerKind===Pn.Invoked||i.triggerKind===Pn.Automatic)}n.is=t})(ta||(ta={}));var na;(function(n){function e(r,i,s){let a={title:r},o=!0;return typeof i=="string"?(o=!1,a.kind=i):Nt.is(i)?a.command=i:a.edit=i,o&&s!==void 0&&(a.kind=s),a}n.create=e;function t(r){let i=r;return i&&p.string(i.title)&&(i.diagnostics===void 0||p.typedArray(i.diagnostics,On.is))&&(i.kind===void 0||p.string(i.kind))&&(i.edit!==void 0||i.command!==void 0)&&(i.command===void 0||Nt.is(i.command))&&(i.isPreferred===void 0||p.boolean(i.isPreferred))&&(i.edit===void 0||Vr.is(i.edit))}n.is=t})(na||(na={}));var ra;(function(n){function e(r,i){let s={range:r};return p.defined(i)&&(s.data=i),s}n.create=e;function t(r){let i=r;return p.defined(i)&&b.is(i.range)&&(p.undefined(i.command)||Nt.is(i.command))}n.is=t})(ra||(ra={}));var ia;(function(n){function e(r,i){return{tabSize:r,insertSpaces:i}}n.create=e;function t(r){let i=r;return p.defined(i)&&p.uinteger(i.tabSize)&&p.boolean(i.insertSpaces)}n.is=t})(ia||(ia={}));var sa;(function(n){function e(r,i,s){return{range:r,target:i,data:s}}n.create=e;function t(r){let i=r;return p.defined(i)&&b.is(i.range)&&(p.undefined(i.target)||p.string(i.target))}n.is=t})(sa||(sa={}));var aa;(function(n){function e(r,i){return{range:r,parent:i}}n.create=e;function t(r){let i=r;return p.objectLiteral(i)&&b.is(i.range)&&(i.parent===void 0||n.is(i.parent))}n.is=t})(aa||(aa={}));var oa;(function(n){n.namespace="namespace",n.type="type",n.class="class",n.enum="enum",n.interface="interface",n.struct="struct",n.typeParameter="typeParameter",n.parameter="parameter",n.variable="variable",n.property="property",n.enumMember="enumMember",n.event="event",n.function="function",n.method="method",n.macro="macro",n.keyword="keyword",n.modifier="modifier",n.comment="comment",n.string="string",n.number="number",n.regexp="regexp",n.operator="operator",n.decorator="decorator"})(oa||(oa={}));var la;(function(n){n.declaration="declaration",n.definition="definition",n.readonly="readonly",n.static="static",n.deprecated="deprecated",n.abstract="abstract",n.async="async",n.modification="modification",n.documentation="documentation",n.defaultLibrary="defaultLibrary"})(la||(la={}));var ca;(function(n){function e(t){const r=t;return p.objectLiteral(r)&&(r.resultId===void 0||typeof r.resultId=="string")&&Array.isArray(r.data)&&(r.data.length===0||typeof r.data[0]=="number")}n.is=e})(ca||(ca={}));var ua;(function(n){function e(r,i){return{range:r,text:i}}n.create=e;function t(r){const i=r;return i!=null&&b.is(i.range)&&p.string(i.text)}n.is=t})(ua||(ua={}));var da;(function(n){function e(r,i,s){return{range:r,variableName:i,caseSensitiveLookup:s}}n.create=e;function t(r){const i=r;return i!=null&&b.is(i.range)&&p.boolean(i.caseSensitiveLookup)&&(p.string(i.variableName)||i.variableName===void 0)}n.is=t})(da||(da={}));var fa;(function(n){function e(r,i){return{range:r,expression:i}}n.create=e;function t(r){const i=r;return i!=null&&b.is(i.range)&&(p.string(i.expression)||i.expression===void 0)}n.is=t})(fa||(fa={}));var ha;(function(n){function e(r,i){return{frameId:r,stoppedLocation:i}}n.create=e;function t(r){const i=r;return p.defined(i)&&b.is(r.stoppedLocation)}n.is=t})(ha||(ha={}));var Hr;(function(n){n.Type=1,n.Parameter=2;function e(t){return t===1||t===2}n.is=e})(Hr||(Hr={}));var Kr;(function(n){function e(r){return{value:r}}n.create=e;function t(r){const i=r;return p.objectLiteral(i)&&(i.tooltip===void 0||p.string(i.tooltip)||Yt.is(i.tooltip))&&(i.location===void 0||$n.is(i.location))&&(i.command===void 0||Nt.is(i.command))}n.is=t})(Kr||(Kr={}));var pa;(function(n){function e(r,i,s){const a={position:r,label:i};return s!==void 0&&(a.kind=s),a}n.create=e;function t(r){const i=r;return p.objectLiteral(i)&&P.is(i.position)&&(p.string(i.label)||p.typedArray(i.label,Kr.is))&&(i.kind===void 0||Hr.is(i.kind))&&i.textEdits===void 0||p.typedArray(i.textEdits,Ct.is)&&(i.tooltip===void 0||p.string(i.tooltip)||Yt.is(i.tooltip))&&(i.paddingLeft===void 0||p.boolean(i.paddingLeft))&&(i.paddingRight===void 0||p.boolean(i.paddingRight))}n.is=t})(pa||(pa={}));var ma;(function(n){function e(t){return{kind:"snippet",value:t}}n.createSnippet=e})(ma||(ma={}));var ga;(function(n){function e(t,r,i,s){return{insertText:t,filterText:r,range:i,command:s}}n.create=e})(ga||(ga={}));var ya;(function(n){function e(t){return{items:t}}n.create=e})(ya||(ya={}));var Ta;(function(n){n.Invoked=0,n.Automatic=1})(Ta||(Ta={}));var Ra;(function(n){function e(t,r){return{range:t,text:r}}n.create=e})(Ra||(Ra={}));var Aa;(function(n){function e(t,r){return{triggerKind:t,selectedCompletionInfo:r}}n.create=e})(Aa||(Aa={}));var Ea;(function(n){function e(t){const r=t;return p.objectLiteral(r)&&br.is(r.uri)&&p.string(r.name)}n.is=e})(Ea||(Ea={}));var va;(function(n){function e(s,a,o,l){return new ep(s,a,o,l)}n.create=e;function t(s){let a=s;return!!(p.defined(a)&&p.string(a.uri)&&(p.undefined(a.languageId)||p.string(a.languageId))&&p.uinteger(a.lineCount)&&p.func(a.getText)&&p.func(a.positionAt)&&p.func(a.offsetAt))}n.is=t;function r(s,a){let o=s.getText(),l=i(a,(u,d)=>{let f=u.range.start.line-d.range.start.line;return f===0?u.range.start.character-d.range.start.character:f}),c=o.length;for(let u=l.length-1;u>=0;u--){let d=l[u],f=s.offsetAt(d.range.start),h=s.offsetAt(d.range.end);if(h<=c)o=o.substring(0,f)+d.newText+o.substring(h,o.length);else throw new Error("Overlapping edit");c=f}return o}n.applyEdits=r;function i(s,a){if(s.length<=1)return s;const o=s.length/2|0,l=s.slice(0,o),c=s.slice(o);i(l,a),i(c,a);let u=0,d=0,f=0;for(;u<l.length&&d<c.length;)a(l[u],c[d])<=0?s[f++]=l[u++]:s[f++]=c[d++];for(;u<l.length;)s[f++]=l[u++];for(;d<c.length;)s[f++]=c[d++];return s}})(va||(va={}));let ep=class{constructor(e,t,r,i){this._uri=e,this._languageId=t,this._version=r,this._content=i,this._lineOffsets=void 0}get uri(){return this._uri}get languageId(){return this._languageId}get version(){return this._version}getText(e){if(e){let t=this.offsetAt(e.start),r=this.offsetAt(e.end);return this._content.substring(t,r)}return this._content}update(e,t){this._content=e.text,this._version=t,this._lineOffsets=void 0}getLineOffsets(){if(this._lineOffsets===void 0){let e=[],t=this._content,r=!0;for(let i=0;i<t.length;i++){r&&(e.push(i),r=!1);let s=t.charAt(i);r=s==="\r"||s===`
`,s==="\r"&&i+1<t.length&&t.charAt(i+1)===`
`&&i++}r&&t.length>0&&e.push(t.length),this._lineOffsets=e}return this._lineOffsets}positionAt(e){e=Math.max(Math.min(e,this._content.length),0);let t=this.getLineOffsets(),r=0,i=t.length;if(i===0)return P.create(0,e);for(;r<i;){let a=Math.floor((r+i)/2);t[a]>e?i=a:r=a+1}let s=r-1;return P.create(s,e-t[s])}offsetAt(e){let t=this.getLineOffsets();if(e.line>=t.length)return this._content.length;if(e.line<0)return 0;let r=t[e.line],i=e.line+1<t.length?t[e.line+1]:this._content.length;return Math.max(Math.min(r+e.character,i),r)}get lineCount(){return this.getLineOffsets().length}};var p;(function(n){const e=Object.prototype.toString;function t(h){return typeof h<"u"}n.defined=t;function r(h){return typeof h>"u"}n.undefined=r;function i(h){return h===!0||h===!1}n.boolean=i;function s(h){return e.call(h)==="[object String]"}n.string=s;function a(h){return e.call(h)==="[object Number]"}n.number=a;function o(h,m,g){return e.call(h)==="[object Number]"&&m<=h&&h<=g}n.numberRange=o;function l(h){return e.call(h)==="[object Number]"&&-2147483648<=h&&h<=2147483647}n.integer=l;function c(h){return e.call(h)==="[object Number]"&&0<=h&&h<=2147483647}n.uinteger=c;function u(h){return e.call(h)==="[object Function]"}n.func=u;function d(h){return h!==null&&typeof h=="object"}n.objectLiteral=d;function f(h,m){return Array.isArray(h)&&h.every(m)}n.typedArray=f})(p||(p={}));class tp{constructor(){this.nodeStack=[]}get current(){return this.nodeStack[this.nodeStack.length-1]}buildRootNode(e){return this.rootNode=new Gl(e),this.rootNode.root=this.rootNode,this.nodeStack=[this.rootNode],this.rootNode}buildCompositeNode(e){const t=new xi;return t.grammarSource=e,t.root=this.rootNode,this.current.content.push(t),this.nodeStack.push(t),t}buildLeafNode(e,t){const r=new zr(e.startOffset,e.image.length,vr(e),e.tokenType,!1);return r.grammarSource=t,r.root=this.rootNode,this.current.content.push(r),r}removeNode(e){const t=e.container;if(t){const r=t.content.indexOf(e);r>=0&&t.content.splice(r,1)}}construct(e){const t=this.current;typeof e.$type=="string"&&(this.current.astNode=e),e.$cstNode=t;const r=this.nodeStack.pop();r?.content.length===0&&this.removeNode(r)}addHiddenTokens(e){for(const t of e){const r=new zr(t.startOffset,t.image.length,vr(t),t.tokenType,!0);r.root=this.rootNode,this.addHiddenToken(this.rootNode,r)}}addHiddenToken(e,t){const{offset:r,end:i}=t;for(let s=0;s<e.content.length;s++){const a=e.content[s],{offset:o,end:l}=a;if(It(a)&&r>o&&i<l){this.addHiddenToken(a,t);return}else if(i<=o){e.content.splice(s,0,t);return}}e.content.push(t)}}class Fl{get parent(){return this.container}get feature(){return this.grammarSource}get hidden(){return!1}get astNode(){var e,t;const r=typeof((e=this._astNode)===null||e===void 0?void 0:e.$type)=="string"?this._astNode:(t=this.container)===null||t===void 0?void 0:t.astNode;if(!r)throw new Error("This node has no associated AST element");return r}set astNode(e){this._astNode=e}get element(){return this.astNode}get text(){return this.root.fullText.substring(this.offset,this.end)}}class zr extends Fl{get offset(){return this._offset}get length(){return this._length}get end(){return this._offset+this._length}get hidden(){return this._hidden}get tokenType(){return this._tokenType}get range(){return this._range}constructor(e,t,r,i,s=!1){super(),this._hidden=s,this._offset=e,this._tokenType=i,this._length=t,this._range=r}}class xi extends Fl{constructor(){super(...arguments),this.content=new Ni(this)}get children(){return this.content}get offset(){var e,t;return(t=(e=this.firstNonHiddenNode)===null||e===void 0?void 0:e.offset)!==null&&t!==void 0?t:0}get length(){return this.end-this.offset}get end(){var e,t;return(t=(e=this.lastNonHiddenNode)===null||e===void 0?void 0:e.end)!==null&&t!==void 0?t:0}get range(){const e=this.firstNonHiddenNode,t=this.lastNonHiddenNode;if(e&&t){if(this._rangeCache===void 0){const{range:r}=e,{range:i}=t;this._rangeCache={start:r.start,end:i.end.line<r.start.line?r.start:i.end}}return this._rangeCache}else return{start:P.create(0,0),end:P.create(0,0)}}get firstNonHiddenNode(){for(const e of this.content)if(!e.hidden)return e;return this.content[0]}get lastNonHiddenNode(){for(let e=this.content.length-1;e>=0;e--){const t=this.content[e];if(!t.hidden)return t}return this.content[this.content.length-1]}}class Ni extends Array{constructor(e){super(),this.parent=e,Object.setPrototypeOf(this,Ni.prototype)}push(...e){return this.addParents(e),super.push(...e)}unshift(...e){return this.addParents(e),super.unshift(...e)}splice(e,t,...r){return this.addParents(r),super.splice(e,t,...r)}addParents(e){for(const t of e)t.container=this.parent}}class Gl extends xi{get text(){return this._text.substring(this.offset,this.end)}get fullText(){return this._text}constructor(e){super(),this._text="",this._text=e??""}}const qr=Symbol("Datatype");function gr(n){return n.$type===qr}const ka="​",Ul=n=>n.endsWith(ka)?n:n+ka;class Bl{constructor(e){this._unorderedGroups=new Map,this.lexer=e.parser.Lexer;const t=this.lexer.definition;this.wrapper=new ap(t,Object.assign(Object.assign({},e.parser.ParserConfig),{errorMessageProvider:e.parser.ParserErrorMessageProvider}))}alternatives(e,t){this.wrapper.wrapOr(e,t)}optional(e,t){this.wrapper.wrapOption(e,t)}many(e,t){this.wrapper.wrapMany(e,t)}atLeastOne(e,t){this.wrapper.wrapAtLeastOne(e,t)}isRecording(){return this.wrapper.IS_RECORDING}get unorderedGroups(){return this._unorderedGroups}getRuleStack(){return this.wrapper.RULE_STACK}finalize(){this.wrapper.wrapSelfAnalysis()}}class np extends Bl{get current(){return this.stack[this.stack.length-1]}constructor(e){super(e),this.nodeBuilder=new tp,this.stack=[],this.assignmentMap=new Map,this.linker=e.references.Linker,this.converter=e.parser.ValueConverter,this.astReflection=e.shared.AstReflection}rule(e,t){const r=e.fragment?void 0:hi(e)?qr:Yn(e),i=this.wrapper.DEFINE_RULE(Ul(e.name),this.startImplementation(r,t).bind(this));return e.entry&&(this.mainRule=i),i}parse(e){this.nodeBuilder.buildRootNode(e);const t=this.lexer.tokenize(e);this.wrapper.input=t.tokens;const r=this.mainRule.call(this.wrapper,{});return this.nodeBuilder.addHiddenTokens(t.hidden),this.unorderedGroups.clear(),{value:r,lexerErrors:t.errors,parserErrors:this.wrapper.errors}}startImplementation(e,t){return r=>{if(!this.isRecording()){const s={$type:e};this.stack.push(s),e===qr&&(s.value="")}let i;try{i=t(r)}catch{i=void 0}return!this.isRecording()&&i===void 0&&(i=this.construct()),i}}consume(e,t,r){const i=this.wrapper.wrapConsume(e,t);if(!this.isRecording()&&this.isValidToken(i)){const s=this.nodeBuilder.buildLeafNode(i,r),{assignment:a,isCrossRef:o}=this.getAssignment(r),l=this.current;if(a){const c=ut(r)?i.image:this.converter.convert(i.image,s);this.assign(a.operator,a.feature,c,s,o)}else if(gr(l)){let c=i.image;ut(r)||(c=this.converter.convert(c,s).toString()),l.value+=c}}}isValidToken(e){return!e.isInsertedInRecovery&&!isNaN(e.startOffset)&&typeof e.endOffset=="number"&&!isNaN(e.endOffset)}subrule(e,t,r,i){let s;this.isRecording()||(s=this.nodeBuilder.buildCompositeNode(r));const a=this.wrapper.wrapSubrule(e,t,i);!this.isRecording()&&s&&s.length>0&&this.performSubruleAssignment(a,r,s)}performSubruleAssignment(e,t,r){const{assignment:i,isCrossRef:s}=this.getAssignment(t);if(i)this.assign(i.operator,i.feature,e,r,s);else if(!i){const a=this.current;if(gr(a))a.value+=e.toString();else if(typeof e=="object"&&e){const o=e.$type,l=this.assignWithoutOverride(e,a);o&&(l.$type=o);const c=l;this.stack.pop(),this.stack.push(c)}}}action(e,t){if(!this.isRecording()){let r=this.current;if(!r.$cstNode&&t.feature&&t.operator){r=this.construct(!1);const s=r.$cstNode.feature;this.nodeBuilder.buildCompositeNode(s)}const i={$type:e};this.stack.pop(),this.stack.push(i),t.feature&&t.operator&&this.assign(t.operator,t.feature,r,r.$cstNode,!1)}}construct(e=!0){if(this.isRecording())return;const t=this.current;return bu(t),this.nodeBuilder.construct(t),e&&this.stack.pop(),gr(t)?this.converter.convert(t.value,t.$cstNode):(Mu(this.astReflection,t),t)}getAssignment(e){if(!this.assignmentMap.has(e)){const t=Kn(e,ct);this.assignmentMap.set(e,{assignment:t,isCrossRef:t?ci(t.terminal):!1})}return this.assignmentMap.get(e)}assign(e,t,r,i,s){const a=this.current;let o;switch(s&&typeof r=="string"?o=this.linker.buildReference(a,t,i,r):o=r,e){case"=":{a[t]=o;break}case"?=":{a[t]=!0;break}case"+=":Array.isArray(a[t])||(a[t]=[]),a[t].push(o)}}assignWithoutOverride(e,t){for(const[r,i]of Object.entries(t)){const s=e[r];s===void 0?e[r]=i:Array.isArray(s)&&Array.isArray(i)&&(i.push(...s),e[r]=i)}return e}get definitionErrors(){return this.wrapper.definitionErrors}}class rp{buildMismatchTokenMessage(e){return Rt.buildMismatchTokenMessage(e)}buildNotAllInputParsedMessage(e){return Rt.buildNotAllInputParsedMessage(e)}buildNoViableAltMessage(e){return Rt.buildNoViableAltMessage(e)}buildEarlyExitMessage(e){return Rt.buildEarlyExitMessage(e)}}class Vl extends rp{buildMismatchTokenMessage({expected:e,actual:t}){return`Expecting ${e.LABEL?"`"+e.LABEL+"`":e.name.endsWith(":KW")?`keyword '${e.name.substring(0,e.name.length-3)}'`:`token of type '${e.name}'`} but found \`${t.image}\`.`}buildNotAllInputParsedMessage({firstRedundant:e}){return`Expecting end of file but found \`${e.image}\`.`}}class ip extends Bl{constructor(){super(...arguments),this.tokens=[],this.elementStack=[],this.lastElementStack=[],this.nextTokenIndex=0,this.stackSize=0}action(){}construct(){}parse(e){this.resetState();const t=this.lexer.tokenize(e);return this.tokens=t.tokens,this.wrapper.input=[...this.tokens],this.mainRule.call(this.wrapper,{}),this.unorderedGroups.clear(),{tokens:this.tokens,elementStack:[...this.lastElementStack],tokenIndex:this.nextTokenIndex}}rule(e,t){const r=this.wrapper.DEFINE_RULE(Ul(e.name),this.startImplementation(t).bind(this));return e.entry&&(this.mainRule=r),r}resetState(){this.elementStack=[],this.lastElementStack=[],this.nextTokenIndex=0,this.stackSize=0}startImplementation(e){return t=>{const r=this.keepStackSize();try{e(t)}finally{this.resetStackSize(r)}}}removeUnexpectedElements(){this.elementStack.splice(this.stackSize)}keepStackSize(){const e=this.elementStack.length;return this.stackSize=e,e}resetStackSize(e){this.removeUnexpectedElements(),this.stackSize=e}consume(e,t,r){this.wrapper.wrapConsume(e,t),this.isRecording()||(this.lastElementStack=[...this.elementStack,r],this.nextTokenIndex=this.currIdx+1)}subrule(e,t,r,i){this.before(r),this.wrapper.wrapSubrule(e,t,i),this.after(r)}before(e){this.isRecording()||this.elementStack.push(e)}after(e){if(!this.isRecording()){const t=this.elementStack.lastIndexOf(e);t>=0&&this.elementStack.splice(t)}}get currIdx(){return this.wrapper.currIdx}}const sp={recoveryEnabled:!0,nodeLocationTracking:"full",skipValidations:!0,errorMessageProvider:new Vl};class ap extends hh{constructor(e,t){const r=t&&"maxLookahead"in t;super(e,Object.assign(Object.assign(Object.assign({},sp),{lookaheadStrategy:r?new Ai({maxLookahead:t.maxLookahead}):new bh}),t))}get IS_RECORDING(){return this.RECORDING_PHASE}DEFINE_RULE(e,t){return this.RULE(e,t)}wrapSelfAnalysis(){this.performSelfAnalysis()}wrapConsume(e,t){return this.consume(e,t)}wrapSubrule(e,t,r){return this.subrule(e,t,{ARGS:[r]})}wrapOr(e,t){this.or(e,t)}wrapOption(e,t){this.option(e,t)}wrapMany(e,t){this.many(e,t)}wrapAtLeastOne(e,t){this.atLeastOne(e,t)}}function Wl(n,e,t){return op({parser:e,tokens:t,rules:new Map,ruleNames:new Map},n),e}function op(n,e){const t=Bo(e,!1),r=Q(e.rules).filter(Ce).filter(i=>t.has(i));for(const i of r){const s=Object.assign(Object.assign({},n),{consume:1,optional:1,subrule:1,many:1,or:1});s.rules.set(i.name,n.parser.rule(i,ht(s,i.definition)))}}function ht(n,e,t=!1){let r;if(ut(e))r=pp(n,e);else if(Hn(e))r=lp(n,e);else if(ct(e))r=ht(n,e.terminal);else if(ci(e))r=jl(n,e);else if(dt(e))r=cp(n,e);else if(Ao(e))r=dp(n,e);else if(bo(e))r=fp(n,e);else if(ui(e))r=hp(n,e);else if(xu(e)){const i=n.consume++;r=()=>n.parser.consume(i,et,e)}else throw new no(e.$cstNode,`Unexpected element type: ${e.$type}`);return Hl(n,t?void 0:Mn(e),r,e.cardinality)}function lp(n,e){const t=Yn(e);return()=>n.parser.action(t,e)}function cp(n,e){const t=e.rule.ref;if(Ce(t)){const r=n.subrule++,i=e.arguments.length>0?up(t,e.arguments):()=>({});return s=>n.parser.subrule(r,Kl(n,t),e,i(s))}else if(mt(t)){const r=n.consume++,i=Yr(n,t.name);return()=>n.parser.consume(r,i,e)}else if(t)jn();else throw new no(e.$cstNode,`Undefined rule type: ${e.$type}`)}function up(n,e){const t=e.map(r=>He(r.value));return r=>{const i={};for(let s=0;s<t.length;s++){const a=n.parameters[s],o=t[s];i[a.name]=o(r)}return i}}function He(n){if(hu(n)){const e=He(n.left),t=He(n.right);return r=>e(r)||t(r)}else if(fu(n)){const e=He(n.left),t=He(n.right);return r=>e(r)&&t(r)}else if(mu(n)){const e=He(n.value);return t=>!e(t)}else if(Tu(n)){const e=n.parameter.ref.name;return t=>t!==void 0&&t[e]===!0}else if(du(n)){const e=!!n.true;return()=>e}jn()}function dp(n,e){if(e.elements.length===1)return ht(n,e.elements[0]);{const t=[];for(const i of e.elements){const s={ALT:ht(n,i,!0)},a=Mn(i);a&&(s.GATE=He(a)),t.push(s)}const r=n.or++;return i=>n.parser.alternatives(r,t.map(s=>{const a={ALT:()=>s.ALT(i)},o=s.GATE;return o&&(a.GATE=()=>o(i)),a}))}}function fp(n,e){if(e.elements.length===1)return ht(n,e.elements[0]);const t=[];for(const o of e.elements){const l={ALT:ht(n,o,!0)},c=Mn(o);c&&(l.GATE=He(c)),t.push(l)}const r=n.or++,i=(o,l)=>{const c=l.getRuleStack().join("-");return`uGroup_${o}_${c}`},s=o=>n.parser.alternatives(r,t.map((l,c)=>{const u={ALT:()=>!0},d=n.parser;u.ALT=()=>{if(l.ALT(o),!d.isRecording()){const h=i(r,d);d.unorderedGroups.get(h)||d.unorderedGroups.set(h,[]);const m=d.unorderedGroups.get(h);typeof m?.[c]>"u"&&(m[c]=!0)}};const f=l.GATE;return f?u.GATE=()=>f(o):u.GATE=()=>{const h=d.unorderedGroups.get(i(r,d));return!h?.[c]},u})),a=Hl(n,Mn(e),s,"*");return o=>{a(o),n.parser.isRecording()||n.parser.unorderedGroups.delete(i(r,n.parser))}}function hp(n,e){const t=e.elements.map(r=>ht(n,r));return r=>t.forEach(i=>i(r))}function Mn(n){if(ui(n))return n.guardCondition}function jl(n,e,t=e.terminal){if(t)if(dt(t)&&Ce(t.rule.ref)){const r=n.subrule++;return i=>n.parser.subrule(r,Kl(n,t.rule.ref),e,i)}else if(dt(t)&&mt(t.rule.ref)){const r=n.consume++,i=Yr(n,t.rule.ref.name);return()=>n.parser.consume(r,i,e)}else if(ut(t)){const r=n.consume++,i=Yr(n,t.value);return()=>n.parser.consume(r,i,e)}else throw new Error("Could not build cross reference parser");else{if(!e.type.ref)throw new Error("Could not resolve reference to type: "+e.type.$refText);const r=jo(e.type.ref),i=r?.terminal;if(!i)throw new Error("Could not find name assignment for type: "+Yn(e.type.ref));return jl(n,e,i)}}function pp(n,e){const t=n.consume++,r=n.tokens[e.value];if(!r)throw new Error("Could not find token for keyword: "+e.value);return()=>n.parser.consume(t,r,e)}function Hl(n,e,t,r){const i=e&&He(e);if(!r)if(i){const s=n.or++;return a=>n.parser.alternatives(s,[{ALT:()=>t(a),GATE:()=>i(a)},{ALT:Ts(),GATE:()=>!i(a)}])}else return t;if(r==="*"){const s=n.many++;return a=>n.parser.many(s,{DEF:()=>t(a),GATE:i?()=>i(a):void 0})}else if(r==="+"){const s=n.many++;if(i){const a=n.or++;return o=>n.parser.alternatives(a,[{ALT:()=>n.parser.atLeastOne(s,{DEF:()=>t(o)}),GATE:()=>i(o)},{ALT:Ts(),GATE:()=>!i(o)}])}else return a=>n.parser.atLeastOne(s,{DEF:()=>t(a)})}else if(r==="?"){const s=n.optional++;return a=>n.parser.optional(s,{DEF:()=>t(a),GATE:i?()=>i(a):void 0})}else jn()}function Kl(n,e){const t=mp(n,e),r=n.rules.get(t);if(!r)throw new Error(`Rule "${t}" not found."`);return r}function mp(n,e){if(Ce(e))return e.name;if(n.ruleNames.has(e))return n.ruleNames.get(e);{let t=e,r=t.$container,i=e.$type;for(;!Ce(r);)(ui(r)||Ao(r)||bo(r))&&(i=r.elements.indexOf(t).toString()+":"+i),t=r,r=r.$container;return i=r.name+":"+i,n.ruleNames.set(e,i),i}}function Yr(n,e){const t=n.tokens[e];if(!t)throw new Error(`Token "${e}" not found."`);return t}function gp(n){const e=n.Grammar,t=n.parser.Lexer,r=new ip(n);return Wl(e,r,t.definition),r.finalize(),r}function yp(n){const e=Tp(n);return e.finalize(),e}function Tp(n){const e=n.Grammar,t=n.parser.Lexer,r=new np(n);return Wl(e,r,t.definition)}class zl{buildTokens(e,t){const r=Q(Bo(e,!1)),i=this.buildTerminalTokens(r),s=this.buildKeywordTokens(r,i,t);return i.forEach(a=>{const o=a.PATTERN;typeof o=="object"&&o&&"test"in o&&qi(o)?s.unshift(a):s.push(a)}),s}buildTerminalTokens(e){return e.filter(mt).filter(t=>!t.fragment).map(t=>this.buildTerminalToken(t)).toArray()}buildTerminalToken(e){const t=pi(e),r=this.requiresCustomPattern(t)?this.regexPatternFunction(t):t,i={name:e.name,PATTERN:r,LINE_BREAKS:!0};return e.hidden&&(i.GROUP=qi(t)?de.SKIPPED:"hidden"),i}requiresCustomPattern(e){return e.flags.includes("u")?!0:!!(e.source.includes("?<=")||e.source.includes("?<!"))}regexPatternFunction(e){const t=new RegExp(e,e.flags+"y");return(r,i)=>(t.lastIndex=i,t.exec(r))}buildKeywordTokens(e,t,r){return e.filter(Ce).flatMap(i=>Zt(i).filter(ut)).distinct(i=>i.value).toArray().sort((i,s)=>s.value.length-i.value.length).map(i=>this.buildKeywordToken(i,t,!!r?.caseInsensitive))}buildKeywordToken(e,t,r){return{name:e.value,PATTERN:this.buildKeywordPattern(e,r),LONGER_ALT:this.findLongerAlt(e,t)}}buildKeywordPattern(e,t){return t?new RegExp(ju(e.value)):e.value}findLongerAlt(e,t){return t.reduce((r,i)=>{const s=i?.PATTERN;return s?.source&&Hu("^"+s.source+"$",e.value)&&r.push(i),r},[])}}class ql{convert(e,t){let r=t.grammarSource;if(ci(r)&&(r=Yu(r)),dt(r)){const i=r.rule.ref;if(!i)throw new Error("This cst node was not parsed by a rule.");return this.runConverter(i,e,t)}return e}runConverter(e,t,r){var i;switch(e.name.toUpperCase()){case"INT":return je.convertInt(t);case"STRING":return je.convertString(t);case"ID":return je.convertID(t)}switch((i=nd(e))===null||i===void 0?void 0:i.toLowerCase()){case"number":return je.convertNumber(t);case"boolean":return je.convertBoolean(t);case"bigint":return je.convertBigint(t);case"date":return je.convertDate(t);default:return t}}}var je;(function(n){function e(c){let u="";for(let d=1;d<c.length-1;d++){const f=c.charAt(d);if(f==="\\"){const h=c.charAt(++d);u+=t(h)}else u+=f}return u}n.convertString=e;function t(c){switch(c){case"b":return"\b";case"f":return"\f";case"n":return`
`;case"r":return"\r";case"t":return"	";case"v":return"\v";case"0":return"\0";default:return c}}function r(c){return c.charAt(0)==="^"?c.substring(1):c}n.convertID=r;function i(c){return parseInt(c)}n.convertInt=i;function s(c){return BigInt(c)}n.convertBigint=s;function a(c){return new Date(c)}n.convertDate=a;function o(c){return Number(c)}n.convertNumber=o;function l(c){return c.toLowerCase()==="true"}n.convertBoolean=l})(je||(je={}));var Xt={},rr={};Object.defineProperty(rr,"__esModule",{value:!0});let Xr;function Jr(){if(Xr===void 0)throw new Error("No runtime abstraction layer installed");return Xr}(function(n){function e(t){if(t===void 0)throw new Error("No runtime abstraction layer provided");Xr=t}n.install=e})(Jr||(Jr={}));rr.default=Jr;var se={};Object.defineProperty(se,"__esModule",{value:!0});se.stringArray=se.array=se.func=se.error=se.number=se.string=se.boolean=void 0;function Rp(n){return n===!0||n===!1}se.boolean=Rp;function Yl(n){return typeof n=="string"||n instanceof String}se.string=Yl;function Ap(n){return typeof n=="number"||n instanceof Number}se.number=Ap;function Ep(n){return n instanceof Error}se.error=Ep;function vp(n){return typeof n=="function"}se.func=vp;function Xl(n){return Array.isArray(n)}se.array=Xl;function kp(n){return Xl(n)&&n.every(e=>Yl(e))}se.stringArray=kp;var _t={};Object.defineProperty(_t,"__esModule",{value:!0});_t.Emitter=_t.Event=void 0;const Sp=rr;var Sa;(function(n){const e={dispose(){}};n.None=function(){return e}})(Sa||(_t.Event=Sa={}));class Ip{add(e,t=null,r){this._callbacks||(this._callbacks=[],this._contexts=[]),this._callbacks.push(e),this._contexts.push(t),Array.isArray(r)&&r.push({dispose:()=>this.remove(e,t)})}remove(e,t=null){if(!this._callbacks)return;let r=!1;for(let i=0,s=this._callbacks.length;i<s;i++)if(this._callbacks[i]===e)if(this._contexts[i]===t){this._callbacks.splice(i,1),this._contexts.splice(i,1);return}else r=!0;if(r)throw new Error("When adding a listener with a context, you should remove it with the same context")}invoke(...e){if(!this._callbacks)return[];const t=[],r=this._callbacks.slice(0),i=this._contexts.slice(0);for(let s=0,a=r.length;s<a;s++)try{t.push(r[s].apply(i[s],e))}catch(o){(0,Sp.default)().console.error(o)}return t}isEmpty(){return!this._callbacks||this._callbacks.length===0}dispose(){this._callbacks=void 0,this._contexts=void 0}}class ir{constructor(e){this._options=e}get event(){return this._event||(this._event=(e,t,r)=>{this._callbacks||(this._callbacks=new Ip),this._options&&this._options.onFirstListenerAdd&&this._callbacks.isEmpty()&&this._options.onFirstListenerAdd(this),this._callbacks.add(e,t);const i={dispose:()=>{this._callbacks&&(this._callbacks.remove(e,t),i.dispose=ir._noop,this._options&&this._options.onLastListenerRemove&&this._callbacks.isEmpty()&&this._options.onLastListenerRemove(this))}};return Array.isArray(r)&&r.push(i),i}),this._event}fire(e){this._callbacks&&this._callbacks.invoke.call(this._callbacks,e)}dispose(){this._callbacks&&(this._callbacks.dispose(),this._callbacks=void 0)}}_t.Emitter=ir;ir._noop=function(){};var X;Object.defineProperty(Xt,"__esModule",{value:!0});var Qr=Xt.CancellationTokenSource=X=Xt.CancellationToken=void 0;const xp=rr,Np=se,Zr=_t;var Dn;(function(n){n.None=Object.freeze({isCancellationRequested:!1,onCancellationRequested:Zr.Event.None}),n.Cancelled=Object.freeze({isCancellationRequested:!0,onCancellationRequested:Zr.Event.None});function e(t){const r=t;return r&&(r===n.None||r===n.Cancelled||Np.boolean(r.isCancellationRequested)&&!!r.onCancellationRequested)}n.is=e})(Dn||(X=Xt.CancellationToken=Dn={}));const Cp=Object.freeze(function(n,e){const t=(0,xp.default)().timer.setTimeout(n.bind(e),0);return{dispose(){t.dispose()}}});class Ia{constructor(){this._isCancelled=!1}cancel(){this._isCancelled||(this._isCancelled=!0,this._emitter&&(this._emitter.fire(void 0),this.dispose()))}get isCancellationRequested(){return this._isCancelled}get onCancellationRequested(){return this._isCancelled?Cp:(this._emitter||(this._emitter=new Zr.Emitter),this._emitter.event)}dispose(){this._emitter&&(this._emitter.dispose(),this._emitter=void 0)}}class wp{get token(){return this._token||(this._token=new Ia),this._token}cancel(){this._token?this._token.cancel():this._token=Dn.Cancelled}dispose(){this._token?this._token instanceof Ia&&this._token.dispose():this._token=Dn.None}}Qr=Xt.CancellationTokenSource=wp;function _p(){return new Promise(n=>{typeof setImmediate>"u"?setTimeout(n,0):setImmediate(n)})}let xa=0,Lp=10;const Fn=Symbol("OperationCancelled");function Ci(n){return n===Fn}async function Oe(n){if(n===X.None)return;const e=Date.now();if(e-xa>=Lp&&(xa=e,await _p()),n.isCancellationRequested)throw Fn}class wi{constructor(){this.promise=new Promise((e,t)=>{this.resolve=r=>(e(r),this),this.reject=r=>(t(r),this)})}}class Jt{constructor(e,t,r,i){this._uri=e,this._languageId=t,this._version=r,this._content=i,this._lineOffsets=void 0}get uri(){return this._uri}get languageId(){return this._languageId}get version(){return this._version}getText(e){if(e){const t=this.offsetAt(e.start),r=this.offsetAt(e.end);return this._content.substring(t,r)}return this._content}update(e,t){for(const r of e)if(Jt.isIncremental(r)){const i=Ql(r.range),s=this.offsetAt(i.start),a=this.offsetAt(i.end);this._content=this._content.substring(0,s)+r.text+this._content.substring(a,this._content.length);const o=Math.max(i.start.line,0),l=Math.max(i.end.line,0);let c=this._lineOffsets;const u=Na(r.text,!1,s);if(l-o===u.length)for(let f=0,h=u.length;f<h;f++)c[f+o+1]=u[f];else u.length<1e4?c.splice(o+1,l-o,...u):this._lineOffsets=c=c.slice(0,o+1).concat(u,c.slice(l+1));const d=r.text.length-(a-s);if(d!==0)for(let f=o+1+u.length,h=c.length;f<h;f++)c[f]=c[f]+d}else if(Jt.isFull(r))this._content=r.text,this._lineOffsets=void 0;else throw new Error("Unknown change event received");this._version=t}getLineOffsets(){return this._lineOffsets===void 0&&(this._lineOffsets=Na(this._content,!0)),this._lineOffsets}positionAt(e){e=Math.max(Math.min(e,this._content.length),0);const t=this.getLineOffsets();let r=0,i=t.length;if(i===0)return{line:0,character:e};for(;r<i;){const a=Math.floor((r+i)/2);t[a]>e?i=a:r=a+1}const s=r-1;return e=this.ensureBeforeEOL(e,t[s]),{line:s,character:e-t[s]}}offsetAt(e){const t=this.getLineOffsets();if(e.line>=t.length)return this._content.length;if(e.line<0)return 0;const r=t[e.line];if(e.character<=0)return r;const i=e.line+1<t.length?t[e.line+1]:this._content.length,s=Math.min(r+e.character,i);return this.ensureBeforeEOL(s,r)}ensureBeforeEOL(e,t){for(;e>t&&Jl(this._content.charCodeAt(e-1));)e--;return e}get lineCount(){return this.getLineOffsets().length}static isIncremental(e){const t=e;return t!=null&&typeof t.text=="string"&&t.range!==void 0&&(t.rangeLength===void 0||typeof t.rangeLength=="number")}static isFull(e){const t=e;return t!=null&&typeof t.text=="string"&&t.range===void 0&&t.rangeLength===void 0}}var ei;(function(n){function e(i,s,a,o){return new Jt(i,s,a,o)}n.create=e;function t(i,s,a){if(i instanceof Jt)return i.update(s,a),i;throw new Error("TextDocument.update: document must be created by TextDocument.create")}n.update=t;function r(i,s){const a=i.getText(),o=ti(s.map($p),(u,d)=>{const f=u.range.start.line-d.range.start.line;return f===0?u.range.start.character-d.range.start.character:f});let l=0;const c=[];for(const u of o){const d=i.offsetAt(u.range.start);if(d<l)throw new Error("Overlapping edit");d>l&&c.push(a.substring(l,d)),u.newText.length&&c.push(u.newText),l=i.offsetAt(u.range.end)}return c.push(a.substr(l)),c.join("")}n.applyEdits=r})(ei||(ei={}));function ti(n,e){if(n.length<=1)return n;const t=n.length/2|0,r=n.slice(0,t),i=n.slice(t);ti(r,e),ti(i,e);let s=0,a=0,o=0;for(;s<r.length&&a<i.length;)e(r[s],i[a])<=0?n[o++]=r[s++]:n[o++]=i[a++];for(;s<r.length;)n[o++]=r[s++];for(;a<i.length;)n[o++]=i[a++];return n}function Na(n,e,t=0){const r=e?[t]:[];for(let i=0;i<n.length;i++){const s=n.charCodeAt(i);Jl(s)&&(s===13&&i+1<n.length&&n.charCodeAt(i+1)===10&&i++,r.push(t+i+1))}return r}function Jl(n){return n===13||n===10}function Ql(n){const e=n.start,t=n.end;return e.line>t.line||e.line===t.line&&e.character>t.character?{start:t,end:e}:n}function $p(n){const e=Ql(n.range);return e!==n.range?{newText:n.newText,range:e}:n}var Zl;(()=>{var n={470:i=>{function s(l){if(typeof l!="string")throw new TypeError("Path must be a string. Received "+JSON.stringify(l))}function a(l,c){for(var u,d="",f=0,h=-1,m=0,g=0;g<=l.length;++g){if(g<l.length)u=l.charCodeAt(g);else{if(u===47)break;u=47}if(u===47){if(!(h===g-1||m===1))if(h!==g-1&&m===2){if(d.length<2||f!==2||d.charCodeAt(d.length-1)!==46||d.charCodeAt(d.length-2)!==46){if(d.length>2){var A=d.lastIndexOf("/");if(A!==d.length-1){A===-1?(d="",f=0):f=(d=d.slice(0,A)).length-1-d.lastIndexOf("/"),h=g,m=0;continue}}else if(d.length===2||d.length===1){d="",f=0,h=g,m=0;continue}}c&&(d.length>0?d+="/..":d="..",f=2)}else d.length>0?d+="/"+l.slice(h+1,g):d=l.slice(h+1,g),f=g-h-1;h=g,m=0}else u===46&&m!==-1?++m:m=-1}return d}var o={resolve:function(){for(var l,c="",u=!1,d=arguments.length-1;d>=-1&&!u;d--){var f;d>=0?f=arguments[d]:(l===void 0&&(l=process.cwd()),f=l),s(f),f.length!==0&&(c=f+"/"+c,u=f.charCodeAt(0)===47)}return c=a(c,!u),u?c.length>0?"/"+c:"/":c.length>0?c:"."},normalize:function(l){if(s(l),l.length===0)return".";var c=l.charCodeAt(0)===47,u=l.charCodeAt(l.length-1)===47;return(l=a(l,!c)).length!==0||c||(l="."),l.length>0&&u&&(l+="/"),c?"/"+l:l},isAbsolute:function(l){return s(l),l.length>0&&l.charCodeAt(0)===47},join:function(){if(arguments.length===0)return".";for(var l,c=0;c<arguments.length;++c){var u=arguments[c];s(u),u.length>0&&(l===void 0?l=u:l+="/"+u)}return l===void 0?".":o.normalize(l)},relative:function(l,c){if(s(l),s(c),l===c||(l=o.resolve(l))===(c=o.resolve(c)))return"";for(var u=1;u<l.length&&l.charCodeAt(u)===47;++u);for(var d=l.length,f=d-u,h=1;h<c.length&&c.charCodeAt(h)===47;++h);for(var m=c.length-h,g=f<m?f:m,A=-1,T=0;T<=g;++T){if(T===g){if(m>g){if(c.charCodeAt(h+T)===47)return c.slice(h+T+1);if(T===0)return c.slice(h+T)}else f>g&&(l.charCodeAt(u+T)===47?A=T:T===0&&(A=0));break}var E=l.charCodeAt(u+T);if(E!==c.charCodeAt(h+T))break;E===47&&(A=T)}var R="";for(T=u+A+1;T<=d;++T)T!==d&&l.charCodeAt(T)!==47||(R.length===0?R+="..":R+="/..");return R.length>0?R+c.slice(h+A):(h+=A,c.charCodeAt(h)===47&&++h,c.slice(h))},_makeLong:function(l){return l},dirname:function(l){if(s(l),l.length===0)return".";for(var c=l.charCodeAt(0),u=c===47,d=-1,f=!0,h=l.length-1;h>=1;--h)if((c=l.charCodeAt(h))===47){if(!f){d=h;break}}else f=!1;return d===-1?u?"/":".":u&&d===1?"//":l.slice(0,d)},basename:function(l,c){if(c!==void 0&&typeof c!="string")throw new TypeError('"ext" argument must be a string');s(l);var u,d=0,f=-1,h=!0;if(c!==void 0&&c.length>0&&c.length<=l.length){if(c.length===l.length&&c===l)return"";var m=c.length-1,g=-1;for(u=l.length-1;u>=0;--u){var A=l.charCodeAt(u);if(A===47){if(!h){d=u+1;break}}else g===-1&&(h=!1,g=u+1),m>=0&&(A===c.charCodeAt(m)?--m==-1&&(f=u):(m=-1,f=g))}return d===f?f=g:f===-1&&(f=l.length),l.slice(d,f)}for(u=l.length-1;u>=0;--u)if(l.charCodeAt(u)===47){if(!h){d=u+1;break}}else f===-1&&(h=!1,f=u+1);return f===-1?"":l.slice(d,f)},extname:function(l){s(l);for(var c=-1,u=0,d=-1,f=!0,h=0,m=l.length-1;m>=0;--m){var g=l.charCodeAt(m);if(g!==47)d===-1&&(f=!1,d=m+1),g===46?c===-1?c=m:h!==1&&(h=1):c!==-1&&(h=-1);else if(!f){u=m+1;break}}return c===-1||d===-1||h===0||h===1&&c===d-1&&c===u+1?"":l.slice(c,d)},format:function(l){if(l===null||typeof l!="object")throw new TypeError('The "pathObject" argument must be of type Object. Received type '+typeof l);return function(c,u){var d=u.dir||u.root,f=u.base||(u.name||"")+(u.ext||"");return d?d===u.root?d+f:d+"/"+f:f}(0,l)},parse:function(l){s(l);var c={root:"",dir:"",base:"",ext:"",name:""};if(l.length===0)return c;var u,d=l.charCodeAt(0),f=d===47;f?(c.root="/",u=1):u=0;for(var h=-1,m=0,g=-1,A=!0,T=l.length-1,E=0;T>=u;--T)if((d=l.charCodeAt(T))!==47)g===-1&&(A=!1,g=T+1),d===46?h===-1?h=T:E!==1&&(E=1):h!==-1&&(E=-1);else if(!A){m=T+1;break}return h===-1||g===-1||E===0||E===1&&h===g-1&&h===m+1?g!==-1&&(c.base=c.name=m===0&&f?l.slice(1,g):l.slice(m,g)):(m===0&&f?(c.name=l.slice(1,h),c.base=l.slice(1,g)):(c.name=l.slice(m,h),c.base=l.slice(m,g)),c.ext=l.slice(h,g)),m>0?c.dir=l.slice(0,m-1):f&&(c.dir="/"),c},sep:"/",delimiter:":",win32:null,posix:null};o.posix=o,i.exports=o}},e={};function t(i){var s=e[i];if(s!==void 0)return s.exports;var a=e[i]={exports:{}};return n[i](a,a.exports,t),a.exports}t.d=(i,s)=>{for(var a in s)t.o(s,a)&&!t.o(i,a)&&Object.defineProperty(i,a,{enumerable:!0,get:s[a]})},t.o=(i,s)=>Object.prototype.hasOwnProperty.call(i,s),t.r=i=>{typeof Symbol<"u"&&Symbol.toStringTag&&Object.defineProperty(i,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(i,"__esModule",{value:!0})};var r={};(()=>{let i;t.r(r),t.d(r,{URI:()=>f,Utils:()=>Se}),typeof process=="object"?i=process.platform==="win32":typeof navigator=="object"&&(i=navigator.userAgent.indexOf("Windows")>=0);const s=/^\w[\w\d+.-]*$/,a=/^\//,o=/^\/\//;function l(v,y){if(!v.scheme&&y)throw new Error(`[UriError]: Scheme is missing: {scheme: "", authority: "${v.authority}", path: "${v.path}", query: "${v.query}", fragment: "${v.fragment}"}`);if(v.scheme&&!s.test(v.scheme))throw new Error("[UriError]: Scheme contains illegal characters.");if(v.path){if(v.authority){if(!a.test(v.path))throw new Error('[UriError]: If a URI contains an authority component, then the path component must either be empty or begin with a slash ("/") character')}else if(o.test(v.path))throw new Error('[UriError]: If a URI does not contain an authority component, then the path cannot begin with two slash characters ("//")')}}const c="",u="/",d=/^(([^:/?#]+?):)?(\/\/([^/?#]*))?([^?#]*)(\?([^#]*))?(#(.*))?/;class f{static isUri(y){return y instanceof f||!!y&&typeof y.authority=="string"&&typeof y.fragment=="string"&&typeof y.path=="string"&&typeof y.query=="string"&&typeof y.scheme=="string"&&typeof y.fsPath=="string"&&typeof y.with=="function"&&typeof y.toString=="function"}scheme;authority;path;query;fragment;constructor(y,x,S,O,$,L=!1){typeof y=="object"?(this.scheme=y.scheme||c,this.authority=y.authority||c,this.path=y.path||c,this.query=y.query||c,this.fragment=y.fragment||c):(this.scheme=function(ye,z){return ye||z?ye:"file"}(y,L),this.authority=x||c,this.path=function(ye,z){switch(ye){case"https":case"http":case"file":z?z[0]!==u&&(z=u+z):z=u}return z}(this.scheme,S||c),this.query=O||c,this.fragment=$||c,l(this,L))}get fsPath(){return E(this)}with(y){if(!y)return this;let{scheme:x,authority:S,path:O,query:$,fragment:L}=y;return x===void 0?x=this.scheme:x===null&&(x=c),S===void 0?S=this.authority:S===null&&(S=c),O===void 0?O=this.path:O===null&&(O=c),$===void 0?$=this.query:$===null&&($=c),L===void 0?L=this.fragment:L===null&&(L=c),x===this.scheme&&S===this.authority&&O===this.path&&$===this.query&&L===this.fragment?this:new m(x,S,O,$,L)}static parse(y,x=!1){const S=d.exec(y);return S?new m(S[2]||c,ne(S[4]||c),ne(S[5]||c),ne(S[7]||c),ne(S[9]||c),x):new m(c,c,c,c,c)}static file(y){let x=c;if(i&&(y=y.replace(/\\/g,u)),y[0]===u&&y[1]===u){const S=y.indexOf(u,2);S===-1?(x=y.substring(2),y=u):(x=y.substring(2,S),y=y.substring(S)||u)}return new m("file",x,y,c,c)}static from(y){const x=new m(y.scheme,y.authority,y.path,y.query,y.fragment);return l(x,!0),x}toString(y=!1){return R(this,y)}toJSON(){return this}static revive(y){if(y){if(y instanceof f)return y;{const x=new m(y);return x._formatted=y.external,x._fsPath=y._sep===h?y.fsPath:null,x}}return y}}const h=i?1:void 0;class m extends f{_formatted=null;_fsPath=null;get fsPath(){return this._fsPath||(this._fsPath=E(this)),this._fsPath}toString(y=!1){return y?R(this,!0):(this._formatted||(this._formatted=R(this,!1)),this._formatted)}toJSON(){const y={$mid:1};return this._fsPath&&(y.fsPath=this._fsPath,y._sep=h),this._formatted&&(y.external=this._formatted),this.path&&(y.path=this.path),this.scheme&&(y.scheme=this.scheme),this.authority&&(y.authority=this.authority),this.query&&(y.query=this.query),this.fragment&&(y.fragment=this.fragment),y}}const g={58:"%3A",47:"%2F",63:"%3F",35:"%23",91:"%5B",93:"%5D",64:"%40",33:"%21",36:"%24",38:"%26",39:"%27",40:"%28",41:"%29",42:"%2A",43:"%2B",44:"%2C",59:"%3B",61:"%3D",32:"%20"};function A(v,y,x){let S,O=-1;for(let $=0;$<v.length;$++){const L=v.charCodeAt($);if(L>=97&&L<=122||L>=65&&L<=90||L>=48&&L<=57||L===45||L===46||L===95||L===126||y&&L===47||x&&L===91||x&&L===93||x&&L===58)O!==-1&&(S+=encodeURIComponent(v.substring(O,$)),O=-1),S!==void 0&&(S+=v.charAt($));else{S===void 0&&(S=v.substr(0,$));const ye=g[L];ye!==void 0?(O!==-1&&(S+=encodeURIComponent(v.substring(O,$)),O=-1),S+=ye):O===-1&&(O=$)}}return O!==-1&&(S+=encodeURIComponent(v.substring(O))),S!==void 0?S:v}function T(v){let y;for(let x=0;x<v.length;x++){const S=v.charCodeAt(x);S===35||S===63?(y===void 0&&(y=v.substr(0,x)),y+=g[S]):y!==void 0&&(y+=v[x])}return y!==void 0?y:v}function E(v,y){let x;return x=v.authority&&v.path.length>1&&v.scheme==="file"?`//${v.authority}${v.path}`:v.path.charCodeAt(0)===47&&(v.path.charCodeAt(1)>=65&&v.path.charCodeAt(1)<=90||v.path.charCodeAt(1)>=97&&v.path.charCodeAt(1)<=122)&&v.path.charCodeAt(2)===58?v.path[1].toLowerCase()+v.path.substr(2):v.path,i&&(x=x.replace(/\//g,"\\")),x}function R(v,y){const x=y?T:A;let S="",{scheme:O,authority:$,path:L,query:ye,fragment:z}=v;if(O&&(S+=O,S+=":"),($||O==="file")&&(S+=u,S+=u),$){let V=$.indexOf("@");if(V!==-1){const st=$.substr(0,V);$=$.substr(V+1),V=st.lastIndexOf(":"),V===-1?S+=x(st,!1,!1):(S+=x(st.substr(0,V),!1,!1),S+=":",S+=x(st.substr(V+1),!1,!0)),S+="@"}$=$.toLowerCase(),V=$.lastIndexOf(":"),V===-1?S+=x($,!1,!0):(S+=x($.substr(0,V),!1,!0),S+=$.substr(V))}if(L){if(L.length>=3&&L.charCodeAt(0)===47&&L.charCodeAt(2)===58){const V=L.charCodeAt(1);V>=65&&V<=90&&(L=`/${String.fromCharCode(V+32)}:${L.substr(3)}`)}else if(L.length>=2&&L.charCodeAt(1)===58){const V=L.charCodeAt(0);V>=65&&V<=90&&(L=`${String.fromCharCode(V+32)}:${L.substr(2)}`)}S+=x(L,!0,!1)}return ye&&(S+="?",S+=x(ye,!1,!1)),z&&(S+="#",S+=y?z:A(z,!1,!1)),S}function N(v){try{return decodeURIComponent(v)}catch{return v.length>3?v.substr(0,3)+N(v.substr(3)):v}}const F=/(%[0-9A-Za-z][0-9A-Za-z])+/g;function ne(v){return v.match(F)?v.replace(F,y=>N(y)):v}var _e=t(470);const ge=_e.posix||_e,De="/";var Se;(function(v){v.joinPath=function(y,...x){return y.with({path:ge.join(y.path,...x)})},v.resolvePath=function(y,...x){let S=y.path,O=!1;S[0]!==De&&(S=De+S,O=!0);let $=ge.resolve(S,...x);return O&&$[0]===De&&!y.authority&&($=$.substring(1)),y.with({path:$})},v.dirname=function(y){if(y.path.length===0||y.path===De)return y;let x=ge.dirname(y.path);return x.length===1&&x.charCodeAt(0)===46&&(x=""),y.with({path:x})},v.basename=function(y){return ge.basename(y.path)},v.extname=function(y){return ge.extname(y.path)}})(Se||(Se={}))})(),Zl=r})();const{URI:Lt,Utils:jt}=Zl;var nt;(function(n){n.basename=jt.basename,n.dirname=jt.dirname,n.extname=jt.extname,n.joinPath=jt.joinPath,n.resolvePath=jt.resolvePath;function e(r,i){return r?.toString()===i?.toString()}n.equals=e;function t(r,i){const s=typeof r=="string"?r:r.path,a=typeof i=="string"?i:i.path,o=s.split("/").filter(f=>f.length>0),l=a.split("/").filter(f=>f.length>0);let c=0;for(;c<o.length&&o[c]===l[c];c++);const u="../".repeat(o.length-c),d=l.slice(c).join("/");return u+d}n.relative=t})(nt||(nt={}));var U;(function(n){n[n.Changed=0]="Changed",n[n.Parsed=1]="Parsed",n[n.IndexedContent=2]="IndexedContent",n[n.ComputedScopes=3]="ComputedScopes",n[n.Linked=4]="Linked",n[n.IndexedReferences=5]="IndexedReferences",n[n.Validated=6]="Validated"})(U||(U={}));class Op{constructor(e){this.serviceRegistry=e.ServiceRegistry,this.textDocuments=e.workspace.TextDocuments,this.fileSystemProvider=e.workspace.FileSystemProvider}async fromUri(e,t=X.None){const r=await this.fileSystemProvider.readFile(e);return this.createAsync(e,r,t)}fromTextDocument(e,t,r){return t=t??Lt.parse(e.uri),r?this.createAsync(t,e,r):this.create(t,e)}fromString(e,t,r){return r?this.createAsync(t,e,r):this.create(t,e)}fromModel(e,t){return this.create(t,{$model:e})}create(e,t){if(typeof t=="string"){const r=this.parse(e,t);return this.createLangiumDocument(r,e,void 0,t)}else if("$model"in t){const r={value:t.$model,parserErrors:[],lexerErrors:[]};return this.createLangiumDocument(r,e)}else{const r=this.parse(e,t.getText());return this.createLangiumDocument(r,e,t)}}async createAsync(e,t,r){if(typeof t=="string"){const i=await this.parseAsync(e,t,r);return this.createLangiumDocument(i,e,void 0,t)}else{const i=await this.parseAsync(e,t.getText(),r);return this.createLangiumDocument(i,e,t)}}createLangiumDocument(e,t,r,i){let s;if(r)s={parseResult:e,uri:t,state:U.Parsed,references:[],textDocument:r};else{const a=this.createTextDocumentGetter(t,i);s={parseResult:e,uri:t,state:U.Parsed,references:[],get textDocument(){return a()}}}return e.value.$document=s,s}async update(e,t){var r,i;const s=(r=e.parseResult.value.$cstNode)===null||r===void 0?void 0:r.root.fullText,a=(i=this.textDocuments)===null||i===void 0?void 0:i.get(e.uri.toString()),o=a?a.getText():await this.fileSystemProvider.readFile(e.uri);if(a)Object.defineProperty(e,"textDocument",{value:a});else{const l=this.createTextDocumentGetter(e.uri,o);Object.defineProperty(e,"textDocument",{get:l})}return s!==o&&(e.parseResult=await this.parseAsync(e.uri,o,t),e.parseResult.value.$document=e),e.state=U.Parsed,e}parse(e,t){return this.serviceRegistry.getServices(e).parser.LangiumParser.parse(t)}parseAsync(e,t,r){return this.serviceRegistry.getServices(e).parser.AsyncParser.parse(t,r)}createTextDocumentGetter(e,t){const r=this.serviceRegistry;let i;return()=>i??(i=ei.create(e.toString(),r.getServices(e).LanguageMetaData.languageId,0,t??""))}}class bp{constructor(e){this.documentMap=new Map,this.langiumDocumentFactory=e.workspace.LangiumDocumentFactory}get all(){return Q(this.documentMap.values())}addDocument(e){const t=e.uri.toString();if(this.documentMap.has(t))throw new Error(`A document with the URI '${t}' is already present.`);this.documentMap.set(t,e)}getDocument(e){const t=e.toString();return this.documentMap.get(t)}async getOrCreateDocument(e,t){let r=this.getDocument(e);return r||(r=await this.langiumDocumentFactory.fromUri(e,t),this.addDocument(r),r)}createDocument(e,t,r){if(r)return this.langiumDocumentFactory.fromString(t,e,r).then(i=>(this.addDocument(i),i));{const i=this.langiumDocumentFactory.fromString(t,e);return this.addDocument(i),i}}hasDocument(e){return this.documentMap.has(e.toString())}invalidateDocument(e){const t=e.toString(),r=this.documentMap.get(t);return r&&(r.state=U.Changed,r.precomputedScopes=void 0,r.references=[],r.diagnostics=void 0),r}deleteDocument(e){const t=e.toString(),r=this.documentMap.get(t);return r&&(r.state=U.Changed,this.documentMap.delete(t)),r}}class Pp{constructor(e){this.reflection=e.shared.AstReflection,this.langiumDocuments=()=>e.shared.workspace.LangiumDocuments,this.scopeProvider=e.references.ScopeProvider,this.astNodeLocator=e.workspace.AstNodeLocator}async link(e,t=X.None){for(const r of At(e.parseResult.value))await Oe(t),Fo(r).forEach(i=>this.doLink(i,e))}doLink(e,t){const r=e.reference;if(r._ref===void 0)try{const i=this.getCandidate(e);if(fn(i))r._ref=i;else if(r._nodeDescription=i,this.langiumDocuments().hasDocument(i.documentUri)){const s=this.loadAstNode(i);r._ref=s??this.createLinkingError(e,i)}}catch(i){r._ref=Object.assign(Object.assign({},e),{message:`An error occurred while resolving reference to '${r.$refText}': ${i}`})}t.references.push(r)}unlink(e){for(const t of e.references)delete t._ref,delete t._nodeDescription;e.references=[]}getCandidate(e){const r=this.scopeProvider.getScope(e).getElement(e.reference.$refText);return r??this.createLinkingError(e)}buildReference(e,t,r,i){const s=this,a={$refNode:r,$refText:i,get ref(){var o;if(ae(this._ref))return this._ref;if(Qc(this._nodeDescription)){const l=s.loadAstNode(this._nodeDescription);this._ref=l??s.createLinkingError({reference:a,container:e,property:t},this._nodeDescription)}else if(this._ref===void 0){const l=s.getLinkedNode({reference:a,container:e,property:t});if(l.error&&Ue(e).state<U.ComputedScopes)return;this._ref=(o=l.node)!==null&&o!==void 0?o:l.error,this._nodeDescription=l.descr}return ae(this._ref)?this._ref:void 0},get $nodeDescription(){return this._nodeDescription},get error(){return fn(this._ref)?this._ref:void 0}};return a}getLinkedNode(e){try{const t=this.getCandidate(e);if(fn(t))return{error:t};const r=this.loadAstNode(t);return r?{node:r,descr:t}:{descr:t,error:this.createLinkingError(e,t)}}catch(t){return{error:Object.assign(Object.assign({},e),{message:`An error occurred while resolving reference to '${e.reference.$refText}': ${t}`})}}}loadAstNode(e){if(e.node)return e.node;const t=this.langiumDocuments().getDocument(e.documentUri);if(t)return this.astNodeLocator.getAstNode(t.parseResult.value,e.path)}createLinkingError(e,t){const r=Ue(e.container);r.state<U.ComputedScopes&&console.warn(`Attempted reference resolution before document reached ComputedScopes state (${r.uri}).`);const i=this.reflection.getReferenceType(e);return Object.assign(Object.assign({},e),{message:`Could not resolve reference to ${i} named '${e.reference.$refText}'.`,targetDescription:t})}}function Mp(n){return typeof n.name=="string"}class Dp{getName(e){if(Mp(e))return e.name}getNameNode(e){return Wo(e.$cstNode,"name")}}class Fp{constructor(e){this.nameProvider=e.references.NameProvider,this.index=e.shared.workspace.IndexManager,this.nodeLocator=e.workspace.AstNodeLocator}findDeclaration(e){if(e){const t=ed(e),r=e.astNode;if(t&&r){const i=r[t.feature];if(Ge(i))return i.ref;if(Array.isArray(i)){for(const s of i)if(Ge(s)&&s.$refNode&&s.$refNode.offset<=e.offset&&s.$refNode.end>=e.end)return s.ref}}if(r){const i=this.nameProvider.getNameNode(r);if(i&&(i===e||tu(e,i)))return r}}}findDeclarationNode(e){const t=this.findDeclaration(e);if(t?.$cstNode){const r=this.nameProvider.getNameNode(t);return r??t.$cstNode}}findReferences(e,t){const r=[];if(t.includeDeclaration){const s=this.getReferenceToSelf(e);s&&r.push(s)}let i=this.index.findAllReferences(e,this.nodeLocator.getAstNodePath(e));return t.documentUri&&(i=i.filter(s=>nt.equals(s.sourceUri,t.documentUri))),r.push(...i),Q(r)}getReferenceToSelf(e){const t=this.nameProvider.getNameNode(e);if(t){const r=Ue(e),i=this.nodeLocator.getAstNodePath(e);return{sourceUri:r.uri,sourcePath:i,targetUri:r.uri,targetPath:i,segment:Rn(t),local:!0}}}}class _i{constructor(e){if(this.map=new Map,e)for(const[t,r]of e)this.add(t,r)}get size(){return Ar.sum(Q(this.map.values()).map(e=>e.length))}clear(){this.map.clear()}delete(e,t){if(t===void 0)return this.map.delete(e);{const r=this.map.get(e);if(r){const i=r.indexOf(t);if(i>=0)return r.length===1?this.map.delete(e):r.splice(i,1),!0}return!1}}get(e){var t;return(t=this.map.get(e))!==null&&t!==void 0?t:[]}has(e,t){if(t===void 0)return this.map.has(e);{const r=this.map.get(e);return r?r.indexOf(t)>=0:!1}}add(e,t){return this.map.has(e)?this.map.get(e).push(t):this.map.set(e,[t]),this}addAll(e,t){return this.map.has(e)?this.map.get(e).push(...t):this.map.set(e,Array.from(t)),this}forEach(e){this.map.forEach((t,r)=>t.forEach(i=>e(i,r,this)))}[Symbol.iterator](){return this.entries().iterator()}entries(){return Q(this.map.entries()).flatMap(([e,t])=>t.map(r=>[e,r]))}keys(){return Q(this.map.keys())}values(){return Q(this.map.values()).flat()}entriesGroupedByKey(){return Q(this.map.entries())}}class Ca{get size(){return this.map.size}constructor(e){if(this.map=new Map,this.inverse=new Map,e)for(const[t,r]of e)this.set(t,r)}clear(){this.map.clear(),this.inverse.clear()}set(e,t){return this.map.set(e,t),this.inverse.set(t,e),this}get(e){return this.map.get(e)}getKey(e){return this.inverse.get(e)}delete(e){const t=this.map.get(e);return t!==void 0?(this.map.delete(e),this.inverse.delete(t),!0):!1}}class Gp{constructor(e){this.nameProvider=e.references.NameProvider,this.descriptions=e.workspace.AstNodeDescriptionProvider}async computeExports(e,t=X.None){return this.computeExportsForNode(e.parseResult.value,e,void 0,t)}async computeExportsForNode(e,t,r=di,i=X.None){const s=[];this.exportNode(e,s,t);for(const a of r(e))await Oe(i),this.exportNode(a,s,t);return s}exportNode(e,t,r){const i=this.nameProvider.getName(e);i&&t.push(this.descriptions.createDescription(e,i,r))}async computeLocalScopes(e,t=X.None){const r=e.parseResult.value,i=new _i;for(const s of Zt(r))await Oe(t),this.processNode(s,e,i);return i}processNode(e,t,r){const i=e.$container;if(i){const s=this.nameProvider.getName(e);s&&r.add(i,this.descriptions.createDescription(e,s,t))}}}class wa{constructor(e,t,r){var i;this.elements=e,this.outerScope=t,this.caseInsensitive=(i=r?.caseInsensitive)!==null&&i!==void 0?i:!1}getAllElements(){return this.outerScope?this.elements.concat(this.outerScope.getAllElements()):this.elements}getElement(e){const t=this.caseInsensitive?this.elements.find(r=>r.name.toLowerCase()===e.toLowerCase()):this.elements.find(r=>r.name===e);if(t)return t;if(this.outerScope)return this.outerScope.getElement(e)}}class Up{constructor(e,t,r){var i;this.elements=new Map,this.caseInsensitive=(i=r?.caseInsensitive)!==null&&i!==void 0?i:!1;for(const s of e){const a=this.caseInsensitive?s.name.toLowerCase():s.name;this.elements.set(a,s)}this.outerScope=t}getElement(e){const t=this.caseInsensitive?e.toLowerCase():e,r=this.elements.get(t);if(r)return r;if(this.outerScope)return this.outerScope.getElement(e)}getAllElements(){let e=Q(this.elements.values());return this.outerScope&&(e=e.concat(this.outerScope.getAllElements())),e}}class ec{constructor(){this.toDispose=[],this.isDisposed=!1}onDispose(e){this.toDispose.push(e)}dispose(){this.throwIfDisposed(),this.clear(),this.isDisposed=!0,this.toDispose.forEach(e=>e.dispose())}throwIfDisposed(){if(this.isDisposed)throw new Error("This cache has already been disposed")}}class Bp extends ec{constructor(){super(...arguments),this.cache=new Map}has(e){return this.throwIfDisposed(),this.cache.has(e)}set(e,t){this.throwIfDisposed(),this.cache.set(e,t)}get(e,t){if(this.throwIfDisposed(),this.cache.has(e))return this.cache.get(e);if(t){const r=t();return this.cache.set(e,r),r}else return}delete(e){return this.throwIfDisposed(),this.cache.delete(e)}clear(){this.throwIfDisposed(),this.cache.clear()}}class Vp extends ec{constructor(e){super(),this.cache=new Map,this.converter=e??(t=>t)}has(e,t){return this.throwIfDisposed(),this.cacheForContext(e).has(t)}set(e,t,r){this.throwIfDisposed(),this.cacheForContext(e).set(t,r)}get(e,t,r){this.throwIfDisposed();const i=this.cacheForContext(e);if(i.has(t))return i.get(t);if(r){const s=r();return i.set(t,s),s}else return}delete(e,t){return this.throwIfDisposed(),this.cacheForContext(e).delete(t)}clear(e){if(this.throwIfDisposed(),e){const t=this.converter(e);this.cache.delete(t)}else this.cache.clear()}cacheForContext(e){const t=this.converter(e);let r=this.cache.get(t);return r||(r=new Map,this.cache.set(t,r)),r}}class Wp extends Bp{constructor(e){super(),this.onDispose(e.workspace.DocumentBuilder.onUpdate(()=>{this.clear()}))}}class jp{constructor(e){this.reflection=e.shared.AstReflection,this.nameProvider=e.references.NameProvider,this.descriptions=e.workspace.AstNodeDescriptionProvider,this.indexManager=e.shared.workspace.IndexManager,this.globalScopeCache=new Wp(e.shared)}getScope(e){const t=[],r=this.reflection.getReferenceType(e),i=Ue(e.container).precomputedScopes;if(i){let a=e.container;do{const o=i.get(a);o.length>0&&t.push(Q(o).filter(l=>this.reflection.isSubtype(l.type,r))),a=a.$container}while(a)}let s=this.getGlobalScope(r,e);for(let a=t.length-1;a>=0;a--)s=this.createScope(t[a],s);return s}createScope(e,t,r){return new wa(Q(e),t,r)}createScopeForNodes(e,t,r){const i=Q(e).map(s=>{const a=this.nameProvider.getName(s);if(a)return this.descriptions.createDescription(s,a)}).nonNullable();return new wa(i,t,r)}getGlobalScope(e,t){return this.globalScopeCache.get(e,()=>new Up(this.indexManager.allElements(e)))}}function Hp(n){return typeof n.$comment=="string"}function _a(n){return typeof n=="object"&&!!n&&("$ref"in n||"$error"in n)}class Kp{constructor(e){this.ignoreProperties=new Set(["$container","$containerProperty","$containerIndex","$document","$cstNode"]),this.langiumDocuments=e.shared.workspace.LangiumDocuments,this.astNodeLocator=e.workspace.AstNodeLocator,this.nameProvider=e.references.NameProvider,this.commentProvider=e.documentation.CommentProvider}serialize(e,t={}){const r=t?.replacer,i=(a,o)=>this.replacer(a,o,t),s=r?(a,o)=>r(a,o,i):i;try{return this.currentDocument=Ue(e),JSON.stringify(e,s,t?.space)}finally{this.currentDocument=void 0}}deserialize(e,t={}){const r=JSON.parse(e);return this.linkNode(r,r,t),r}replacer(e,t,{refText:r,sourceText:i,textRegions:s,comments:a,uriConverter:o}){var l,c,u,d;if(!this.ignoreProperties.has(e))if(Ge(t)){const f=t.ref,h=r?t.$refText:void 0;if(f){const m=Ue(f);let g="";this.currentDocument&&this.currentDocument!==m&&(o?g=o(m.uri,t):g=m.uri.toString());const A=this.astNodeLocator.getAstNodePath(f);return{$ref:`${g}#${A}`,$refText:h}}else return{$error:(c=(l=t.error)===null||l===void 0?void 0:l.message)!==null&&c!==void 0?c:"Could not resolve reference",$refText:h}}else if(ae(t)){let f;if(s&&(f=this.addAstNodeRegionWithAssignmentsTo(Object.assign({},t)),(!e||t.$document)&&f?.$textRegion&&(f.$textRegion.documentURI=(u=this.currentDocument)===null||u===void 0?void 0:u.uri.toString())),i&&!e&&(f??(f=Object.assign({},t)),f.$sourceText=(d=t.$cstNode)===null||d===void 0?void 0:d.text),a){f??(f=Object.assign({},t));const h=this.commentProvider.getComment(t);h&&(f.$comment=h.replace(/\r/g,""))}return f??t}else return t}addAstNodeRegionWithAssignmentsTo(e){const t=r=>({offset:r.offset,end:r.end,length:r.length,range:r.range});if(e.$cstNode){const r=e.$textRegion=t(e.$cstNode),i=r.assignments={};return Object.keys(e).filter(s=>!s.startsWith("$")).forEach(s=>{const a=Ju(e.$cstNode,s).map(t);a.length!==0&&(i[s]=a)}),e}}linkNode(e,t,r,i,s,a){for(const[l,c]of Object.entries(e))if(Array.isArray(c))for(let u=0;u<c.length;u++){const d=c[u];_a(d)?c[u]=this.reviveReference(e,l,t,d,r):ae(d)&&this.linkNode(d,t,r,e,l,u)}else _a(c)?e[l]=this.reviveReference(e,l,t,c,r):ae(c)&&this.linkNode(c,t,r,e,l);const o=e;o.$container=i,o.$containerProperty=s,o.$containerIndex=a}reviveReference(e,t,r,i,s){let a=i.$refText,o=i.$error;if(i.$ref){const l=this.getRefNode(r,i.$ref,s.uriConverter);if(ae(l))return a||(a=this.nameProvider.getName(l)),{$refText:a??"",ref:l};o=l}if(o){const l={$refText:a??""};return l.error={container:e,property:t,message:o,reference:l},l}else return}getRefNode(e,t,r){try{const i=t.indexOf("#");if(i===0){const l=this.astNodeLocator.getAstNode(e,t.substring(1));return l||"Could not resolve path: "+t}if(i<0){const l=r?r(t):Lt.parse(t),c=this.langiumDocuments.getDocument(l);return c?c.parseResult.value:"Could not find document for URI: "+t}const s=r?r(t.substring(0,i)):Lt.parse(t.substring(0,i)),a=this.langiumDocuments.getDocument(s);if(!a)return"Could not find document for URI: "+t;if(i===t.length-1)return a.parseResult.value;const o=this.astNodeLocator.getAstNode(a.parseResult.value,t.substring(i+1));return o||"Could not resolve URI: "+t}catch(i){return String(i)}}}class zp{register(e){if(!this.singleton&&!this.map){this.singleton=e;return}if(!this.map&&(this.map={},this.singleton)){for(const t of this.singleton.LanguageMetaData.fileExtensions)this.map[t]=this.singleton;this.singleton=void 0}for(const t of e.LanguageMetaData.fileExtensions)this.map[t]!==void 0&&this.map[t]!==e&&console.warn(`The file extension ${t} is used by multiple languages. It is now assigned to '${e.LanguageMetaData.languageId}'.`),this.map[t]=e}getServices(e){if(this.singleton!==void 0)return this.singleton;if(this.map===void 0)throw new Error("The service registry is empty. Use `register` to register the services of a language.");const t=nt.extname(e),r=this.map[t];if(!r)throw new Error(`The service registry contains no services for the extension '${t}'.`);return r}get all(){return this.singleton!==void 0?[this.singleton]:this.map!==void 0?Object.values(this.map):[]}}function La(n){return{code:n}}var Gn;(function(n){n.all=["fast","slow","built-in"]})(Gn||(Gn={}));class qp{constructor(e){this.entries=new _i,this.reflection=e.shared.AstReflection}register(e,t=this,r="fast"){if(r==="built-in")throw new Error("The 'built-in' category is reserved for lexer, parser, and linker errors.");for(const[i,s]of Object.entries(e)){const a=s;if(Array.isArray(a))for(const o of a){const l={check:this.wrapValidationException(o,t),category:r};this.addEntry(i,l)}else if(typeof a=="function"){const o={check:this.wrapValidationException(a,t),category:r};this.addEntry(i,o)}}}wrapValidationException(e,t){return async(r,i,s)=>{try{await e.call(t,r,i,s)}catch(a){if(Ci(a))throw a;console.error("An error occurred during validation:",a);const o=a instanceof Error?a.message:String(a);a instanceof Error&&a.stack&&console.error(a.stack),i("error","An error occurred during validation: "+o,{node:r})}}}addEntry(e,t){if(e==="AstNode"){this.entries.add("AstNode",t);return}for(const r of this.reflection.getAllSubTypes(e))this.entries.add(r,t)}getChecks(e,t){let r=Q(this.entries.get(e)).concat(this.entries.get("AstNode"));return t&&(r=r.filter(i=>t.includes(i.category))),r.map(i=>i.check)}}class Yp{constructor(e){this.validationRegistry=e.validation.ValidationRegistry,this.metadata=e.LanguageMetaData}async validateDocument(e,t={},r=X.None){const i=e.parseResult,s=[];if(await Oe(r),(!t.categories||t.categories.includes("built-in"))&&(this.processLexingErrors(i,s,t),t.stopAfterLexingErrors&&s.some(a=>{var o;return((o=a.data)===null||o===void 0?void 0:o.code)===Je.LexingError})||(this.processParsingErrors(i,s,t),t.stopAfterParsingErrors&&s.some(a=>{var o;return((o=a.data)===null||o===void 0?void 0:o.code)===Je.ParsingError}))||(this.processLinkingErrors(e,s,t),t.stopAfterLinkingErrors&&s.some(a=>{var o;return((o=a.data)===null||o===void 0?void 0:o.code)===Je.LinkingError}))))return s;try{s.push(...await this.validateAst(i.value,t,r))}catch(a){if(Ci(a))throw a;console.error("An error occurred during validation:",a)}return await Oe(r),s}processLexingErrors(e,t,r){for(const i of e.lexerErrors){const s={severity:yr("error"),range:{start:{line:i.line-1,character:i.column-1},end:{line:i.line-1,character:i.column+i.length-1}},message:i.message,data:La(Je.LexingError),source:this.getSource()};t.push(s)}}processParsingErrors(e,t,r){for(const i of e.parserErrors){let s;if(isNaN(i.token.startOffset)){if("previousToken"in i){const a=i.previousToken;if(isNaN(a.startOffset)){const o={line:0,character:0};s={start:o,end:o}}else{const o={line:a.endLine-1,character:a.endColumn};s={start:o,end:o}}}}else s=vr(i.token);if(s){const a={severity:yr("error"),range:s,message:i.message,data:La(Je.ParsingError),source:this.getSource()};t.push(a)}}}processLinkingErrors(e,t,r){for(const i of e.references){const s=i.error;if(s){const a={node:s.container,property:s.property,index:s.index,data:{code:Je.LinkingError,containerType:s.container.$type,property:s.property,refText:s.reference.$refText}};t.push(this.toDiagnostic("error",s.message,a))}}}async validateAst(e,t,r=X.None){const i=[],s=(a,o,l)=>{i.push(this.toDiagnostic(a,o,l))};return await Promise.all(At(e).map(async a=>{await Oe(r);const o=this.validationRegistry.getChecks(a.$type,t.categories);for(const l of o)await l(a,s,r)})),i}toDiagnostic(e,t,r){return{message:t,range:Xp(r),severity:yr(e),code:r.code,codeDescription:r.codeDescription,tags:r.tags,relatedInformation:r.relatedInformation,data:r.data,source:this.getSource()}}getSource(){return this.metadata.languageId}}function Xp(n){if(n.range)return n.range;let e;return typeof n.property=="string"?e=Wo(n.node.$cstNode,n.property,n.index):typeof n.keyword=="string"&&(e=Qu(n.node.$cstNode,n.keyword,n.index)),e??(e=n.node.$cstNode),e?e.range:{start:{line:0,character:0},end:{line:0,character:0}}}function yr(n){switch(n){case"error":return 1;case"warning":return 2;case"info":return 3;case"hint":return 4;default:throw new Error("Invalid diagnostic severity: "+n)}}var Je;(function(n){n.LexingError="lexing-error",n.ParsingError="parsing-error",n.LinkingError="linking-error"})(Je||(Je={}));class Jp{constructor(e){this.astNodeLocator=e.workspace.AstNodeLocator,this.nameProvider=e.references.NameProvider}createDescription(e,t,r=Ue(e)){t??(t=this.nameProvider.getName(e));const i=this.astNodeLocator.getAstNodePath(e);if(!t)throw new Error(`Node at path ${i} has no name.`);let s;const a=()=>{var o;return s??(s=Rn((o=this.nameProvider.getNameNode(e))!==null&&o!==void 0?o:e.$cstNode))};return{node:e,name:t,get nameSegment(){return a()},selectionSegment:Rn(e.$cstNode),type:e.$type,documentUri:r.uri,path:i}}}class Qp{constructor(e){this.nodeLocator=e.workspace.AstNodeLocator}async createDescriptions(e,t=X.None){const r=[],i=e.parseResult.value;for(const s of At(i))await Oe(t),Fo(s).filter(a=>!fn(a)).forEach(a=>{const o=this.createDescription(a);o&&r.push(o)});return r}createDescription(e){const t=e.reference.$nodeDescription,r=e.reference.$refNode;if(!t||!r)return;const i=Ue(e.container).uri;return{sourceUri:i,sourcePath:this.nodeLocator.getAstNodePath(e.container),targetUri:t.documentUri,targetPath:t.path,segment:Rn(r),local:nt.equals(t.documentUri,i)}}}class Zp{constructor(){this.segmentSeparator="/",this.indexSeparator="@"}getAstNodePath(e){if(e.$container){const t=this.getAstNodePath(e.$container),r=this.getPathSegment(e);return t+this.segmentSeparator+r}return""}getPathSegment({$containerProperty:e,$containerIndex:t}){if(!e)throw new Error("Missing '$containerProperty' in AST node.");return t!==void 0?e+this.indexSeparator+t:e}getAstNode(e,t){return t.split(this.segmentSeparator).reduce((i,s)=>{if(!i||s.length===0)return i;const a=s.indexOf(this.indexSeparator);if(a>0){const o=s.substring(0,a),l=parseInt(s.substring(a+1)),c=i[o];return c?.[l]}return i[s]},e)}}class em{constructor(e){this._ready=new wi,this.settings={},this.workspaceConfig=!1,this.serviceRegistry=e.ServiceRegistry}get ready(){return this._ready.promise}initialize(e){var t,r;this.workspaceConfig=(r=(t=e.capabilities.workspace)===null||t===void 0?void 0:t.configuration)!==null&&r!==void 0?r:!1}async initialized(e){if(this.workspaceConfig){if(e.register){const t=this.serviceRegistry.all;e.register({section:t.map(r=>this.toSectionName(r.LanguageMetaData.languageId))})}if(e.fetchConfiguration){const t=this.serviceRegistry.all.map(i=>({section:this.toSectionName(i.LanguageMetaData.languageId)})),r=await e.fetchConfiguration(t);t.forEach((i,s)=>{this.updateSectionConfiguration(i.section,r[s])})}}this._ready.resolve()}updateConfiguration(e){e.settings&&Object.keys(e.settings).forEach(t=>{this.updateSectionConfiguration(t,e.settings[t])})}updateSectionConfiguration(e,t){this.settings[e]=t}async getConfiguration(e,t){await this.ready;const r=this.toSectionName(e);if(this.settings[r])return this.settings[r][t]}toSectionName(e){return`${e}`}}var Un;(function(n){function e(t){return{dispose:async()=>await t()}}n.create=e})(Un||(Un={}));class tm{constructor(e){this.updateBuildOptions={validation:{categories:["built-in","fast"]}},this.updateListeners=[],this.buildPhaseListeners=new _i,this.buildState=new Map,this.documentBuildWaiters=new Map,this.currentState=U.Changed,this.langiumDocuments=e.workspace.LangiumDocuments,this.langiumDocumentFactory=e.workspace.LangiumDocumentFactory,this.indexManager=e.workspace.IndexManager,this.serviceRegistry=e.ServiceRegistry}async build(e,t={},r=X.None){var i,s;for(const a of e){const o=a.uri.toString();if(a.state===U.Validated){if(typeof t.validation=="boolean"&&t.validation)a.state=U.IndexedReferences,a.diagnostics=void 0,this.buildState.delete(o);else if(typeof t.validation=="object"){const l=this.buildState.get(o),c=(i=l?.result)===null||i===void 0?void 0:i.validationChecks;if(c){const d=((s=t.validation.categories)!==null&&s!==void 0?s:Gn.all).filter(f=>!c.includes(f));d.length>0&&(this.buildState.set(o,{completed:!1,options:{validation:Object.assign(Object.assign({},t.validation),{categories:d})},result:l.result}),a.state=U.IndexedReferences)}}}else this.buildState.delete(o)}this.currentState=U.Changed,await this.emitUpdate(e.map(a=>a.uri),[]),await this.buildDocuments(e,t,r)}async update(e,t,r=X.None){this.currentState=U.Changed;for(const a of t)this.langiumDocuments.deleteDocument(a),this.buildState.delete(a.toString()),this.indexManager.remove(a);for(const a of e){if(!this.langiumDocuments.invalidateDocument(a)){const l=this.langiumDocumentFactory.fromModel({$type:"INVALID"},a);l.state=U.Changed,this.langiumDocuments.addDocument(l)}this.buildState.delete(a.toString())}const i=Q(e).concat(t).map(a=>a.toString()).toSet();this.langiumDocuments.all.filter(a=>!i.has(a.uri.toString())&&this.shouldRelink(a,i)).forEach(a=>{this.serviceRegistry.getServices(a.uri).references.Linker.unlink(a),a.state=Math.min(a.state,U.ComputedScopes),a.diagnostics=void 0}),await this.emitUpdate(e,t),await Oe(r);const s=this.langiumDocuments.all.filter(a=>{var o;return a.state<U.Linked||!(!((o=this.buildState.get(a.uri.toString()))===null||o===void 0)&&o.completed)}).toArray();await this.buildDocuments(s,this.updateBuildOptions,r)}async emitUpdate(e,t){await Promise.all(this.updateListeners.map(r=>r(e,t)))}shouldRelink(e,t){return e.references.some(r=>r.error!==void 0)?!0:this.indexManager.isAffected(e,t)}onUpdate(e){return this.updateListeners.push(e),Un.create(()=>{const t=this.updateListeners.indexOf(e);t>=0&&this.updateListeners.splice(t,1)})}async buildDocuments(e,t,r){this.prepareBuild(e,t),await this.runCancelable(e,U.Parsed,r,s=>this.langiumDocumentFactory.update(s,r)),await this.runCancelable(e,U.IndexedContent,r,s=>this.indexManager.updateContent(s,r)),await this.runCancelable(e,U.ComputedScopes,r,async s=>{const a=this.serviceRegistry.getServices(s.uri).references.ScopeComputation;s.precomputedScopes=await a.computeLocalScopes(s,r)}),await this.runCancelable(e,U.Linked,r,s=>this.serviceRegistry.getServices(s.uri).references.Linker.link(s,r)),await this.runCancelable(e,U.IndexedReferences,r,s=>this.indexManager.updateReferences(s,r));const i=e.filter(s=>this.shouldValidate(s));await this.runCancelable(i,U.Validated,r,s=>this.validate(s,r));for(const s of e){const a=this.buildState.get(s.uri.toString());a&&(a.completed=!0)}}prepareBuild(e,t){for(const r of e){const i=r.uri.toString(),s=this.buildState.get(i);(!s||s.completed)&&this.buildState.set(i,{completed:!1,options:t,result:s?.result})}}async runCancelable(e,t,r,i){const s=e.filter(a=>a.state<t);for(const a of s)await Oe(r),await i(a),a.state=t;await this.notifyBuildPhase(s,t,r),this.currentState=t}onBuildPhase(e,t){return this.buildPhaseListeners.add(e,t),Un.create(()=>{this.buildPhaseListeners.delete(e,t)})}waitUntil(e,t,r){let i;if(t&&"path"in t?i=t:r=t,r??(r=X.None),i){const s=this.langiumDocuments.getDocument(i);if(s&&s.state>e)return Promise.resolve(i)}return this.currentState>=e?Promise.resolve(void 0):r.isCancellationRequested?Promise.reject(Fn):new Promise((s,a)=>{const o=this.onBuildPhase(e,()=>{if(o.dispose(),l.dispose(),i){const c=this.langiumDocuments.getDocument(i);s(c?.uri)}else s(void 0)}),l=r.onCancellationRequested(()=>{o.dispose(),l.dispose(),a(Fn)})})}async notifyBuildPhase(e,t,r){if(e.length===0)return;const i=this.buildPhaseListeners.get(t);for(const s of i)await Oe(r),await s(e,r)}shouldValidate(e){return!!this.getBuildOptions(e).validation}async validate(e,t){var r,i;const s=this.serviceRegistry.getServices(e.uri).validation.DocumentValidator,a=this.getBuildOptions(e).validation,o=typeof a=="object"?a:void 0,l=await s.validateDocument(e,o,t);e.diagnostics?e.diagnostics.push(...l):e.diagnostics=l;const c=this.buildState.get(e.uri.toString());if(c){(r=c.result)!==null&&r!==void 0||(c.result={});const u=(i=o?.categories)!==null&&i!==void 0?i:Gn.all;c.result.validationChecks?c.result.validationChecks.push(...u):c.result.validationChecks=[...u]}}getBuildOptions(e){var t,r;return(r=(t=this.buildState.get(e.uri.toString()))===null||t===void 0?void 0:t.options)!==null&&r!==void 0?r:{}}}class nm{constructor(e){this.symbolIndex=new Map,this.symbolByTypeIndex=new Vp,this.referenceIndex=new Map,this.documents=e.workspace.LangiumDocuments,this.serviceRegistry=e.ServiceRegistry,this.astReflection=e.AstReflection}findAllReferences(e,t){const r=Ue(e).uri,i=[];return this.referenceIndex.forEach(s=>{s.forEach(a=>{nt.equals(a.targetUri,r)&&a.targetPath===t&&i.push(a)})}),Q(i)}allElements(e,t){let r=Q(this.symbolIndex.keys());return t&&(r=r.filter(i=>!t||t.has(i))),r.map(i=>this.getFileDescriptions(i,e)).flat()}getFileDescriptions(e,t){var r;return t?this.symbolByTypeIndex.get(e,t,()=>{var s;return((s=this.symbolIndex.get(e))!==null&&s!==void 0?s:[]).filter(o=>this.astReflection.isSubtype(o.type,t))}):(r=this.symbolIndex.get(e))!==null&&r!==void 0?r:[]}remove(e){const t=e.toString();this.symbolIndex.delete(t),this.symbolByTypeIndex.clear(t),this.referenceIndex.delete(t)}async updateContent(e,t=X.None){const i=await this.serviceRegistry.getServices(e.uri).references.ScopeComputation.computeExports(e,t),s=e.uri.toString();this.symbolIndex.set(s,i),this.symbolByTypeIndex.clear(s)}async updateReferences(e,t=X.None){const i=await this.serviceRegistry.getServices(e.uri).workspace.ReferenceDescriptionProvider.createDescriptions(e,t);this.referenceIndex.set(e.uri.toString(),i)}isAffected(e,t){const r=this.referenceIndex.get(e.uri.toString());return r?r.some(i=>!i.local&&t.has(i.targetUri.toString())):!1}}class rm{constructor(e){this.initialBuildOptions={},this._ready=new wi,this.serviceRegistry=e.ServiceRegistry,this.langiumDocuments=e.workspace.LangiumDocuments,this.documentBuilder=e.workspace.DocumentBuilder,this.fileSystemProvider=e.workspace.FileSystemProvider,this.mutex=e.workspace.WorkspaceLock}get ready(){return this._ready.promise}initialize(e){var t;this.folders=(t=e.workspaceFolders)!==null&&t!==void 0?t:void 0}initialized(e){return this.mutex.write(t=>{var r;return this.initializeWorkspace((r=this.folders)!==null&&r!==void 0?r:[],t)})}async initializeWorkspace(e,t=X.None){const r=await this.performStartup(e);await Oe(t),await this.documentBuilder.build(r,this.initialBuildOptions,t)}async performStartup(e){const t=this.serviceRegistry.all.flatMap(s=>s.LanguageMetaData.fileExtensions),r=[],i=s=>{r.push(s),this.langiumDocuments.hasDocument(s.uri)||this.langiumDocuments.addDocument(s)};return await this.loadAdditionalDocuments(e,i),await Promise.all(e.map(s=>[s,this.getRootFolder(s)]).map(async s=>this.traverseFolder(...s,t,i))),this._ready.resolve(),r}loadAdditionalDocuments(e,t){return Promise.resolve()}getRootFolder(e){return Lt.parse(e.uri)}async traverseFolder(e,t,r,i){const s=await this.fileSystemProvider.readDirectory(t);await Promise.all(s.map(async a=>{if(this.includeEntry(e,a,r)){if(a.isDirectory)await this.traverseFolder(e,a.uri,r,i);else if(a.isFile){const o=await this.langiumDocuments.getOrCreateDocument(a.uri);i(o)}}}))}includeEntry(e,t,r){const i=nt.basename(t.uri);if(i.startsWith("."))return!1;if(t.isDirectory)return i!=="node_modules"&&i!=="out";if(t.isFile){const s=nt.extname(t.uri);return r.includes(s)}return!1}}class im{constructor(e){const t=e.parser.TokenBuilder.buildTokens(e.Grammar,{caseInsensitive:e.LanguageMetaData.caseInsensitive});this.tokenTypes=this.toTokenTypeDictionary(t);const r=$a(t)?Object.values(t):t;this.chevrotainLexer=new de(r,{positionTracking:"full"})}get definition(){return this.tokenTypes}tokenize(e){var t;const r=this.chevrotainLexer.tokenize(e);return{tokens:r.tokens,errors:r.errors,hidden:(t=r.groups.hidden)!==null&&t!==void 0?t:[]}}toTokenTypeDictionary(e){if($a(e))return e;const t=tc(e)?Object.values(e.modes).flat():e,r={};return t.forEach(i=>r[i.name]=i),r}}function sm(n){return Array.isArray(n)&&(n.length===0||"name"in n[0])}function tc(n){return n&&"modes"in n&&"defaultMode"in n}function $a(n){return!sm(n)&&!tc(n)}function am(n,e,t){let r,i;typeof n=="string"?(i=e,r=t):(i=n.range.start,r=e),i||(i=P.create(0,0));const s=nc(n),a=Li(r),o=cm({lines:s,position:i,options:a});return pm({index:0,tokens:o,position:i})}function om(n,e){const t=Li(e),r=nc(n);if(r.length===0)return!1;const i=r[0],s=r[r.length-1],a=t.start,o=t.end;return!!a?.exec(i)&&!!o?.exec(s)}function nc(n){let e="";return typeof n=="string"?e=n:e=n.text,e.split(Uu)}const Oa=/\s*(@([\p{L}][\p{L}\p{N}]*)?)/uy,lm=/\{(@[\p{L}][\p{L}\p{N}]*)(\s*)([^\r\n}]+)?\}/gu;function cm(n){var e,t,r;const i=[];let s=n.position.line,a=n.position.character;for(let o=0;o<n.lines.length;o++){const l=o===0,c=o===n.lines.length-1;let u=n.lines[o],d=0;if(l&&n.options.start){const h=(e=n.options.start)===null||e===void 0?void 0:e.exec(u);h&&(d=h.index+h[0].length)}else{const h=(t=n.options.line)===null||t===void 0?void 0:t.exec(u);h&&(d=h.index+h[0].length)}if(c){const h=(r=n.options.end)===null||r===void 0?void 0:r.exec(u);h&&(u=u.substring(0,h.index))}if(u=u.substring(0,hm(u)),ni(u,d)>=u.length){if(i.length>0){const h=P.create(s,a);i.push({type:"break",content:"",range:b.create(h,h)})}}else{Oa.lastIndex=d;const h=Oa.exec(u);if(h){const m=h[0],g=h[1],A=P.create(s,a+d),T=P.create(s,a+d+m.length);i.push({type:"tag",content:g,range:b.create(A,T)}),d+=m.length,d=ni(u,d)}if(d<u.length){const m=u.substring(d),g=Array.from(m.matchAll(lm));i.push(...um(g,m,s,a+d))}}s++,a=0}return i.length>0&&i[i.length-1].type==="break"?i.slice(0,-1):i}function um(n,e,t,r){const i=[];if(n.length===0){const s=P.create(t,r),a=P.create(t,r+e.length);i.push({type:"text",content:e,range:b.create(s,a)})}else{let s=0;for(const o of n){const l=o.index,c=e.substring(s,l);c.length>0&&i.push({type:"text",content:e.substring(s,l),range:b.create(P.create(t,s+r),P.create(t,l+r))});let u=c.length+1;const d=o[1];if(i.push({type:"inline-tag",content:d,range:b.create(P.create(t,s+u+r),P.create(t,s+u+d.length+r))}),u+=d.length,o.length===4){u+=o[2].length;const f=o[3];i.push({type:"text",content:f,range:b.create(P.create(t,s+u+r),P.create(t,s+u+f.length+r))})}else i.push({type:"text",content:"",range:b.create(P.create(t,s+u+r),P.create(t,s+u+r))});s=l+o[0].length}const a=e.substring(s);a.length>0&&i.push({type:"text",content:a,range:b.create(P.create(t,s+r),P.create(t,s+r+a.length))})}return i}const dm=/\S/,fm=/\s*$/;function ni(n,e){const t=n.substring(e).match(dm);return t?e+t.index:n.length}function hm(n){const e=n.match(fm);if(e&&typeof e.index=="number")return e.index}function pm(n){var e,t,r,i;const s=P.create(n.position.line,n.position.character);if(n.tokens.length===0)return new ba([],b.create(s,s));const a=[];for(;n.index<n.tokens.length;){const c=mm(n,a[a.length-1]);c&&a.push(c)}const o=(t=(e=a[0])===null||e===void 0?void 0:e.range.start)!==null&&t!==void 0?t:s,l=(i=(r=a[a.length-1])===null||r===void 0?void 0:r.range.end)!==null&&i!==void 0?i:s;return new ba(a,b.create(o,l))}function mm(n,e){const t=n.tokens[n.index];if(t.type==="tag")return ic(n,!1);if(t.type==="text"||t.type==="inline-tag")return rc(n);gm(t,e),n.index++}function gm(n,e){if(e){const t=new ac("",n.range);"inlines"in e?e.inlines.push(t):e.content.inlines.push(t)}}function rc(n){let e=n.tokens[n.index];const t=e;let r=e;const i=[];for(;e&&e.type!=="break"&&e.type!=="tag";)i.push(ym(n)),r=e,e=n.tokens[n.index];return new ri(i,b.create(t.range.start,r.range.end))}function ym(n){return n.tokens[n.index].type==="inline-tag"?ic(n,!0):sc(n)}function ic(n,e){const t=n.tokens[n.index++],r=t.content.substring(1),i=n.tokens[n.index];if(i?.type==="text")if(e){const s=sc(n);return new Rr(r,new ri([s],s.range),e,b.create(t.range.start,s.range.end))}else{const s=rc(n);return new Rr(r,s,e,b.create(t.range.start,s.range.end))}else{const s=t.range;return new Rr(r,new ri([],s),e,s)}}function sc(n){const e=n.tokens[n.index++];return new ac(e.content,e.range)}function Li(n){if(!n)return Li({start:"/**",end:"*/",line:"*"});const{start:e,end:t,line:r}=n;return{start:Tr(e,!0),end:Tr(t,!1),line:Tr(r,!0)}}function Tr(n,e){if(typeof n=="string"||typeof n=="object"){const t=typeof n=="string"?qn(n):n.source;return e?new RegExp(`^\\s*${t}`):new RegExp(`\\s*${t}\\s*$`)}else return n}class ba{constructor(e,t){this.elements=e,this.range=t}getTag(e){return this.getAllTags().find(t=>t.name===e)}getTags(e){return this.getAllTags().filter(t=>t.name===e)}getAllTags(){return this.elements.filter(e=>"name"in e)}toString(){let e="";for(const t of this.elements)if(e.length===0)e=t.toString();else{const r=t.toString();e+=Pa(e)+r}return e.trim()}toMarkdown(e){let t="";for(const r of this.elements)if(t.length===0)t=r.toMarkdown(e);else{const i=r.toMarkdown(e);t+=Pa(t)+i}return t.trim()}}class Rr{constructor(e,t,r,i){this.name=e,this.content=t,this.inline=r,this.range=i}toString(){let e=`@${this.name}`;const t=this.content.toString();return this.content.inlines.length===1?e=`${e} ${t}`:this.content.inlines.length>1&&(e=`${e}
${t}`),this.inline?`{${e}}`:e}toMarkdown(e){var t,r;return(r=(t=e?.renderTag)===null||t===void 0?void 0:t.call(e,this))!==null&&r!==void 0?r:this.toMarkdownDefault(e)}toMarkdownDefault(e){const t=this.content.toMarkdown(e);if(this.inline){const s=Tm(this.name,t,e??{});if(typeof s=="string")return s}let r="";e?.tag==="italic"||e?.tag===void 0?r="*":e?.tag==="bold"?r="**":e?.tag==="bold-italic"&&(r="***");let i=`${r}@${this.name}${r}`;return this.content.inlines.length===1?i=`${i} — ${t}`:this.content.inlines.length>1&&(i=`${i}
${t}`),this.inline?`{${i}}`:i}}function Tm(n,e,t){var r,i;if(n==="linkplain"||n==="linkcode"||n==="link"){const s=e.indexOf(" ");let a=e;if(s>0){const l=ni(e,s);a=e.substring(l),e=e.substring(0,s)}return(n==="linkcode"||n==="link"&&t.link==="code")&&(a=`\`${a}\``),(i=(r=t.renderLink)===null||r===void 0?void 0:r.call(t,e,a))!==null&&i!==void 0?i:Rm(e,a)}}function Rm(n,e){try{return Lt.parse(n,!0),`[${e}](${n})`}catch{return n}}class ri{constructor(e,t){this.inlines=e,this.range=t}toString(){let e="";for(let t=0;t<this.inlines.length;t++){const r=this.inlines[t],i=this.inlines[t+1];e+=r.toString(),i&&i.range.start.line>r.range.start.line&&(e+=`
`)}return e}toMarkdown(e){let t="";for(let r=0;r<this.inlines.length;r++){const i=this.inlines[r],s=this.inlines[r+1];t+=i.toMarkdown(e),s&&s.range.start.line>i.range.start.line&&(t+=`
`)}return t}}class ac{constructor(e,t){this.text=e,this.range=t}toString(){return this.text}toMarkdown(){return this.text}}function Pa(n){return n.endsWith(`
`)?`
`:`

`}class Am{constructor(e){this.indexManager=e.shared.workspace.IndexManager,this.commentProvider=e.documentation.CommentProvider}getDocumentation(e){const t=this.commentProvider.getComment(e);if(t&&om(t))return am(t).toMarkdown({renderLink:(i,s)=>this.documentationLinkRenderer(e,i,s),renderTag:i=>this.documentationTagRenderer(e,i)})}documentationLinkRenderer(e,t,r){var i;const s=(i=this.findNameInPrecomputedScopes(e,t))!==null&&i!==void 0?i:this.findNameInGlobalScope(e,t);if(s&&s.nameSegment){const a=s.nameSegment.range.start.line+1,o=s.nameSegment.range.start.character+1,l=s.documentUri.with({fragment:`L${a},${o}`});return`[${r}](${l.toString()})`}else return}documentationTagRenderer(e,t){}findNameInPrecomputedScopes(e,t){const i=Ue(e).precomputedScopes;if(!i)return;let s=e;do{const o=i.get(s).find(l=>l.name===t);if(o)return o;s=s.$container}while(s)}findNameInGlobalScope(e,t){return this.indexManager.allElements().find(i=>i.name===t)}}class Em{constructor(e){this.grammarConfig=()=>e.parser.GrammarConfig}getComment(e){var t;return Hp(e)?e.$comment:(t=su(e.$cstNode,this.grammarConfig().multilineCommentRules))===null||t===void 0?void 0:t.text}}class vm{constructor(e){this.syncParser=e.parser.LangiumParser}parse(e){return Promise.resolve(this.syncParser.parse(e))}}class km{constructor(){this.previousTokenSource=new Qr,this.writeQueue=[],this.readQueue=[],this.done=!0}write(e){this.cancelWrite();const t=new Qr;return this.previousTokenSource=t,this.enqueue(this.writeQueue,e,t.token)}read(e){return this.enqueue(this.readQueue,e)}enqueue(e,t,r){const i=new wi,s={action:t,deferred:i,cancellationToken:r??X.None};return e.push(s),this.performNextOperation(),i.promise}async performNextOperation(){if(!this.done)return;const e=[];if(this.writeQueue.length>0)e.push(this.writeQueue.shift());else if(this.readQueue.length>0)e.push(...this.readQueue.splice(0,this.readQueue.length));else return;this.done=!1,await Promise.all(e.map(async({action:t,deferred:r,cancellationToken:i})=>{try{const s=await Promise.resolve().then(()=>t(i));r.resolve(s)}catch(s){Ci(s)?r.resolve(void 0):r.reject(s)}})),this.done=!0,this.performNextOperation()}cancelWrite(){this.previousTokenSource.cancel()}}class Sm{constructor(e){this.grammarElementIdMap=new Ca,this.tokenTypeIdMap=new Ca,this.grammar=e.Grammar,this.lexer=e.parser.Lexer,this.linker=e.references.Linker}dehydrate(e){return{lexerErrors:e.lexerErrors.map(t=>Object.assign({},t)),parserErrors:e.parserErrors.map(t=>Object.assign({},t)),value:this.dehydrateAstNode(e.value,this.createDehyrationContext(e.value))}}createDehyrationContext(e){const t=new Map,r=new Map;for(const i of At(e))t.set(i,{});if(e.$cstNode)for(const i of Er(e.$cstNode))r.set(i,{});return{astNodes:t,cstNodes:r}}dehydrateAstNode(e,t){const r=t.astNodes.get(e);r.$type=e.$type,r.$containerIndex=e.$containerIndex,r.$containerProperty=e.$containerProperty,e.$cstNode!==void 0&&(r.$cstNode=this.dehydrateCstNode(e.$cstNode,t));for(const[i,s]of Object.entries(e))if(!i.startsWith("$"))if(Array.isArray(s)){const a=[];r[i]=a;for(const o of s)ae(o)?a.push(this.dehydrateAstNode(o,t)):Ge(o)?a.push(this.dehydrateReference(o,t)):a.push(o)}else ae(s)?r[i]=this.dehydrateAstNode(s,t):Ge(s)?r[i]=this.dehydrateReference(s,t):s!==void 0&&(r[i]=s);return r}dehydrateReference(e,t){const r={};return r.$refText=e.$refText,e.$refNode&&(r.$refNode=t.cstNodes.get(e.$refNode)),r}dehydrateCstNode(e,t){const r=t.cstNodes.get(e);return to(e)?r.fullText=e.fullText:r.grammarSource=this.getGrammarElementId(e.grammarSource),r.hidden=e.hidden,r.astNode=t.astNodes.get(e.astNode),It(e)?r.content=e.content.map(i=>this.dehydrateCstNode(i,t)):eo(e)&&(r.tokenType=e.tokenType.name,r.offset=e.offset,r.length=e.length,r.startLine=e.range.start.line,r.startColumn=e.range.start.character,r.endLine=e.range.end.line,r.endColumn=e.range.end.character),r}hydrate(e){const t=e.value,r=this.createHydrationContext(t);return"$cstNode"in t&&this.hydrateCstNode(t.$cstNode,r),{lexerErrors:e.lexerErrors,parserErrors:e.parserErrors,value:this.hydrateAstNode(t,r)}}createHydrationContext(e){const t=new Map,r=new Map;for(const s of At(e))t.set(s,{});let i;if(e.$cstNode)for(const s of Er(e.$cstNode)){let a;"fullText"in s?(a=new Gl(s.fullText),i=a):"content"in s?a=new xi:"tokenType"in s&&(a=this.hydrateCstLeafNode(s)),a&&(r.set(s,a),a.root=i)}return{astNodes:t,cstNodes:r}}hydrateAstNode(e,t){const r=t.astNodes.get(e);r.$type=e.$type,r.$containerIndex=e.$containerIndex,r.$containerProperty=e.$containerProperty,e.$cstNode&&(r.$cstNode=t.cstNodes.get(e.$cstNode));for(const[i,s]of Object.entries(e))if(!i.startsWith("$"))if(Array.isArray(s)){const a=[];r[i]=a;for(const o of s)ae(o)?a.push(this.setParent(this.hydrateAstNode(o,t),r)):Ge(o)?a.push(this.hydrateReference(o,r,i,t)):a.push(o)}else ae(s)?r[i]=this.setParent(this.hydrateAstNode(s,t),r):Ge(s)?r[i]=this.hydrateReference(s,r,i,t):s!==void 0&&(r[i]=s);return r}setParent(e,t){return e.$container=t,e}hydrateReference(e,t,r,i){return this.linker.buildReference(t,r,i.cstNodes.get(e.$refNode),e.$refText)}hydrateCstNode(e,t,r=0){const i=t.cstNodes.get(e);if(typeof e.grammarSource=="number"&&(i.grammarSource=this.getGrammarElement(e.grammarSource)),i.astNode=t.astNodes.get(e.astNode),It(i))for(const s of e.content){const a=this.hydrateCstNode(s,t,r++);i.content.push(a)}return i}hydrateCstLeafNode(e){const t=this.getTokenType(e.tokenType),r=e.offset,i=e.length,s=e.startLine,a=e.startColumn,o=e.endLine,l=e.endColumn,c=e.hidden;return new zr(r,i,{start:{line:s,character:a},end:{line:o,character:l}},t,c)}getTokenType(e){return this.lexer.definition[e]}getGrammarElementId(e){return this.grammarElementIdMap.size===0&&this.createGrammarElementIdMap(),this.grammarElementIdMap.get(e)}getGrammarElement(e){this.grammarElementIdMap.size===0&&this.createGrammarElementIdMap();const t=this.grammarElementIdMap.getKey(e);if(t)return t;throw new Error("Invalid grammar element id: "+e)}createGrammarElementIdMap(){let e=0;for(const t of At(this.grammar))lu(t)&&this.grammarElementIdMap.set(t,e++)}}function Dt(n){return{documentation:{CommentProvider:e=>new Em(e),DocumentationProvider:e=>new Am(e)},parser:{AsyncParser:e=>new vm(e),GrammarConfig:e=>ld(e),LangiumParser:e=>yp(e),CompletionParser:e=>gp(e),ValueConverter:()=>new ql,TokenBuilder:()=>new zl,Lexer:e=>new im(e),ParserErrorMessageProvider:()=>new Vl},workspace:{AstNodeLocator:()=>new Zp,AstNodeDescriptionProvider:e=>new Jp(e),ReferenceDescriptionProvider:e=>new Qp(e)},references:{Linker:e=>new Pp(e),NameProvider:()=>new Dp,ScopeProvider:e=>new jp(e),ScopeComputation:e=>new Gp(e),References:e=>new Fp(e)},serializer:{Hydrator:e=>new Sm(e),JsonSerializer:e=>new Kp(e)},validation:{DocumentValidator:e=>new Yp(e),ValidationRegistry:e=>new qp(e)},shared:()=>n.shared}}function Ft(n){return{ServiceRegistry:()=>new zp,workspace:{LangiumDocuments:e=>new bp(e),LangiumDocumentFactory:e=>new Op(e),DocumentBuilder:e=>new tm(e),IndexManager:e=>new nm(e),WorkspaceManager:e=>new rm(e),FileSystemProvider:e=>n.fileSystemProvider(e),WorkspaceLock:()=>new km,ConfigurationProvider:e=>new em(e)}}}var Ma;(function(n){n.merge=(e,t)=>Bn(Bn({},e),t)})(Ma||(Ma={}));function we(n,e,t,r,i,s,a,o,l){const c=[n,e,t,r,i,s,a,o,l].reduce(Bn,{});return oc(c)}const Da=Symbol("isProxy");function oc(n,e){const t=new Proxy({},{deleteProperty:()=>!1,get:(r,i)=>Ga(r,i,n,e||t),getOwnPropertyDescriptor:(r,i)=>(Ga(r,i,n,e||t),Object.getOwnPropertyDescriptor(r,i)),has:(r,i)=>i in n,ownKeys:()=>[...Reflect.ownKeys(n),Da]});return t[Da]=!0,t}const Fa=Symbol();function Ga(n,e,t,r){if(e in n){if(n[e]instanceof Error)throw new Error("Construction failure. Please make sure that your dependencies are constructable.",{cause:n[e]});if(n[e]===Fa)throw new Error('Cycle detected. Please make "'+String(e)+'" lazy. See https://langium.org/docs/configuration-services/#resolving-cyclic-dependencies');return n[e]}else if(e in t){const i=t[e];n[e]=Fa;try{n[e]=typeof i=="function"?i(r):oc(i,r)}catch(s){throw n[e]=s instanceof Error?s:void 0,s}return n[e]}else return}function Bn(n,e){if(e){for(const[t,r]of Object.entries(e))if(r!==void 0){const i=n[t];i!==null&&r!==null&&typeof i=="object"&&typeof r=="object"?n[t]=Bn(i,r):n[t]=r}}return n}class Im{readFile(){throw new Error("No file system is available.")}async readDirectory(){return[]}}const Gt={fileSystemProvider:()=>new Im},xm={Grammar:()=>{},LanguageMetaData:()=>({caseInsensitive:!1,fileExtensions:[".langium"],languageId:"langium"})},Nm={AstReflection:()=>new Do};function Cm(){const n=we(Ft(Gt),Nm),e=we(Dt({shared:n}),xm);return n.ServiceRegistry.register(e),e}function an(n){var e;const t=Cm(),r=t.serializer.JsonSerializer.deserialize(n);return t.shared.workspace.LangiumDocumentFactory.fromModel(r,Lt.parse(`memory://${(e=r.name)!==null&&e!==void 0?e:"grammar"}.langium`)),r}var wm=Object.defineProperty,I=(n,e)=>wm(n,"name",{value:e,configurable:!0}),_m="Statement",Lm="Architecture";function $m(n){return Me.isInstance(n,Lm)}I($m,"isArchitecture");var lc="Branch";function Om(n){return Me.isInstance(n,lc)}I(Om,"isBranch");var bm="Checkout",Pm="CherryPicking",cc="Commit";function Mm(n){return Me.isInstance(n,cc)}I(Mm,"isCommit");var Dm="Common";function Fm(n){return Me.isInstance(n,Dm)}I(Fm,"isCommon");var uc="GitGraph";function Gm(n){return Me.isInstance(n,uc)}I(Gm,"isGitGraph");var Um="Info";function Bm(n){return Me.isInstance(n,Um)}I(Bm,"isInfo");var dc="Merge";function Vm(n){return Me.isInstance(n,dc)}I(Vm,"isMerge");var Wm="Packet";function jm(n){return Me.isInstance(n,Wm)}I(jm,"isPacket");var Hm="PacketBlock";function Km(n){return Me.isInstance(n,Hm)}I(Km,"isPacketBlock");var zm="Pie";function qm(n){return Me.isInstance(n,zm)}I(qm,"isPie");var Ym="PieSection";function Xm(n){return Me.isInstance(n,Ym)}I(Xm,"isPieSection");var Jm="Direction",fc=class extends Za{static{I(this,"MermaidAstReflection")}getAllTypes(){return["Architecture","Branch","Checkout","CherryPicking","Commit","Common","Direction","Edge","GitGraph","Group","Info","Junction","Merge","Packet","PacketBlock","Pie","PieSection","Service","Statement"]}computeIsSubtype(n,e){switch(n){case lc:case bm:case Pm:case cc:case dc:return this.isSubtype(_m,e);case Jm:return this.isSubtype(uc,e);default:return!1}}getReferenceType(n){const e=`${n.container.$type}:${n.property}`;switch(e){default:throw new Error(`${e} is not a valid reference id.`)}}getTypeMetaData(n){switch(n){case"Architecture":return{name:"Architecture",properties:[{name:"accDescr"},{name:"accTitle"},{name:"edges",defaultValue:[]},{name:"groups",defaultValue:[]},{name:"junctions",defaultValue:[]},{name:"services",defaultValue:[]},{name:"title"}]};case"Branch":return{name:"Branch",properties:[{name:"name"},{name:"order"}]};case"Checkout":return{name:"Checkout",properties:[{name:"branch"}]};case"CherryPicking":return{name:"CherryPicking",properties:[{name:"id"},{name:"parent"},{name:"tags",defaultValue:[]}]};case"Commit":return{name:"Commit",properties:[{name:"id"},{name:"message"},{name:"tags",defaultValue:[]},{name:"type"}]};case"Common":return{name:"Common",properties:[{name:"accDescr"},{name:"accTitle"},{name:"title"}]};case"Edge":return{name:"Edge",properties:[{name:"lhsDir"},{name:"lhsGroup",defaultValue:!1},{name:"lhsId"},{name:"lhsInto",defaultValue:!1},{name:"rhsDir"},{name:"rhsGroup",defaultValue:!1},{name:"rhsId"},{name:"rhsInto",defaultValue:!1},{name:"title"}]};case"GitGraph":return{name:"GitGraph",properties:[{name:"accDescr"},{name:"accTitle"},{name:"statements",defaultValue:[]},{name:"title"}]};case"Group":return{name:"Group",properties:[{name:"icon"},{name:"id"},{name:"in"},{name:"title"}]};case"Info":return{name:"Info",properties:[{name:"accDescr"},{name:"accTitle"},{name:"title"}]};case"Junction":return{name:"Junction",properties:[{name:"id"},{name:"in"}]};case"Merge":return{name:"Merge",properties:[{name:"branch"},{name:"id"},{name:"tags",defaultValue:[]},{name:"type"}]};case"Packet":return{name:"Packet",properties:[{name:"accDescr"},{name:"accTitle"},{name:"blocks",defaultValue:[]},{name:"title"}]};case"PacketBlock":return{name:"PacketBlock",properties:[{name:"end"},{name:"label"},{name:"start"}]};case"Pie":return{name:"Pie",properties:[{name:"accDescr"},{name:"accTitle"},{name:"sections",defaultValue:[]},{name:"showData",defaultValue:!1},{name:"title"}]};case"PieSection":return{name:"PieSection",properties:[{name:"label"},{name:"value"}]};case"Service":return{name:"Service",properties:[{name:"icon"},{name:"iconText"},{name:"id"},{name:"in"},{name:"title"}]};case"Direction":return{name:"Direction",properties:[{name:"accDescr"},{name:"accTitle"},{name:"dir"},{name:"statements",defaultValue:[]},{name:"title"}]};default:return{name:n,properties:[]}}}},Me=new fc,Ua,Qm=I(()=>Ua??(Ua=an('{"$type":"Grammar","isDeclared":true,"name":"Info","imports":[],"rules":[{"$type":"ParserRule","name":"Info","entry":true,"definition":{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@3"},"arguments":[],"cardinality":"*"},{"$type":"Keyword","value":"info"},{"$type":"RuleCall","rule":{"$ref":"#/rules@3"},"arguments":[],"cardinality":"*"},{"$type":"Group","elements":[{"$type":"Keyword","value":"showInfo"},{"$type":"RuleCall","rule":{"$ref":"#/rules@3"},"arguments":[],"cardinality":"*"}],"cardinality":"?"},{"$type":"RuleCall","rule":{"$ref":"#/rules@1"},"arguments":[],"cardinality":"?"}]},"definesHiddenTokens":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"TitleAndAccessibilities","fragment":true,"definition":{"$type":"Group","elements":[{"$type":"Alternatives","elements":[{"$type":"Assignment","feature":"accDescr","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@4"},"arguments":[]}},{"$type":"Assignment","feature":"accTitle","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@5"},"arguments":[]}},{"$type":"Assignment","feature":"title","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@6"},"arguments":[]}}]},{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[]}],"cardinality":"+"},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"EOL","fragment":true,"dataType":"string","definition":{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@3"},"arguments":[],"cardinality":"+"},{"$type":"EndOfFile"}]},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"TerminalRule","name":"NEWLINE","definition":{"$type":"RegexToken","regex":"/\\\\r?\\\\n/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ACC_DESCR","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*accDescr(?:[\\\\t ]*:([^\\\\n\\\\r]*?(?=%%)|[^\\\\n\\\\r]*)|\\\\s*{([^}]*)})/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ACC_TITLE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*accTitle[\\\\t ]*:(?:[^\\\\n\\\\r]*?(?=%%)|[^\\\\n\\\\r]*)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"TITLE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*title(?:[\\\\t ][^\\\\n\\\\r]*?(?=%%)|[\\\\t ][^\\\\n\\\\r]*|)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","hidden":true,"name":"WHITESPACE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]+/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"YAML","definition":{"$type":"RegexToken","regex":"/---[\\\\t ]*\\\\r?\\\\n(?:[\\\\S\\\\s]*?\\\\r?\\\\n)?---(?:\\\\r?\\\\n|(?!\\\\S))/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"DIRECTIVE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*%%{[\\\\S\\\\s]*?}%%(?:\\\\r?\\\\n|(?!\\\\S))/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"SINGLE_LINE_COMMENT","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*%%[^\\\\n\\\\r]*/"},"fragment":false}],"definesHiddenTokens":false,"hiddenTokens":[],"interfaces":[{"$type":"Interface","name":"Common","attributes":[{"$type":"TypeAttribute","name":"accDescr","isOptional":true,"type":{"$type":"SimpleType","primitiveType":"string"}},{"$type":"TypeAttribute","name":"accTitle","isOptional":true,"type":{"$type":"SimpleType","primitiveType":"string"}},{"$type":"TypeAttribute","name":"title","isOptional":true,"type":{"$type":"SimpleType","primitiveType":"string"}}],"superTypes":[]}],"types":[],"usedGrammars":[]}')),"InfoGrammar"),Ba,Zm=I(()=>Ba??(Ba=an(`{"$type":"Grammar","isDeclared":true,"name":"Packet","imports":[],"rules":[{"$type":"ParserRule","name":"Packet","entry":true,"definition":{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@6"},"arguments":[],"cardinality":"*"},{"$type":"Keyword","value":"packet-beta"},{"$type":"Alternatives","elements":[{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@6"},"arguments":[],"cardinality":"*"},{"$type":"RuleCall","rule":{"$ref":"#/rules@4"},"arguments":[]},{"$type":"Assignment","feature":"blocks","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@1"},"arguments":[]},"cardinality":"*"}]},{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@6"},"arguments":[],"cardinality":"+"},{"$type":"Assignment","feature":"blocks","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@1"},"arguments":[]},"cardinality":"+"}]},{"$type":"RuleCall","rule":{"$ref":"#/rules@6"},"arguments":[],"cardinality":"*"}]}]},"definesHiddenTokens":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"PacketBlock","definition":{"$type":"Group","elements":[{"$type":"Assignment","feature":"start","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[]}},{"$type":"Group","elements":[{"$type":"Keyword","value":"-"},{"$type":"Assignment","feature":"end","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[]}}],"cardinality":"?"},{"$type":"Keyword","value":":"},{"$type":"Assignment","feature":"label","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@3"},"arguments":[]}},{"$type":"RuleCall","rule":{"$ref":"#/rules@5"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"TerminalRule","name":"INT","type":{"$type":"ReturnType","name":"number"},"definition":{"$type":"RegexToken","regex":"/0|[1-9][0-9]*/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"STRING","definition":{"$type":"RegexToken","regex":"/\\"[^\\"]*\\"|'[^']*'/"},"fragment":false,"hidden":false},{"$type":"ParserRule","name":"TitleAndAccessibilities","fragment":true,"definition":{"$type":"Group","elements":[{"$type":"Alternatives","elements":[{"$type":"Assignment","feature":"accDescr","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@7"},"arguments":[]}},{"$type":"Assignment","feature":"accTitle","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@8"},"arguments":[]}},{"$type":"Assignment","feature":"title","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@9"},"arguments":[]}}]},{"$type":"RuleCall","rule":{"$ref":"#/rules@5"},"arguments":[]}],"cardinality":"+"},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"EOL","fragment":true,"dataType":"string","definition":{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@6"},"arguments":[],"cardinality":"+"},{"$type":"EndOfFile"}]},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"TerminalRule","name":"NEWLINE","definition":{"$type":"RegexToken","regex":"/\\\\r?\\\\n/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ACC_DESCR","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*accDescr(?:[\\\\t ]*:([^\\\\n\\\\r]*?(?=%%)|[^\\\\n\\\\r]*)|\\\\s*{([^}]*)})/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ACC_TITLE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*accTitle[\\\\t ]*:(?:[^\\\\n\\\\r]*?(?=%%)|[^\\\\n\\\\r]*)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"TITLE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*title(?:[\\\\t ][^\\\\n\\\\r]*?(?=%%)|[\\\\t ][^\\\\n\\\\r]*|)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","hidden":true,"name":"WHITESPACE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]+/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"YAML","definition":{"$type":"RegexToken","regex":"/---[\\\\t ]*\\\\r?\\\\n(?:[\\\\S\\\\s]*?\\\\r?\\\\n)?---(?:\\\\r?\\\\n|(?!\\\\S))/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"DIRECTIVE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*%%{[\\\\S\\\\s]*?}%%(?:\\\\r?\\\\n|(?!\\\\S))/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"SINGLE_LINE_COMMENT","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*%%[^\\\\n\\\\r]*/"},"fragment":false}],"definesHiddenTokens":false,"hiddenTokens":[],"interfaces":[{"$type":"Interface","name":"Common","attributes":[{"$type":"TypeAttribute","name":"accDescr","isOptional":true,"type":{"$type":"SimpleType","primitiveType":"string"}},{"$type":"TypeAttribute","name":"accTitle","isOptional":true,"type":{"$type":"SimpleType","primitiveType":"string"}},{"$type":"TypeAttribute","name":"title","isOptional":true,"type":{"$type":"SimpleType","primitiveType":"string"}}],"superTypes":[]}],"types":[],"usedGrammars":[]}`)),"PacketGrammar"),Va,eg=I(()=>Va??(Va=an('{"$type":"Grammar","isDeclared":true,"name":"Pie","imports":[],"rules":[{"$type":"ParserRule","name":"Pie","entry":true,"definition":{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@6"},"arguments":[],"cardinality":"*"},{"$type":"Keyword","value":"pie"},{"$type":"Assignment","feature":"showData","operator":"?=","terminal":{"$type":"Keyword","value":"showData"},"cardinality":"?"},{"$type":"Alternatives","elements":[{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@6"},"arguments":[],"cardinality":"*"},{"$type":"RuleCall","rule":{"$ref":"#/rules@4"},"arguments":[]},{"$type":"Assignment","feature":"sections","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@1"},"arguments":[]},"cardinality":"*"}]},{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@6"},"arguments":[],"cardinality":"+"},{"$type":"Assignment","feature":"sections","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@1"},"arguments":[]},"cardinality":"+"}]},{"$type":"RuleCall","rule":{"$ref":"#/rules@6"},"arguments":[],"cardinality":"*"}]}]},"definesHiddenTokens":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"PieSection","definition":{"$type":"Group","elements":[{"$type":"Assignment","feature":"label","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[]}},{"$type":"Keyword","value":":"},{"$type":"Assignment","feature":"value","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@3"},"arguments":[]}},{"$type":"RuleCall","rule":{"$ref":"#/rules@5"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"TerminalRule","name":"PIE_SECTION_LABEL","definition":{"$type":"RegexToken","regex":"/\\"[^\\"]+\\"/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"PIE_SECTION_VALUE","type":{"$type":"ReturnType","name":"number"},"definition":{"$type":"RegexToken","regex":"/(0|[1-9][0-9]*)(\\\\.[0-9]+)?/"},"fragment":false,"hidden":false},{"$type":"ParserRule","name":"TitleAndAccessibilities","fragment":true,"definition":{"$type":"Group","elements":[{"$type":"Alternatives","elements":[{"$type":"Assignment","feature":"accDescr","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@7"},"arguments":[]}},{"$type":"Assignment","feature":"accTitle","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@8"},"arguments":[]}},{"$type":"Assignment","feature":"title","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@9"},"arguments":[]}}]},{"$type":"RuleCall","rule":{"$ref":"#/rules@5"},"arguments":[]}],"cardinality":"+"},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"EOL","fragment":true,"dataType":"string","definition":{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@6"},"arguments":[],"cardinality":"+"},{"$type":"EndOfFile"}]},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"TerminalRule","name":"NEWLINE","definition":{"$type":"RegexToken","regex":"/\\\\r?\\\\n/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ACC_DESCR","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*accDescr(?:[\\\\t ]*:([^\\\\n\\\\r]*?(?=%%)|[^\\\\n\\\\r]*)|\\\\s*{([^}]*)})/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ACC_TITLE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*accTitle[\\\\t ]*:(?:[^\\\\n\\\\r]*?(?=%%)|[^\\\\n\\\\r]*)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"TITLE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*title(?:[\\\\t ][^\\\\n\\\\r]*?(?=%%)|[\\\\t ][^\\\\n\\\\r]*|)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","hidden":true,"name":"WHITESPACE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]+/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"YAML","definition":{"$type":"RegexToken","regex":"/---[\\\\t ]*\\\\r?\\\\n(?:[\\\\S\\\\s]*?\\\\r?\\\\n)?---(?:\\\\r?\\\\n|(?!\\\\S))/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"DIRECTIVE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*%%{[\\\\S\\\\s]*?}%%(?:\\\\r?\\\\n|(?!\\\\S))/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"SINGLE_LINE_COMMENT","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*%%[^\\\\n\\\\r]*/"},"fragment":false}],"definesHiddenTokens":false,"hiddenTokens":[],"interfaces":[{"$type":"Interface","name":"Common","attributes":[{"$type":"TypeAttribute","name":"accDescr","isOptional":true,"type":{"$type":"SimpleType","primitiveType":"string"}},{"$type":"TypeAttribute","name":"accTitle","isOptional":true,"type":{"$type":"SimpleType","primitiveType":"string"}},{"$type":"TypeAttribute","name":"title","isOptional":true,"type":{"$type":"SimpleType","primitiveType":"string"}}],"superTypes":[]}],"types":[],"usedGrammars":[]}')),"PieGrammar"),Wa,tg=I(()=>Wa??(Wa=an('{"$type":"Grammar","isDeclared":true,"name":"Architecture","imports":[],"rules":[{"$type":"ParserRule","name":"Architecture","entry":true,"definition":{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@18"},"arguments":[],"cardinality":"*"},{"$type":"Keyword","value":"architecture-beta"},{"$type":"Alternatives","elements":[{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@18"},"arguments":[],"cardinality":"*"},{"$type":"RuleCall","rule":{"$ref":"#/rules@16"},"arguments":[]}]},{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@18"},"arguments":[],"cardinality":"*"},{"$type":"RuleCall","rule":{"$ref":"#/rules@1"},"arguments":[],"cardinality":"*"}]},{"$type":"RuleCall","rule":{"$ref":"#/rules@18"},"arguments":[],"cardinality":"*"}]}]},"definesHiddenTokens":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Statement","fragment":true,"definition":{"$type":"Alternatives","elements":[{"$type":"Assignment","feature":"groups","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@5"},"arguments":[]}},{"$type":"Assignment","feature":"services","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@6"},"arguments":[]}},{"$type":"Assignment","feature":"junctions","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@7"},"arguments":[]}},{"$type":"Assignment","feature":"edges","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@8"},"arguments":[]}}]},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"LeftPort","fragment":true,"definition":{"$type":"Group","elements":[{"$type":"Keyword","value":":"},{"$type":"Assignment","feature":"lhsDir","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@9"},"arguments":[]}}]},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"RightPort","fragment":true,"definition":{"$type":"Group","elements":[{"$type":"Assignment","feature":"rhsDir","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@9"},"arguments":[]}},{"$type":"Keyword","value":":"}]},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Arrow","fragment":true,"definition":{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[]},{"$type":"Assignment","feature":"lhsInto","operator":"?=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@15"},"arguments":[]},"cardinality":"?"},{"$type":"Alternatives","elements":[{"$type":"Keyword","value":"--"},{"$type":"Group","elements":[{"$type":"Keyword","value":"-"},{"$type":"Assignment","feature":"title","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@13"},"arguments":[]}},{"$type":"Keyword","value":"-"}]}]},{"$type":"Assignment","feature":"rhsInto","operator":"?=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@15"},"arguments":[]},"cardinality":"?"},{"$type":"RuleCall","rule":{"$ref":"#/rules@3"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Group","definition":{"$type":"Group","elements":[{"$type":"Keyword","value":"group"},{"$type":"Assignment","feature":"id","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@10"},"arguments":[]}},{"$type":"Assignment","feature":"icon","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@12"},"arguments":[]},"cardinality":"?"},{"$type":"Assignment","feature":"title","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@13"},"arguments":[]},"cardinality":"?"},{"$type":"Group","elements":[{"$type":"Keyword","value":"in"},{"$type":"Assignment","feature":"in","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@10"},"arguments":[]}}],"cardinality":"?"},{"$type":"RuleCall","rule":{"$ref":"#/rules@17"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Service","definition":{"$type":"Group","elements":[{"$type":"Keyword","value":"service"},{"$type":"Assignment","feature":"id","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@10"},"arguments":[]}},{"$type":"Alternatives","elements":[{"$type":"Assignment","feature":"iconText","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@11"},"arguments":[]}},{"$type":"Assignment","feature":"icon","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@12"},"arguments":[]}}],"cardinality":"?"},{"$type":"Assignment","feature":"title","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@13"},"arguments":[]},"cardinality":"?"},{"$type":"Group","elements":[{"$type":"Keyword","value":"in"},{"$type":"Assignment","feature":"in","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@10"},"arguments":[]}}],"cardinality":"?"},{"$type":"RuleCall","rule":{"$ref":"#/rules@17"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Junction","definition":{"$type":"Group","elements":[{"$type":"Keyword","value":"junction"},{"$type":"Assignment","feature":"id","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@10"},"arguments":[]}},{"$type":"Group","elements":[{"$type":"Keyword","value":"in"},{"$type":"Assignment","feature":"in","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@10"},"arguments":[]}}],"cardinality":"?"},{"$type":"RuleCall","rule":{"$ref":"#/rules@17"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Edge","definition":{"$type":"Group","elements":[{"$type":"Assignment","feature":"lhsId","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@10"},"arguments":[]}},{"$type":"Assignment","feature":"lhsGroup","operator":"?=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@14"},"arguments":[]},"cardinality":"?"},{"$type":"RuleCall","rule":{"$ref":"#/rules@4"},"arguments":[]},{"$type":"Assignment","feature":"rhsId","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@10"},"arguments":[]}},{"$type":"Assignment","feature":"rhsGroup","operator":"?=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@14"},"arguments":[]},"cardinality":"?"},{"$type":"RuleCall","rule":{"$ref":"#/rules@17"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"TerminalRule","name":"ARROW_DIRECTION","definition":{"$type":"TerminalAlternatives","elements":[{"$type":"TerminalAlternatives","elements":[{"$type":"TerminalAlternatives","elements":[{"$type":"CharacterRange","left":{"$type":"Keyword","value":"L"}},{"$type":"CharacterRange","left":{"$type":"Keyword","value":"R"}}]},{"$type":"CharacterRange","left":{"$type":"Keyword","value":"T"}}]},{"$type":"CharacterRange","left":{"$type":"Keyword","value":"B"}}]},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ARCH_ID","definition":{"$type":"RegexToken","regex":"/[\\\\w]+/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ARCH_TEXT_ICON","definition":{"$type":"RegexToken","regex":"/\\\\(\\"[^\\"]+\\"\\\\)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ARCH_ICON","definition":{"$type":"RegexToken","regex":"/\\\\([\\\\w-:]+\\\\)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ARCH_TITLE","definition":{"$type":"RegexToken","regex":"/\\\\[[\\\\w ]+\\\\]/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ARROW_GROUP","definition":{"$type":"RegexToken","regex":"/\\\\{group\\\\}/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ARROW_INTO","definition":{"$type":"RegexToken","regex":"/<|>/"},"fragment":false,"hidden":false},{"$type":"ParserRule","name":"TitleAndAccessibilities","fragment":true,"definition":{"$type":"Group","elements":[{"$type":"Alternatives","elements":[{"$type":"Assignment","feature":"accDescr","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@19"},"arguments":[]}},{"$type":"Assignment","feature":"accTitle","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@20"},"arguments":[]}},{"$type":"Assignment","feature":"title","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@21"},"arguments":[]}}]},{"$type":"RuleCall","rule":{"$ref":"#/rules@17"},"arguments":[]}],"cardinality":"+"},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"EOL","fragment":true,"dataType":"string","definition":{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@18"},"arguments":[],"cardinality":"+"},{"$type":"EndOfFile"}]},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"TerminalRule","name":"NEWLINE","definition":{"$type":"RegexToken","regex":"/\\\\r?\\\\n/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ACC_DESCR","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*accDescr(?:[\\\\t ]*:([^\\\\n\\\\r]*?(?=%%)|[^\\\\n\\\\r]*)|\\\\s*{([^}]*)})/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ACC_TITLE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*accTitle[\\\\t ]*:(?:[^\\\\n\\\\r]*?(?=%%)|[^\\\\n\\\\r]*)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"TITLE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*title(?:[\\\\t ][^\\\\n\\\\r]*?(?=%%)|[\\\\t ][^\\\\n\\\\r]*|)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","hidden":true,"name":"WHITESPACE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]+/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"YAML","definition":{"$type":"RegexToken","regex":"/---[\\\\t ]*\\\\r?\\\\n(?:[\\\\S\\\\s]*?\\\\r?\\\\n)?---(?:\\\\r?\\\\n|(?!\\\\S))/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"DIRECTIVE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*%%{[\\\\S\\\\s]*?}%%(?:\\\\r?\\\\n|(?!\\\\S))/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"SINGLE_LINE_COMMENT","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*%%[^\\\\n\\\\r]*/"},"fragment":false}],"definesHiddenTokens":false,"hiddenTokens":[],"interfaces":[{"$type":"Interface","name":"Common","attributes":[{"$type":"TypeAttribute","name":"accDescr","isOptional":true,"type":{"$type":"SimpleType","primitiveType":"string"}},{"$type":"TypeAttribute","name":"accTitle","isOptional":true,"type":{"$type":"SimpleType","primitiveType":"string"}},{"$type":"TypeAttribute","name":"title","isOptional":true,"type":{"$type":"SimpleType","primitiveType":"string"}}],"superTypes":[]}],"types":[],"usedGrammars":[]}')),"ArchitectureGrammar"),ja,ng=I(()=>ja??(ja=an(`{"$type":"Grammar","isDeclared":true,"name":"GitGraph","interfaces":[{"$type":"Interface","name":"Common","attributes":[{"$type":"TypeAttribute","name":"accDescr","isOptional":true,"type":{"$type":"SimpleType","primitiveType":"string"}},{"$type":"TypeAttribute","name":"accTitle","isOptional":true,"type":{"$type":"SimpleType","primitiveType":"string"}},{"$type":"TypeAttribute","name":"title","isOptional":true,"type":{"$type":"SimpleType","primitiveType":"string"}}],"superTypes":[]}],"rules":[{"$type":"ParserRule","name":"TitleAndAccessibilities","fragment":true,"definition":{"$type":"Group","elements":[{"$type":"Alternatives","elements":[{"$type":"Assignment","feature":"accDescr","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@3"},"arguments":[]}},{"$type":"Assignment","feature":"accTitle","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@4"},"arguments":[]}},{"$type":"Assignment","feature":"title","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@5"},"arguments":[]}}]},{"$type":"RuleCall","rule":{"$ref":"#/rules@1"},"arguments":[]}],"cardinality":"+"},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"EOL","fragment":true,"dataType":"string","definition":{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[],"cardinality":"+"},{"$type":"EndOfFile"}]},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"TerminalRule","name":"NEWLINE","definition":{"$type":"RegexToken","regex":"/\\\\r?\\\\n/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ACC_DESCR","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*accDescr(?:[\\\\t ]*:([^\\\\n\\\\r]*?(?=%%)|[^\\\\n\\\\r]*)|\\\\s*{([^}]*)})/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ACC_TITLE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*accTitle[\\\\t ]*:(?:[^\\\\n\\\\r]*?(?=%%)|[^\\\\n\\\\r]*)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"TITLE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*title(?:[\\\\t ][^\\\\n\\\\r]*?(?=%%)|[\\\\t ][^\\\\n\\\\r]*|)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","hidden":true,"name":"WHITESPACE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]+/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"YAML","definition":{"$type":"RegexToken","regex":"/---[\\\\t ]*\\\\r?\\\\n(?:[\\\\S\\\\s]*?\\\\r?\\\\n)?---(?:\\\\r?\\\\n|(?!\\\\S))/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"DIRECTIVE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*%%{[\\\\S\\\\s]*?}%%(?:\\\\r?\\\\n|(?!\\\\S))/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"SINGLE_LINE_COMMENT","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*%%[^\\\\n\\\\r]*/"},"fragment":false},{"$type":"ParserRule","name":"GitGraph","entry":true,"definition":{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[],"cardinality":"*"},{"$type":"Alternatives","elements":[{"$type":"Keyword","value":"gitGraph"},{"$type":"Group","elements":[{"$type":"Keyword","value":"gitGraph"},{"$type":"Keyword","value":":"}]},{"$type":"Keyword","value":"gitGraph:"},{"$type":"Group","elements":[{"$type":"Keyword","value":"gitGraph"},{"$type":"RuleCall","rule":{"$ref":"#/rules@12"},"arguments":[]},{"$type":"Keyword","value":":"}]}]},{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[],"cardinality":"*"},{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[],"cardinality":"*"},{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@0"},"arguments":[]},{"$type":"Assignment","feature":"statements","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@11"},"arguments":[]}},{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[]}],"cardinality":"*"}]}]},"definesHiddenTokens":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Statement","definition":{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@13"},"arguments":[]},{"$type":"RuleCall","rule":{"$ref":"#/rules@14"},"arguments":[]},{"$type":"RuleCall","rule":{"$ref":"#/rules@15"},"arguments":[]},{"$type":"RuleCall","rule":{"$ref":"#/rules@16"},"arguments":[]},{"$type":"RuleCall","rule":{"$ref":"#/rules@17"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Direction","definition":{"$type":"Assignment","feature":"dir","operator":"=","terminal":{"$type":"Alternatives","elements":[{"$type":"Keyword","value":"LR"},{"$type":"Keyword","value":"TB"},{"$type":"Keyword","value":"BT"}]}},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Commit","definition":{"$type":"Group","elements":[{"$type":"Keyword","value":"commit"},{"$type":"Alternatives","elements":[{"$type":"Group","elements":[{"$type":"Keyword","value":"id:"},{"$type":"Assignment","feature":"id","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@20"},"arguments":[]}}]},{"$type":"Group","elements":[{"$type":"Keyword","value":"msg:","cardinality":"?"},{"$type":"Assignment","feature":"message","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@20"},"arguments":[]}}]},{"$type":"Group","elements":[{"$type":"Keyword","value":"tag:"},{"$type":"Assignment","feature":"tags","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@20"},"arguments":[]}}]},{"$type":"Group","elements":[{"$type":"Keyword","value":"type:"},{"$type":"Assignment","feature":"type","operator":"=","terminal":{"$type":"Alternatives","elements":[{"$type":"Keyword","value":"NORMAL"},{"$type":"Keyword","value":"REVERSE"},{"$type":"Keyword","value":"HIGHLIGHT"}]}}]}],"cardinality":"*"},{"$type":"RuleCall","rule":{"$ref":"#/rules@1"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Branch","definition":{"$type":"Group","elements":[{"$type":"Keyword","value":"branch"},{"$type":"Assignment","feature":"name","operator":"=","terminal":{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@19"},"arguments":[]},{"$type":"RuleCall","rule":{"$ref":"#/rules@20"},"arguments":[]}]}},{"$type":"Group","elements":[{"$type":"Keyword","value":"order:"},{"$type":"Assignment","feature":"order","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@18"},"arguments":[]}}],"cardinality":"?"},{"$type":"RuleCall","rule":{"$ref":"#/rules@1"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Merge","definition":{"$type":"Group","elements":[{"$type":"Keyword","value":"merge"},{"$type":"Assignment","feature":"branch","operator":"=","terminal":{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@19"},"arguments":[]},{"$type":"RuleCall","rule":{"$ref":"#/rules@20"},"arguments":[]}]}},{"$type":"Alternatives","elements":[{"$type":"Group","elements":[{"$type":"Keyword","value":"id:"},{"$type":"Assignment","feature":"id","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@20"},"arguments":[]}}]},{"$type":"Group","elements":[{"$type":"Keyword","value":"tag:"},{"$type":"Assignment","feature":"tags","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@20"},"arguments":[]}}]},{"$type":"Group","elements":[{"$type":"Keyword","value":"type:"},{"$type":"Assignment","feature":"type","operator":"=","terminal":{"$type":"Alternatives","elements":[{"$type":"Keyword","value":"NORMAL"},{"$type":"Keyword","value":"REVERSE"},{"$type":"Keyword","value":"HIGHLIGHT"}]}}]}],"cardinality":"*"},{"$type":"RuleCall","rule":{"$ref":"#/rules@1"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Checkout","definition":{"$type":"Group","elements":[{"$type":"Alternatives","elements":[{"$type":"Keyword","value":"checkout"},{"$type":"Keyword","value":"switch"}]},{"$type":"Assignment","feature":"branch","operator":"=","terminal":{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@19"},"arguments":[]},{"$type":"RuleCall","rule":{"$ref":"#/rules@20"},"arguments":[]}]}},{"$type":"RuleCall","rule":{"$ref":"#/rules@1"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"CherryPicking","definition":{"$type":"Group","elements":[{"$type":"Keyword","value":"cherry-pick"},{"$type":"Alternatives","elements":[{"$type":"Group","elements":[{"$type":"Keyword","value":"id:"},{"$type":"Assignment","feature":"id","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@20"},"arguments":[]}}]},{"$type":"Group","elements":[{"$type":"Keyword","value":"tag:"},{"$type":"Assignment","feature":"tags","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@20"},"arguments":[]}}]},{"$type":"Group","elements":[{"$type":"Keyword","value":"parent:"},{"$type":"Assignment","feature":"parent","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@20"},"arguments":[]}}]}],"cardinality":"*"},{"$type":"RuleCall","rule":{"$ref":"#/rules@1"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"TerminalRule","name":"INT","type":{"$type":"ReturnType","name":"number"},"definition":{"$type":"RegexToken","regex":"/[0-9]+(?=\\\\s)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ID","type":{"$type":"ReturnType","name":"string"},"definition":{"$type":"RegexToken","regex":"/\\\\w([-\\\\./\\\\w]*[-\\\\w])?/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"STRING","definition":{"$type":"RegexToken","regex":"/\\"[^\\"]*\\"|'[^']*'/"},"fragment":false,"hidden":false}],"definesHiddenTokens":false,"hiddenTokens":[],"imports":[],"types":[],"usedGrammars":[]}`)),"GitGraphGrammar"),rg={languageId:"info",fileExtensions:[".mmd",".mermaid"],caseInsensitive:!1},ig={languageId:"packet",fileExtensions:[".mmd",".mermaid"],caseInsensitive:!1},sg={languageId:"pie",fileExtensions:[".mmd",".mermaid"],caseInsensitive:!1},ag={languageId:"architecture",fileExtensions:[".mmd",".mermaid"],caseInsensitive:!1},og={languageId:"gitGraph",fileExtensions:[".mmd",".mermaid"],caseInsensitive:!1},on={AstReflection:I(()=>new fc,"AstReflection")},lg={Grammar:I(()=>Qm(),"Grammar"),LanguageMetaData:I(()=>rg,"LanguageMetaData"),parser:{}},cg={Grammar:I(()=>Zm(),"Grammar"),LanguageMetaData:I(()=>ig,"LanguageMetaData"),parser:{}},ug={Grammar:I(()=>eg(),"Grammar"),LanguageMetaData:I(()=>sg,"LanguageMetaData"),parser:{}},dg={Grammar:I(()=>tg(),"Grammar"),LanguageMetaData:I(()=>ag,"LanguageMetaData"),parser:{}},fg={Grammar:I(()=>ng(),"Grammar"),LanguageMetaData:I(()=>og,"LanguageMetaData"),parser:{}},hg=/accDescr(?:[\t ]*:([^\n\r]*)|\s*{([^}]*)})/,pg=/accTitle[\t ]*:([^\n\r]*)/,mg=/title([\t ][^\n\r]*|)/,gg={ACC_DESCR:hg,ACC_TITLE:pg,TITLE:mg},$i=class extends ql{static{I(this,"AbstractMermaidValueConverter")}runConverter(n,e,t){let r=this.runCommonConverter(n,e,t);return r===void 0&&(r=this.runCustomConverter(n,e,t)),r===void 0?super.runConverter(n,e,t):r}runCommonConverter(n,e,t){const r=gg[n.name];if(r===void 0)return;const i=r.exec(e);if(i!==null){if(i[1]!==void 0)return i[1].trim().replace(/[\t ]{2,}/gm," ");if(i[2]!==void 0)return i[2].replace(/^\s*/gm,"").replace(/\s+$/gm,"").replace(/[\t ]{2,}/gm," ").replace(/[\n\r]{2,}/gm,`
`)}}},Oi=class extends $i{static{I(this,"CommonValueConverter")}runCustomConverter(n,e,t){}},Ut=class extends zl{static{I(this,"AbstractMermaidTokenBuilder")}constructor(n){super(),this.keywords=new Set(n)}buildKeywordTokens(n,e,t){const r=super.buildKeywordTokens(n,e,t);return r.forEach(i=>{this.keywords.has(i.name)&&i.PATTERN!==void 0&&(i.PATTERN=new RegExp(i.PATTERN.toString()+"(?:(?=%%)|(?!\\S))"))}),r}};(class extends Ut{static{I(this,"CommonTokenBuilder")}});var yg=class extends Ut{static{I(this,"GitGraphTokenBuilder")}constructor(){super(["gitGraph"])}},Tg={parser:{TokenBuilder:I(()=>new yg,"TokenBuilder"),ValueConverter:I(()=>new Oi,"ValueConverter")}};function Rg(n=Gt){const e=we(Ft(n),on),t=we(Dt({shared:e}),fg,Tg);return e.ServiceRegistry.register(t),{shared:e,GitGraph:t}}I(Rg,"createGitGraphServices");var Ag=class extends Ut{static{I(this,"InfoTokenBuilder")}constructor(){super(["info","showInfo"])}},Eg={parser:{TokenBuilder:I(()=>new Ag,"TokenBuilder"),ValueConverter:I(()=>new Oi,"ValueConverter")}};function vg(n=Gt){const e=we(Ft(n),on),t=we(Dt({shared:e}),lg,Eg);return e.ServiceRegistry.register(t),{shared:e,Info:t}}I(vg,"createInfoServices");var kg=class extends Ut{static{I(this,"PacketTokenBuilder")}constructor(){super(["packet-beta"])}},Sg={parser:{TokenBuilder:I(()=>new kg,"TokenBuilder"),ValueConverter:I(()=>new Oi,"ValueConverter")}};function Ig(n=Gt){const e=we(Ft(n),on),t=we(Dt({shared:e}),cg,Sg);return e.ServiceRegistry.register(t),{shared:e,Packet:t}}I(Ig,"createPacketServices");var xg=class extends Ut{static{I(this,"PieTokenBuilder")}constructor(){super(["pie","showData"])}},Ng=class extends $i{static{I(this,"PieValueConverter")}runCustomConverter(n,e,t){if(n.name==="PIE_SECTION_LABEL")return e.replace(/"/g,"").trim()}},Cg={parser:{TokenBuilder:I(()=>new xg,"TokenBuilder"),ValueConverter:I(()=>new Ng,"ValueConverter")}};function wg(n=Gt){const e=we(Ft(n),on),t=we(Dt({shared:e}),ug,Cg);return e.ServiceRegistry.register(t),{shared:e,Pie:t}}I(wg,"createPieServices");var _g=class extends Ut{static{I(this,"ArchitectureTokenBuilder")}constructor(){super(["architecture"])}},Lg=class extends $i{static{I(this,"ArchitectureValueConverter")}runCustomConverter(n,e,t){if(n.name==="ARCH_ICON")return e.replace(/[()]/g,"").trim();if(n.name==="ARCH_TEXT_ICON")return e.replace(/["()]/g,"");if(n.name==="ARCH_TITLE")return e.replace(/[[\]]/g,"").trim()}},$g={parser:{TokenBuilder:I(()=>new _g,"TokenBuilder"),ValueConverter:I(()=>new Lg,"ValueConverter")}};function Og(n=Gt){const e=we(Ft(n),on),t=we(Dt({shared:e}),dg,$g);return e.ServiceRegistry.register(t),{shared:e,Architecture:t}}I(Og,"createArchitectureServices");var ot={},bg={info:I(async()=>{const{createInfoServices:n}=await Bt(()=>import("./info-46DW6VJ7-T6H96B96.js"),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9]),import.meta.url),e=n().Info.parser.LangiumParser;ot.info=e},"info"),packet:I(async()=>{const{createPacketServices:n}=await Bt(()=>import("./packet-W2GHVCYJ-Dn1z_8ol.js"),__vite__mapDeps([10,1,2,3,4,5,6,7,8,9]),import.meta.url),e=n().Packet.parser.LangiumParser;ot.packet=e},"packet"),pie:I(async()=>{const{createPieServices:n}=await Bt(()=>import("./pie-BEWT4RHE-hOQ3iOqr.js"),__vite__mapDeps([11,1,2,3,4,5,6,7,8,9]),import.meta.url),e=n().Pie.parser.LangiumParser;ot.pie=e},"pie"),architecture:I(async()=>{const{createArchitectureServices:n}=await Bt(()=>import("./architecture-I3QFYML2-B1cwIXOR.js"),__vite__mapDeps([12,1,2,3,4,5,6,7,8,9]),import.meta.url),e=n().Architecture.parser.LangiumParser;ot.architecture=e},"architecture"),gitGraph:I(async()=>{const{createGitGraphServices:n}=await Bt(()=>import("./gitGraph-YCYPL57B-BCpxlQQm.js"),__vite__mapDeps([13,1,2,3,4,5,6,7,8,9]),import.meta.url),e=n().GitGraph.parser.LangiumParser;ot.gitGraph=e},"gitGraph")};async function Pg(n,e){const t=bg[n];if(!t)throw new Error(`Unknown diagram type: ${n}`);ot[n]||await t();const i=ot[n].parse(e);if(i.lexerErrors.length>0||i.parserErrors.length>0)throw new Mg(i);return i.value}I(Pg,"parse");var Mg=class extends Error{constructor(n){const e=n.lexerErrors.map(r=>r.message).join(`
`),t=n.parserErrors.map(r=>r.message).join(`
`);super(`Parsing failed: ${e} ${t}`),this.result=n}static{I(this,"MermaidParseError")}};export{$g as A,Tg as G,Eg as I,Sg as P,Ig as a,Cg as b,vg as c,wg as d,Og as e,Rg as f,Pg as p};
//# sourceMappingURL=mermaid-parser.core-CrjEtzGx.js.map
