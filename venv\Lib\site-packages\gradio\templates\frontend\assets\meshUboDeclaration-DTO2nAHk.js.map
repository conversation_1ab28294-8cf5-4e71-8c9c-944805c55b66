{"version": 3, "file": "meshUboDeclaration-DTO2nAHk.js", "sources": ["../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/ShadersWGSL/ShadersInclude/sceneUboDeclaration.js", "../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/ShadersWGSL/ShadersInclude/meshUboDeclaration.js"], "sourcesContent": ["// Do not edit.\nimport { ShaderStore } from \"../../Engines/shaderStore.js\";\nconst name = \"sceneUboDeclaration\";\nconst shader = `struct Scene {viewProjection : mat4x4<f32>,\n#ifdef MULTIVIEW\nviewProjectionR : mat4x4<f32>,\n#endif \nview : mat4x4<f32>,\nprojection : mat4x4<f32>,\nvEyePosition : vec4<f32>,};\n#define SCENE_UBO\nvar<uniform> scene : Scene;\n`;\n// Sideeffect\nif (!ShaderStore.IncludesShadersStoreWGSL[name]) {\n    ShaderStore.IncludesShadersStoreWGSL[name] = shader;\n}\n/** @internal */\nexport const sceneUboDeclarationWGSL = { name, shader };\n//# sourceMappingURL=sceneUboDeclaration.js.map", "// Do not edit.\nimport { ShaderStore } from \"../../Engines/shaderStore.js\";\nconst name = \"meshUboDeclaration\";\nconst shader = `struct Mesh {world : mat4x4<f32>,\nvisibility : f32,};var<uniform> mesh : Mesh;\n#define WORLD_UBO\n`;\n// Sideeffect\nif (!ShaderStore.IncludesShadersStoreWGSL[name]) {\n    ShaderStore.IncludesShadersStoreWGSL[name] = shader;\n}\n/** @internal */\nexport const meshUboDeclarationWGSL = { name, shader };\n//# sourceMappingURL=meshUboDeclaration.js.map"], "names": ["name", "shader", "ShaderStore"], "mappings": "wCAEA,MAAMA,EAAO,sBACPC,EAAS;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWVC,EAAY,yBAAyBF,CAAI,IAC1CE,EAAY,yBAAyBF,CAAI,EAAIC,GCbjD,MAAMD,EAAO,qBACPC,EAAS;AAAA;AAAA;AAAA,EAKVC,EAAY,yBAAyBF,CAAI,IAC1CE,EAAY,yBAAyBF,CAAI,EAAIC", "x_google_ignoreList": [0, 1]}