{"version": 3, "file": "objectModelMapping-ErbZJar3.js", "sources": ["../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/Engines/constants.js", "../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/Lights/shadowLight.js", "../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/Lights/spotLight.js", "../../../../node_modules/.pnpm/@babylonjs+loaders@8.2.0_@babylonjs+core@8.2.0_babylonjs-gltf2interface@7.25.2/node_modules/@babylonjs/loaders/glTF/2.0/Extensions/gltfPathToObjectConverter.js", "../../../../node_modules/.pnpm/@babylonjs+loaders@8.2.0_@babylonjs+core@8.2.0_babylonjs-gltf2interface@7.25.2/node_modules/@babylonjs/loaders/glTF/2.0/Extensions/objectModelMapping.js"], "sourcesContent": ["/* eslint-disable @typescript-eslint/naming-convention */\n/** Defines the cross module used constants to avoid circular dependencies */\nexport class Constants {\n}\n/** Sampler suffix when associated with a texture name */\nConstants.AUTOSAMPLERSUFFIX = \"Sampler\";\n/** Flag used to disable diagnostics for WebGPU */\nConstants.DISABLEUA = \"#define DISABLE_UNIFORMITY_ANALYSIS\";\n/** Defines that alpha blending is disabled */\nConstants.ALPHA_DISABLE = 0;\n/** Defines that alpha blending is SRC ALPHA * SRC + DEST */\nConstants.ALPHA_ADD = 1;\n/** Defines that alpha blending is SRC ALPHA * SRC + (1 - <PERSON><PERSON> ALPHA) * DEST */\nConstants.ALPHA_COMBINE = 2;\n/** Defines that alpha blending is DEST - SRC * DEST */\nConstants.ALPHA_SUBTRACT = 3;\n/** Defines that alpha blending is SRC * DEST */\nConstants.ALPHA_MULTIPLY = 4;\n/** Defines that alpha blending is SRC ALPHA * SRC + (1 - <PERSON><PERSON>) * DEST */\nConstants.ALPHA_MAXIMIZED = 5;\n/** Defines that alpha blending is SRC + DEST */\nConstants.ALPHA_ONEONE = 6;\n/** Defines that alpha blending is SRC + (1 - SRC ALPHA) * DEST */\nConstants.ALPHA_PREMULTIPLIED = 7;\n/**\n * Defines that alpha blending is SRC + (1 - SRC ALPHA) * DEST\n * Alpha will be set to (1 - SRC ALPHA) * DEST ALPHA\n */\nConstants.ALPHA_PREMULTIPLIED_PORTERDUFF = 8;\n/** Defines that alpha blending is CST * SRC + (1 - CST) * DEST */\nConstants.ALPHA_INTERPOLATE = 9;\n/**\n * Defines that alpha blending is SRC + (1 - SRC) * DEST\n * Alpha will be set to SRC ALPHA + (1 - SRC ALPHA) * DEST ALPHA\n */\nConstants.ALPHA_SCREENMODE = 10;\n/**\n * Defines that alpha blending is SRC + DST\n * Alpha will be set to SRC ALPHA + DST ALPHA\n */\nConstants.ALPHA_ONEONE_ONEONE = 11;\n/**\n * Defines that alpha blending is SRC * DST ALPHA + DST\n * Alpha will be set to 0\n */\nConstants.ALPHA_ALPHATOCOLOR = 12;\n/**\n * Defines that alpha blending is SRC * (1 - DST) + DST * (1 - SRC)\n */\nConstants.ALPHA_REVERSEONEMINUS = 13;\n/**\n * Defines that alpha blending is SRC + DST * (1 - SRC ALPHA)\n * Alpha will be set to SRC ALPHA + DST ALPHA * (1 - SRC ALPHA)\n */\nConstants.ALPHA_SRC_DSTONEMINUSSRCALPHA = 14;\n/**\n * Defines that alpha blending is SRC + DST\n * Alpha will be set to SRC ALPHA\n */\nConstants.ALPHA_ONEONE_ONEZERO = 15;\n/**\n * Defines that alpha blending is SRC * (1 - DST) + DST * (1 - SRC)\n * Alpha will be set to DST ALPHA\n */\nConstants.ALPHA_EXCLUSION = 16;\n/**\n * Defines that alpha blending is SRC * SRC ALPHA + DST * (1 - SRC ALPHA)\n * Alpha will be set to SRC ALPHA + (1 - SRC ALPHA) * DST ALPHA\n */\nConstants.ALPHA_LAYER_ACCUMULATE = 17;\n/** Defines that alpha blending equation a SUM */\nConstants.ALPHA_EQUATION_ADD = 0;\n/** Defines that alpha blending equation a SUBSTRACTION */\nConstants.ALPHA_EQUATION_SUBSTRACT = 1;\n/** Defines that alpha blending equation a REVERSE SUBSTRACTION */\nConstants.ALPHA_EQUATION_REVERSE_SUBTRACT = 2;\n/** Defines that alpha blending equation a MAX operation */\nConstants.ALPHA_EQUATION_MAX = 3;\n/** Defines that alpha blending equation a MIN operation */\nConstants.ALPHA_EQUATION_MIN = 4;\n/**\n * Defines that alpha blending equation a DARKEN operation:\n * It takes the min of the src and sums the alpha channels.\n */\nConstants.ALPHA_EQUATION_DARKEN = 5;\n/** Defines that the resource is not delayed*/\nConstants.DELAYLOADSTATE_NONE = 0;\n/** Defines that the resource was successfully delay loaded */\nConstants.DELAYLOADSTATE_LOADED = 1;\n/** Defines that the resource is currently delay loading */\nConstants.DELAYLOADSTATE_LOADING = 2;\n/** Defines that the resource is delayed and has not started loading */\nConstants.DELAYLOADSTATE_NOTLOADED = 4;\n// Depth or Stencil test Constants.\n/** Passed to depthFunction or stencilFunction to specify depth or stencil tests will never pass. i.e. Nothing will be drawn */\nConstants.NEVER = 0x0200;\n/** Passed to depthFunction or stencilFunction to specify depth or stencil tests will always pass. i.e. Pixels will be drawn in the order they are drawn */\nConstants.ALWAYS = 0x0207;\n/** Passed to depthFunction or stencilFunction to specify depth or stencil tests will pass if the new depth value is less than the stored value */\nConstants.LESS = 0x0201;\n/** Passed to depthFunction or stencilFunction to specify depth or stencil tests will pass if the new depth value is equals to the stored value */\nConstants.EQUAL = 0x0202;\n/** Passed to depthFunction or stencilFunction to specify depth or stencil tests will pass if the new depth value is less than or equal to the stored value */\nConstants.LEQUAL = 0x0203;\n/** Passed to depthFunction or stencilFunction to specify depth or stencil tests will pass if the new depth value is greater than the stored value */\nConstants.GREATER = 0x0204;\n/** Passed to depthFunction or stencilFunction to specify depth or stencil tests will pass if the new depth value is greater than or equal to the stored value */\nConstants.GEQUAL = 0x0206;\n/** Passed to depthFunction or stencilFunction to specify depth or stencil tests will pass if the new depth value is not equal to the stored value */\nConstants.NOTEQUAL = 0x0205;\n// Stencil Actions Constants.\n/** Passed to stencilOperation to specify that stencil value must be kept */\nConstants.KEEP = 0x1e00;\n/** Passed to stencilOperation to specify that stencil value must be zero */\nConstants.ZERO = 0x0000;\n/** Passed to stencilOperation to specify that stencil value must be replaced */\nConstants.REPLACE = 0x1e01;\n/** Passed to stencilOperation to specify that stencil value must be incremented */\nConstants.INCR = 0x1e02;\n/** Passed to stencilOperation to specify that stencil value must be decremented */\nConstants.DECR = 0x1e03;\n/** Passed to stencilOperation to specify that stencil value must be inverted */\nConstants.INVERT = 0x150a;\n/** Passed to stencilOperation to specify that stencil value must be incremented with wrapping */\nConstants.INCR_WRAP = 0x8507;\n/** Passed to stencilOperation to specify that stencil value must be decremented with wrapping */\nConstants.DECR_WRAP = 0x8508;\n/** Texture is not repeating outside of 0..1 UVs */\nConstants.TEXTURE_CLAMP_ADDRESSMODE = 0;\n/** Texture is repeating outside of 0..1 UVs */\nConstants.TEXTURE_WRAP_ADDRESSMODE = 1;\n/** Texture is repeating and mirrored */\nConstants.TEXTURE_MIRROR_ADDRESSMODE = 2;\n/** Flag to create a storage texture */\nConstants.TEXTURE_CREATIONFLAG_STORAGE = 1;\n/** ALPHA */\nConstants.TEXTUREFORMAT_ALPHA = 0;\n/** LUMINANCE */\nConstants.TEXTUREFORMAT_LUMINANCE = 1;\n/** LUMINANCE_ALPHA */\nConstants.TEXTUREFORMAT_LUMINANCE_ALPHA = 2;\n/** RGB */\nConstants.TEXTUREFORMAT_RGB = 4;\n/** RGBA */\nConstants.TEXTUREFORMAT_RGBA = 5;\n/** RED */\nConstants.TEXTUREFORMAT_RED = 6;\n/** RED (2nd reference) */\nConstants.TEXTUREFORMAT_R = 6;\n/** RED unsigned short normed to [0, 1] **/\nConstants.TEXTUREFORMAT_R16_UNORM = 0x822a;\n/** RG unsigned short normed to [0, 1] **/\nConstants.TEXTUREFORMAT_RG16_UNORM = 0x822c;\n/** RGB unsigned short normed to [0, 1] **/\nConstants.TEXTUREFORMAT_RGB16_UNORM = 0x8054;\n/** RGBA unsigned short normed to [0, 1] **/\nConstants.TEXTUREFORMAT_RGBA16_UNORM = 0x805b;\n/** RED signed short normed to [-1, 1] **/\nConstants.TEXTUREFORMAT_R16_SNORM = 0x8f98;\n/** RG signed short normed to [-1, 1] **/\nConstants.TEXTUREFORMAT_RG16_SNORM = 0x8f99;\n/** RGB signed short normed to [-1, 1] **/\nConstants.TEXTUREFORMAT_RGB16_SNORM = 0x8f9a;\n/** RGBA signed short normed to [-1, 1] **/\nConstants.TEXTUREFORMAT_RGBA16_SNORM = 0x8f9b;\n/** RG */\nConstants.TEXTUREFORMAT_RG = 7;\n/** RED_INTEGER */\nConstants.TEXTUREFORMAT_RED_INTEGER = 8;\n/** RED_INTEGER (2nd reference) */\nConstants.TEXTUREFORMAT_R_INTEGER = 8;\n/** RG_INTEGER */\nConstants.TEXTUREFORMAT_RG_INTEGER = 9;\n/** RGB_INTEGER */\nConstants.TEXTUREFORMAT_RGB_INTEGER = 10;\n/** RGBA_INTEGER */\nConstants.TEXTUREFORMAT_RGBA_INTEGER = 11;\n/** BGRA */\nConstants.TEXTUREFORMAT_BGRA = 12;\n/** Depth 24 bits + Stencil 8 bits */\nConstants.TEXTUREFORMAT_DEPTH24_STENCIL8 = 13;\n/** Depth 32 bits float */\nConstants.TEXTUREFORMAT_DEPTH32_FLOAT = 14;\n/** Depth 16 bits */\nConstants.TEXTUREFORMAT_DEPTH16 = 15;\n/** Depth 24 bits */\nConstants.TEXTUREFORMAT_DEPTH24 = 16;\n/** Depth 24 bits unorm + Stencil 8 bits */\nConstants.TEXTUREFORMAT_DEPTH24UNORM_STENCIL8 = 17;\n/** Depth 32 bits float + Stencil 8 bits */\nConstants.TEXTUREFORMAT_DEPTH32FLOAT_STENCIL8 = 18;\n/** Stencil 8 bits */\nConstants.TEXTUREFORMAT_STENCIL8 = 19;\n/** UNDEFINED */\nConstants.TEXTUREFORMAT_UNDEFINED = 0xffffffff;\n/** Compressed BC7 */\nConstants.TEXTUREFORMAT_COMPRESSED_RGBA_BPTC_UNORM = 36492;\n/** Compressed BC7 (SRGB) */\nConstants.TEXTUREFORMAT_COMPRESSED_SRGB_ALPHA_BPTC_UNORM = 36493;\n/** Compressed BC6 unsigned float */\nConstants.TEXTUREFORMAT_COMPRESSED_RGB_BPTC_UNSIGNED_FLOAT = 36495;\n/** Compressed BC6 signed float */\nConstants.TEXTUREFORMAT_COMPRESSED_RGB_BPTC_SIGNED_FLOAT = 36494;\n/** Compressed BC3 */\nConstants.TEXTUREFORMAT_COMPRESSED_RGBA_S3TC_DXT5 = 33779;\n/** Compressed BC3 (SRGB) */\nConstants.TEXTUREFORMAT_COMPRESSED_SRGB_ALPHA_S3TC_DXT5_EXT = 35919;\n/** Compressed BC2 */\nConstants.TEXTUREFORMAT_COMPRESSED_RGBA_S3TC_DXT3 = 33778;\n/** Compressed BC2 (SRGB) */\nConstants.TEXTUREFORMAT_COMPRESSED_SRGB_ALPHA_S3TC_DXT3_EXT = 35918;\n/** Compressed BC1 (RGBA) */\nConstants.TEXTUREFORMAT_COMPRESSED_RGBA_S3TC_DXT1 = 33777;\n/** Compressed BC1 (RGB) */\nConstants.TEXTUREFORMAT_COMPRESSED_RGB_S3TC_DXT1 = 33776;\n/** Compressed BC1 (SRGB+A) */\nConstants.TEXTUREFORMAT_COMPRESSED_SRGB_ALPHA_S3TC_DXT1_EXT = 35917;\n/** Compressed BC1 (SRGB) */\nConstants.TEXTUREFORMAT_COMPRESSED_SRGB_S3TC_DXT1_EXT = 35916;\n/** Compressed ASTC 4x4 */\nConstants.TEXTUREFORMAT_COMPRESSED_RGBA_ASTC_4x4 = 37808;\n/** Compressed ASTC 4x4 (SRGB) */\nConstants.TEXTUREFORMAT_COMPRESSED_SRGB8_ALPHA8_ASTC_4x4_KHR = 37840;\n/** Compressed ETC1 (RGB) */\nConstants.TEXTUREFORMAT_COMPRESSED_RGB_ETC1_WEBGL = 36196;\n/** Compressed ETC2 (RGB) */\nConstants.TEXTUREFORMAT_COMPRESSED_RGB8_ETC2 = 37492;\n/** Compressed ETC2 (SRGB) */\nConstants.TEXTUREFORMAT_COMPRESSED_SRGB8_ETC2 = 37493;\n/** Compressed ETC2 (RGB+A1) */\nConstants.TEXTUREFORMAT_COMPRESSED_RGB8_PUNCHTHROUGH_ALPHA1_ETC2 = 37494;\n/** Compressed ETC2 (SRGB+A1)*/\nConstants.TEXTUREFORMAT_COMPRESSED_SRGB8_PUNCHTHROUGH_ALPHA1_ETC2 = 37495;\n/** Compressed ETC2 (RGB+A) */\nConstants.TEXTUREFORMAT_COMPRESSED_RGBA8_ETC2_EAC = 37496;\n/** Compressed ETC2 (SRGB+1) */\nConstants.TEXTUREFORMAT_COMPRESSED_SRGB8_ALPHA8_ETC2_EAC = 37497;\n/** UNSIGNED_BYTE */\nConstants.TEXTURETYPE_UNSIGNED_BYTE = 0;\n/** @deprecated use more explicit TEXTURETYPE_UNSIGNED_BYTE instead. Use TEXTURETYPE_UNSIGNED_INTEGER for 32bits values.*/\nConstants.TEXTURETYPE_UNSIGNED_INT = 0;\n/** FLOAT */\nConstants.TEXTURETYPE_FLOAT = 1;\n/** HALF_FLOAT */\nConstants.TEXTURETYPE_HALF_FLOAT = 2;\n/** BYTE */\nConstants.TEXTURETYPE_BYTE = 3;\n/** SHORT */\nConstants.TEXTURETYPE_SHORT = 4;\n/** UNSIGNED_SHORT */\nConstants.TEXTURETYPE_UNSIGNED_SHORT = 5;\n/** INT */\nConstants.TEXTURETYPE_INT = 6;\n/** UNSIGNED_INT */\nConstants.TEXTURETYPE_UNSIGNED_INTEGER = 7;\n/** UNSIGNED_SHORT_4_4_4_4 */\nConstants.TEXTURETYPE_UNSIGNED_SHORT_4_4_4_4 = 8;\n/** UNSIGNED_SHORT_5_5_5_1 */\nConstants.TEXTURETYPE_UNSIGNED_SHORT_5_5_5_1 = 9;\n/** UNSIGNED_SHORT_5_6_5 */\nConstants.TEXTURETYPE_UNSIGNED_SHORT_5_6_5 = 10;\n/** UNSIGNED_INT_2_10_10_10_REV */\nConstants.TEXTURETYPE_UNSIGNED_INT_2_10_10_10_REV = 11;\n/** UNSIGNED_INT_24_8 */\nConstants.TEXTURETYPE_UNSIGNED_INT_24_8 = 12;\n/** UNSIGNED_INT_10F_11F_11F_REV */\nConstants.TEXTURETYPE_UNSIGNED_INT_10F_11F_11F_REV = 13;\n/** UNSIGNED_INT_5_9_9_9_REV */\nConstants.TEXTURETYPE_UNSIGNED_INT_5_9_9_9_REV = 14;\n/** FLOAT_32_UNSIGNED_INT_24_8_REV */\nConstants.TEXTURETYPE_FLOAT_32_UNSIGNED_INT_24_8_REV = 15;\n/** UNDEFINED */\nConstants.TEXTURETYPE_UNDEFINED = 16;\n/** 2D Texture target*/\nConstants.TEXTURE_2D = 3553;\n/** 2D Array Texture target */\nConstants.TEXTURE_2D_ARRAY = 35866;\n/** Cube Map Texture target */\nConstants.TEXTURE_CUBE_MAP = 34067;\n/** Cube Map Array Texture target */\nConstants.TEXTURE_CUBE_MAP_ARRAY = 0xdeadbeef;\n/** 3D Texture target */\nConstants.TEXTURE_3D = 32879;\n/** nearest is mag = nearest and min = nearest and no mip */\nConstants.TEXTURE_NEAREST_SAMPLINGMODE = 1;\n/** mag = nearest and min = nearest and mip = none */\nConstants.TEXTURE_NEAREST_NEAREST = 1;\n/** Bilinear is mag = linear and min = linear and no mip */\nConstants.TEXTURE_BILINEAR_SAMPLINGMODE = 2;\n/** mag = linear and min = linear and mip = none */\nConstants.TEXTURE_LINEAR_LINEAR = 2;\n/** Trilinear is mag = linear and min = linear and mip = linear */\nConstants.TEXTURE_TRILINEAR_SAMPLINGMODE = 3;\n/** Trilinear is mag = linear and min = linear and mip = linear */\nConstants.TEXTURE_LINEAR_LINEAR_MIPLINEAR = 3;\n/** mag = nearest and min = nearest and mip = nearest */\nConstants.TEXTURE_NEAREST_NEAREST_MIPNEAREST = 4;\n/** mag = nearest and min = linear and mip = nearest */\nConstants.TEXTURE_NEAREST_LINEAR_MIPNEAREST = 5;\n/** mag = nearest and min = linear and mip = linear */\nConstants.TEXTURE_NEAREST_LINEAR_MIPLINEAR = 6;\n/** mag = nearest and min = linear and mip = none */\nConstants.TEXTURE_NEAREST_LINEAR = 7;\n/** nearest is mag = nearest and min = nearest and mip = linear */\nConstants.TEXTURE_NEAREST_NEAREST_MIPLINEAR = 8;\n/** mag = linear and min = nearest and mip = nearest */\nConstants.TEXTURE_LINEAR_NEAREST_MIPNEAREST = 9;\n/** mag = linear and min = nearest and mip = linear */\nConstants.TEXTURE_LINEAR_NEAREST_MIPLINEAR = 10;\n/** Bilinear is mag = linear and min = linear and mip = nearest */\nConstants.TEXTURE_LINEAR_LINEAR_MIPNEAREST = 11;\n/** mag = linear and min = nearest and mip = none */\nConstants.TEXTURE_LINEAR_NEAREST = 12;\n/** Explicit coordinates mode */\nConstants.TEXTURE_EXPLICIT_MODE = 0;\n/** Spherical coordinates mode */\nConstants.TEXTURE_SPHERICAL_MODE = 1;\n/** Planar coordinates mode */\nConstants.TEXTURE_PLANAR_MODE = 2;\n/** Cubic coordinates mode */\nConstants.TEXTURE_CUBIC_MODE = 3;\n/** Projection coordinates mode */\nConstants.TEXTURE_PROJECTION_MODE = 4;\n/** Skybox coordinates mode */\nConstants.TEXTURE_SKYBOX_MODE = 5;\n/** Inverse Cubic coordinates mode */\nConstants.TEXTURE_INVCUBIC_MODE = 6;\n/** Equirectangular coordinates mode */\nConstants.TEXTURE_EQUIRECTANGULAR_MODE = 7;\n/** Equirectangular Fixed coordinates mode */\nConstants.TEXTURE_FIXED_EQUIRECTANGULAR_MODE = 8;\n/** Equirectangular Fixed Mirrored coordinates mode */\nConstants.TEXTURE_FIXED_EQUIRECTANGULAR_MIRRORED_MODE = 9;\n/** Offline (baking) quality for texture filtering */\nConstants.TEXTURE_FILTERING_QUALITY_OFFLINE = 4096;\n/** High quality for texture filtering */\nConstants.TEXTURE_FILTERING_QUALITY_HIGH = 64;\n/** Medium quality for texture filtering */\nConstants.TEXTURE_FILTERING_QUALITY_MEDIUM = 16;\n/** Low quality for texture filtering */\nConstants.TEXTURE_FILTERING_QUALITY_LOW = 8;\n// Texture rescaling mode\n/** Defines that texture rescaling will use a floor to find the closer power of 2 size */\nConstants.SCALEMODE_FLOOR = 1;\n/** Defines that texture rescaling will look for the nearest power of 2 size */\nConstants.SCALEMODE_NEAREST = 2;\n/** Defines that texture rescaling will use a ceil to find the closer power of 2 size */\nConstants.SCALEMODE_CEILING = 3;\n/**\n * The dirty texture flag value\n */\nConstants.MATERIAL_TextureDirtyFlag = 1;\n/**\n * The dirty light flag value\n */\nConstants.MATERIAL_LightDirtyFlag = 2;\n/**\n * The dirty fresnel flag value\n */\nConstants.MATERIAL_FresnelDirtyFlag = 4;\n/**\n * The dirty attribute flag value\n */\nConstants.MATERIAL_AttributesDirtyFlag = 8;\n/**\n * The dirty misc flag value\n */\nConstants.MATERIAL_MiscDirtyFlag = 16;\n/**\n * The dirty prepass flag value\n */\nConstants.MATERIAL_PrePassDirtyFlag = 32;\n/**\n * The dirty image processing flag value\n */\nConstants.MATERIAL_ImageProcessingDirtyFlag = 64;\n/**\n * The all dirty flag value\n */\nConstants.MATERIAL_AllDirtyFlag = 127;\n/**\n * Returns the triangle fill mode\n */\nConstants.MATERIAL_TriangleFillMode = 0;\n/**\n * Returns the wireframe mode\n */\nConstants.MATERIAL_WireFrameFillMode = 1;\n/**\n * Returns the point fill mode\n */\nConstants.MATERIAL_PointFillMode = 2;\n/**\n * Returns the point list draw mode\n */\nConstants.MATERIAL_PointListDrawMode = 3;\n/**\n * Returns the line list draw mode\n */\nConstants.MATERIAL_LineListDrawMode = 4;\n/**\n * Returns the line loop draw mode\n */\nConstants.MATERIAL_LineLoopDrawMode = 5;\n/**\n * Returns the line strip draw mode\n */\nConstants.MATERIAL_LineStripDrawMode = 6;\n/**\n * Returns the triangle strip draw mode\n */\nConstants.MATERIAL_TriangleStripDrawMode = 7;\n/**\n * Returns the triangle fan draw mode\n */\nConstants.MATERIAL_TriangleFanDrawMode = 8;\n/**\n * Stores the clock-wise side orientation\n */\nConstants.MATERIAL_ClockWiseSideOrientation = 0;\n/**\n * Stores the counter clock-wise side orientation\n */\nConstants.MATERIAL_CounterClockWiseSideOrientation = 1;\n/**\n * Nothing\n * @see https://doc.babylonjs.com/features/featuresDeepDive/events/actions#triggers\n */\nConstants.ACTION_NothingTrigger = 0;\n/**\n * On pick\n * @see https://doc.babylonjs.com/features/featuresDeepDive/events/actions#triggers\n */\nConstants.ACTION_OnPickTrigger = 1;\n/**\n * On left pick\n * @see https://doc.babylonjs.com/features/featuresDeepDive/events/actions#triggers\n */\nConstants.ACTION_OnLeftPickTrigger = 2;\n/**\n * On right pick\n * @see https://doc.babylonjs.com/features/featuresDeepDive/events/actions#triggers\n */\nConstants.ACTION_OnRightPickTrigger = 3;\n/**\n * On center pick\n * @see https://doc.babylonjs.com/features/featuresDeepDive/events/actions#triggers\n */\nConstants.ACTION_OnCenterPickTrigger = 4;\n/**\n * On pick down\n * @see https://doc.babylonjs.com/features/featuresDeepDive/events/actions#triggers\n */\nConstants.ACTION_OnPickDownTrigger = 5;\n/**\n * On double pick\n * @see https://doc.babylonjs.com/features/featuresDeepDive/events/actions#triggers\n */\nConstants.ACTION_OnDoublePickTrigger = 6;\n/**\n * On pick up\n * @see https://doc.babylonjs.com/features/featuresDeepDive/events/actions#triggers\n */\nConstants.ACTION_OnPickUpTrigger = 7;\n/**\n * On pick out.\n * This trigger will only be raised if you also declared a OnPickDown\n * @see https://doc.babylonjs.com/features/featuresDeepDive/events/actions#triggers\n */\nConstants.ACTION_OnPickOutTrigger = 16;\n/**\n * On long press\n * @see https://doc.babylonjs.com/features/featuresDeepDive/events/actions#triggers\n */\nConstants.ACTION_OnLongPressTrigger = 8;\n/**\n * On pointer over\n * @see https://doc.babylonjs.com/features/featuresDeepDive/events/actions#triggers\n */\nConstants.ACTION_OnPointerOverTrigger = 9;\n/**\n * On pointer out\n * @see https://doc.babylonjs.com/features/featuresDeepDive/events/actions#triggers\n */\nConstants.ACTION_OnPointerOutTrigger = 10;\n/**\n * On every frame\n * @see https://doc.babylonjs.com/features/featuresDeepDive/events/actions#triggers\n */\nConstants.ACTION_OnEveryFrameTrigger = 11;\n/**\n * On intersection enter\n * @see https://doc.babylonjs.com/features/featuresDeepDive/events/actions#triggers\n */\nConstants.ACTION_OnIntersectionEnterTrigger = 12;\n/**\n * On intersection exit\n * @see https://doc.babylonjs.com/features/featuresDeepDive/events/actions#triggers\n */\nConstants.ACTION_OnIntersectionExitTrigger = 13;\n/**\n * On key down\n * @see https://doc.babylonjs.com/features/featuresDeepDive/events/actions#triggers\n */\nConstants.ACTION_OnKeyDownTrigger = 14;\n/**\n * On key up\n * @see https://doc.babylonjs.com/features/featuresDeepDive/events/actions#triggers\n */\nConstants.ACTION_OnKeyUpTrigger = 15;\n/**\n * Billboard mode will only apply to Y axis\n */\nConstants.PARTICLES_BILLBOARDMODE_Y = 2;\n/**\n * Billboard mode will apply to all axes\n */\nConstants.PARTICLES_BILLBOARDMODE_ALL = 7;\n/**\n * Special billboard mode where the particle will be biilboard to the camera but rotated to align with direction\n */\nConstants.PARTICLES_BILLBOARDMODE_STRETCHED = 8;\n/**\n * Special billboard mode where the particle will be billboard to the camera but only around the axis of the direction of particle emission\n */\nConstants.PARTICLES_BILLBOARDMODE_STRETCHED_LOCAL = 9;\n/** Default culling strategy : this is an exclusion test and it's the more accurate.\n *  Test order :\n *  Is the bounding sphere outside the frustum ?\n *  If not, are the bounding box vertices outside the frustum ?\n *  It not, then the cullable object is in the frustum.\n */\nConstants.MESHES_CULLINGSTRATEGY_STANDARD = 0;\n/** Culling strategy : Bounding Sphere Only.\n *  This is an exclusion test. It's faster than the standard strategy because the bounding box is not tested.\n *  It's also less accurate than the standard because some not visible objects can still be selected.\n *  Test : is the bounding sphere outside the frustum ?\n *  If not, then the cullable object is in the frustum.\n */\nConstants.MESHES_CULLINGSTRATEGY_BOUNDINGSPHERE_ONLY = 1;\n/** Culling strategy : Optimistic Inclusion.\n *  This in an inclusion test first, then the standard exclusion test.\n *  This can be faster when a cullable object is expected to be almost always in the camera frustum.\n *  This could also be a little slower than the standard test when the tested object center is not the frustum but one of its bounding box vertex is still inside.\n *  Anyway, it's as accurate as the standard strategy.\n *  Test :\n *  Is the cullable object bounding sphere center in the frustum ?\n *  If not, apply the default culling strategy.\n */\nConstants.MESHES_CULLINGSTRATEGY_OPTIMISTIC_INCLUSION = 2;\n/** Culling strategy : Optimistic Inclusion then Bounding Sphere Only.\n *  This in an inclusion test first, then the bounding sphere only exclusion test.\n *  This can be the fastest test when a cullable object is expected to be almost always in the camera frustum.\n *  This could also be a little slower than the BoundingSphereOnly strategy when the tested object center is not in the frustum but its bounding sphere still intersects it.\n *  It's less accurate than the standard strategy and as accurate as the BoundingSphereOnly strategy.\n *  Test :\n *  Is the cullable object bounding sphere center in the frustum ?\n *  If not, apply the Bounding Sphere Only strategy. No Bounding Box is tested here.\n */\nConstants.MESHES_CULLINGSTRATEGY_OPTIMISTIC_INCLUSION_THEN_BSPHERE_ONLY = 3;\n/**\n * No logging while loading\n */\nConstants.SCENELOADER_NO_LOGGING = 0;\n/**\n * Minimal logging while loading\n */\nConstants.SCENELOADER_MINIMAL_LOGGING = 1;\n/**\n * Summary logging while loading\n */\nConstants.SCENELOADER_SUMMARY_LOGGING = 2;\n/**\n * Detailed logging while loading\n */\nConstants.SCENELOADER_DETAILED_LOGGING = 3;\n/**\n * Constant used to retrieve the irradiance texture index in the textures array in the prepass\n * using getIndex(Constants.PREPASS_IRRADIANCE_TEXTURE_TYPE)\n */\nConstants.PREPASS_IRRADIANCE_TEXTURE_TYPE = 0;\n/**\n * Constant used to retrieve the position texture index in the textures array in the prepass\n * using getIndex(Constants.PREPASS_POSITION_TEXTURE_INDEX)\n */\nConstants.PREPASS_POSITION_TEXTURE_TYPE = 1;\n/**\n * Constant used to retrieve the velocity texture index in the textures array in the prepass\n * using getIndex(Constants.PREPASS_VELOCITY_TEXTURE_TYPE)\n */\nConstants.PREPASS_VELOCITY_TEXTURE_TYPE = 2;\n/**\n * Constant used to retrieve the reflectivity texture index in the textures array in the prepass\n * using the getIndex(Constants.PREPASS_REFLECTIVITY_TEXTURE_TYPE)\n */\nConstants.PREPASS_REFLECTIVITY_TEXTURE_TYPE = 3;\n/**\n * Constant used to retrieve the lit color texture index in the textures array in the prepass\n * using the getIndex(Constants.PREPASS_COLOR_TEXTURE_TYPE)\n */\nConstants.PREPASS_COLOR_TEXTURE_TYPE = 4;\n/**\n * Constant used to retrieve depth index in the textures array in the prepass\n * using the getIndex(Constants.PREPASS_DEPTH_TEXTURE_TYPE)\n */\nConstants.PREPASS_DEPTH_TEXTURE_TYPE = 5;\n/**\n * Constant used to retrieve normal index in the textures array in the prepass\n * using the getIndex(Constants.PREPASS_NORMAL_TEXTURE_TYPE)\n */\nConstants.PREPASS_NORMAL_TEXTURE_TYPE = 6;\n/**\n * Constant used to retrieve (sqrt) albedo index in the textures array in the prepass\n * using the getIndex(Constants.PREPASS_ALBEDO_SQRT_TEXTURE_TYPE)\n */\nConstants.PREPASS_ALBEDO_SQRT_TEXTURE_TYPE = 7;\n/**\n * Constant used to retrieve world normal index in the textures array in the prepass\n * using the getIndex(Constants.PREPASS_WORLD_NORMAL_TEXTURE_TYPE)\n */\nConstants.PREPASS_WORLD_NORMAL_TEXTURE_TYPE = 8;\n/**\n * Constant used to retrieve the local position texture index in the textures array in the prepass\n * using getIndex(Constants.PREPASS_LOCAL_POSITION_TEXTURE_TYPE)\n */\nConstants.PREPASS_LOCAL_POSITION_TEXTURE_TYPE = 9;\n/**\n * Constant used to retrieve screen-space (non-linear) depth index in the textures array in the prepass\n * using the getIndex(Constants.PREPASS_SCREENSPACE_DEPTH_TEXTURE_TYPE)\n */\nConstants.PREPASS_SCREENSPACE_DEPTH_TEXTURE_TYPE = 10;\n/**\n * Constant used to retrieve the velocity texture index in the textures array in the prepass\n * using getIndex(Constants.PREPASS_VELOCITY_LINEAR_TEXTURE_TYPE)\n */\nConstants.PREPASS_VELOCITY_LINEAR_TEXTURE_TYPE = 11;\n/**\n * Constant used to retrieve albedo index in the textures array in the prepass\n * using the getIndex(Constants.PREPASS_ALBEDO_TEXTURE_TYPE)\n */\nConstants.PREPASS_ALBEDO_TEXTURE_TYPE = 12;\n/** Flag to create a readable buffer (the buffer can be the source of a copy) */\nConstants.BUFFER_CREATIONFLAG_READ = 1;\n/** Flag to create a writable buffer (the buffer can be the destination of a copy) */\nConstants.BUFFER_CREATIONFLAG_WRITE = 2;\n/** Flag to create a readable and writable buffer */\nConstants.BUFFER_CREATIONFLAG_READWRITE = 3;\n/** Flag to create a buffer suitable to be used as a uniform buffer */\nConstants.BUFFER_CREATIONFLAG_UNIFORM = 4;\n/** Flag to create a buffer suitable to be used as a vertex buffer */\nConstants.BUFFER_CREATIONFLAG_VERTEX = 8;\n/** Flag to create a buffer suitable to be used as an index buffer */\nConstants.BUFFER_CREATIONFLAG_INDEX = 16;\n/** Flag to create a buffer suitable to be used as a storage buffer */\nConstants.BUFFER_CREATIONFLAG_STORAGE = 32;\n/** Flag to create a buffer suitable to be used for indirect calls, such as `dispatchIndirect` */\nConstants.BUFFER_CREATIONFLAG_INDIRECT = 64;\n/**\n * Prefixes used by the engine for sub mesh draw wrappers\n */\n/** @internal */\nConstants.RENDERPASS_MAIN = 0;\n/**\n * Constant used as key code for Alt key\n */\nConstants.INPUT_ALT_KEY = 18;\n/**\n * Constant used as key code for Ctrl key\n */\nConstants.INPUT_CTRL_KEY = 17;\n/**\n * Constant used as key code for Meta key (Left Win, Left Cmd)\n */\nConstants.INPUT_META_KEY1 = 91;\n/**\n * Constant used as key code for Meta key (Right Win)\n */\nConstants.INPUT_META_KEY2 = 92;\n/**\n * Constant used as key code for Meta key (Right Win, Right Cmd)\n */\nConstants.INPUT_META_KEY3 = 93;\n/**\n * Constant used as key code for Shift key\n */\nConstants.INPUT_SHIFT_KEY = 16;\n/** Standard snapshot rendering. In this mode, some form of dynamic behavior is possible (for eg, uniform buffers are still updated) */\nConstants.SNAPSHOTRENDERING_STANDARD = 0;\n/** Fast snapshot rendering. In this mode, everything is static and only some limited form of dynamic behaviour is possible */\nConstants.SNAPSHOTRENDERING_FAST = 1;\n/**\n * This is the default projection mode used by the cameras.\n * It helps recreating a feeling of perspective and better appreciate depth.\n * This is the best way to simulate real life cameras.\n */\nConstants.PERSPECTIVE_CAMERA = 0;\n/**\n * This helps creating camera with an orthographic mode.\n * Orthographic is commonly used in engineering as a means to produce object specifications that communicate dimensions unambiguously, each line of 1 unit length (cm, meter..whatever) will appear to have the same length everywhere on the drawing. This allows the drafter to dimension only a subset of lines and let the reader know that other lines of that length on the drawing are also that length in reality. Every parallel line in the drawing is also parallel in the object.\n */\nConstants.ORTHOGRAPHIC_CAMERA = 1;\n/**\n * This is the default FOV mode for perspective cameras.\n * This setting aligns the upper and lower bounds of the viewport to the upper and lower bounds of the camera frustum.\n */\nConstants.FOVMODE_VERTICAL_FIXED = 0;\n/**\n * This setting aligns the left and right bounds of the viewport to the left and right bounds of the camera frustum.\n */\nConstants.FOVMODE_HORIZONTAL_FIXED = 1;\n/**\n * This specifies there is no need for a camera rig.\n * Basically only one eye is rendered corresponding to the camera.\n */\nConstants.RIG_MODE_NONE = 0;\n/**\n * Simulates a camera Rig with one blue eye and one red eye.\n * This can be use with 3d blue and red glasses.\n */\nConstants.RIG_MODE_STEREOSCOPIC_ANAGLYPH = 10;\n/**\n * Defines that both eyes of the camera will be rendered side by side with a parallel target.\n */\nConstants.RIG_MODE_STEREOSCOPIC_SIDEBYSIDE_PARALLEL = 11;\n/**\n * Defines that both eyes of the camera will be rendered side by side with a none parallel target.\n */\nConstants.RIG_MODE_STEREOSCOPIC_SIDEBYSIDE_CROSSEYED = 12;\n/**\n * Defines that both eyes of the camera will be rendered over under each other.\n */\nConstants.RIG_MODE_STEREOSCOPIC_OVERUNDER = 13;\n/**\n * Defines that both eyes of the camera will be rendered on successive lines interlaced for passive 3d monitors.\n */\nConstants.RIG_MODE_STEREOSCOPIC_INTERLACED = 14;\n/**\n * Defines that both eyes of the camera should be renderered in a VR mode (carbox).\n */\nConstants.RIG_MODE_VR = 20;\n/**\n * Custom rig mode allowing rig cameras to be populated manually with any number of cameras\n */\nConstants.RIG_MODE_CUSTOM = 22;\n/**\n * Maximum number of uv sets supported\n */\nConstants.MAX_SUPPORTED_UV_SETS = 6;\n/**\n * GL constants\n */\n/** Alpha blend equation: ADD */\nConstants.GL_ALPHA_EQUATION_ADD = 0x8006;\n/** Alpha equation: MIN */\nConstants.GL_ALPHA_EQUATION_MIN = 0x8007;\n/** Alpha equation: MAX */\nConstants.GL_ALPHA_EQUATION_MAX = 0x8008;\n/** Alpha equation: SUBTRACT */\nConstants.GL_ALPHA_EQUATION_SUBTRACT = 0x800a;\n/** Alpha equation: REVERSE_SUBTRACT */\nConstants.GL_ALPHA_EQUATION_REVERSE_SUBTRACT = 0x800b;\n/** Alpha blend function: SRC */\nConstants.GL_ALPHA_FUNCTION_SRC = 0x0300;\n/** Alpha blend function: ONE_MINUS_SRC */\nConstants.GL_ALPHA_FUNCTION_ONE_MINUS_SRC_COLOR = 0x0301;\n/** Alpha blend function: SRC_ALPHA */\nConstants.GL_ALPHA_FUNCTION_SRC_ALPHA = 0x0302;\n/** Alpha blend function: ONE_MINUS_SRC_ALPHA */\nConstants.GL_ALPHA_FUNCTION_ONE_MINUS_SRC_ALPHA = 0x0303;\n/** Alpha blend function: DST_ALPHA */\nConstants.GL_ALPHA_FUNCTION_DST_ALPHA = 0x0304;\n/** Alpha blend function: ONE_MINUS_DST_ALPHA */\nConstants.GL_ALPHA_FUNCTION_ONE_MINUS_DST_ALPHA = 0x0305;\n/** Alpha blend function: ONE_MINUS_DST */\nConstants.GL_ALPHA_FUNCTION_DST_COLOR = 0x0306;\n/** Alpha blend function: ONE_MINUS_DST */\nConstants.GL_ALPHA_FUNCTION_ONE_MINUS_DST_COLOR = 0x0307;\n/** Alpha blend function: SRC_ALPHA_SATURATED */\nConstants.GL_ALPHA_FUNCTION_SRC_ALPHA_SATURATED = 0x0308;\n/** Alpha blend function: CONSTANT */\nConstants.GL_ALPHA_FUNCTION_CONSTANT_COLOR = 0x8001;\n/** Alpha blend function: ONE_MINUS_CONSTANT */\nConstants.GL_ALPHA_FUNCTION_ONE_MINUS_CONSTANT_COLOR = 0x8002;\n/** Alpha blend function: CONSTANT_ALPHA */\nConstants.GL_ALPHA_FUNCTION_CONSTANT_ALPHA = 0x8003;\n/** Alpha blend function: ONE_MINUS_CONSTANT_ALPHA */\nConstants.GL_ALPHA_FUNCTION_ONE_MINUS_CONSTANT_ALPHA = 0x8004;\n/** Alpha blend function: SRC1 */\nConstants.GL_ALPHA_FUNCTION_SRC1_COLOR = 0x88f9;\n/** Alpha blend function: SRC1 */\nConstants.GL_ALPHA_FUNCTION_ONE_MINUS_SRC1_COLOR = 0x88fa;\n/** Alpha blend function: SRC1 */\nConstants.GL_ALPHA_FUNCTION_SRC1_ALPHA = 0x8589;\n/** Alpha blend function: SRC1 */\nConstants.GL_ALPHA_FUNCTION_ONE_MINUS_SRC1_ALPHA = 0x88fb;\n/** URL to the snippet server. Points to the public snippet server by default */\nConstants.SnippetUrl = \"https://snippet.babylonjs.com\";\n/** The fog is deactivated */\nConstants.FOGMODE_NONE = 0;\n/** The fog density is following an exponential function */\nConstants.FOGMODE_EXP = 1;\n/** The fog density is following an exponential function faster than FOGMODE_EXP */\nConstants.FOGMODE_EXP2 = 2;\n/** The fog density is following a linear function. */\nConstants.FOGMODE_LINEAR = 3;\n/**\n * The byte type.\n */\nConstants.BYTE = 5120;\n/**\n * The unsigned byte type.\n */\nConstants.UNSIGNED_BYTE = 5121;\n/**\n * The short type.\n */\nConstants.SHORT = 5122;\n/**\n * The unsigned short type.\n */\nConstants.UNSIGNED_SHORT = 5123;\n/**\n * The integer type.\n */\nConstants.INT = 5124;\n/**\n * The unsigned integer type.\n */\nConstants.UNSIGNED_INT = 5125;\n/**\n * The float type.\n */\nConstants.FLOAT = 5126;\n/**\n * Positions\n */\nConstants.PositionKind = \"position\";\n/**\n * Normals\n */\nConstants.NormalKind = \"normal\";\n/**\n * Tangents\n */\nConstants.TangentKind = \"tangent\";\n/**\n * Texture coordinates\n */\nConstants.UVKind = \"uv\";\n/**\n * Texture coordinates 2\n */\nConstants.UV2Kind = \"uv2\";\n/**\n * Texture coordinates 3\n */\nConstants.UV3Kind = \"uv3\";\n/**\n * Texture coordinates 4\n */\nConstants.UV4Kind = \"uv4\";\n/**\n * Texture coordinates 5\n */\nConstants.UV5Kind = \"uv5\";\n/**\n * Texture coordinates 6\n */\nConstants.UV6Kind = \"uv6\";\n/**\n * Colors\n */\nConstants.ColorKind = \"color\";\n/**\n * Instance Colors\n */\nConstants.ColorInstanceKind = \"instanceColor\";\n/**\n * Matrix indices (for bones)\n */\nConstants.MatricesIndicesKind = \"matricesIndices\";\n/**\n * Matrix weights (for bones)\n */\nConstants.MatricesWeightsKind = \"matricesWeights\";\n/**\n * Additional matrix indices (for bones)\n */\nConstants.MatricesIndicesExtraKind = \"matricesIndicesExtra\";\n/**\n * Additional matrix weights (for bones)\n */\nConstants.MatricesWeightsExtraKind = \"matricesWeightsExtra\";\n// Animation type\n/**\n * Float animation type\n */\nConstants.ANIMATIONTYPE_FLOAT = 0;\n/**\n * Vector3 animation type\n */\nConstants.ANIMATIONTYPE_VECTOR3 = 1;\n/**\n * Quaternion animation type\n */\nConstants.ANIMATIONTYPE_QUATERNION = 2;\n/**\n * Matrix animation type\n */\nConstants.ANIMATIONTYPE_MATRIX = 3;\n/**\n * Color3 animation type\n */\nConstants.ANIMATIONTYPE_COLOR3 = 4;\n/**\n * Color3 animation type\n */\nConstants.ANIMATIONTYPE_COLOR4 = 7;\n/**\n * Vector2 animation type\n */\nConstants.ANIMATIONTYPE_VECTOR2 = 5;\n/**\n * Size animation type\n */\nConstants.ANIMATIONTYPE_SIZE = 6;\n/**\n * The default minZ value for the near plane of a frustum light\n */\nConstants.ShadowMinZ = 0;\n/**\n * The default maxZ value for the far plane of a frustum light\n */\nConstants.ShadowMaxZ = 10000;\n//# sourceMappingURL=constants.js.map", "import { __decorate } from \"../tslib.es6.js\";\nimport { serialize, serializeAsVector3 } from \"../Misc/decorators.js\";\nimport { Matrix, TmpVectors, Vector3 } from \"../Maths/math.vector.js\";\nimport { Light } from \"./light.js\";\nimport { Axis } from \"../Maths/math.axis.js\";\n\n/**\n * Base implementation IShadowLight\n * It groups all the common behaviour in order to reduce duplication and better follow the DRY pattern.\n */\nexport class ShadowLight extends Light {\n    constructor() {\n        super(...arguments);\n        this._needProjectionMatrixCompute = true;\n        this._viewMatrix = Matrix.Identity();\n        this._projectionMatrix = Matrix.Identity();\n    }\n    _setPosition(value) {\n        this._position = value;\n    }\n    /**\n     * Sets the position the shadow will be casted from. Also use as the light position for both\n     * point and spot lights.\n     */\n    get position() {\n        return this._position;\n    }\n    /**\n     * Sets the position the shadow will be casted from. Also use as the light position for both\n     * point and spot lights.\n     */\n    set position(value) {\n        this._setPosition(value);\n    }\n    _setDirection(value) {\n        this._direction = value;\n    }\n    /**\n     * In 2d mode (needCube being false), gets the direction used to cast the shadow.\n     * Also use as the light direction on spot and directional lights.\n     */\n    get direction() {\n        return this._direction;\n    }\n    /**\n     * In 2d mode (needCube being false), sets the direction used to cast the shadow.\n     * Also use as the light direction on spot and directional lights.\n     */\n    set direction(value) {\n        this._setDirection(value);\n    }\n    /**\n     * Gets the shadow projection clipping minimum z value.\n     */\n    get shadowMinZ() {\n        return this._shadowMinZ;\n    }\n    /**\n     * Sets the shadow projection clipping minimum z value.\n     */\n    set shadowMinZ(value) {\n        this._shadowMinZ = value;\n        this.forceProjectionMatrixCompute();\n    }\n    /**\n     * Sets the shadow projection clipping maximum z value.\n     */\n    get shadowMaxZ() {\n        return this._shadowMaxZ;\n    }\n    /**\n     * Gets the shadow projection clipping maximum z value.\n     */\n    set shadowMaxZ(value) {\n        this._shadowMaxZ = value;\n        this.forceProjectionMatrixCompute();\n    }\n    /**\n     * Computes the transformed information (transformedPosition and transformedDirection in World space) of the current light\n     * @returns true if the information has been computed, false if it does not need to (no parenting)\n     */\n    computeTransformedInformation() {\n        if (this.parent && this.parent.getWorldMatrix) {\n            if (!this.transformedPosition) {\n                this.transformedPosition = Vector3.Zero();\n            }\n            Vector3.TransformCoordinatesToRef(this.position, this.parent.getWorldMatrix(), this.transformedPosition);\n            // In case the direction is present.\n            if (this.direction) {\n                if (!this.transformedDirection) {\n                    this.transformedDirection = Vector3.Zero();\n                }\n                Vector3.TransformNormalToRef(this.direction, this.parent.getWorldMatrix(), this.transformedDirection);\n            }\n            return true;\n        }\n        return false;\n    }\n    /**\n     * Return the depth scale used for the shadow map.\n     * @returns the depth scale.\n     */\n    getDepthScale() {\n        return 50.0;\n    }\n    /**\n     * Get the direction to use to render the shadow map. In case of cube texture, the face index can be passed.\n     * @param faceIndex The index of the face we are computed the direction to generate shadow\n     * @returns The set direction in 2d mode otherwise the direction to the cubemap face if needCube() is true\n     */\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n    getShadowDirection(faceIndex) {\n        return this.transformedDirection ? this.transformedDirection : this.direction;\n    }\n    /**\n     * If computeTransformedInformation has been called, returns the ShadowLight absolute position in the world. Otherwise, returns the local position.\n     * @returns the position vector in world space\n     */\n    getAbsolutePosition() {\n        return this.transformedPosition ? this.transformedPosition : this.position;\n    }\n    /**\n     * Sets the ShadowLight direction toward the passed target.\n     * @param target The point to target in local space\n     * @returns the updated ShadowLight direction\n     */\n    setDirectionToTarget(target) {\n        this.direction = Vector3.Normalize(target.subtract(this.position));\n        return this.direction;\n    }\n    /**\n     * Returns the light rotation in euler definition.\n     * @returns the x y z rotation in local space.\n     */\n    getRotation() {\n        this.direction.normalize();\n        const xaxis = Vector3.Cross(this.direction, Axis.Y);\n        const yaxis = Vector3.Cross(xaxis, this.direction);\n        return Vector3.RotationFromAxis(xaxis, yaxis, this.direction);\n    }\n    /**\n     * Returns whether or not the shadow generation require a cube texture or a 2d texture.\n     * @returns true if a cube texture needs to be use\n     */\n    needCube() {\n        return false;\n    }\n    /**\n     * Detects if the projection matrix requires to be recomputed this frame.\n     * @returns true if it requires to be recomputed otherwise, false.\n     */\n    needProjectionMatrixCompute() {\n        return this._needProjectionMatrixCompute;\n    }\n    /**\n     * Forces the shadow generator to recompute the projection matrix even if position and direction did not changed.\n     */\n    forceProjectionMatrixCompute() {\n        this._needProjectionMatrixCompute = true;\n    }\n    /** @internal */\n    _initCache() {\n        super._initCache();\n        this._cache.position = Vector3.Zero();\n    }\n    /** @internal */\n    _isSynchronized() {\n        if (!this._cache.position.equals(this.position)) {\n            return false;\n        }\n        return true;\n    }\n    /**\n     * Computes the world matrix of the node\n     * @param force defines if the cache version should be invalidated forcing the world matrix to be created from scratch\n     * @returns the world matrix\n     */\n    computeWorldMatrix(force) {\n        if (!force && this.isSynchronized()) {\n            this._currentRenderId = this.getScene().getRenderId();\n            return this._worldMatrix;\n        }\n        this._updateCache();\n        this._cache.position.copyFrom(this.position);\n        if (!this._worldMatrix) {\n            this._worldMatrix = Matrix.Identity();\n        }\n        Matrix.TranslationToRef(this.position.x, this.position.y, this.position.z, this._worldMatrix);\n        if (this.parent && this.parent.getWorldMatrix) {\n            this._worldMatrix.multiplyToRef(this.parent.getWorldMatrix(), this._worldMatrix);\n            this._markSyncedWithParent();\n        }\n        // Cache the determinant\n        this._worldMatrixDeterminantIsDirty = true;\n        return this._worldMatrix;\n    }\n    /**\n     * Gets the minZ used for shadow according to both the scene and the light.\n     * @param activeCamera The camera we are returning the min for\n     * @returns the depth min z\n     */\n    getDepthMinZ(activeCamera) {\n        return this.shadowMinZ !== undefined ? this.shadowMinZ : activeCamera?.minZ || 0;\n    }\n    /**\n     * Gets the maxZ used for shadow according to both the scene and the light.\n     * @param activeCamera The camera we are returning the max for\n     * @returns the depth max z\n     */\n    getDepthMaxZ(activeCamera) {\n        return this.shadowMaxZ !== undefined ? this.shadowMaxZ : activeCamera?.maxZ || 10000;\n    }\n    /**\n     * Sets the shadow projection matrix in parameter to the generated projection matrix.\n     * @param matrix The matrix to updated with the projection information\n     * @param viewMatrix The transform matrix of the light\n     * @param renderList The list of mesh to render in the map\n     * @returns The current light\n     */\n    setShadowProjectionMatrix(matrix, viewMatrix, renderList) {\n        if (this.customProjectionMatrixBuilder) {\n            this.customProjectionMatrixBuilder(viewMatrix, renderList, matrix);\n        }\n        else {\n            this._setDefaultShadowProjectionMatrix(matrix, viewMatrix, renderList);\n        }\n        return this;\n    }\n    /** @internal */\n    _syncParentEnabledState() {\n        super._syncParentEnabledState();\n        if (!this.parent || !this.parent.getWorldMatrix) {\n            this.transformedPosition = null;\n            this.transformedDirection = null;\n        }\n    }\n    /**\n     * Returns the view matrix.\n     * @param faceIndex The index of the face for which we want to extract the view matrix. Only used for point light types.\n     * @returns The view matrix. Can be null, if a view matrix cannot be defined for the type of light considered (as for a hemispherical light, for example).\n     */\n    getViewMatrix(faceIndex) {\n        const lightDirection = TmpVectors.Vector3[0];\n        let lightPosition = this.position;\n        if (this.computeTransformedInformation()) {\n            lightPosition = this.transformedPosition;\n        }\n        Vector3.NormalizeToRef(this.getShadowDirection(faceIndex), lightDirection);\n        if (Math.abs(Vector3.Dot(lightDirection, Vector3.Up())) === 1.0) {\n            lightDirection.z = 0.0000000000001; // Required to avoid perfectly perpendicular light\n        }\n        const lightTarget = TmpVectors.Vector3[1];\n        lightPosition.addToRef(lightDirection, lightTarget);\n        Matrix.LookAtLHToRef(lightPosition, lightTarget, Vector3.Up(), this._viewMatrix);\n        return this._viewMatrix;\n    }\n    /**\n     * Returns the projection matrix.\n     * Note that viewMatrix and renderList are optional and are only used by lights that calculate the projection matrix from a list of meshes (e.g. directional lights with automatic extents calculation).\n     * @param viewMatrix The view transform matrix of the light (optional).\n     * @param renderList The list of meshes to take into account when calculating the projection matrix (optional).\n     * @returns The projection matrix. Can be null, if a projection matrix cannot be defined for the type of light considered (as for a hemispherical light, for example).\n     */\n    getProjectionMatrix(viewMatrix, renderList) {\n        this.setShadowProjectionMatrix(this._projectionMatrix, viewMatrix ?? this._viewMatrix, renderList ?? []);\n        return this._projectionMatrix;\n    }\n}\n__decorate([\n    serializeAsVector3()\n], ShadowLight.prototype, \"position\", null);\n__decorate([\n    serializeAsVector3()\n], ShadowLight.prototype, \"direction\", null);\n__decorate([\n    serialize()\n], ShadowLight.prototype, \"shadowMinZ\", null);\n__decorate([\n    serialize()\n], ShadowLight.prototype, \"shadowMaxZ\", null);\n//# sourceMappingURL=shadowLight.js.map", "import { __decorate } from \"../tslib.es6.js\";\nimport { serialize, serializeAsTexture } from \"../Misc/decorators.js\";\nimport { Matrix, Vector3 } from \"../Maths/math.vector.js\";\nimport { Node } from \"../node.js\";\nimport { Light } from \"./light.js\";\nimport { ShadowLight } from \"./shadowLight.js\";\nimport { Texture } from \"../Materials/Textures/texture.js\";\nimport { RegisterClass } from \"../Misc/typeStore.js\";\n\nNode.AddNodeConstructor(\"Light_Type_2\", (name, scene) => {\n    return () => new SpotLight(name, Vector3.Zero(), Vector3.Zero(), 0, 0, scene);\n});\n/**\n * A spot light is defined by a position, a direction, an angle, and an exponent.\n * These values define a cone of light starting from the position, emitting toward the direction.\n * The angle, in radians, defines the size (field of illumination) of the spotlight's conical beam,\n * and the exponent defines the speed of the decay of the light with distance (reach).\n * Documentation: https://doc.babylonjs.com/features/featuresDeepDive/lights/lights_introduction\n */\nexport class SpotLight extends ShadowLight {\n    /**\n     * Gets or sets the IES profile texture used to create the spotlight\n     * @see https://playground.babylonjs.com/#UIAXAU#1\n     */\n    get iesProfileTexture() {\n        return this._iesProfileTexture;\n    }\n    set iesProfileTexture(value) {\n        if (this._iesProfileTexture === value) {\n            return;\n        }\n        this._iesProfileTexture = value;\n        if (this._iesProfileTexture && SpotLight._IsTexture(this._iesProfileTexture)) {\n            this._iesProfileTexture.onLoadObservable.addOnce(() => {\n                this._markMeshesAsLightDirty();\n            });\n        }\n    }\n    /**\n     * Gets the cone angle of the spot light in Radians.\n     */\n    get angle() {\n        return this._angle;\n    }\n    /**\n     * Sets the cone angle of the spot light in Radians.\n     */\n    set angle(value) {\n        this._angle = value;\n        this._cosHalfAngle = Math.cos(value * 0.5);\n        this._projectionTextureProjectionLightDirty = true;\n        this.forceProjectionMatrixCompute();\n        this._computeAngleValues();\n    }\n    /**\n     * Only used in gltf falloff mode, this defines the angle where\n     * the directional falloff will start before cutting at angle which could be seen\n     * as outer angle.\n     */\n    get innerAngle() {\n        return this._innerAngle;\n    }\n    /**\n     * Only used in gltf falloff mode, this defines the angle where\n     * the directional falloff will start before cutting at angle which could be seen\n     * as outer angle.\n     */\n    set innerAngle(value) {\n        this._innerAngle = value;\n        this._computeAngleValues();\n    }\n    /**\n     * Allows scaling the angle of the light for shadow generation only.\n     */\n    get shadowAngleScale() {\n        return this._shadowAngleScale;\n    }\n    /**\n     * Allows scaling the angle of the light for shadow generation only.\n     */\n    set shadowAngleScale(value) {\n        this._shadowAngleScale = value;\n        this.forceProjectionMatrixCompute();\n    }\n    /**\n     * Allows reading the projection texture\n     */\n    get projectionTextureMatrix() {\n        return this._projectionTextureMatrix;\n    }\n    /**\n     * Gets the near clip of the Spotlight for texture projection.\n     */\n    get projectionTextureLightNear() {\n        return this._projectionTextureLightNear;\n    }\n    /**\n     * Sets the near clip of the Spotlight for texture projection.\n     */\n    set projectionTextureLightNear(value) {\n        this._projectionTextureLightNear = value;\n        this._projectionTextureProjectionLightDirty = true;\n    }\n    /**\n     * Gets the far clip of the Spotlight for texture projection.\n     */\n    get projectionTextureLightFar() {\n        return this._projectionTextureLightFar;\n    }\n    /**\n     * Sets the far clip of the Spotlight for texture projection.\n     */\n    set projectionTextureLightFar(value) {\n        this._projectionTextureLightFar = value;\n        this._projectionTextureProjectionLightDirty = true;\n    }\n    /**\n     * Gets the Up vector of the Spotlight for texture projection.\n     */\n    get projectionTextureUpDirection() {\n        return this._projectionTextureUpDirection;\n    }\n    /**\n     * Sets the Up vector of the Spotlight for texture projection.\n     */\n    set projectionTextureUpDirection(value) {\n        this._projectionTextureUpDirection = value;\n        this._projectionTextureProjectionLightDirty = true;\n    }\n    /**\n     * Gets the projection texture of the light.\n     */\n    get projectionTexture() {\n        return this._projectionTexture;\n    }\n    /**\n     * Sets the projection texture of the light.\n     */\n    set projectionTexture(value) {\n        if (this._projectionTexture === value) {\n            return;\n        }\n        this._projectionTexture = value;\n        this._projectionTextureDirty = true;\n        if (this._projectionTexture && !this._projectionTexture.isReady()) {\n            if (SpotLight._IsProceduralTexture(this._projectionTexture)) {\n                this._projectionTexture.getEffect().executeWhenCompiled(() => {\n                    this._markMeshesAsLightDirty();\n                });\n            }\n            else if (SpotLight._IsTexture(this._projectionTexture)) {\n                this._projectionTexture.onLoadObservable.addOnce(() => {\n                    this._markMeshesAsLightDirty();\n                });\n            }\n        }\n    }\n    static _IsProceduralTexture(texture) {\n        return texture.onGeneratedObservable !== undefined;\n    }\n    static _IsTexture(texture) {\n        return texture.onLoadObservable !== undefined;\n    }\n    /**\n     * Gets or sets the light projection matrix as used by the projection texture\n     */\n    get projectionTextureProjectionLightMatrix() {\n        return this._projectionTextureProjectionLightMatrix;\n    }\n    set projectionTextureProjectionLightMatrix(projection) {\n        this._projectionTextureProjectionLightMatrix = projection;\n        this._projectionTextureProjectionLightDirty = false;\n        this._projectionTextureDirty = true;\n    }\n    /**\n     * Creates a SpotLight object in the scene. A spot light is a simply light oriented cone.\n     * It can cast shadows.\n     * Documentation : https://doc.babylonjs.com/features/featuresDeepDive/lights/lights_introduction\n     * @param name The light friendly name\n     * @param position The position of the spot light in the scene\n     * @param direction The direction of the light in the scene\n     * @param angle The cone angle of the light in Radians\n     * @param exponent The light decay speed with the distance from the emission spot\n     * @param scene The scene the lights belongs to\n     */\n    constructor(name, position, direction, angle, exponent, scene) {\n        super(name, scene);\n        this._innerAngle = 0;\n        this._iesProfileTexture = null;\n        this._projectionTextureMatrix = Matrix.Zero();\n        this._projectionTextureLightNear = 1e-6;\n        this._projectionTextureLightFar = 1000.0;\n        this._projectionTextureUpDirection = Vector3.Up();\n        this._projectionTextureViewLightDirty = true;\n        this._projectionTextureProjectionLightDirty = true;\n        this._projectionTextureDirty = true;\n        this._projectionTextureViewTargetVector = Vector3.Zero();\n        this._projectionTextureViewLightMatrix = Matrix.Zero();\n        this._projectionTextureProjectionLightMatrix = Matrix.Zero();\n        this._projectionTextureScalingMatrix = Matrix.FromValues(0.5, 0.0, 0.0, 0.0, 0.0, 0.5, 0.0, 0.0, 0.0, 0.0, 0.5, 0.0, 0.5, 0.5, 0.5, 1.0);\n        this.position = position;\n        this.direction = direction;\n        this.angle = angle;\n        this.exponent = exponent;\n    }\n    /**\n     * Returns the string \"SpotLight\".\n     * @returns the class name\n     */\n    getClassName() {\n        return \"SpotLight\";\n    }\n    /**\n     * Returns the integer 2.\n     * @returns The light Type id as a constant defines in Light.LIGHTTYPEID_x\n     */\n    getTypeID() {\n        return Light.LIGHTTYPEID_SPOTLIGHT;\n    }\n    /**\n     * Overrides the direction setter to recompute the projection texture view light Matrix.\n     * @param value\n     */\n    _setDirection(value) {\n        super._setDirection(value);\n        this._projectionTextureViewLightDirty = true;\n    }\n    /**\n     * Overrides the position setter to recompute the projection texture view light Matrix.\n     * @param value\n     */\n    _setPosition(value) {\n        super._setPosition(value);\n        this._projectionTextureViewLightDirty = true;\n    }\n    /**\n     * Sets the passed matrix \"matrix\" as perspective projection matrix for the shadows and the passed view matrix with the fov equal to the SpotLight angle and and aspect ratio of 1.0.\n     * Returns the SpotLight.\n     * @param matrix\n     * @param viewMatrix\n     * @param renderList\n     */\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n    _setDefaultShadowProjectionMatrix(matrix, viewMatrix, renderList) {\n        const activeCamera = this.getScene().activeCamera;\n        if (!activeCamera) {\n            return;\n        }\n        this._shadowAngleScale = this._shadowAngleScale || 1;\n        const angle = this._shadowAngleScale * this._angle;\n        const minZ = this.shadowMinZ !== undefined ? this.shadowMinZ : activeCamera.minZ;\n        const maxZ = this.shadowMaxZ !== undefined ? this.shadowMaxZ : activeCamera.maxZ;\n        const useReverseDepthBuffer = this.getScene().getEngine().useReverseDepthBuffer;\n        Matrix.PerspectiveFovLHToRef(angle, 1.0, useReverseDepthBuffer ? maxZ : minZ, useReverseDepthBuffer ? minZ : maxZ, matrix, true, this._scene.getEngine().isNDCHalfZRange, undefined, useReverseDepthBuffer);\n    }\n    _computeProjectionTextureViewLightMatrix() {\n        this._projectionTextureViewLightDirty = false;\n        this._projectionTextureDirty = true;\n        this.getAbsolutePosition().addToRef(this.getShadowDirection(), this._projectionTextureViewTargetVector);\n        Matrix.LookAtLHToRef(this.getAbsolutePosition(), this._projectionTextureViewTargetVector, this._projectionTextureUpDirection, this._projectionTextureViewLightMatrix);\n    }\n    _computeProjectionTextureProjectionLightMatrix() {\n        this._projectionTextureProjectionLightDirty = false;\n        this._projectionTextureDirty = true;\n        const lightFar = this.projectionTextureLightFar;\n        const lightNear = this.projectionTextureLightNear;\n        const P = lightFar / (lightFar - lightNear);\n        const Q = -P * lightNear;\n        const S = 1.0 / Math.tan(this._angle / 2.0);\n        const A = 1.0;\n        Matrix.FromValuesToRef(S / A, 0.0, 0.0, 0.0, 0.0, S, 0.0, 0.0, 0.0, 0.0, P, 1.0, 0.0, 0.0, Q, 0.0, this._projectionTextureProjectionLightMatrix);\n    }\n    /**\n     * Main function for light texture projection matrix computing.\n     */\n    _computeProjectionTextureMatrix() {\n        this._projectionTextureDirty = false;\n        this._projectionTextureViewLightMatrix.multiplyToRef(this._projectionTextureProjectionLightMatrix, this._projectionTextureMatrix);\n        if (this._projectionTexture instanceof Texture) {\n            const u = this._projectionTexture.uScale / 2.0;\n            const v = this._projectionTexture.vScale / 2.0;\n            Matrix.FromValuesToRef(u, 0.0, 0.0, 0.0, 0.0, v, 0.0, 0.0, 0.0, 0.0, 0.5, 0.0, 0.5, 0.5, 0.5, 1.0, this._projectionTextureScalingMatrix);\n        }\n        this._projectionTextureMatrix.multiplyToRef(this._projectionTextureScalingMatrix, this._projectionTextureMatrix);\n    }\n    _buildUniformLayout() {\n        this._uniformBuffer.addUniform(\"vLightData\", 4);\n        this._uniformBuffer.addUniform(\"vLightDiffuse\", 4);\n        this._uniformBuffer.addUniform(\"vLightSpecular\", 4);\n        this._uniformBuffer.addUniform(\"vLightDirection\", 3);\n        this._uniformBuffer.addUniform(\"vLightFalloff\", 4);\n        this._uniformBuffer.addUniform(\"shadowsInfo\", 3);\n        this._uniformBuffer.addUniform(\"depthValues\", 2);\n        this._uniformBuffer.create();\n    }\n    _computeAngleValues() {\n        this._lightAngleScale = 1.0 / Math.max(0.001, Math.cos(this._innerAngle * 0.5) - this._cosHalfAngle);\n        this._lightAngleOffset = -this._cosHalfAngle * this._lightAngleScale;\n    }\n    /**\n     * Sets the passed Effect \"effect\" with the Light textures.\n     * @param effect The effect to update\n     * @param lightIndex The index of the light in the effect to update\n     * @returns The light\n     */\n    transferTexturesToEffect(effect, lightIndex) {\n        if (this.projectionTexture && this.projectionTexture.isReady()) {\n            if (this._projectionTextureViewLightDirty) {\n                this._computeProjectionTextureViewLightMatrix();\n            }\n            if (this._projectionTextureProjectionLightDirty) {\n                this._computeProjectionTextureProjectionLightMatrix();\n            }\n            if (this._projectionTextureDirty) {\n                this._computeProjectionTextureMatrix();\n            }\n            effect.setMatrix(\"textureProjectionMatrix\" + lightIndex, this._projectionTextureMatrix);\n            effect.setTexture(\"projectionLightTexture\" + lightIndex, this.projectionTexture);\n        }\n        if (this._iesProfileTexture && this._iesProfileTexture.isReady()) {\n            effect.setTexture(\"iesLightTexture\" + lightIndex, this._iesProfileTexture);\n        }\n        return this;\n    }\n    /**\n     * Sets the passed Effect object with the SpotLight transformed position (or position if not parented) and normalized direction.\n     * @param effect The effect to update\n     * @param lightIndex The index of the light in the effect to update\n     * @returns The spot light\n     */\n    transferToEffect(effect, lightIndex) {\n        let normalizeDirection;\n        if (this.computeTransformedInformation()) {\n            this._uniformBuffer.updateFloat4(\"vLightData\", this.transformedPosition.x, this.transformedPosition.y, this.transformedPosition.z, this.exponent, lightIndex);\n            normalizeDirection = Vector3.Normalize(this.transformedDirection);\n        }\n        else {\n            this._uniformBuffer.updateFloat4(\"vLightData\", this.position.x, this.position.y, this.position.z, this.exponent, lightIndex);\n            normalizeDirection = Vector3.Normalize(this.direction);\n        }\n        this._uniformBuffer.updateFloat4(\"vLightDirection\", normalizeDirection.x, normalizeDirection.y, normalizeDirection.z, this._cosHalfAngle, lightIndex);\n        this._uniformBuffer.updateFloat4(\"vLightFalloff\", this.range, this._inverseSquaredRange, this._lightAngleScale, this._lightAngleOffset, lightIndex);\n        return this;\n    }\n    transferToNodeMaterialEffect(effect, lightDataUniformName) {\n        let normalizeDirection;\n        if (this.computeTransformedInformation()) {\n            normalizeDirection = Vector3.Normalize(this.transformedDirection);\n        }\n        else {\n            normalizeDirection = Vector3.Normalize(this.direction);\n        }\n        if (this.getScene().useRightHandedSystem) {\n            effect.setFloat3(lightDataUniformName, -normalizeDirection.x, -normalizeDirection.y, -normalizeDirection.z);\n        }\n        else {\n            effect.setFloat3(lightDataUniformName, normalizeDirection.x, normalizeDirection.y, normalizeDirection.z);\n        }\n        return this;\n    }\n    /**\n     * Disposes the light and the associated resources.\n     */\n    dispose() {\n        super.dispose();\n        if (this._projectionTexture) {\n            this._projectionTexture.dispose();\n        }\n        if (this._iesProfileTexture) {\n            this._iesProfileTexture.dispose();\n            this._iesProfileTexture = null;\n        }\n    }\n    /**\n     * Gets the minZ used for shadow according to both the scene and the light.\n     * @param activeCamera The camera we are returning the min for\n     * @returns the depth min z\n     */\n    getDepthMinZ(activeCamera) {\n        const engine = this._scene.getEngine();\n        const minZ = this.shadowMinZ !== undefined ? this.shadowMinZ : (activeCamera?.minZ ?? 0);\n        return engine.useReverseDepthBuffer && engine.isNDCHalfZRange ? minZ : this._scene.getEngine().isNDCHalfZRange ? 0 : minZ;\n    }\n    /**\n     * Gets the maxZ used for shadow according to both the scene and the light.\n     * @param activeCamera The camera we are returning the max for\n     * @returns the depth max z\n     */\n    getDepthMaxZ(activeCamera) {\n        const engine = this._scene.getEngine();\n        const maxZ = this.shadowMaxZ !== undefined ? this.shadowMaxZ : (activeCamera?.maxZ ?? 10000);\n        return engine.useReverseDepthBuffer && engine.isNDCHalfZRange ? 0 : maxZ;\n    }\n    /**\n     * Prepares the list of defines specific to the light type.\n     * @param defines the list of defines\n     * @param lightIndex defines the index of the light for the effect\n     */\n    prepareLightSpecificDefines(defines, lightIndex) {\n        defines[\"SPOTLIGHT\" + lightIndex] = true;\n        defines[\"PROJECTEDLIGHTTEXTURE\" + lightIndex] = this.projectionTexture && this.projectionTexture.isReady() ? true : false;\n        defines[\"IESLIGHTTEXTURE\" + lightIndex] = this._iesProfileTexture && this._iesProfileTexture.isReady() ? true : false;\n    }\n}\n__decorate([\n    serialize()\n], SpotLight.prototype, \"angle\", null);\n__decorate([\n    serialize()\n], SpotLight.prototype, \"innerAngle\", null);\n__decorate([\n    serialize()\n], SpotLight.prototype, \"shadowAngleScale\", null);\n__decorate([\n    serialize()\n], SpotLight.prototype, \"exponent\", void 0);\n__decorate([\n    serialize()\n], SpotLight.prototype, \"projectionTextureLightNear\", null);\n__decorate([\n    serialize()\n], SpotLight.prototype, \"projectionTextureLightFar\", null);\n__decorate([\n    serialize()\n], SpotLight.prototype, \"projectionTextureUpDirection\", null);\n__decorate([\n    serializeAsTexture(\"projectedLightTexture\")\n], SpotLight.prototype, \"_projectionTexture\", void 0);\n// Register Class Name\nRegisterClass(\"BABYLON.SpotLight\", SpotLight);\n//# sourceMappingURL=spotLight.js.map", "/**\n * A converter that takes a glTF Object Model JSON Pointer\n * and transforms it into an ObjectAccessorContainer, allowing\n * objects referenced in the glTF to be associated with their\n * respective Babylon.js objects.\n */\nexport class GLTFPathToObjectConverter {\n    constructor(_gltf, _infoTree) {\n        this._gltf = _gltf;\n        this._infoTree = _infoTree;\n    }\n    /**\n     * The pointer string is represented by a [JSON pointer](https://datatracker.ietf.org/doc/html/rfc6901).\n     * See also https://github.com/KhronosGroup/glTF/blob/main/specification/2.0/ObjectModel.adoc#core-pointers\n     * <animationPointer> := /<rootNode>/<assetIndex>/<propertyPath>\n     * <rootNode> := \"nodes\" | \"materials\" | \"meshes\" | \"cameras\" | \"extensions\"\n     * <assetIndex> := <digit> | <name>\n     * <propertyPath> := <extensionPath> | <standardPath>\n     * <extensionPath> := \"extensions\"/<name>/<standardPath>\n     * <standardPath> := <name> | <name>/<standardPath>\n     * <name> := W+\n     * <digit> := D+\n     *\n     * Examples:\n     *  - \"/nodes/0/rotation\"\n     * - \"/nodes.length\"\n     *  - \"/materials/2/emissiveFactor\"\n     *  - \"/materials/2/pbrMetallicRoughness/baseColorFactor\"\n     *  - \"/materials/2/extensions/KHR_materials_emissive_strength/emissiveStrength\"\n     *\n     * @param path The path to convert\n     * @returns The object and info associated with the path\n     */\n    convert(path) {\n        let objectTree = this._gltf;\n        let infoTree = this._infoTree;\n        let target = undefined;\n        if (!path.startsWith(\"/\")) {\n            throw new Error(\"Path must start with a /\");\n        }\n        const parts = path.split(\"/\");\n        parts.shift();\n        //if the last part has \".length\" in it, separate that as an extra part\n        if (parts[parts.length - 1].includes(\".length\")) {\n            const lastPart = parts[parts.length - 1];\n            const split = lastPart.split(\".\");\n            parts.pop();\n            parts.push(...split);\n        }\n        let ignoreObjectTree = false;\n        for (const part of parts) {\n            const isLength = part === \"length\";\n            if (isLength && !infoTree.__array__) {\n                throw new Error(`Path ${path} is invalid`);\n            }\n            if (infoTree.__ignoreObjectTree__) {\n                ignoreObjectTree = true;\n            }\n            if (infoTree.__array__ && !isLength) {\n                infoTree = infoTree.__array__;\n            }\n            else {\n                infoTree = infoTree[part];\n                if (!infoTree) {\n                    throw new Error(`Path ${path} is invalid`);\n                }\n            }\n            if (!ignoreObjectTree) {\n                if (objectTree === undefined) {\n                    throw new Error(`Path ${path} is invalid`);\n                }\n                if (!isLength) {\n                    objectTree = objectTree?.[part];\n                }\n            }\n            if (infoTree.__target__ || isLength) {\n                target = objectTree;\n            }\n        }\n        return {\n            object: target,\n            info: infoTree,\n        };\n    }\n}\n//# sourceMappingURL=gltfPathToObjectConverter.js.map", "/* eslint-disable @typescript-eslint/naming-convention */\nimport { Matrix, Quaternion, Vector2 } from \"@babylonjs/core/Maths/math.vector.js\";\nimport { Constants } from \"@babylonjs/core/Engines/constants.js\";\nimport { Color4 } from \"@babylonjs/core/Maths/math.color.js\";\nimport { SpotLight } from \"@babylonjs/core/Lights/spotLight.js\";\nimport { GLTFPathToObjectConverter } from \"./gltfPathToObjectConverter.js\";\nconst nodesTree = {\n    length: {\n        type: \"number\",\n        get: (nodes) => nodes.length,\n        getTarget: (nodes) => nodes.map((node) => node._babylonTransformNode),\n        getPropertyName: [() => \"length\"],\n    },\n    __array__: {\n        __target__: true,\n        translation: {\n            type: \"Vector3\",\n            get: (node) => node._babylonTransformNode?.position,\n            set: (value, node) => node._babylonTransformNode?.position.copyFrom(value),\n            getTarget: (node) => node._babylonTransformNode,\n            getPropertyName: [() => \"position\"],\n        },\n        rotation: {\n            type: \"Quaternion\",\n            get: (node) => node._babylonTransformNode?.rotationQuaternion,\n            set: (value, node) => node._babylonTransformNode?.rotationQuaternion?.copyFrom(value),\n            getTarget: (node) => node._babylonTransformNode,\n            getPropertyName: [() => \"rotationQuaternion\"],\n        },\n        scale: {\n            type: \"Vector3\",\n            get: (node) => node._babylonTransformNode?.scaling,\n            set: (value, node) => node._babylonTransformNode?.scaling.copyFrom(value),\n            getTarget: (node) => node._babylonTransformNode,\n            getPropertyName: [() => \"scaling\"],\n        },\n        weights: {\n            length: {\n                type: \"number\",\n                get: (node) => node._numMorphTargets,\n                getTarget: (node) => node._babylonTransformNode,\n                getPropertyName: [() => \"influence\"],\n            },\n            __array__: {\n                __target__: true,\n                type: \"number\",\n                get: (node, index) => (index !== undefined ? node._primitiveBabylonMeshes?.[0].morphTargetManager?.getTarget(index).influence : undefined),\n                // set: (value: number, node: INode, index?: number) => node._babylonTransformNode?.getMorphTargetManager()?.getTarget(index)?.setInfluence(value),\n                getTarget: (node) => node._babylonTransformNode,\n                getPropertyName: [() => \"influence\"],\n            },\n            type: \"number[]\",\n            get: (node, index) => [0], // TODO: get the weights correctly\n            // set: (value: number, node: INode, index?: number) => node._babylonTransformNode?.getMorphTargetManager()?.getTarget(index)?.setInfluence(value),\n            getTarget: (node) => node._babylonTransformNode,\n            getPropertyName: [() => \"influence\"],\n        },\n        // readonly!\n        matrix: {\n            type: \"Matrix\",\n            get: (node) => Matrix.Compose(node._babylonTransformNode?.scaling, node._babylonTransformNode?.rotationQuaternion, node._babylonTransformNode?.position),\n            getTarget: (node) => node._babylonTransformNode,\n            isReadOnly: true,\n        },\n        globalMatrix: {\n            type: \"Matrix\",\n            get: (node) => {\n                const matrix = Matrix.Identity();\n                // RHS/LHS support\n                let rootNode = node.parent;\n                while (rootNode && rootNode.parent) {\n                    rootNode = rootNode.parent;\n                }\n                const forceUpdate = node._babylonTransformNode?.position._isDirty || node._babylonTransformNode?.rotationQuaternion?._isDirty || node._babylonTransformNode?.scaling._isDirty;\n                if (rootNode) {\n                    // take the parent root node's world matrix, invert it, and multiply it with the current node's world matrix\n                    // This will provide the global matrix, ignoring the RHS->LHS conversion\n                    const rootMatrix = rootNode._babylonTransformNode?.computeWorldMatrix(true).invert();\n                    if (rootMatrix) {\n                        node._babylonTransformNode?.computeWorldMatrix(forceUpdate)?.multiplyToRef(rootMatrix, matrix);\n                    }\n                }\n                else if (node._babylonTransformNode) {\n                    matrix.copyFrom(node._babylonTransformNode.computeWorldMatrix(forceUpdate));\n                }\n                return matrix;\n            },\n            getTarget: (node) => node._babylonTransformNode,\n            isReadOnly: true,\n        },\n        extensions: {\n            EXT_lights_ies: {\n                multiplier: {\n                    type: \"number\",\n                    get: (node) => {\n                        return node._babylonTransformNode?.getChildren((child) => child instanceof SpotLight, true)[0]?.intensity;\n                    },\n                    getTarget: (node) => node._babylonTransformNode?.getChildren((child) => child instanceof SpotLight, true)[0],\n                    set: (value, node) => {\n                        if (node._babylonTransformNode) {\n                            const light = node._babylonTransformNode.getChildren((child) => child instanceof SpotLight, true)[0];\n                            if (light) {\n                                light.intensity = value;\n                            }\n                        }\n                    },\n                },\n                color: {\n                    type: \"Color3\",\n                    get: (node) => {\n                        return node._babylonTransformNode?.getChildren((child) => child instanceof SpotLight, true)[0]?.diffuse;\n                    },\n                    getTarget: (node) => node._babylonTransformNode?.getChildren((child) => child instanceof SpotLight, true)[0],\n                    set: (value, node) => {\n                        if (node._babylonTransformNode) {\n                            const light = node._babylonTransformNode.getChildren((child) => child instanceof SpotLight, true)[0];\n                            if (light) {\n                                light.diffuse = value;\n                            }\n                        }\n                    },\n                },\n            },\n        },\n    },\n};\nconst animationsTree = {\n    length: {\n        type: \"number\",\n        get: (animations) => animations.length,\n        getTarget: (animations) => animations.map((animation) => animation._babylonAnimationGroup),\n        getPropertyName: [() => \"length\"],\n    },\n    __array__: {},\n};\nconst meshesTree = {\n    length: {\n        type: \"number\",\n        get: (meshes) => meshes.length,\n        getTarget: (meshes) => meshes.map((mesh) => mesh.primitives[0]._instanceData?.babylonSourceMesh),\n        getPropertyName: [() => \"length\"],\n    },\n    __array__: {},\n};\nconst camerasTree = {\n    __array__: {\n        __target__: true,\n        orthographic: {\n            xmag: {\n                componentsCount: 2,\n                type: \"Vector2\",\n                get: (camera) => new Vector2(camera._babylonCamera?.orthoLeft ?? 0, camera._babylonCamera?.orthoRight ?? 0),\n                set: (value, camera) => {\n                    if (camera._babylonCamera) {\n                        camera._babylonCamera.orthoLeft = value.x;\n                        camera._babylonCamera.orthoRight = value.y;\n                    }\n                },\n                getTarget: (camera) => camera,\n                getPropertyName: [() => \"orthoLeft\", () => \"orthoRight\"],\n            },\n            ymag: {\n                componentsCount: 2,\n                type: \"Vector2\",\n                get: (camera) => new Vector2(camera._babylonCamera?.orthoBottom ?? 0, camera._babylonCamera?.orthoTop ?? 0),\n                set: (value, camera) => {\n                    if (camera._babylonCamera) {\n                        camera._babylonCamera.orthoBottom = value.x;\n                        camera._babylonCamera.orthoTop = value.y;\n                    }\n                },\n                getTarget: (camera) => camera,\n                getPropertyName: [() => \"orthoBottom\", () => \"orthoTop\"],\n            },\n            zfar: {\n                type: \"number\",\n                get: (camera) => camera._babylonCamera?.maxZ,\n                set: (value, camera) => {\n                    if (camera._babylonCamera) {\n                        camera._babylonCamera.maxZ = value;\n                    }\n                },\n                getTarget: (camera) => camera,\n                getPropertyName: [() => \"maxZ\"],\n            },\n            znear: {\n                type: \"number\",\n                get: (camera) => camera._babylonCamera?.minZ,\n                set: (value, camera) => {\n                    if (camera._babylonCamera) {\n                        camera._babylonCamera.minZ = value;\n                    }\n                },\n                getTarget: (camera) => camera,\n                getPropertyName: [() => \"minZ\"],\n            },\n        },\n        perspective: {\n            aspectRatio: {\n                type: \"number\",\n                get: (camera) => camera._babylonCamera?.getEngine().getAspectRatio(camera._babylonCamera),\n                getTarget: (camera) => camera,\n                getPropertyName: [() => \"aspectRatio\"],\n                isReadOnly: true, // might not be the case for glTF?\n            },\n            yfov: {\n                type: \"number\",\n                get: (camera) => camera._babylonCamera?.fov,\n                set: (value, camera) => {\n                    if (camera._babylonCamera) {\n                        camera._babylonCamera.fov = value;\n                    }\n                },\n                getTarget: (camera) => camera,\n                getPropertyName: [() => \"fov\"],\n            },\n            zfar: {\n                type: \"number\",\n                get: (camera) => camera._babylonCamera?.maxZ,\n                set: (value, camera) => {\n                    if (camera._babylonCamera) {\n                        camera._babylonCamera.maxZ = value;\n                    }\n                },\n                getTarget: (camera) => camera,\n                getPropertyName: [() => \"maxZ\"],\n            },\n            znear: {\n                type: \"number\",\n                get: (camera) => camera._babylonCamera?.minZ,\n                set: (value, camera) => {\n                    if (camera._babylonCamera) {\n                        camera._babylonCamera.minZ = value;\n                    }\n                },\n                getTarget: (camera) => camera,\n                getPropertyName: [() => \"minZ\"],\n            },\n        },\n    },\n};\nconst materialsTree = {\n    __array__: {\n        __target__: true,\n        emissiveFactor: {\n            type: \"Color3\",\n            get: (material, index, payload) => _GetMaterial(material, index, payload).emissiveColor,\n            set: (value, material, index, payload) => _GetMaterial(material, index, payload).emissiveColor.copyFrom(value),\n            getTarget: (material, index, payload) => _GetMaterial(material, index, payload),\n            getPropertyName: [() => \"emissiveColor\"],\n        },\n        emissiveTexture: {\n            extensions: {\n                KHR_texture_transform: _GenerateTextureMap(\"emissiveTexture\"),\n            },\n        },\n        normalTexture: {\n            scale: {\n                type: \"number\",\n                get: (material, index, payload) => _GetTexture(material, payload, \"bumpTexture\")?.level,\n                set: (value, material, index, payload) => {\n                    const texture = _GetTexture(material, payload, \"bumpTexture\");\n                    if (texture) {\n                        texture.level = value;\n                    }\n                },\n                getTarget: (material, index, payload) => _GetMaterial(material, index, payload),\n                getPropertyName: [() => \"level\"],\n            },\n            extensions: {\n                KHR_texture_transform: _GenerateTextureMap(\"bumpTexture\"),\n            },\n        },\n        occlusionTexture: {\n            strength: {\n                type: \"number\",\n                get: (material, index, payload) => _GetMaterial(material, index, payload).ambientTextureStrength,\n                set: (value, material, index, payload) => {\n                    const mat = _GetMaterial(material, index, payload);\n                    if (mat) {\n                        mat.ambientTextureStrength = value;\n                    }\n                },\n                getTarget: (material, index, payload) => _GetMaterial(material, index, payload),\n                getPropertyName: [() => \"ambientTextureStrength\"],\n            },\n            extensions: {\n                KHR_texture_transform: _GenerateTextureMap(\"ambientTexture\"),\n            },\n        },\n        pbrMetallicRoughness: {\n            baseColorFactor: {\n                type: \"Color4\",\n                get: (material, index, payload) => {\n                    const mat = _GetMaterial(material, index, payload);\n                    return Color4.FromColor3(mat.albedoColor, mat.alpha);\n                },\n                set: (value, material, index, payload) => {\n                    const mat = _GetMaterial(material, index, payload);\n                    mat.albedoColor.set(value.r, value.g, value.b);\n                    mat.alpha = value.a;\n                },\n                getTarget: (material, index, payload) => _GetMaterial(material, index, payload),\n                // This is correct on the animation level, but incorrect as a single property of a type Color4\n                getPropertyName: [() => \"albedoColor\", () => \"alpha\"],\n            },\n            baseColorTexture: {\n                extensions: {\n                    KHR_texture_transform: _GenerateTextureMap(\"albedoTexture\"),\n                },\n            },\n            metallicFactor: {\n                type: \"number\",\n                get: (material, index, payload) => _GetMaterial(material, index, payload).metallic,\n                set: (value, material, index, payload) => {\n                    const mat = _GetMaterial(material, index, payload);\n                    if (mat) {\n                        mat.metallic = value;\n                    }\n                },\n                getTarget: (material, index, payload) => _GetMaterial(material, index, payload),\n                getPropertyName: [() => \"metallic\"],\n            },\n            roughnessFactor: {\n                type: \"number\",\n                get: (material, index, payload) => _GetMaterial(material, index, payload).roughness,\n                set: (value, material, index, payload) => {\n                    const mat = _GetMaterial(material, index, payload);\n                    if (mat) {\n                        mat.roughness = value;\n                    }\n                },\n                getTarget: (material, index, payload) => _GetMaterial(material, index, payload),\n                getPropertyName: [() => \"roughness\"],\n            },\n            metallicRoughnessTexture: {\n                extensions: {\n                    KHR_texture_transform: _GenerateTextureMap(\"metallicTexture\"),\n                },\n            },\n        },\n        extensions: {\n            KHR_materials_anisotropy: {\n                anisotropyStrength: {\n                    type: \"number\",\n                    get: (material, index, payload) => _GetMaterial(material, index, payload).anisotropy.intensity,\n                    set: (value, material, index, payload) => {\n                        _GetMaterial(material, index, payload).anisotropy.intensity = value;\n                    },\n                    getTarget: (material, index, payload) => _GetMaterial(material, index, payload),\n                    getPropertyName: [() => \"anisotropy.intensity\"],\n                },\n                anisotropyRotation: {\n                    type: \"number\",\n                    get: (material, index, payload) => _GetMaterial(material, index, payload).anisotropy.angle,\n                    set: (value, material, index, payload) => {\n                        _GetMaterial(material, index, payload).anisotropy.angle = value;\n                    },\n                    getTarget: (material, index, payload) => _GetMaterial(material, index, payload),\n                    getPropertyName: [() => \"anisotropy.angle\"],\n                },\n                anisotropyTexture: {\n                    extensions: {\n                        KHR_texture_transform: _GenerateTextureMap(\"anisotropy\", \"texture\"),\n                    },\n                },\n            },\n            KHR_materials_clearcoat: {\n                clearcoatFactor: {\n                    type: \"number\",\n                    get: (material, index, payload) => _GetMaterial(material, index, payload).clearCoat.intensity,\n                    set: (value, material, index, payload) => {\n                        _GetMaterial(material, index, payload).clearCoat.intensity = value;\n                    },\n                    getTarget: (material, index, payload) => _GetMaterial(material, index, payload),\n                    getPropertyName: [() => \"clearCoat.intensity\"],\n                },\n                clearcoatRoughnessFactor: {\n                    type: \"number\",\n                    get: (material, index, payload) => _GetMaterial(material, index, payload).clearCoat.roughness,\n                    set: (value, material, index, payload) => {\n                        _GetMaterial(material, index, payload).clearCoat.roughness = value;\n                    },\n                    getTarget: (material, index, payload) => _GetMaterial(material, index, payload),\n                    getPropertyName: [() => \"clearCoat.roughness\"],\n                },\n                clearcoatTexture: {\n                    extensions: {\n                        KHR_texture_transform: _GenerateTextureMap(\"clearCoat\", \"texture\"),\n                    },\n                },\n                clearcoatNormalTexture: {\n                    scale: {\n                        type: \"number\",\n                        get: (material, index, payload) => _GetMaterial(material, index, payload).clearCoat.bumpTexture?.level,\n                        getTarget: _GetMaterial,\n                        set: (value, material, index, payload) => (_GetMaterial(material, index, payload).clearCoat.bumpTexture.level = value),\n                    },\n                    extensions: {\n                        KHR_texture_transform: _GenerateTextureMap(\"clearCoat\", \"bumpTexture\"),\n                    },\n                },\n                clearcoatRoughnessTexture: {\n                    extensions: {\n                        KHR_texture_transform: _GenerateTextureMap(\"clearCoat\", \"textureRoughness\"),\n                    },\n                },\n            },\n            KHR_materials_dispersion: {\n                dispersion: {\n                    type: \"number\",\n                    get: (material, index, payload) => _GetMaterial(material, index, payload).subSurface.dispersion,\n                    getTarget: _GetMaterial,\n                    set: (value, material, index, payload) => (_GetMaterial(material, index, payload).subSurface.dispersion = value),\n                },\n            },\n            KHR_materials_emissive_strength: {\n                emissiveStrength: {\n                    type: \"number\",\n                    get: (material, index, payload) => _GetMaterial(material, index, payload).emissiveIntensity,\n                    getTarget: _GetMaterial,\n                    set: (value, material, index, payload) => (_GetMaterial(material, index, payload).emissiveIntensity = value),\n                },\n            },\n            KHR_materials_ior: {\n                ior: {\n                    type: \"number\",\n                    get: (material, index, payload) => _GetMaterial(material, index, payload).indexOfRefraction,\n                    getTarget: _GetMaterial,\n                    set: (value, material, index, payload) => (_GetMaterial(material, index, payload).indexOfRefraction = value),\n                },\n            },\n            KHR_materials_iridescence: {\n                iridescenceFactor: {\n                    type: \"number\",\n                    get: (material, index, payload) => _GetMaterial(material, index, payload).iridescence.intensity,\n                    getTarget: _GetMaterial,\n                    set: (value, material, index, payload) => (_GetMaterial(material, index, payload).iridescence.intensity = value),\n                },\n                iridescenceIor: {\n                    type: \"number\",\n                    get: (material, index, payload) => _GetMaterial(material, index, payload).iridescence.indexOfRefraction,\n                    getTarget: _GetMaterial,\n                    set: (value, material, index, payload) => (_GetMaterial(material, index, payload).iridescence.indexOfRefraction = value),\n                },\n                iridescenceTexture: {\n                    extensions: {\n                        KHR_texture_transform: _GenerateTextureMap(\"iridescence\", \"texture\"),\n                    },\n                },\n                iridescenceThicknessMaximum: {\n                    type: \"number\",\n                    get: (material, index, payload) => _GetMaterial(material, index, payload).iridescence.maximumThickness,\n                    getTarget: _GetMaterial,\n                    set: (value, material, index, payload) => (_GetMaterial(material, index, payload).iridescence.maximumThickness = value),\n                },\n                iridescenceThicknessMinimum: {\n                    type: \"number\",\n                    get: (material, index, payload) => _GetMaterial(material, index, payload).iridescence.minimumThickness,\n                    getTarget: _GetMaterial,\n                    set: (value, material, index, payload) => (_GetMaterial(material, index, payload).iridescence.minimumThickness = value),\n                },\n                iridescenceThicknessTexture: {\n                    extensions: {\n                        KHR_texture_transform: _GenerateTextureMap(\"iridescence\", \"thicknessTexture\"),\n                    },\n                },\n            },\n            KHR_materials_sheen: {\n                sheenColorFactor: {\n                    type: \"Color3\",\n                    get: (material, index, payload) => _GetMaterial(material, index, payload).sheen.color,\n                    getTarget: _GetMaterial,\n                    set: (value, material, index, payload) => _GetMaterial(material, index, payload).sheen.color.copyFrom(value),\n                },\n                sheenColorTexture: {\n                    extensions: {\n                        KHR_texture_transform: _GenerateTextureMap(\"sheen\", \"texture\"),\n                    },\n                },\n                sheenRoughnessFactor: {\n                    type: \"number\",\n                    get: (material, index, payload) => _GetMaterial(material, index, payload).sheen.intensity,\n                    getTarget: _GetMaterial,\n                    set: (value, material, index, payload) => (_GetMaterial(material, index, payload).sheen.intensity = value),\n                },\n                sheenRoughnessTexture: {\n                    extensions: {\n                        KHR_texture_transform: _GenerateTextureMap(\"sheen\", \"thicknessTexture\"),\n                    },\n                },\n            },\n            KHR_materials_specular: {\n                specularFactor: {\n                    type: \"number\",\n                    get: (material, index, payload) => _GetMaterial(material, index, payload).metallicF0Factor,\n                    getTarget: _GetMaterial,\n                    set: (value, material, index, payload) => (_GetMaterial(material, index, payload).metallicF0Factor = value),\n                    getPropertyName: [() => \"metallicF0Factor\"],\n                },\n                specularColorFactor: {\n                    type: \"Color3\",\n                    get: (material, index, payload) => _GetMaterial(material, index, payload).metallicReflectanceColor,\n                    getTarget: _GetMaterial,\n                    set: (value, material, index, payload) => _GetMaterial(material, index, payload).metallicReflectanceColor.copyFrom(value),\n                    getPropertyName: [() => \"metallicReflectanceColor\"],\n                },\n                specularTexture: {\n                    extensions: {\n                        KHR_texture_transform: _GenerateTextureMap(\"metallicReflectanceTexture\"),\n                    },\n                },\n                specularColorTexture: {\n                    extensions: {\n                        KHR_texture_transform: _GenerateTextureMap(\"reflectanceTexture\"),\n                    },\n                },\n            },\n            KHR_materials_transmission: {\n                transmissionFactor: {\n                    type: \"number\",\n                    get: (material, index, payload) => _GetMaterial(material, index, payload).subSurface.refractionIntensity,\n                    getTarget: _GetMaterial,\n                    set: (value, material, index, payload) => (_GetMaterial(material, index, payload).subSurface.refractionIntensity = value),\n                    getPropertyName: [() => \"subSurface.refractionIntensity\"],\n                },\n                transmissionTexture: {\n                    extensions: {\n                        KHR_texture_transform: _GenerateTextureMap(\"subSurface\", \"refractionIntensityTexture\"),\n                    },\n                },\n            },\n            KHR_materials_diffuse_transmission: {\n                diffuseTransmissionFactor: {\n                    type: \"number\",\n                    get: (material, index, payload) => _GetMaterial(material, index, payload).subSurface.translucencyIntensity,\n                    getTarget: _GetMaterial,\n                    set: (value, material, index, payload) => (_GetMaterial(material, index, payload).subSurface.translucencyIntensity = value),\n                },\n                diffuseTransmissionTexture: {\n                    extensions: {\n                        KHR_texture_transform: _GenerateTextureMap(\"subSurface\", \"translucencyIntensityTexture\"),\n                    },\n                },\n                diffuseTransmissionColorFactor: {\n                    type: \"Color3\",\n                    get: (material, index, payload) => _GetMaterial(material, index, payload).subSurface.translucencyColor,\n                    getTarget: _GetMaterial,\n                    set: (value, material, index, payload) => value && _GetMaterial(material, index, payload).subSurface.translucencyColor?.copyFrom(value),\n                },\n                diffuseTransmissionColorTexture: {\n                    extensions: {\n                        KHR_texture_transform: _GenerateTextureMap(\"subSurface\", \"translucencyColorTexture\"),\n                    },\n                },\n            },\n            KHR_materials_volume: {\n                attenuationColor: {\n                    type: \"Color3\",\n                    get: (material, index, payload) => _GetMaterial(material, index, payload).subSurface.tintColor,\n                    getTarget: _GetMaterial,\n                    set: (value, material, index, payload) => _GetMaterial(material, index, payload).subSurface.tintColor.copyFrom(value),\n                },\n                attenuationDistance: {\n                    type: \"number\",\n                    get: (material, index, payload) => _GetMaterial(material, index, payload).subSurface.tintColorAtDistance,\n                    getTarget: _GetMaterial,\n                    set: (value, material, index, payload) => (_GetMaterial(material, index, payload).subSurface.tintColorAtDistance = value),\n                },\n                thicknessFactor: {\n                    type: \"number\",\n                    get: (material, index, payload) => _GetMaterial(material, index, payload).subSurface.maximumThickness,\n                    getTarget: _GetMaterial,\n                    set: (value, material, index, payload) => (_GetMaterial(material, index, payload).subSurface.maximumThickness = value),\n                },\n                thicknessTexture: {\n                    extensions: {\n                        KHR_texture_transform: _GenerateTextureMap(\"subSurface\", \"thicknessTexture\"),\n                    },\n                },\n            },\n        },\n    },\n};\nconst extensionsTree = {\n    KHR_lights_punctual: {\n        lights: {\n            length: {\n                type: \"number\",\n                get: (lights) => lights.length,\n                getTarget: (lights) => lights.map((light) => light._babylonLight),\n                getPropertyName: [(_lights) => \"length\"],\n            },\n            __array__: {\n                __target__: true,\n                color: {\n                    type: \"Color3\",\n                    get: (light) => light._babylonLight?.diffuse,\n                    set: (value, light) => light._babylonLight?.diffuse.copyFrom(value),\n                    getTarget: (light) => light._babylonLight,\n                    getPropertyName: [(_light) => \"diffuse\"],\n                },\n                intensity: {\n                    type: \"number\",\n                    get: (light) => light._babylonLight?.intensity,\n                    set: (value, light) => (light._babylonLight ? (light._babylonLight.intensity = value) : undefined),\n                    getTarget: (light) => light._babylonLight,\n                    getPropertyName: [(_light) => \"intensity\"],\n                },\n                range: {\n                    type: \"number\",\n                    get: (light) => light._babylonLight?.range,\n                    set: (value, light) => (light._babylonLight ? (light._babylonLight.range = value) : undefined),\n                    getTarget: (light) => light._babylonLight,\n                    getPropertyName: [(_light) => \"range\"],\n                },\n                spot: {\n                    innerConeAngle: {\n                        type: \"number\",\n                        get: (light) => light._babylonLight?.innerAngle,\n                        set: (value, light) => (light._babylonLight ? (light._babylonLight.innerAngle = value) : undefined),\n                        getTarget: (light) => light._babylonLight,\n                        getPropertyName: [(_light) => \"innerConeAngle\"],\n                    },\n                    outerConeAngle: {\n                        type: \"number\",\n                        get: (light) => light._babylonLight?.angle,\n                        set: (value, light) => (light._babylonLight ? (light._babylonLight.angle = value) : undefined),\n                        getTarget: (light) => light._babylonLight,\n                        getPropertyName: [(_light) => \"outerConeAngle\"],\n                    },\n                },\n            },\n        },\n    },\n    EXT_lights_ies: {\n        lights: {\n            length: {\n                type: \"number\",\n                get: (lights) => lights.length,\n                getTarget: (lights) => lights.map((light) => light._babylonLight),\n                getPropertyName: [(_lights) => \"length\"],\n            },\n        },\n    },\n    EXT_lights_image_based: {\n        lights: {\n            length: {\n                type: \"number\",\n                get: (lights) => lights.length,\n                getTarget: (lights) => lights.map((light) => light._babylonTexture),\n                getPropertyName: [(_lights) => \"length\"],\n            },\n            __array__: {\n                __target__: true,\n                intensity: {\n                    type: \"number\",\n                    get: (light) => light._babylonTexture?.level,\n                    set: (value, light) => {\n                        if (light._babylonTexture)\n                            light._babylonTexture.level = value;\n                    },\n                    getTarget: (light) => light._babylonTexture,\n                },\n                rotation: {\n                    type: \"Quaternion\",\n                    get: (light) => light._babylonTexture && Quaternion.FromRotationMatrix(light._babylonTexture?.getReflectionTextureMatrix()),\n                    set: (value, light) => {\n                        if (!light._babylonTexture)\n                            return;\n                        // Invert the rotation so that positive rotation is counter-clockwise.\n                        if (!light._babylonTexture.getScene()?.useRightHandedSystem) {\n                            value = Quaternion.Inverse(value);\n                        }\n                        Matrix.FromQuaternionToRef(value, light._babylonTexture.getReflectionTextureMatrix());\n                    },\n                    getTarget: (light) => light._babylonTexture,\n                },\n            },\n        },\n    },\n};\nfunction _GetTexture(material, payload, textureType, textureInObject) {\n    const babylonMaterial = _GetMaterial(material, payload);\n    return textureInObject ? babylonMaterial[textureType][textureInObject] : babylonMaterial[textureType];\n}\nfunction _GetMaterial(material, _index, payload) {\n    return material._data?.[payload?.fillMode ?? Constants.MATERIAL_TriangleFillMode]?.babylonMaterial;\n}\nfunction _GenerateTextureMap(textureType, textureInObject) {\n    return {\n        offset: {\n            componentsCount: 2,\n            // assuming two independent values for u and v, and NOT a Vector2\n            type: \"Vector2\",\n            get: (material, _index, payload) => {\n                const texture = _GetTexture(material, payload, textureType, textureInObject);\n                return new Vector2(texture?.uOffset, texture?.vOffset);\n            },\n            getTarget: _GetMaterial,\n            set: (value, material, _index, payload) => {\n                const texture = _GetTexture(material, payload, textureType, textureInObject);\n                (texture.uOffset = value.x), (texture.vOffset = value.y);\n            },\n            getPropertyName: [\n                () => `${textureType}${textureInObject ? \".\" + textureInObject : \"\"}.uOffset`,\n                () => `${textureType}${textureInObject ? \".\" + textureInObject : \"\"}.vOffset`,\n            ],\n        },\n        rotation: {\n            type: \"number\",\n            get: (material, _index, payload) => _GetTexture(material, payload, textureType, textureInObject)?.wAng,\n            getTarget: _GetMaterial,\n            set: (value, material, _index, payload) => (_GetTexture(material, payload, textureType, textureInObject).wAng = value),\n            getPropertyName: [() => `${textureType}${textureInObject ? \".\" + textureInObject : \"\"}.wAng`],\n        },\n        scale: {\n            componentsCount: 2,\n            type: \"Vector2\",\n            get: (material, _index, payload) => {\n                const texture = _GetTexture(material, payload, textureType, textureInObject);\n                return new Vector2(texture?.uScale, texture?.vScale);\n            },\n            getTarget: _GetMaterial,\n            set: (value, material, index, payload) => {\n                const texture = _GetTexture(material, payload, textureType, textureInObject);\n                (texture.uScale = value.x), (texture.vScale = value.y);\n            },\n            getPropertyName: [\n                () => `${textureType}${textureInObject ? \".\" + textureInObject : \"\"}.uScale`,\n                () => `${textureType}${textureInObject ? \".\" + textureInObject : \"\"}.vScale`,\n            ],\n        },\n    };\n}\nconst objectModelMapping = {\n    cameras: camerasTree,\n    nodes: nodesTree,\n    materials: materialsTree,\n    extensions: extensionsTree,\n    animations: animationsTree,\n    meshes: meshesTree,\n};\n/**\n * get a path-to-object converter for the given glTF tree\n * @param gltf the glTF tree to use\n * @returns a path-to-object converter for the given glTF tree\n */\nexport function GetPathToObjectConverter(gltf) {\n    return new GLTFPathToObjectConverter(gltf, objectModelMapping);\n}\n/**\n * This function will return the object accessor for the given key in the object model\n * If the key is not found, it will return undefined\n * @param key the key to get the mapping for, for example /materials/\\{\\}/emissiveFactor\n * @returns an object accessor for the given key, or undefined if the key is not found\n */\nexport function GetMappingForKey(key) {\n    // replace every `{}` in key with __array__ to match the object model\n    const keyParts = key.split(\"/\").map((part) => part.replace(/{}/g, \"__array__\"));\n    let current = objectModelMapping;\n    for (const part of keyParts) {\n        // make sure part is not empty\n        if (!part) {\n            continue;\n        }\n        current = current[part];\n    }\n    // validate that current is an object accessor\n    if (current && current.type && current.get) {\n        return current;\n    }\n    return undefined;\n}\n/**\n * Set interpolation for a specific key in the object model\n * @param key the key to set, for example /materials/\\{\\}/emissiveFactor\n * @param interpolation the interpolation elements array\n */\nexport function SetInterpolationForKey(key, interpolation) {\n    // replace every `{}` in key with __array__ to match the object model\n    const keyParts = key.split(\"/\").map((part) => part.replace(/{}/g, \"__array__\"));\n    let current = objectModelMapping;\n    for (const part of keyParts) {\n        // make sure part is not empty\n        if (!part) {\n            continue;\n        }\n        current = current[part];\n    }\n    // validate that the current object is an object accessor\n    if (current && current.type && current.get) {\n        current.interpolation = interpolation;\n    }\n}\n/**\n * This will ad a new object accessor in the object model at the given key.\n * Note that this will NOT change the typescript types. To do that you will need to change the interface itself (extending it in the module that uses it)\n * @param key the key to add the object accessor at. For example /cameras/\\{\\}/perspective/aspectRatio\n * @param accessor the object accessor to add\n */\nexport function AddObjectAccessorToKey(key, accessor) {\n    // replace every `{}` in key with __array__ to match the object model\n    const keyParts = key.split(\"/\").map((part) => part.replace(/{}/g, \"__array__\"));\n    let current = objectModelMapping;\n    for (const part of keyParts) {\n        // make sure part is not empty\n        if (!part) {\n            continue;\n        }\n        if (!current[part]) {\n            if (part === \"?\") {\n                current.__ignoreObjectTree__ = true;\n                continue;\n            }\n            current[part] = {};\n            // if the part is __array__ then add the __target__ property\n            if (part === \"__array__\") {\n                current[part].__target__ = true;\n            }\n        }\n        current = current[part];\n    }\n    Object.assign(current, accessor);\n}\n//# sourceMappingURL=objectModelMapping.js.map"], "names": ["Constants", "ShadowLight", "Light", "Matrix", "value", "Vector3", "faceIndex", "target", "xaxis", "Axis", "yaxis", "force", "activeCamera", "matrix", "viewMatrix", "renderList", "lightDirection", "TmpVectors", "lightPosition", "lightTarget", "__decorate", "serializeAsVector3", "serialize", "Node", "name", "scene", "SpotLight", "texture", "projection", "position", "direction", "angle", "exponent", "minZ", "maxZ", "useReverseDepthBuffer", "lightFar", "lightNear", "P", "Q", "S", "Texture", "u", "v", "effect", "lightIndex", "normalizeDirection", "lightDataUniformName", "engine", "defines", "serializeAsTexture", "RegisterClass", "GLTFPathToObjectConverter", "_gltf", "_infoTree", "path", "objectTree", "infoTree", "parts", "split", "ignoreObjectTree", "part", "<PERSON><PERSON><PERSON><PERSON>", "nodesTree", "nodes", "node", "index", "rootNode", "forceUpdate", "rootMatrix", "child", "light", "animationsTree", "animations", "animation", "meshesTree", "meshes", "mesh", "camerasTree", "camera", "Vector2", "materialsTree", "material", "payload", "_GetMaterial", "_GenerateTextureMap", "_GetTexture", "mat", "Color4", "extensionsTree", "lights", "_lights", "_light", "Quaternion", "textureType", "textureInObject", "babylonMaterial", "_index", "objectModelMapping", "GetPathToObjectConverter", "gltf", "GetMappingForKey", "key", "keyParts", "current", "SetInterpolationForKey", "interpolation", "AddObjectAccessorToKey", "accessor"], "mappings": "8IAEO,MAAMA,CAAU,CACvB,CAEAA,EAAU,kBAAoB,UAE9BA,EAAU,UAAY,sCAEtBA,EAAU,cAAgB,EAE1BA,EAAU,UAAY,EAEtBA,EAAU,cAAgB,EAE1BA,EAAU,eAAiB,EAE3BA,EAAU,eAAiB,EAE3BA,EAAU,gBAAkB,EAE5BA,EAAU,aAAe,EAEzBA,EAAU,oBAAsB,EAKhCA,EAAU,+BAAiC,EAE3CA,EAAU,kBAAoB,EAK9BA,EAAU,iBAAmB,GAK7BA,EAAU,oBAAsB,GAKhCA,EAAU,mBAAqB,GAI/BA,EAAU,sBAAwB,GAKlCA,EAAU,8BAAgC,GAK1CA,EAAU,qBAAuB,GAKjCA,EAAU,gBAAkB,GAK5BA,EAAU,uBAAyB,GAEnCA,EAAU,mBAAqB,EAE/BA,EAAU,yBAA2B,EAErCA,EAAU,gCAAkC,EAE5CA,EAAU,mBAAqB,EAE/BA,EAAU,mBAAqB,EAK/BA,EAAU,sBAAwB,EAElCA,EAAU,oBAAsB,EAEhCA,EAAU,sBAAwB,EAElCA,EAAU,uBAAyB,EAEnCA,EAAU,yBAA2B,EAGrCA,EAAU,MAAQ,IAElBA,EAAU,OAAS,IAEnBA,EAAU,KAAO,IAEjBA,EAAU,MAAQ,IAElBA,EAAU,OAAS,IAEnBA,EAAU,QAAU,IAEpBA,EAAU,OAAS,IAEnBA,EAAU,SAAW,IAGrBA,EAAU,KAAO,KAEjBA,EAAU,KAAO,EAEjBA,EAAU,QAAU,KAEpBA,EAAU,KAAO,KAEjBA,EAAU,KAAO,KAEjBA,EAAU,OAAS,KAEnBA,EAAU,UAAY,MAEtBA,EAAU,UAAY,MAEtBA,EAAU,0BAA4B,EAEtCA,EAAU,yBAA2B,EAErCA,EAAU,2BAA6B,EAEvCA,EAAU,6BAA+B,EAEzCA,EAAU,oBAAsB,EAEhCA,EAAU,wBAA0B,EAEpCA,EAAU,8BAAgC,EAE1CA,EAAU,kBAAoB,EAE9BA,EAAU,mBAAqB,EAE/BA,EAAU,kBAAoB,EAE9BA,EAAU,gBAAkB,EAE5BA,EAAU,wBAA0B,MAEpCA,EAAU,yBAA2B,MAErCA,EAAU,0BAA4B,MAEtCA,EAAU,2BAA6B,MAEvCA,EAAU,wBAA0B,MAEpCA,EAAU,yBAA2B,MAErCA,EAAU,0BAA4B,MAEtCA,EAAU,2BAA6B,MAEvCA,EAAU,iBAAmB,EAE7BA,EAAU,0BAA4B,EAEtCA,EAAU,wBAA0B,EAEpCA,EAAU,yBAA2B,EAErCA,EAAU,0BAA4B,GAEtCA,EAAU,2BAA6B,GAEvCA,EAAU,mBAAqB,GAE/BA,EAAU,+BAAiC,GAE3CA,EAAU,4BAA8B,GAExCA,EAAU,sBAAwB,GAElCA,EAAU,sBAAwB,GAElCA,EAAU,oCAAsC,GAEhDA,EAAU,oCAAsC,GAEhDA,EAAU,uBAAyB,GAEnCA,EAAU,wBAA0B,WAEpCA,EAAU,yCAA2C,MAErDA,EAAU,+CAAiD,MAE3DA,EAAU,iDAAmD,MAE7DA,EAAU,+CAAiD,MAE3DA,EAAU,wCAA0C,MAEpDA,EAAU,kDAAoD,MAE9DA,EAAU,wCAA0C,MAEpDA,EAAU,kDAAoD,MAE9DA,EAAU,wCAA0C,MAEpDA,EAAU,uCAAyC,MAEnDA,EAAU,kDAAoD,MAE9DA,EAAU,4CAA8C,MAExDA,EAAU,uCAAyC,MAEnDA,EAAU,mDAAqD,MAE/DA,EAAU,wCAA0C,MAEpDA,EAAU,mCAAqC,MAE/CA,EAAU,oCAAsC,MAEhDA,EAAU,uDAAyD,MAEnEA,EAAU,wDAA0D,MAEpEA,EAAU,wCAA0C,MAEpDA,EAAU,+CAAiD,MAE3DA,EAAU,0BAA4B,EAEtCA,EAAU,yBAA2B,EAErCA,EAAU,kBAAoB,EAE9BA,EAAU,uBAAyB,EAEnCA,EAAU,iBAAmB,EAE7BA,EAAU,kBAAoB,EAE9BA,EAAU,2BAA6B,EAEvCA,EAAU,gBAAkB,EAE5BA,EAAU,6BAA+B,EAEzCA,EAAU,mCAAqC,EAE/CA,EAAU,mCAAqC,EAE/CA,EAAU,iCAAmC,GAE7CA,EAAU,wCAA0C,GAEpDA,EAAU,8BAAgC,GAE1CA,EAAU,yCAA2C,GAErDA,EAAU,qCAAuC,GAEjDA,EAAU,2CAA6C,GAEvDA,EAAU,sBAAwB,GAElCA,EAAU,WAAa,KAEvBA,EAAU,iBAAmB,MAE7BA,EAAU,iBAAmB,MAE7BA,EAAU,uBAAyB,WAEnCA,EAAU,WAAa,MAEvBA,EAAU,6BAA+B,EAEzCA,EAAU,wBAA0B,EAEpCA,EAAU,8BAAgC,EAE1CA,EAAU,sBAAwB,EAElCA,EAAU,+BAAiC,EAE3CA,EAAU,gCAAkC,EAE5CA,EAAU,mCAAqC,EAE/CA,EAAU,kCAAoC,EAE9CA,EAAU,iCAAmC,EAE7CA,EAAU,uBAAyB,EAEnCA,EAAU,kCAAoC,EAE9CA,EAAU,kCAAoC,EAE9CA,EAAU,iCAAmC,GAE7CA,EAAU,iCAAmC,GAE7CA,EAAU,uBAAyB,GAEnCA,EAAU,sBAAwB,EAElCA,EAAU,uBAAyB,EAEnCA,EAAU,oBAAsB,EAEhCA,EAAU,mBAAqB,EAE/BA,EAAU,wBAA0B,EAEpCA,EAAU,oBAAsB,EAEhCA,EAAU,sBAAwB,EAElCA,EAAU,6BAA+B,EAEzCA,EAAU,mCAAqC,EAE/CA,EAAU,4CAA8C,EAExDA,EAAU,kCAAoC,KAE9CA,EAAU,+BAAiC,GAE3CA,EAAU,iCAAmC,GAE7CA,EAAU,8BAAgC,EAG1CA,EAAU,gBAAkB,EAE5BA,EAAU,kBAAoB,EAE9BA,EAAU,kBAAoB,EAI9BA,EAAU,0BAA4B,EAItCA,EAAU,wBAA0B,EAIpCA,EAAU,0BAA4B,EAItCA,EAAU,6BAA+B,EAIzCA,EAAU,uBAAyB,GAInCA,EAAU,0BAA4B,GAItCA,EAAU,kCAAoC,GAI9CA,EAAU,sBAAwB,IAIlCA,EAAU,0BAA4B,EAItCA,EAAU,2BAA6B,EAIvCA,EAAU,uBAAyB,EAInCA,EAAU,2BAA6B,EAIvCA,EAAU,0BAA4B,EAItCA,EAAU,0BAA4B,EAItCA,EAAU,2BAA6B,EAIvCA,EAAU,+BAAiC,EAI3CA,EAAU,6BAA+B,EAIzCA,EAAU,kCAAoC,EAI9CA,EAAU,yCAA2C,EAKrDA,EAAU,sBAAwB,EAKlCA,EAAU,qBAAuB,EAKjCA,EAAU,yBAA2B,EAKrCA,EAAU,0BAA4B,EAKtCA,EAAU,2BAA6B,EAKvCA,EAAU,yBAA2B,EAKrCA,EAAU,2BAA6B,EAKvCA,EAAU,uBAAyB,EAMnCA,EAAU,wBAA0B,GAKpCA,EAAU,0BAA4B,EAKtCA,EAAU,4BAA8B,EAKxCA,EAAU,2BAA6B,GAKvCA,EAAU,2BAA6B,GAKvCA,EAAU,kCAAoC,GAK9CA,EAAU,iCAAmC,GAK7CA,EAAU,wBAA0B,GAKpCA,EAAU,sBAAwB,GAIlCA,EAAU,0BAA4B,EAItCA,EAAU,4BAA8B,EAIxCA,EAAU,kCAAoC,EAI9CA,EAAU,wCAA0C,EAOpDA,EAAU,gCAAkC,EAO5CA,EAAU,2CAA6C,EAUvDA,EAAU,4CAA8C,EAUxDA,EAAU,8DAAgE,EAI1EA,EAAU,uBAAyB,EAInCA,EAAU,4BAA8B,EAIxCA,EAAU,4BAA8B,EAIxCA,EAAU,6BAA+B,EAKzCA,EAAU,gCAAkC,EAK5CA,EAAU,8BAAgC,EAK1CA,EAAU,8BAAgC,EAK1CA,EAAU,kCAAoC,EAK9CA,EAAU,2BAA6B,EAKvCA,EAAU,2BAA6B,EAKvCA,EAAU,4BAA8B,EAKxCA,EAAU,iCAAmC,EAK7CA,EAAU,kCAAoC,EAK9CA,EAAU,oCAAsC,EAKhDA,EAAU,uCAAyC,GAKnDA,EAAU,qCAAuC,GAKjDA,EAAU,4BAA8B,GAExCA,EAAU,yBAA2B,EAErCA,EAAU,0BAA4B,EAEtCA,EAAU,8BAAgC,EAE1CA,EAAU,4BAA8B,EAExCA,EAAU,2BAA6B,EAEvCA,EAAU,0BAA4B,GAEtCA,EAAU,4BAA8B,GAExCA,EAAU,6BAA+B,GAKzCA,EAAU,gBAAkB,EAI5BA,EAAU,cAAgB,GAI1BA,EAAU,eAAiB,GAI3BA,EAAU,gBAAkB,GAI5BA,EAAU,gBAAkB,GAI5BA,EAAU,gBAAkB,GAI5BA,EAAU,gBAAkB,GAE5BA,EAAU,2BAA6B,EAEvCA,EAAU,uBAAyB,EAMnCA,EAAU,mBAAqB,EAK/BA,EAAU,oBAAsB,EAKhCA,EAAU,uBAAyB,EAInCA,EAAU,yBAA2B,EAKrCA,EAAU,cAAgB,EAK1BA,EAAU,+BAAiC,GAI3CA,EAAU,0CAA4C,GAItDA,EAAU,2CAA6C,GAIvDA,EAAU,gCAAkC,GAI5CA,EAAU,iCAAmC,GAI7CA,EAAU,YAAc,GAIxBA,EAAU,gBAAkB,GAI5BA,EAAU,sBAAwB,EAKlCA,EAAU,sBAAwB,MAElCA,EAAU,sBAAwB,MAElCA,EAAU,sBAAwB,MAElCA,EAAU,2BAA6B,MAEvCA,EAAU,mCAAqC,MAE/CA,EAAU,sBAAwB,IAElCA,EAAU,sCAAwC,IAElDA,EAAU,4BAA8B,IAExCA,EAAU,sCAAwC,IAElDA,EAAU,4BAA8B,IAExCA,EAAU,sCAAwC,IAElDA,EAAU,4BAA8B,IAExCA,EAAU,sCAAwC,IAElDA,EAAU,sCAAwC,IAElDA,EAAU,iCAAmC,MAE7CA,EAAU,2CAA6C,MAEvDA,EAAU,iCAAmC,MAE7CA,EAAU,2CAA6C,MAEvDA,EAAU,6BAA+B,MAEzCA,EAAU,uCAAyC,MAEnDA,EAAU,6BAA+B,MAEzCA,EAAU,uCAAyC,MAEnDA,EAAU,WAAa,gCAEvBA,EAAU,aAAe,EAEzBA,EAAU,YAAc,EAExBA,EAAU,aAAe,EAEzBA,EAAU,eAAiB,EAI3BA,EAAU,KAAO,KAIjBA,EAAU,cAAgB,KAI1BA,EAAU,MAAQ,KAIlBA,EAAU,eAAiB,KAI3BA,EAAU,IAAM,KAIhBA,EAAU,aAAe,KAIzBA,EAAU,MAAQ,KAIlBA,EAAU,aAAe,WAIzBA,EAAU,WAAa,SAIvBA,EAAU,YAAc,UAIxBA,EAAU,OAAS,KAInBA,EAAU,QAAU,MAIpBA,EAAU,QAAU,MAIpBA,EAAU,QAAU,MAIpBA,EAAU,QAAU,MAIpBA,EAAU,QAAU,MAIpBA,EAAU,UAAY,QAItBA,EAAU,kBAAoB,gBAI9BA,EAAU,oBAAsB,kBAIhCA,EAAU,oBAAsB,kBAIhCA,EAAU,yBAA2B,uBAIrCA,EAAU,yBAA2B,uBAKrCA,EAAU,oBAAsB,EAIhCA,EAAU,sBAAwB,EAIlCA,EAAU,yBAA2B,EAIrCA,EAAU,qBAAuB,EAIjCA,EAAU,qBAAuB,EAIjCA,EAAU,qBAAuB,EAIjCA,EAAU,sBAAwB,EAIlCA,EAAU,mBAAqB,EAI/BA,EAAU,WAAa,EAIvBA,EAAU,WAAa,IC35BhB,MAAMC,UAAoBC,CAAM,CACnC,aAAc,CACV,MAAM,GAAG,SAAS,EAClB,KAAK,6BAA+B,GACpC,KAAK,YAAcC,EAAO,WAC1B,KAAK,kBAAoBA,EAAO,UACnC,CACD,aAAaC,EAAO,CAChB,KAAK,UAAYA,CACpB,CAKD,IAAI,UAAW,CACX,OAAO,KAAK,SACf,CAKD,IAAI,SAASA,EAAO,CAChB,KAAK,aAAaA,CAAK,CAC1B,CACD,cAAcA,EAAO,CACjB,KAAK,WAAaA,CACrB,CAKD,IAAI,WAAY,CACZ,OAAO,KAAK,UACf,CAKD,IAAI,UAAUA,EAAO,CACjB,KAAK,cAAcA,CAAK,CAC3B,CAID,IAAI,YAAa,CACb,OAAO,KAAK,WACf,CAID,IAAI,WAAWA,EAAO,CAClB,KAAK,YAAcA,EACnB,KAAK,6BAA4B,CACpC,CAID,IAAI,YAAa,CACb,OAAO,KAAK,WACf,CAID,IAAI,WAAWA,EAAO,CAClB,KAAK,YAAcA,EACnB,KAAK,6BAA4B,CACpC,CAKD,+BAAgC,CAC5B,OAAI,KAAK,QAAU,KAAK,OAAO,gBACtB,KAAK,sBACN,KAAK,oBAAsBC,EAAQ,QAEvCA,EAAQ,0BAA0B,KAAK,SAAU,KAAK,OAAO,eAAgB,EAAE,KAAK,mBAAmB,EAEnG,KAAK,YACA,KAAK,uBACN,KAAK,qBAAuBA,EAAQ,QAExCA,EAAQ,qBAAqB,KAAK,UAAW,KAAK,OAAO,eAAgB,EAAE,KAAK,oBAAoB,GAEjG,IAEJ,EACV,CAKD,eAAgB,CACZ,MAAO,GACV,CAOD,mBAAmBC,EAAW,CAC1B,OAAO,KAAK,qBAAuB,KAAK,qBAAuB,KAAK,SACvE,CAKD,qBAAsB,CAClB,OAAO,KAAK,oBAAsB,KAAK,oBAAsB,KAAK,QACrE,CAMD,qBAAqBC,EAAQ,CACzB,YAAK,UAAYF,EAAQ,UAAUE,EAAO,SAAS,KAAK,QAAQ,CAAC,EAC1D,KAAK,SACf,CAKD,aAAc,CACV,KAAK,UAAU,YACf,MAAMC,EAAQH,EAAQ,MAAM,KAAK,UAAWI,EAAK,CAAC,EAC5CC,EAAQL,EAAQ,MAAMG,EAAO,KAAK,SAAS,EACjD,OAAOH,EAAQ,iBAAiBG,EAAOE,EAAO,KAAK,SAAS,CAC/D,CAKD,UAAW,CACP,MAAO,EACV,CAKD,6BAA8B,CAC1B,OAAO,KAAK,4BACf,CAID,8BAA+B,CAC3B,KAAK,6BAA+B,EACvC,CAED,YAAa,CACT,MAAM,WAAU,EAChB,KAAK,OAAO,SAAWL,EAAQ,KAAI,CACtC,CAED,iBAAkB,CACd,MAAK,OAAK,OAAO,SAAS,OAAO,KAAK,QAAQ,CAIjD,CAMD,mBAAmBM,EAAO,CACtB,MAAI,CAACA,GAAS,KAAK,kBACf,KAAK,iBAAmB,KAAK,SAAU,EAAC,YAAW,EAC5C,KAAK,eAEhB,KAAK,aAAY,EACjB,KAAK,OAAO,SAAS,SAAS,KAAK,QAAQ,EACtC,KAAK,eACN,KAAK,aAAeR,EAAO,YAE/BA,EAAO,iBAAiB,KAAK,SAAS,EAAG,KAAK,SAAS,EAAG,KAAK,SAAS,EAAG,KAAK,YAAY,EACxF,KAAK,QAAU,KAAK,OAAO,iBAC3B,KAAK,aAAa,cAAc,KAAK,OAAO,eAAgB,EAAE,KAAK,YAAY,EAC/E,KAAK,sBAAqB,GAG9B,KAAK,+BAAiC,GAC/B,KAAK,aACf,CAMD,aAAaS,EAAc,CACvB,OAAO,KAAK,aAAe,OAAY,KAAK,WAAaA,GAAc,MAAQ,CAClF,CAMD,aAAaA,EAAc,CACvB,OAAO,KAAK,aAAe,OAAY,KAAK,WAAaA,GAAc,MAAQ,GAClF,CAQD,0BAA0BC,EAAQC,EAAYC,EAAY,CACtD,OAAI,KAAK,8BACL,KAAK,8BAA8BD,EAAYC,EAAYF,CAAM,EAGjE,KAAK,kCAAkCA,EAAQC,EAAYC,CAAU,EAElE,IACV,CAED,yBAA0B,CACtB,MAAM,wBAAuB,GACzB,CAAC,KAAK,QAAU,CAAC,KAAK,OAAO,kBAC7B,KAAK,oBAAsB,KAC3B,KAAK,qBAAuB,KAEnC,CAMD,cAAcT,EAAW,CACrB,MAAMU,EAAiBC,EAAW,QAAQ,CAAC,EAC3C,IAAIC,EAAgB,KAAK,SACrB,KAAK,kCACLA,EAAgB,KAAK,qBAEzBb,EAAQ,eAAe,KAAK,mBAAmBC,CAAS,EAAGU,CAAc,EACrE,KAAK,IAAIX,EAAQ,IAAIW,EAAgBX,EAAQ,GAAE,CAAE,CAAC,IAAM,IACxDW,EAAe,EAAI,OAEvB,MAAMG,EAAcF,EAAW,QAAQ,CAAC,EACxC,OAAAC,EAAc,SAASF,EAAgBG,CAAW,EAClDhB,EAAO,cAAce,EAAeC,EAAad,EAAQ,GAAI,EAAE,KAAK,WAAW,EACxE,KAAK,WACf,CAQD,oBAAoBS,EAAYC,EAAY,CACxC,YAAK,0BAA0B,KAAK,kBAAmBD,GAAc,KAAK,YAAaC,GAAc,CAAA,CAAE,EAChG,KAAK,iBACf,CACL,CACAK,EAAW,CACPC,EAAoB,CACxB,EAAGpB,EAAY,UAAW,WAAY,IAAI,EAC1CmB,EAAW,CACPC,EAAoB,CACxB,EAAGpB,EAAY,UAAW,YAAa,IAAI,EAC3CmB,EAAW,CACPE,EAAW,CACf,EAAGrB,EAAY,UAAW,aAAc,IAAI,EAC5CmB,EAAW,CACPE,EAAW,CACf,EAAGrB,EAAY,UAAW,aAAc,IAAI,EC9Q5CsB,EAAK,mBAAmB,eAAgB,CAACC,EAAMC,IACpC,IAAM,IAAIC,EAAUF,EAAMnB,EAAQ,KAAI,EAAIA,EAAQ,KAAI,EAAI,EAAG,EAAGoB,CAAK,CAC/E,EAQM,MAAMC,UAAkBzB,CAAY,CAKvC,IAAI,mBAAoB,CACpB,OAAO,KAAK,kBACf,CACD,IAAI,kBAAkBG,EAAO,CACrB,KAAK,qBAAuBA,IAGhC,KAAK,mBAAqBA,EACtB,KAAK,oBAAsBsB,EAAU,WAAW,KAAK,kBAAkB,GACvE,KAAK,mBAAmB,iBAAiB,QAAQ,IAAM,CACnD,KAAK,wBAAuB,CAC5C,CAAa,EAER,CAID,IAAI,OAAQ,CACR,OAAO,KAAK,MACf,CAID,IAAI,MAAMtB,EAAO,CACb,KAAK,OAASA,EACd,KAAK,cAAgB,KAAK,IAAIA,EAAQ,EAAG,EACzC,KAAK,uCAAyC,GAC9C,KAAK,6BAA4B,EACjC,KAAK,oBAAmB,CAC3B,CAMD,IAAI,YAAa,CACb,OAAO,KAAK,WACf,CAMD,IAAI,WAAWA,EAAO,CAClB,KAAK,YAAcA,EACnB,KAAK,oBAAmB,CAC3B,CAID,IAAI,kBAAmB,CACnB,OAAO,KAAK,iBACf,CAID,IAAI,iBAAiBA,EAAO,CACxB,KAAK,kBAAoBA,EACzB,KAAK,6BAA4B,CACpC,CAID,IAAI,yBAA0B,CAC1B,OAAO,KAAK,wBACf,CAID,IAAI,4BAA6B,CAC7B,OAAO,KAAK,2BACf,CAID,IAAI,2BAA2BA,EAAO,CAClC,KAAK,4BAA8BA,EACnC,KAAK,uCAAyC,EACjD,CAID,IAAI,2BAA4B,CAC5B,OAAO,KAAK,0BACf,CAID,IAAI,0BAA0BA,EAAO,CACjC,KAAK,2BAA6BA,EAClC,KAAK,uCAAyC,EACjD,CAID,IAAI,8BAA+B,CAC/B,OAAO,KAAK,6BACf,CAID,IAAI,6BAA6BA,EAAO,CACpC,KAAK,8BAAgCA,EACrC,KAAK,uCAAyC,EACjD,CAID,IAAI,mBAAoB,CACpB,OAAO,KAAK,kBACf,CAID,IAAI,kBAAkBA,EAAO,CACrB,KAAK,qBAAuBA,IAGhC,KAAK,mBAAqBA,EAC1B,KAAK,wBAA0B,GAC3B,KAAK,oBAAsB,CAAC,KAAK,mBAAmB,QAAO,IACvDsB,EAAU,qBAAqB,KAAK,kBAAkB,EACtD,KAAK,mBAAmB,UAAW,EAAC,oBAAoB,IAAM,CAC1D,KAAK,wBAAuB,CAChD,CAAiB,EAEIA,EAAU,WAAW,KAAK,kBAAkB,GACjD,KAAK,mBAAmB,iBAAiB,QAAQ,IAAM,CACnD,KAAK,wBAAuB,CAChD,CAAiB,GAGZ,CACD,OAAO,qBAAqBC,EAAS,CACjC,OAAOA,EAAQ,wBAA0B,MAC5C,CACD,OAAO,WAAWA,EAAS,CACvB,OAAOA,EAAQ,mBAAqB,MACvC,CAID,IAAI,wCAAyC,CACzC,OAAO,KAAK,uCACf,CACD,IAAI,uCAAuCC,EAAY,CACnD,KAAK,wCAA0CA,EAC/C,KAAK,uCAAyC,GAC9C,KAAK,wBAA0B,EAClC,CAYD,YAAYJ,EAAMK,EAAUC,EAAWC,EAAOC,EAAUP,EAAO,CAC3D,MAAMD,EAAMC,CAAK,EACjB,KAAK,YAAc,EACnB,KAAK,mBAAqB,KAC1B,KAAK,yBAA2BtB,EAAO,OACvC,KAAK,4BAA8B,KACnC,KAAK,2BAA6B,IAClC,KAAK,8BAAgCE,EAAQ,KAC7C,KAAK,iCAAmC,GACxC,KAAK,uCAAyC,GAC9C,KAAK,wBAA0B,GAC/B,KAAK,mCAAqCA,EAAQ,OAClD,KAAK,kCAAoCF,EAAO,OAChD,KAAK,wCAA0CA,EAAO,OACtD,KAAK,gCAAkCA,EAAO,WAAW,GAAK,EAAK,EAAK,EAAK,EAAK,GAAK,EAAK,EAAK,EAAK,EAAK,GAAK,EAAK,GAAK,GAAK,GAAK,CAAG,EACvI,KAAK,SAAW0B,EAChB,KAAK,UAAYC,EACjB,KAAK,MAAQC,EACb,KAAK,SAAWC,CACnB,CAKD,cAAe,CACX,MAAO,WACV,CAKD,WAAY,CACR,OAAO9B,EAAM,qBAChB,CAKD,cAAcE,EAAO,CACjB,MAAM,cAAcA,CAAK,EACzB,KAAK,iCAAmC,EAC3C,CAKD,aAAaA,EAAO,CAChB,MAAM,aAAaA,CAAK,EACxB,KAAK,iCAAmC,EAC3C,CASD,kCAAkCS,EAAQC,EAAYC,EAAY,CAC9D,MAAMH,EAAe,KAAK,SAAQ,EAAG,aACrC,GAAI,CAACA,EACD,OAEJ,KAAK,kBAAoB,KAAK,mBAAqB,EACnD,MAAMmB,EAAQ,KAAK,kBAAoB,KAAK,OACtCE,EAAO,KAAK,aAAe,OAAY,KAAK,WAAarB,EAAa,KACtEsB,EAAO,KAAK,aAAe,OAAY,KAAK,WAAatB,EAAa,KACtEuB,EAAwB,KAAK,SAAU,EAAC,UAAS,EAAG,sBAC1DhC,EAAO,sBAAsB4B,EAAO,EAAKI,EAAwBD,EAAOD,EAAME,EAAwBF,EAAOC,EAAMrB,EAAQ,GAAM,KAAK,OAAO,UAAS,EAAG,gBAAiB,OAAWsB,CAAqB,CAC7M,CACD,0CAA2C,CACvC,KAAK,iCAAmC,GACxC,KAAK,wBAA0B,GAC/B,KAAK,oBAAqB,EAAC,SAAS,KAAK,qBAAsB,KAAK,kCAAkC,EACtGhC,EAAO,cAAc,KAAK,sBAAuB,KAAK,mCAAoC,KAAK,8BAA+B,KAAK,iCAAiC,CACvK,CACD,gDAAiD,CAC7C,KAAK,uCAAyC,GAC9C,KAAK,wBAA0B,GAC/B,MAAMiC,EAAW,KAAK,0BAChBC,EAAY,KAAK,2BACjBC,EAAIF,GAAYA,EAAWC,GAC3BE,EAAI,CAACD,EAAID,EACTG,EAAI,EAAM,KAAK,IAAI,KAAK,OAAS,CAAG,EAE1CrC,EAAO,gBAAgBqC,EADb,EACoB,EAAK,EAAK,EAAK,EAAKA,EAAG,EAAK,EAAK,EAAK,EAAKF,EAAG,EAAK,EAAK,EAAKC,EAAG,EAAK,KAAK,uCAAuC,CAClJ,CAID,iCAAkC,CAG9B,GAFA,KAAK,wBAA0B,GAC/B,KAAK,kCAAkC,cAAc,KAAK,wCAAyC,KAAK,wBAAwB,EAC5H,KAAK,8BAA8BE,EAAS,CAC5C,MAAMC,EAAI,KAAK,mBAAmB,OAAS,EACrCC,EAAI,KAAK,mBAAmB,OAAS,EAC3CxC,EAAO,gBAAgBuC,EAAG,EAAK,EAAK,EAAK,EAAKC,EAAG,EAAK,EAAK,EAAK,EAAK,GAAK,EAAK,GAAK,GAAK,GAAK,EAAK,KAAK,+BAA+B,CAC1I,CACD,KAAK,yBAAyB,cAAc,KAAK,gCAAiC,KAAK,wBAAwB,CAClH,CACD,qBAAsB,CAClB,KAAK,eAAe,WAAW,aAAc,CAAC,EAC9C,KAAK,eAAe,WAAW,gBAAiB,CAAC,EACjD,KAAK,eAAe,WAAW,iBAAkB,CAAC,EAClD,KAAK,eAAe,WAAW,kBAAmB,CAAC,EACnD,KAAK,eAAe,WAAW,gBAAiB,CAAC,EACjD,KAAK,eAAe,WAAW,cAAe,CAAC,EAC/C,KAAK,eAAe,WAAW,cAAe,CAAC,EAC/C,KAAK,eAAe,QACvB,CACD,qBAAsB,CAClB,KAAK,iBAAmB,EAAM,KAAK,IAAI,KAAO,KAAK,IAAI,KAAK,YAAc,EAAG,EAAI,KAAK,aAAa,EACnG,KAAK,kBAAoB,CAAC,KAAK,cAAgB,KAAK,gBACvD,CAOD,yBAAyBC,EAAQC,EAAY,CACzC,OAAI,KAAK,mBAAqB,KAAK,kBAAkB,QAAO,IACpD,KAAK,kCACL,KAAK,yCAAwC,EAE7C,KAAK,wCACL,KAAK,+CAA8C,EAEnD,KAAK,yBACL,KAAK,gCAA+B,EAExCD,EAAO,UAAU,0BAA4BC,EAAY,KAAK,wBAAwB,EACtFD,EAAO,WAAW,yBAA2BC,EAAY,KAAK,iBAAiB,GAE/E,KAAK,oBAAsB,KAAK,mBAAmB,QAAO,GAC1DD,EAAO,WAAW,kBAAoBC,EAAY,KAAK,kBAAkB,EAEtE,IACV,CAOD,iBAAiBD,EAAQC,EAAY,CACjC,IAAIC,EACJ,OAAI,KAAK,iCACL,KAAK,eAAe,aAAa,aAAc,KAAK,oBAAoB,EAAG,KAAK,oBAAoB,EAAG,KAAK,oBAAoB,EAAG,KAAK,SAAUD,CAAU,EAC5JC,EAAqBzC,EAAQ,UAAU,KAAK,oBAAoB,IAGhE,KAAK,eAAe,aAAa,aAAc,KAAK,SAAS,EAAG,KAAK,SAAS,EAAG,KAAK,SAAS,EAAG,KAAK,SAAUwC,CAAU,EAC3HC,EAAqBzC,EAAQ,UAAU,KAAK,SAAS,GAEzD,KAAK,eAAe,aAAa,kBAAmByC,EAAmB,EAAGA,EAAmB,EAAGA,EAAmB,EAAG,KAAK,cAAeD,CAAU,EACpJ,KAAK,eAAe,aAAa,gBAAiB,KAAK,MAAO,KAAK,qBAAsB,KAAK,iBAAkB,KAAK,kBAAmBA,CAAU,EAC3I,IACV,CACD,6BAA6BD,EAAQG,EAAsB,CACvD,IAAID,EACJ,OAAI,KAAK,gCACLA,EAAqBzC,EAAQ,UAAU,KAAK,oBAAoB,EAGhEyC,EAAqBzC,EAAQ,UAAU,KAAK,SAAS,EAErD,KAAK,SAAU,EAAC,qBAChBuC,EAAO,UAAUG,EAAsB,CAACD,EAAmB,EAAG,CAACA,EAAmB,EAAG,CAACA,EAAmB,CAAC,EAG1GF,EAAO,UAAUG,EAAsBD,EAAmB,EAAGA,EAAmB,EAAGA,EAAmB,CAAC,EAEpG,IACV,CAID,SAAU,CACN,MAAM,QAAO,EACT,KAAK,oBACL,KAAK,mBAAmB,UAExB,KAAK,qBACL,KAAK,mBAAmB,UACxB,KAAK,mBAAqB,KAEjC,CAMD,aAAalC,EAAc,CACvB,MAAMoC,EAAS,KAAK,OAAO,UAAS,EAC9Bf,EAAO,KAAK,aAAe,OAAY,KAAK,WAAcrB,GAAc,MAAQ,EACtF,OAAOoC,EAAO,uBAAyBA,EAAO,gBAAkBf,EAAO,KAAK,OAAO,UAAW,EAAC,gBAAkB,EAAIA,CACxH,CAMD,aAAarB,EAAc,CACvB,MAAMoC,EAAS,KAAK,OAAO,UAAS,EAC9Bd,EAAO,KAAK,aAAe,OAAY,KAAK,WAActB,GAAc,MAAQ,IACtF,OAAOoC,EAAO,uBAAyBA,EAAO,gBAAkB,EAAId,CACvE,CAMD,4BAA4Be,EAASJ,EAAY,CAC7CI,EAAQ,YAAcJ,CAAU,EAAI,GACpCI,EAAQ,wBAA0BJ,CAAU,EAAI,QAAK,mBAAqB,KAAK,kBAAkB,WACjGI,EAAQ,kBAAoBJ,CAAU,EAAI,QAAK,oBAAsB,KAAK,mBAAmB,UAChG,CACL,CACAzB,EAAW,CACPE,EAAW,CACf,EAAGI,EAAU,UAAW,QAAS,IAAI,EACrCN,EAAW,CACPE,EAAW,CACf,EAAGI,EAAU,UAAW,aAAc,IAAI,EAC1CN,EAAW,CACPE,EAAW,CACf,EAAGI,EAAU,UAAW,mBAAoB,IAAI,EAChDN,EAAW,CACPE,EAAW,CACf,EAAGI,EAAU,UAAW,WAAY,MAAM,EAC1CN,EAAW,CACPE,EAAW,CACf,EAAGI,EAAU,UAAW,6BAA8B,IAAI,EAC1DN,EAAW,CACPE,EAAW,CACf,EAAGI,EAAU,UAAW,4BAA6B,IAAI,EACzDN,EAAW,CACPE,EAAW,CACf,EAAGI,EAAU,UAAW,+BAAgC,IAAI,EAC5DN,EAAW,CACP8B,EAAmB,uBAAuB,CAC9C,EAAGxB,EAAU,UAAW,qBAAsB,MAAM,EAEpDyB,EAAc,oBAAqBzB,CAAS,ECvarC,MAAM0B,CAA0B,CACnC,YAAYC,EAAOC,EAAW,CAC1B,KAAK,MAAQD,EACb,KAAK,UAAYC,CACpB,CAuBD,QAAQC,EAAM,CACV,IAAIC,EAAa,KAAK,MAClBC,EAAW,KAAK,UAChBlD,EACJ,GAAI,CAACgD,EAAK,WAAW,GAAG,EACpB,MAAM,IAAI,MAAM,0BAA0B,EAE9C,MAAMG,EAAQH,EAAK,MAAM,GAAG,EAG5B,GAFAG,EAAM,MAAK,EAEPA,EAAMA,EAAM,OAAS,CAAC,EAAE,SAAS,SAAS,EAAG,CAE7C,MAAMC,EADWD,EAAMA,EAAM,OAAS,CAAC,EAChB,MAAM,GAAG,EAChCA,EAAM,IAAG,EACTA,EAAM,KAAK,GAAGC,CAAK,CACtB,CACD,IAAIC,EAAmB,GACvB,UAAWC,KAAQH,EAAO,CACtB,MAAMI,EAAWD,IAAS,SAC1B,GAAIC,GAAY,CAACL,EAAS,UACtB,MAAM,IAAI,MAAM,QAAQF,CAAI,aAAa,EAK7C,GAHIE,EAAS,uBACTG,EAAmB,IAEnBH,EAAS,WAAa,CAACK,EACvBL,EAAWA,EAAS,kBAGpBA,EAAWA,EAASI,CAAI,EACpB,CAACJ,EACD,MAAM,IAAI,MAAM,QAAQF,CAAI,aAAa,EAGjD,GAAI,CAACK,EAAkB,CACnB,GAAIJ,IAAe,OACf,MAAM,IAAI,MAAM,QAAQD,CAAI,aAAa,EAExCO,IACDN,EAAaA,IAAaK,CAAI,EAErC,EACGJ,EAAS,YAAcK,KACvBvD,EAASiD,EAEhB,CACD,MAAO,CACH,OAAQjD,EACR,KAAMkD,CAClB,CACK,CACL,CC9EA,MAAMM,EAAY,CACd,OAAQ,CACJ,KAAM,SACN,IAAMC,GAAUA,EAAM,OACtB,UAAYA,GAAUA,EAAM,IAAKC,GAASA,EAAK,qBAAqB,EACpE,gBAAiB,CAAC,IAAM,QAAQ,CACnC,EACD,UAAW,CACP,WAAY,GACZ,YAAa,CACT,KAAM,UACN,IAAMA,GAASA,EAAK,uBAAuB,SAC3C,IAAK,CAAC7D,EAAO6D,IAASA,EAAK,uBAAuB,SAAS,SAAS7D,CAAK,EACzE,UAAY6D,GAASA,EAAK,sBAC1B,gBAAiB,CAAC,IAAM,UAAU,CACrC,EACD,SAAU,CACN,KAAM,aACN,IAAMA,GAASA,EAAK,uBAAuB,mBAC3C,IAAK,CAAC7D,EAAO6D,IAASA,EAAK,uBAAuB,oBAAoB,SAAS7D,CAAK,EACpF,UAAY6D,GAASA,EAAK,sBAC1B,gBAAiB,CAAC,IAAM,oBAAoB,CAC/C,EACD,MAAO,CACH,KAAM,UACN,IAAMA,GAASA,EAAK,uBAAuB,QAC3C,IAAK,CAAC7D,EAAO6D,IAASA,EAAK,uBAAuB,QAAQ,SAAS7D,CAAK,EACxE,UAAY6D,GAASA,EAAK,sBAC1B,gBAAiB,CAAC,IAAM,SAAS,CACpC,EACD,QAAS,CACL,OAAQ,CACJ,KAAM,SACN,IAAMA,GAASA,EAAK,iBACpB,UAAYA,GAASA,EAAK,sBAC1B,gBAAiB,CAAC,IAAM,WAAW,CACtC,EACD,UAAW,CACP,WAAY,GACZ,KAAM,SACN,IAAK,CAACA,EAAMC,IAAWA,IAAU,OAAYD,EAAK,0BAA0B,CAAC,EAAE,oBAAoB,UAAUC,CAAK,EAAE,UAAY,OAEhI,UAAYD,GAASA,EAAK,sBAC1B,gBAAiB,CAAC,IAAM,WAAW,CACtC,EACD,KAAM,WACN,IAAK,CAACA,EAAMC,IAAU,CAAC,CAAC,EAExB,UAAYD,GAASA,EAAK,sBAC1B,gBAAiB,CAAC,IAAM,WAAW,CACtC,EAED,OAAQ,CACJ,KAAM,SACN,IAAMA,GAAS9D,EAAO,QAAQ8D,EAAK,uBAAuB,QAASA,EAAK,uBAAuB,mBAAoBA,EAAK,uBAAuB,QAAQ,EACvJ,UAAYA,GAASA,EAAK,sBAC1B,WAAY,EACf,EACD,aAAc,CACV,KAAM,SACN,IAAMA,GAAS,CACX,MAAMpD,EAASV,EAAO,WAEtB,IAAIgE,EAAWF,EAAK,OACpB,KAAOE,GAAYA,EAAS,QACxBA,EAAWA,EAAS,OAExB,MAAMC,EAAcH,EAAK,uBAAuB,SAAS,UAAYA,EAAK,uBAAuB,oBAAoB,UAAYA,EAAK,uBAAuB,QAAQ,SACrK,GAAIE,EAAU,CAGV,MAAME,EAAaF,EAAS,uBAAuB,mBAAmB,EAAI,EAAE,SACxEE,GACAJ,EAAK,uBAAuB,mBAAmBG,CAAW,GAAG,cAAcC,EAAYxD,CAAM,CAEpG,MACQoD,EAAK,uBACVpD,EAAO,SAASoD,EAAK,sBAAsB,mBAAmBG,CAAW,CAAC,EAE9E,OAAOvD,CACV,EACD,UAAYoD,GAASA,EAAK,sBAC1B,WAAY,EACf,EACD,WAAY,CACR,eAAgB,CACZ,WAAY,CACR,KAAM,SACN,IAAMA,GACKA,EAAK,uBAAuB,YAAaK,GAAUA,aAAiB5C,EAAW,EAAI,EAAE,CAAC,GAAG,UAEpG,UAAYuC,GAASA,EAAK,uBAAuB,YAAaK,GAAUA,aAAiB5C,EAAW,EAAI,EAAE,CAAC,EAC3G,IAAK,CAACtB,EAAO6D,IAAS,CAClB,GAAIA,EAAK,sBAAuB,CAC5B,MAAMM,EAAQN,EAAK,sBAAsB,YAAaK,GAAUA,aAAiB5C,EAAW,EAAI,EAAE,CAAC,EAC/F6C,IACAA,EAAM,UAAYnE,EAEzB,CACJ,CACJ,EACD,MAAO,CACH,KAAM,SACN,IAAM6D,GACKA,EAAK,uBAAuB,YAAaK,GAAUA,aAAiB5C,EAAW,EAAI,EAAE,CAAC,GAAG,QAEpG,UAAYuC,GAASA,EAAK,uBAAuB,YAAaK,GAAUA,aAAiB5C,EAAW,EAAI,EAAE,CAAC,EAC3G,IAAK,CAACtB,EAAO6D,IAAS,CAClB,GAAIA,EAAK,sBAAuB,CAC5B,MAAMM,EAAQN,EAAK,sBAAsB,YAAaK,GAAUA,aAAiB5C,EAAW,EAAI,EAAE,CAAC,EAC/F6C,IACAA,EAAM,QAAUnE,EAEvB,CACJ,CACJ,CACJ,CACJ,CACJ,CACL,EACMoE,EAAiB,CACnB,OAAQ,CACJ,KAAM,SACN,IAAMC,GAAeA,EAAW,OAChC,UAAYA,GAAeA,EAAW,IAAKC,GAAcA,EAAU,sBAAsB,EACzF,gBAAiB,CAAC,IAAM,QAAQ,CACnC,EACD,UAAW,CAAE,CACjB,EACMC,EAAa,CACf,OAAQ,CACJ,KAAM,SACN,IAAMC,GAAWA,EAAO,OACxB,UAAYA,GAAWA,EAAO,IAAKC,GAASA,EAAK,WAAW,CAAC,EAAE,eAAe,iBAAiB,EAC/F,gBAAiB,CAAC,IAAM,QAAQ,CACnC,EACD,UAAW,CAAE,CACjB,EACMC,EAAc,CAChB,UAAW,CACP,WAAY,GACZ,aAAc,CACV,KAAM,CACF,gBAAiB,EACjB,KAAM,UACN,IAAMC,GAAW,IAAIC,EAAQD,EAAO,gBAAgB,WAAa,EAAGA,EAAO,gBAAgB,YAAc,CAAC,EAC1G,IAAK,CAAC3E,EAAO2E,IAAW,CAChBA,EAAO,iBACPA,EAAO,eAAe,UAAY3E,EAAM,EACxC2E,EAAO,eAAe,WAAa3E,EAAM,EAEhD,EACD,UAAY2E,GAAWA,EACvB,gBAAiB,CAAC,IAAM,YAAa,IAAM,YAAY,CAC1D,EACD,KAAM,CACF,gBAAiB,EACjB,KAAM,UACN,IAAMA,GAAW,IAAIC,EAAQD,EAAO,gBAAgB,aAAe,EAAGA,EAAO,gBAAgB,UAAY,CAAC,EAC1G,IAAK,CAAC3E,EAAO2E,IAAW,CAChBA,EAAO,iBACPA,EAAO,eAAe,YAAc3E,EAAM,EAC1C2E,EAAO,eAAe,SAAW3E,EAAM,EAE9C,EACD,UAAY2E,GAAWA,EACvB,gBAAiB,CAAC,IAAM,cAAe,IAAM,UAAU,CAC1D,EACD,KAAM,CACF,KAAM,SACN,IAAMA,GAAWA,EAAO,gBAAgB,KACxC,IAAK,CAAC3E,EAAO2E,IAAW,CAChBA,EAAO,iBACPA,EAAO,eAAe,KAAO3E,EAEpC,EACD,UAAY2E,GAAWA,EACvB,gBAAiB,CAAC,IAAM,MAAM,CACjC,EACD,MAAO,CACH,KAAM,SACN,IAAMA,GAAWA,EAAO,gBAAgB,KACxC,IAAK,CAAC3E,EAAO2E,IAAW,CAChBA,EAAO,iBACPA,EAAO,eAAe,KAAO3E,EAEpC,EACD,UAAY2E,GAAWA,EACvB,gBAAiB,CAAC,IAAM,MAAM,CACjC,CACJ,EACD,YAAa,CACT,YAAa,CACT,KAAM,SACN,IAAMA,GAAWA,EAAO,gBAAgB,YAAY,eAAeA,EAAO,cAAc,EACxF,UAAYA,GAAWA,EACvB,gBAAiB,CAAC,IAAM,aAAa,EACrC,WAAY,EACf,EACD,KAAM,CACF,KAAM,SACN,IAAMA,GAAWA,EAAO,gBAAgB,IACxC,IAAK,CAAC3E,EAAO2E,IAAW,CAChBA,EAAO,iBACPA,EAAO,eAAe,IAAM3E,EAEnC,EACD,UAAY2E,GAAWA,EACvB,gBAAiB,CAAC,IAAM,KAAK,CAChC,EACD,KAAM,CACF,KAAM,SACN,IAAMA,GAAWA,EAAO,gBAAgB,KACxC,IAAK,CAAC3E,EAAO2E,IAAW,CAChBA,EAAO,iBACPA,EAAO,eAAe,KAAO3E,EAEpC,EACD,UAAY2E,GAAWA,EACvB,gBAAiB,CAAC,IAAM,MAAM,CACjC,EACD,MAAO,CACH,KAAM,SACN,IAAMA,GAAWA,EAAO,gBAAgB,KACxC,IAAK,CAAC3E,EAAO2E,IAAW,CAChBA,EAAO,iBACPA,EAAO,eAAe,KAAO3E,EAEpC,EACD,UAAY2E,GAAWA,EACvB,gBAAiB,CAAC,IAAM,MAAM,CACjC,CACJ,CACJ,CACL,EACME,EAAgB,CAClB,UAAW,CACP,WAAY,GACZ,eAAgB,CACZ,KAAM,SACN,IAAK,CAACC,EAAUhB,EAAOiB,IAAYC,EAAaF,EAAUhB,EAAOiB,CAAO,EAAE,cAC1E,IAAK,CAAC/E,EAAO8E,EAAUhB,EAAOiB,IAAYC,EAAaF,EAAUhB,EAAOiB,CAAO,EAAE,cAAc,SAAS/E,CAAK,EAC7G,UAAW,CAAC8E,EAAUhB,EAAOiB,IAAYC,EAAaF,EAAUhB,EAAOiB,CAAO,EAC9E,gBAAiB,CAAC,IAAM,eAAe,CAC1C,EACD,gBAAiB,CACb,WAAY,CACR,sBAAuBE,EAAoB,iBAAiB,CAC/D,CACJ,EACD,cAAe,CACX,MAAO,CACH,KAAM,SACN,IAAK,CAACH,EAAUhB,EAAOiB,IAAYG,EAAYJ,EAAUC,EAAS,aAAa,GAAG,MAClF,IAAK,CAAC/E,EAAO8E,EAAUhB,EAAOiB,IAAY,CACtC,MAAMxD,EAAU2D,EAAYJ,EAAUC,EAAS,aAAa,EACxDxD,IACAA,EAAQ,MAAQvB,EAEvB,EACD,UAAW,CAAC8E,EAAUhB,EAAOiB,IAAYC,EAAaF,EAAUhB,EAAOiB,CAAO,EAC9E,gBAAiB,CAAC,IAAM,OAAO,CAClC,EACD,WAAY,CACR,sBAAuBE,EAAoB,aAAa,CAC3D,CACJ,EACD,iBAAkB,CACd,SAAU,CACN,KAAM,SACN,IAAK,CAACH,EAAUhB,EAAOiB,IAAYC,EAAaF,EAAUhB,EAAOiB,CAAO,EAAE,uBAC1E,IAAK,CAAC/E,EAAO8E,EAAUhB,EAAOiB,IAAY,CACtC,MAAMI,EAAMH,EAAaF,EAAUhB,EAAOiB,CAAO,EAC7CI,IACAA,EAAI,uBAAyBnF,EAEpC,EACD,UAAW,CAAC8E,EAAUhB,EAAOiB,IAAYC,EAAaF,EAAUhB,EAAOiB,CAAO,EAC9E,gBAAiB,CAAC,IAAM,wBAAwB,CACnD,EACD,WAAY,CACR,sBAAuBE,EAAoB,gBAAgB,CAC9D,CACJ,EACD,qBAAsB,CAClB,gBAAiB,CACb,KAAM,SACN,IAAK,CAACH,EAAUhB,EAAOiB,IAAY,CAC/B,MAAMI,EAAMH,EAAaF,EAAUhB,EAAOiB,CAAO,EACjD,OAAOK,EAAO,WAAWD,EAAI,YAAaA,EAAI,KAAK,CACtD,EACD,IAAK,CAACnF,EAAO8E,EAAUhB,EAAOiB,IAAY,CACtC,MAAMI,EAAMH,EAAaF,EAAUhB,EAAOiB,CAAO,EACjDI,EAAI,YAAY,IAAInF,EAAM,EAAGA,EAAM,EAAGA,EAAM,CAAC,EAC7CmF,EAAI,MAAQnF,EAAM,CACrB,EACD,UAAW,CAAC8E,EAAUhB,EAAOiB,IAAYC,EAAaF,EAAUhB,EAAOiB,CAAO,EAE9E,gBAAiB,CAAC,IAAM,cAAe,IAAM,OAAO,CACvD,EACD,iBAAkB,CACd,WAAY,CACR,sBAAuBE,EAAoB,eAAe,CAC7D,CACJ,EACD,eAAgB,CACZ,KAAM,SACN,IAAK,CAACH,EAAUhB,EAAOiB,IAAYC,EAAaF,EAAUhB,EAAOiB,CAAO,EAAE,SAC1E,IAAK,CAAC/E,EAAO8E,EAAUhB,EAAOiB,IAAY,CACtC,MAAMI,EAAMH,EAAaF,EAAUhB,EAAOiB,CAAO,EAC7CI,IACAA,EAAI,SAAWnF,EAEtB,EACD,UAAW,CAAC8E,EAAUhB,EAAOiB,IAAYC,EAAaF,EAAUhB,EAAOiB,CAAO,EAC9E,gBAAiB,CAAC,IAAM,UAAU,CACrC,EACD,gBAAiB,CACb,KAAM,SACN,IAAK,CAACD,EAAUhB,EAAOiB,IAAYC,EAAaF,EAAUhB,EAAOiB,CAAO,EAAE,UAC1E,IAAK,CAAC/E,EAAO8E,EAAUhB,EAAOiB,IAAY,CACtC,MAAMI,EAAMH,EAAaF,EAAUhB,EAAOiB,CAAO,EAC7CI,IACAA,EAAI,UAAYnF,EAEvB,EACD,UAAW,CAAC8E,EAAUhB,EAAOiB,IAAYC,EAAaF,EAAUhB,EAAOiB,CAAO,EAC9E,gBAAiB,CAAC,IAAM,WAAW,CACtC,EACD,yBAA0B,CACtB,WAAY,CACR,sBAAuBE,EAAoB,iBAAiB,CAC/D,CACJ,CACJ,EACD,WAAY,CACR,yBAA0B,CACtB,mBAAoB,CAChB,KAAM,SACN,IAAK,CAACH,EAAUhB,EAAOiB,IAAYC,EAAaF,EAAUhB,EAAOiB,CAAO,EAAE,WAAW,UACrF,IAAK,CAAC/E,EAAO8E,EAAUhB,EAAOiB,IAAY,CACtCC,EAAaF,EAAUhB,EAAOiB,CAAO,EAAE,WAAW,UAAY/E,CACjE,EACD,UAAW,CAAC8E,EAAUhB,EAAOiB,IAAYC,EAAaF,EAAUhB,EAAOiB,CAAO,EAC9E,gBAAiB,CAAC,IAAM,sBAAsB,CACjD,EACD,mBAAoB,CAChB,KAAM,SACN,IAAK,CAACD,EAAUhB,EAAOiB,IAAYC,EAAaF,EAAUhB,EAAOiB,CAAO,EAAE,WAAW,MACrF,IAAK,CAAC/E,EAAO8E,EAAUhB,EAAOiB,IAAY,CACtCC,EAAaF,EAAUhB,EAAOiB,CAAO,EAAE,WAAW,MAAQ/E,CAC7D,EACD,UAAW,CAAC8E,EAAUhB,EAAOiB,IAAYC,EAAaF,EAAUhB,EAAOiB,CAAO,EAC9E,gBAAiB,CAAC,IAAM,kBAAkB,CAC7C,EACD,kBAAmB,CACf,WAAY,CACR,sBAAuBE,EAAoB,aAAc,SAAS,CACrE,CACJ,CACJ,EACD,wBAAyB,CACrB,gBAAiB,CACb,KAAM,SACN,IAAK,CAACH,EAAUhB,EAAOiB,IAAYC,EAAaF,EAAUhB,EAAOiB,CAAO,EAAE,UAAU,UACpF,IAAK,CAAC/E,EAAO8E,EAAUhB,EAAOiB,IAAY,CACtCC,EAAaF,EAAUhB,EAAOiB,CAAO,EAAE,UAAU,UAAY/E,CAChE,EACD,UAAW,CAAC8E,EAAUhB,EAAOiB,IAAYC,EAAaF,EAAUhB,EAAOiB,CAAO,EAC9E,gBAAiB,CAAC,IAAM,qBAAqB,CAChD,EACD,yBAA0B,CACtB,KAAM,SACN,IAAK,CAACD,EAAUhB,EAAOiB,IAAYC,EAAaF,EAAUhB,EAAOiB,CAAO,EAAE,UAAU,UACpF,IAAK,CAAC/E,EAAO8E,EAAUhB,EAAOiB,IAAY,CACtCC,EAAaF,EAAUhB,EAAOiB,CAAO,EAAE,UAAU,UAAY/E,CAChE,EACD,UAAW,CAAC8E,EAAUhB,EAAOiB,IAAYC,EAAaF,EAAUhB,EAAOiB,CAAO,EAC9E,gBAAiB,CAAC,IAAM,qBAAqB,CAChD,EACD,iBAAkB,CACd,WAAY,CACR,sBAAuBE,EAAoB,YAAa,SAAS,CACpE,CACJ,EACD,uBAAwB,CACpB,MAAO,CACH,KAAM,SACN,IAAK,CAACH,EAAUhB,EAAOiB,IAAYC,EAAaF,EAAUhB,EAAOiB,CAAO,EAAE,UAAU,aAAa,MACjG,UAAWC,EACX,IAAK,CAAChF,EAAO8E,EAAUhB,EAAOiB,IAAaC,EAAaF,EAAUhB,EAAOiB,CAAO,EAAE,UAAU,YAAY,MAAQ/E,CACnH,EACD,WAAY,CACR,sBAAuBiF,EAAoB,YAAa,aAAa,CACxE,CACJ,EACD,0BAA2B,CACvB,WAAY,CACR,sBAAuBA,EAAoB,YAAa,kBAAkB,CAC7E,CACJ,CACJ,EACD,yBAA0B,CACtB,WAAY,CACR,KAAM,SACN,IAAK,CAACH,EAAUhB,EAAOiB,IAAYC,EAAaF,EAAUhB,EAAOiB,CAAO,EAAE,WAAW,WACrF,UAAWC,EACX,IAAK,CAAChF,EAAO8E,EAAUhB,EAAOiB,IAAaC,EAAaF,EAAUhB,EAAOiB,CAAO,EAAE,WAAW,WAAa/E,CAC7G,CACJ,EACD,gCAAiC,CAC7B,iBAAkB,CACd,KAAM,SACN,IAAK,CAAC8E,EAAUhB,EAAOiB,IAAYC,EAAaF,EAAUhB,EAAOiB,CAAO,EAAE,kBAC1E,UAAWC,EACX,IAAK,CAAChF,EAAO8E,EAAUhB,EAAOiB,IAAaC,EAAaF,EAAUhB,EAAOiB,CAAO,EAAE,kBAAoB/E,CACzG,CACJ,EACD,kBAAmB,CACf,IAAK,CACD,KAAM,SACN,IAAK,CAAC8E,EAAUhB,EAAOiB,IAAYC,EAAaF,EAAUhB,EAAOiB,CAAO,EAAE,kBAC1E,UAAWC,EACX,IAAK,CAAChF,EAAO8E,EAAUhB,EAAOiB,IAAaC,EAAaF,EAAUhB,EAAOiB,CAAO,EAAE,kBAAoB/E,CACzG,CACJ,EACD,0BAA2B,CACvB,kBAAmB,CACf,KAAM,SACN,IAAK,CAAC8E,EAAUhB,EAAOiB,IAAYC,EAAaF,EAAUhB,EAAOiB,CAAO,EAAE,YAAY,UACtF,UAAWC,EACX,IAAK,CAAChF,EAAO8E,EAAUhB,EAAOiB,IAAaC,EAAaF,EAAUhB,EAAOiB,CAAO,EAAE,YAAY,UAAY/E,CAC7G,EACD,eAAgB,CACZ,KAAM,SACN,IAAK,CAAC8E,EAAUhB,EAAOiB,IAAYC,EAAaF,EAAUhB,EAAOiB,CAAO,EAAE,YAAY,kBACtF,UAAWC,EACX,IAAK,CAAChF,EAAO8E,EAAUhB,EAAOiB,IAAaC,EAAaF,EAAUhB,EAAOiB,CAAO,EAAE,YAAY,kBAAoB/E,CACrH,EACD,mBAAoB,CAChB,WAAY,CACR,sBAAuBiF,EAAoB,cAAe,SAAS,CACtE,CACJ,EACD,4BAA6B,CACzB,KAAM,SACN,IAAK,CAACH,EAAUhB,EAAOiB,IAAYC,EAAaF,EAAUhB,EAAOiB,CAAO,EAAE,YAAY,iBACtF,UAAWC,EACX,IAAK,CAAChF,EAAO8E,EAAUhB,EAAOiB,IAAaC,EAAaF,EAAUhB,EAAOiB,CAAO,EAAE,YAAY,iBAAmB/E,CACpH,EACD,4BAA6B,CACzB,KAAM,SACN,IAAK,CAAC8E,EAAUhB,EAAOiB,IAAYC,EAAaF,EAAUhB,EAAOiB,CAAO,EAAE,YAAY,iBACtF,UAAWC,EACX,IAAK,CAAChF,EAAO8E,EAAUhB,EAAOiB,IAAaC,EAAaF,EAAUhB,EAAOiB,CAAO,EAAE,YAAY,iBAAmB/E,CACpH,EACD,4BAA6B,CACzB,WAAY,CACR,sBAAuBiF,EAAoB,cAAe,kBAAkB,CAC/E,CACJ,CACJ,EACD,oBAAqB,CACjB,iBAAkB,CACd,KAAM,SACN,IAAK,CAACH,EAAUhB,EAAOiB,IAAYC,EAAaF,EAAUhB,EAAOiB,CAAO,EAAE,MAAM,MAChF,UAAWC,EACX,IAAK,CAAChF,EAAO8E,EAAUhB,EAAOiB,IAAYC,EAAaF,EAAUhB,EAAOiB,CAAO,EAAE,MAAM,MAAM,SAAS/E,CAAK,CAC9G,EACD,kBAAmB,CACf,WAAY,CACR,sBAAuBiF,EAAoB,QAAS,SAAS,CAChE,CACJ,EACD,qBAAsB,CAClB,KAAM,SACN,IAAK,CAACH,EAAUhB,EAAOiB,IAAYC,EAAaF,EAAUhB,EAAOiB,CAAO,EAAE,MAAM,UAChF,UAAWC,EACX,IAAK,CAAChF,EAAO8E,EAAUhB,EAAOiB,IAAaC,EAAaF,EAAUhB,EAAOiB,CAAO,EAAE,MAAM,UAAY/E,CACvG,EACD,sBAAuB,CACnB,WAAY,CACR,sBAAuBiF,EAAoB,QAAS,kBAAkB,CACzE,CACJ,CACJ,EACD,uBAAwB,CACpB,eAAgB,CACZ,KAAM,SACN,IAAK,CAACH,EAAUhB,EAAOiB,IAAYC,EAAaF,EAAUhB,EAAOiB,CAAO,EAAE,iBAC1E,UAAWC,EACX,IAAK,CAAChF,EAAO8E,EAAUhB,EAAOiB,IAAaC,EAAaF,EAAUhB,EAAOiB,CAAO,EAAE,iBAAmB/E,EACrG,gBAAiB,CAAC,IAAM,kBAAkB,CAC7C,EACD,oBAAqB,CACjB,KAAM,SACN,IAAK,CAAC8E,EAAUhB,EAAOiB,IAAYC,EAAaF,EAAUhB,EAAOiB,CAAO,EAAE,yBAC1E,UAAWC,EACX,IAAK,CAAChF,EAAO8E,EAAUhB,EAAOiB,IAAYC,EAAaF,EAAUhB,EAAOiB,CAAO,EAAE,yBAAyB,SAAS/E,CAAK,EACxH,gBAAiB,CAAC,IAAM,0BAA0B,CACrD,EACD,gBAAiB,CACb,WAAY,CACR,sBAAuBiF,EAAoB,4BAA4B,CAC1E,CACJ,EACD,qBAAsB,CAClB,WAAY,CACR,sBAAuBA,EAAoB,oBAAoB,CAClE,CACJ,CACJ,EACD,2BAA4B,CACxB,mBAAoB,CAChB,KAAM,SACN,IAAK,CAACH,EAAUhB,EAAOiB,IAAYC,EAAaF,EAAUhB,EAAOiB,CAAO,EAAE,WAAW,oBACrF,UAAWC,EACX,IAAK,CAAChF,EAAO8E,EAAUhB,EAAOiB,IAAaC,EAAaF,EAAUhB,EAAOiB,CAAO,EAAE,WAAW,oBAAsB/E,EACnH,gBAAiB,CAAC,IAAM,gCAAgC,CAC3D,EACD,oBAAqB,CACjB,WAAY,CACR,sBAAuBiF,EAAoB,aAAc,4BAA4B,CACxF,CACJ,CACJ,EACD,mCAAoC,CAChC,0BAA2B,CACvB,KAAM,SACN,IAAK,CAACH,EAAUhB,EAAOiB,IAAYC,EAAaF,EAAUhB,EAAOiB,CAAO,EAAE,WAAW,sBACrF,UAAWC,EACX,IAAK,CAAChF,EAAO8E,EAAUhB,EAAOiB,IAAaC,EAAaF,EAAUhB,EAAOiB,CAAO,EAAE,WAAW,sBAAwB/E,CACxH,EACD,2BAA4B,CACxB,WAAY,CACR,sBAAuBiF,EAAoB,aAAc,8BAA8B,CAC1F,CACJ,EACD,+BAAgC,CAC5B,KAAM,SACN,IAAK,CAACH,EAAUhB,EAAOiB,IAAYC,EAAaF,EAAUhB,EAAOiB,CAAO,EAAE,WAAW,kBACrF,UAAWC,EACX,IAAK,CAAChF,EAAO8E,EAAUhB,EAAOiB,IAAY/E,GAASgF,EAAaF,EAAUhB,EAAOiB,CAAO,EAAE,WAAW,mBAAmB,SAAS/E,CAAK,CACzI,EACD,gCAAiC,CAC7B,WAAY,CACR,sBAAuBiF,EAAoB,aAAc,0BAA0B,CACtF,CACJ,CACJ,EACD,qBAAsB,CAClB,iBAAkB,CACd,KAAM,SACN,IAAK,CAACH,EAAUhB,EAAOiB,IAAYC,EAAaF,EAAUhB,EAAOiB,CAAO,EAAE,WAAW,UACrF,UAAWC,EACX,IAAK,CAAChF,EAAO8E,EAAUhB,EAAOiB,IAAYC,EAAaF,EAAUhB,EAAOiB,CAAO,EAAE,WAAW,UAAU,SAAS/E,CAAK,CACvH,EACD,oBAAqB,CACjB,KAAM,SACN,IAAK,CAAC8E,EAAUhB,EAAOiB,IAAYC,EAAaF,EAAUhB,EAAOiB,CAAO,EAAE,WAAW,oBACrF,UAAWC,EACX,IAAK,CAAChF,EAAO8E,EAAUhB,EAAOiB,IAAaC,EAAaF,EAAUhB,EAAOiB,CAAO,EAAE,WAAW,oBAAsB/E,CACtH,EACD,gBAAiB,CACb,KAAM,SACN,IAAK,CAAC8E,EAAUhB,EAAOiB,IAAYC,EAAaF,EAAUhB,EAAOiB,CAAO,EAAE,WAAW,iBACrF,UAAWC,EACX,IAAK,CAAChF,EAAO8E,EAAUhB,EAAOiB,IAAaC,EAAaF,EAAUhB,EAAOiB,CAAO,EAAE,WAAW,iBAAmB/E,CACnH,EACD,iBAAkB,CACd,WAAY,CACR,sBAAuBiF,EAAoB,aAAc,kBAAkB,CAC9E,CACJ,CACJ,CACJ,CACJ,CACL,EACMI,EAAiB,CACnB,oBAAqB,CACjB,OAAQ,CACJ,OAAQ,CACJ,KAAM,SACN,IAAMC,GAAWA,EAAO,OACxB,UAAYA,GAAWA,EAAO,IAAKnB,GAAUA,EAAM,aAAa,EAChE,gBAAiB,CAAEoB,GAAY,QAAQ,CAC1C,EACD,UAAW,CACP,WAAY,GACZ,MAAO,CACH,KAAM,SACN,IAAMpB,GAAUA,EAAM,eAAe,QACrC,IAAK,CAACnE,EAAOmE,IAAUA,EAAM,eAAe,QAAQ,SAASnE,CAAK,EAClE,UAAYmE,GAAUA,EAAM,cAC5B,gBAAiB,CAAEqB,GAAW,SAAS,CAC1C,EACD,UAAW,CACP,KAAM,SACN,IAAMrB,GAAUA,EAAM,eAAe,UACrC,IAAK,CAACnE,EAAOmE,IAAWA,EAAM,cAAiBA,EAAM,cAAc,UAAYnE,EAAS,OACxF,UAAYmE,GAAUA,EAAM,cAC5B,gBAAiB,CAAEqB,GAAW,WAAW,CAC5C,EACD,MAAO,CACH,KAAM,SACN,IAAMrB,GAAUA,EAAM,eAAe,MACrC,IAAK,CAACnE,EAAOmE,IAAWA,EAAM,cAAiBA,EAAM,cAAc,MAAQnE,EAAS,OACpF,UAAYmE,GAAUA,EAAM,cAC5B,gBAAiB,CAAEqB,GAAW,OAAO,CACxC,EACD,KAAM,CACF,eAAgB,CACZ,KAAM,SACN,IAAMrB,GAAUA,EAAM,eAAe,WACrC,IAAK,CAACnE,EAAOmE,IAAWA,EAAM,cAAiBA,EAAM,cAAc,WAAanE,EAAS,OACzF,UAAYmE,GAAUA,EAAM,cAC5B,gBAAiB,CAAEqB,GAAW,gBAAgB,CACjD,EACD,eAAgB,CACZ,KAAM,SACN,IAAMrB,GAAUA,EAAM,eAAe,MACrC,IAAK,CAACnE,EAAOmE,IAAWA,EAAM,cAAiBA,EAAM,cAAc,MAAQnE,EAAS,OACpF,UAAYmE,GAAUA,EAAM,cAC5B,gBAAiB,CAAEqB,GAAW,gBAAgB,CACjD,CACJ,CACJ,CACJ,CACJ,EACD,eAAgB,CACZ,OAAQ,CACJ,OAAQ,CACJ,KAAM,SACN,IAAMF,GAAWA,EAAO,OACxB,UAAYA,GAAWA,EAAO,IAAKnB,GAAUA,EAAM,aAAa,EAChE,gBAAiB,CAAEoB,GAAY,QAAQ,CAC1C,CACJ,CACJ,EACD,uBAAwB,CACpB,OAAQ,CACJ,OAAQ,CACJ,KAAM,SACN,IAAMD,GAAWA,EAAO,OACxB,UAAYA,GAAWA,EAAO,IAAKnB,GAAUA,EAAM,eAAe,EAClE,gBAAiB,CAAEoB,GAAY,QAAQ,CAC1C,EACD,UAAW,CACP,WAAY,GACZ,UAAW,CACP,KAAM,SACN,IAAMpB,GAAUA,EAAM,iBAAiB,MACvC,IAAK,CAACnE,EAAOmE,IAAU,CACfA,EAAM,kBACNA,EAAM,gBAAgB,MAAQnE,EACrC,EACD,UAAYmE,GAAUA,EAAM,eAC/B,EACD,SAAU,CACN,KAAM,aACN,IAAMA,GAAUA,EAAM,iBAAmBsB,EAAW,mBAAmBtB,EAAM,iBAAiB,4BAA4B,EAC1H,IAAK,CAACnE,EAAOmE,IAAU,CACdA,EAAM,kBAGNA,EAAM,gBAAgB,SAAQ,GAAI,uBACnCnE,EAAQyF,EAAW,QAAQzF,CAAK,GAEpCD,EAAO,oBAAoBC,EAAOmE,EAAM,gBAAgB,2BAA0B,CAAE,EACvF,EACD,UAAYA,GAAUA,EAAM,eAC/B,CACJ,CACJ,CACJ,CACL,EACA,SAASe,EAAYJ,EAAUC,EAASW,EAAaC,EAAiB,CAClE,MAAMC,EAAkBZ,EAAaF,CAAiB,EACtD,OAAOa,EAAkBC,EAAgBF,CAAW,EAAEC,CAAe,EAAIC,EAAgBF,CAAW,CACxG,CACA,SAASV,EAAaF,EAAUe,EAAQd,EAAS,CAC7C,OAAOD,EAAS,QAAQC,GAAS,UAAYnF,EAAU,yBAAyB,GAAG,eACvF,CACA,SAASqF,EAAoBS,EAAaC,EAAiB,CACvD,MAAO,CACH,OAAQ,CACJ,gBAAiB,EAEjB,KAAM,UACN,IAAK,CAACb,EAAUe,EAAQd,IAAY,CAChC,MAAMxD,EAAU2D,EAAYJ,EAAUC,EAASW,EAAaC,CAAe,EAC3E,OAAO,IAAIf,EAAQrD,GAAS,QAASA,GAAS,OAAO,CACxD,EACD,UAAWyD,EACX,IAAK,CAAChF,EAAO8E,EAAUe,EAAQd,IAAY,CACvC,MAAMxD,EAAU2D,EAAYJ,EAAUC,EAASW,EAAaC,CAAe,EAC1EpE,EAAQ,QAAUvB,EAAM,EAAKuB,EAAQ,QAAUvB,EAAM,CACzD,EACD,gBAAiB,CACb,IAAM,GAAG0F,CAAW,GAAGC,EAAkB,IAAMA,EAAkB,EAAE,WACnE,IAAM,GAAGD,CAAW,GAAGC,EAAkB,IAAMA,EAAkB,EAAE,UACtE,CACJ,EACD,SAAU,CACN,KAAM,SACN,IAAK,CAACb,EAAUe,EAAQd,IAAYG,EAAYJ,EAAUC,EAASW,EAAaC,CAAe,GAAG,KAClG,UAAWX,EACX,IAAK,CAAChF,EAAO8E,EAAUe,EAAQd,IAAaG,EAAYJ,EAAUC,EAASW,EAAaC,CAAe,EAAE,KAAO3F,EAChH,gBAAiB,CAAC,IAAM,GAAG0F,CAAW,GAAGC,EAAkB,IAAMA,EAAkB,EAAE,OAAO,CAC/F,EACD,MAAO,CACH,gBAAiB,EACjB,KAAM,UACN,IAAK,CAACb,EAAUe,EAAQd,IAAY,CAChC,MAAMxD,EAAU2D,EAAYJ,EAAUC,EAASW,EAAaC,CAAe,EAC3E,OAAO,IAAIf,EAAQrD,GAAS,OAAQA,GAAS,MAAM,CACtD,EACD,UAAWyD,EACX,IAAK,CAAChF,EAAO8E,EAAUhB,EAAOiB,IAAY,CACtC,MAAMxD,EAAU2D,EAAYJ,EAAUC,EAASW,EAAaC,CAAe,EAC1EpE,EAAQ,OAASvB,EAAM,EAAKuB,EAAQ,OAASvB,EAAM,CACvD,EACD,gBAAiB,CACb,IAAM,GAAG0F,CAAW,GAAGC,EAAkB,IAAMA,EAAkB,EAAE,UACnE,IAAM,GAAGD,CAAW,GAAGC,EAAkB,IAAMA,EAAkB,EAAE,SACtE,CACJ,CACT,CACA,CACA,MAAMG,EAAqB,CACvB,QAASpB,EACT,MAAOf,EACP,UAAWkB,EACX,WAAYQ,EACZ,WAAYjB,EACZ,OAAQG,CACZ,EAMO,SAASwB,EAAyBC,EAAM,CAC3C,OAAO,IAAIhD,EAA0BgD,EAAMF,CAAkB,CACjE,CAOO,SAASG,EAAiBC,EAAK,CAElC,MAAMC,EAAWD,EAAI,MAAM,GAAG,EAAE,IAAKzC,GAASA,EAAK,QAAQ,MAAO,WAAW,CAAC,EAC9E,IAAI2C,EAAUN,EACd,UAAWrC,KAAQ0C,EAEV1C,IAGL2C,EAAUA,EAAQ3C,CAAI,GAG1B,GAAI2C,GAAWA,EAAQ,MAAQA,EAAQ,IACnC,OAAOA,CAGf,CAMO,SAASC,EAAuBH,EAAKI,EAAe,CAEvD,MAAMH,EAAWD,EAAI,MAAM,GAAG,EAAE,IAAKzC,GAASA,EAAK,QAAQ,MAAO,WAAW,CAAC,EAC9E,IAAI2C,EAAUN,EACd,UAAWrC,KAAQ0C,EAEV1C,IAGL2C,EAAUA,EAAQ3C,CAAI,GAGtB2C,GAAWA,EAAQ,MAAQA,EAAQ,MACnCA,EAAQ,cAAgBE,EAEhC,CAOO,SAASC,EAAuBL,EAAKM,EAAU,CAElD,MAAML,EAAWD,EAAI,MAAM,GAAG,EAAE,IAAKzC,GAASA,EAAK,QAAQ,MAAO,WAAW,CAAC,EAC9E,IAAI2C,EAAUN,EACd,UAAWrC,KAAQ0C,EAEf,GAAK1C,EAGL,IAAI,CAAC2C,EAAQ3C,CAAI,EAAG,CAChB,GAAIA,IAAS,IAAK,CACd2C,EAAQ,qBAAuB,GAC/B,QACH,CACDA,EAAQ3C,CAAI,EAAI,GAEZA,IAAS,cACT2C,EAAQ3C,CAAI,EAAE,WAAa,GAElC,CACD2C,EAAUA,EAAQ3C,CAAI,EAE1B,OAAO,OAAO2C,EAASI,CAAQ,CACnC", "x_google_ignoreList": [0, 1, 2, 3, 4]}