{"version": 3, "file": "pieDiagram-OC6WZ2SS-Ci8KDMpk.js", "sources": ["../../../../node_modules/.pnpm/d3-shape@3.2.0/node_modules/d3-shape/src/descending.js", "../../../../node_modules/.pnpm/d3-shape@3.2.0/node_modules/d3-shape/src/identity.js", "../../../../node_modules/.pnpm/d3-shape@3.2.0/node_modules/d3-shape/src/pie.js", "../../../../node_modules/.pnpm/mermaid@11.5.0/node_modules/mermaid/dist/chunks/mermaid.core/pieDiagram-OC6WZ2SS.mjs"], "sourcesContent": ["export default function(a, b) {\n  return b < a ? -1 : b > a ? 1 : b >= a ? 0 : NaN;\n}\n", "export default function(d) {\n  return d;\n}\n", "import array from \"./array.js\";\nimport constant from \"./constant.js\";\nimport descending from \"./descending.js\";\nimport identity from \"./identity.js\";\nimport {tau} from \"./math.js\";\n\nexport default function() {\n  var value = identity,\n      sortValues = descending,\n      sort = null,\n      startAngle = constant(0),\n      endAngle = constant(tau),\n      padAngle = constant(0);\n\n  function pie(data) {\n    var i,\n        n = (data = array(data)).length,\n        j,\n        k,\n        sum = 0,\n        index = new Array(n),\n        arcs = new Array(n),\n        a0 = +startAngle.apply(this, arguments),\n        da = Math.min(tau, Math.max(-tau, endAngle.apply(this, arguments) - a0)),\n        a1,\n        p = Math.min(Math.abs(da) / n, padAngle.apply(this, arguments)),\n        pa = p * (da < 0 ? -1 : 1),\n        v;\n\n    for (i = 0; i < n; ++i) {\n      if ((v = arcs[index[i] = i] = +value(data[i], i, data)) > 0) {\n        sum += v;\n      }\n    }\n\n    // Optionally sort the arcs by previously-computed values or by data.\n    if (sortValues != null) index.sort(function(i, j) { return sortValues(arcs[i], arcs[j]); });\n    else if (sort != null) index.sort(function(i, j) { return sort(data[i], data[j]); });\n\n    // Compute the arcs! They are stored in the original data's order.\n    for (i = 0, k = sum ? (da - n * pa) / sum : 0; i < n; ++i, a0 = a1) {\n      j = index[i], v = arcs[j], a1 = a0 + (v > 0 ? v * k : 0) + pa, arcs[j] = {\n        data: data[j],\n        index: i,\n        value: v,\n        startAngle: a0,\n        endAngle: a1,\n        padAngle: p\n      };\n    }\n\n    return arcs;\n  }\n\n  pie.value = function(_) {\n    return arguments.length ? (value = typeof _ === \"function\" ? _ : constant(+_), pie) : value;\n  };\n\n  pie.sortValues = function(_) {\n    return arguments.length ? (sortValues = _, sort = null, pie) : sortValues;\n  };\n\n  pie.sort = function(_) {\n    return arguments.length ? (sort = _, sortValues = null, pie) : sort;\n  };\n\n  pie.startAngle = function(_) {\n    return arguments.length ? (startAngle = typeof _ === \"function\" ? _ : constant(+_), pie) : startAngle;\n  };\n\n  pie.endAngle = function(_) {\n    return arguments.length ? (endAngle = typeof _ === \"function\" ? _ : constant(+_), pie) : endAngle;\n  };\n\n  pie.padAngle = function(_) {\n    return arguments.length ? (padAngle = typeof _ === \"function\" ? _ : constant(+_), pie) : padAngle;\n  };\n\n  return pie;\n}\n", "import {\n  populateCommonDb\n} from \"./chunk-IUKPXING.mjs\";\nimport {\n  cleanAndMerge,\n  parseFontSize\n} from \"./chunk-ABD7OU7K.mjs\";\nimport {\n  selectSvgElement\n} from \"./chunk-XYJ2X5CJ.mjs\";\nimport {\n  __name,\n  clear,\n  configureSvgSize,\n  defaultConfig_default,\n  getAccDescription,\n  getAccTitle,\n  getConfig2 as getConfig,\n  getDiagramTitle,\n  log,\n  setAccDescription,\n  setAccTitle,\n  setDiagramTitle\n} from \"./chunk-O7R7247Q.mjs\";\n\n// src/diagrams/pie/pieParser.ts\nimport { parse } from \"@mermaid-js/parser\";\n\n// src/diagrams/pie/pieDb.ts\nvar DEFAULT_PIE_CONFIG = defaultConfig_default.pie;\nvar DEFAULT_PIE_DB = {\n  sections: /* @__PURE__ */ new Map(),\n  showData: false,\n  config: DEFAULT_PIE_CONFIG\n};\nvar sections = DEFAULT_PIE_DB.sections;\nvar showData = DEFAULT_PIE_DB.showData;\nvar config = structuredClone(DEFAULT_PIE_CONFIG);\nvar getConfig2 = /* @__PURE__ */ __name(() => structuredClone(config), \"getConfig\");\nvar clear2 = /* @__PURE__ */ __name(() => {\n  sections = /* @__PURE__ */ new Map();\n  showData = DEFAULT_PIE_DB.showData;\n  clear();\n}, \"clear\");\nvar addSection = /* @__PURE__ */ __name(({ label, value }) => {\n  if (!sections.has(label)) {\n    sections.set(label, value);\n    log.debug(`added new section: ${label}, with value: ${value}`);\n  }\n}, \"addSection\");\nvar getSections = /* @__PURE__ */ __name(() => sections, \"getSections\");\nvar setShowData = /* @__PURE__ */ __name((toggle) => {\n  showData = toggle;\n}, \"setShowData\");\nvar getShowData = /* @__PURE__ */ __name(() => showData, \"getShowData\");\nvar db = {\n  getConfig: getConfig2,\n  clear: clear2,\n  setDiagramTitle,\n  getDiagramTitle,\n  setAccTitle,\n  getAccTitle,\n  setAccDescription,\n  getAccDescription,\n  addSection,\n  getSections,\n  setShowData,\n  getShowData\n};\n\n// src/diagrams/pie/pieParser.ts\nvar populateDb = /* @__PURE__ */ __name((ast, db2) => {\n  populateCommonDb(ast, db2);\n  db2.setShowData(ast.showData);\n  ast.sections.map(db2.addSection);\n}, \"populateDb\");\nvar parser = {\n  parse: /* @__PURE__ */ __name(async (input) => {\n    const ast = await parse(\"pie\", input);\n    log.debug(ast);\n    populateDb(ast, db);\n  }, \"parse\")\n};\n\n// src/diagrams/pie/pieStyles.ts\nvar getStyles = /* @__PURE__ */ __name((options) => `\n  .pieCircle{\n    stroke: ${options.pieStrokeColor};\n    stroke-width : ${options.pieStrokeWidth};\n    opacity : ${options.pieOpacity};\n  }\n  .pieOuterCircle{\n    stroke: ${options.pieOuterStrokeColor};\n    stroke-width: ${options.pieOuterStrokeWidth};\n    fill: none;\n  }\n  .pieTitleText {\n    text-anchor: middle;\n    font-size: ${options.pieTitleTextSize};\n    fill: ${options.pieTitleTextColor};\n    font-family: ${options.fontFamily};\n  }\n  .slice {\n    font-family: ${options.fontFamily};\n    fill: ${options.pieSectionTextColor};\n    font-size:${options.pieSectionTextSize};\n    // fill: white;\n  }\n  .legend text {\n    fill: ${options.pieLegendTextColor};\n    font-family: ${options.fontFamily};\n    font-size: ${options.pieLegendTextSize};\n  }\n`, \"getStyles\");\nvar pieStyles_default = getStyles;\n\n// src/diagrams/pie/pieRenderer.ts\nimport { arc, pie as d3pie, scaleOrdinal } from \"d3\";\nvar createPieArcs = /* @__PURE__ */ __name((sections2) => {\n  const pieData = [...sections2.entries()].map((element) => {\n    return {\n      label: element[0],\n      value: element[1]\n    };\n  }).sort((a, b) => {\n    return b.value - a.value;\n  });\n  const pie = d3pie().value(\n    (d3Section) => d3Section.value\n  );\n  return pie(pieData);\n}, \"createPieArcs\");\nvar draw = /* @__PURE__ */ __name((text, id, _version, diagObj) => {\n  log.debug(\"rendering pie chart\\n\" + text);\n  const db2 = diagObj.db;\n  const globalConfig = getConfig();\n  const pieConfig = cleanAndMerge(db2.getConfig(), globalConfig.pie);\n  const MARGIN = 40;\n  const LEGEND_RECT_SIZE = 18;\n  const LEGEND_SPACING = 4;\n  const height = 450;\n  const pieWidth = height;\n  const svg = selectSvgElement(id);\n  const group = svg.append(\"g\");\n  group.attr(\"transform\", \"translate(\" + pieWidth / 2 + \",\" + height / 2 + \")\");\n  const { themeVariables } = globalConfig;\n  let [outerStrokeWidth] = parseFontSize(themeVariables.pieOuterStrokeWidth);\n  outerStrokeWidth ??= 2;\n  const textPosition = pieConfig.textPosition;\n  const radius = Math.min(pieWidth, height) / 2 - MARGIN;\n  const arcGenerator = arc().innerRadius(0).outerRadius(radius);\n  const labelArcGenerator = arc().innerRadius(radius * textPosition).outerRadius(radius * textPosition);\n  group.append(\"circle\").attr(\"cx\", 0).attr(\"cy\", 0).attr(\"r\", radius + outerStrokeWidth / 2).attr(\"class\", \"pieOuterCircle\");\n  const sections2 = db2.getSections();\n  const arcs = createPieArcs(sections2);\n  const myGeneratedColors = [\n    themeVariables.pie1,\n    themeVariables.pie2,\n    themeVariables.pie3,\n    themeVariables.pie4,\n    themeVariables.pie5,\n    themeVariables.pie6,\n    themeVariables.pie7,\n    themeVariables.pie8,\n    themeVariables.pie9,\n    themeVariables.pie10,\n    themeVariables.pie11,\n    themeVariables.pie12\n  ];\n  const color = scaleOrdinal(myGeneratedColors);\n  group.selectAll(\"mySlices\").data(arcs).enter().append(\"path\").attr(\"d\", arcGenerator).attr(\"fill\", (datum) => {\n    return color(datum.data.label);\n  }).attr(\"class\", \"pieCircle\");\n  let sum = 0;\n  sections2.forEach((section) => {\n    sum += section;\n  });\n  group.selectAll(\"mySlices\").data(arcs).enter().append(\"text\").text((datum) => {\n    return (datum.data.value / sum * 100).toFixed(0) + \"%\";\n  }).attr(\"transform\", (datum) => {\n    return \"translate(\" + labelArcGenerator.centroid(datum) + \")\";\n  }).style(\"text-anchor\", \"middle\").attr(\"class\", \"slice\");\n  group.append(\"text\").text(db2.getDiagramTitle()).attr(\"x\", 0).attr(\"y\", -(height - 50) / 2).attr(\"class\", \"pieTitleText\");\n  const legend = group.selectAll(\".legend\").data(color.domain()).enter().append(\"g\").attr(\"class\", \"legend\").attr(\"transform\", (_datum, index) => {\n    const height2 = LEGEND_RECT_SIZE + LEGEND_SPACING;\n    const offset = height2 * color.domain().length / 2;\n    const horizontal = 12 * LEGEND_RECT_SIZE;\n    const vertical = index * height2 - offset;\n    return \"translate(\" + horizontal + \",\" + vertical + \")\";\n  });\n  legend.append(\"rect\").attr(\"width\", LEGEND_RECT_SIZE).attr(\"height\", LEGEND_RECT_SIZE).style(\"fill\", color).style(\"stroke\", color);\n  legend.data(arcs).append(\"text\").attr(\"x\", LEGEND_RECT_SIZE + LEGEND_SPACING).attr(\"y\", LEGEND_RECT_SIZE - LEGEND_SPACING).text((datum) => {\n    const { label, value } = datum.data;\n    if (db2.getShowData()) {\n      return `${label} [${value}]`;\n    }\n    return label;\n  });\n  const longestTextWidth = Math.max(\n    ...legend.selectAll(\"text\").nodes().map((node) => node?.getBoundingClientRect().width ?? 0)\n  );\n  const totalWidth = pieWidth + MARGIN + LEGEND_RECT_SIZE + LEGEND_SPACING + longestTextWidth;\n  svg.attr(\"viewBox\", `0 0 ${totalWidth} ${height}`);\n  configureSvgSize(svg, height, totalWidth, pieConfig.useMaxWidth);\n}, \"draw\");\nvar renderer = { draw };\n\n// src/diagrams/pie/pieDiagram.ts\nvar diagram = {\n  parser,\n  db,\n  renderer,\n  styles: pieStyles_default\n};\nexport {\n  diagram\n};\n"], "names": ["descending", "a", "b", "identity", "d", "d3pie", "value", "sortValues", "sort", "startAngle", "constant", "endAngle", "tau", "padAngle", "pie", "data", "i", "n", "array", "j", "k", "sum", "index", "arcs", "a0", "da", "a1", "p", "pa", "v", "_", "DEFAULT_PIE_CONFIG", "defaultConfig_default", "DEFAULT_PIE_DB", "sections", "showData", "config", "getConfig2", "__name", "clear2", "clear", "addSection", "label", "log", "getSections", "setShowData", "toggle", "getShowData", "db", "setDiagramTitle", "getDiagramTitle", "setAccTitle", "getAccTitle", "setAccDescription", "getAccDescription", "populateDb", "ast", "db2", "populateCommonDb", "parser", "input", "parse", "getStyles", "options", "pieStyles_default", "createPieArcs", "sections2", "pieData", "element", "d3Section", "draw", "text", "id", "_version", "diagObj", "globalConfig", "getConfig", "pieConfig", "cleanAndMerge", "MARGIN", "LEGEND_RECT_SIZE", "LEGEND_SPACING", "height", "<PERSON><PERSON><PERSON><PERSON>", "svg", "selectSvgElement", "group", "themeVariables", "outerStrokeWidth", "parseFontSize", "textPosition", "radius", "arcGenerator", "arc", "labelArcGenerator", "myGeneratedColors", "color", "scaleOrdinal", "datum", "section", "legend", "_datum", "height2", "offset", "horizontal", "vertical", "longestTextWidth", "node", "totalWidth", "configureSvgSize", "renderer", "diagram"], "mappings": "gnBAAe,SAAAA,GAASC,EAAGC,EAAG,CAC5B,OAAOA,EAAID,EAAI,GAAKC,EAAID,EAAI,EAAIC,GAAKD,EAAI,EAAI,GAC/C,CCFe,SAAQE,GAACC,EAAG,CACzB,OAAOA,CACT,CCIe,SAAAC,IAAW,CACxB,IAAIC,EAAQH,GACRI,EAAaP,GACbQ,EAAO,KACPC,EAAaC,EAAS,CAAC,EACvBC,EAAWD,EAASE,CAAG,EACvBC,EAAWH,EAAS,CAAC,EAEzB,SAASI,EAAIC,EAAM,CACjB,IAAIC,EACAC,GAAKF,EAAOG,GAAMH,CAAI,GAAG,OACzBI,EACAC,EACAC,EAAM,EACNC,EAAQ,IAAI,MAAML,CAAC,EACnBM,EAAO,IAAI,MAAMN,CAAC,EAClBO,EAAK,CAACf,EAAW,MAAM,KAAM,SAAS,EACtCgB,EAAK,KAAK,IAAIb,EAAK,KAAK,IAAI,CAACA,EAAKD,EAAS,MAAM,KAAM,SAAS,EAAIa,CAAE,CAAC,EACvEE,EACAC,EAAI,KAAK,IAAI,KAAK,IAAIF,CAAE,EAAIR,EAAGJ,EAAS,MAAM,KAAM,SAAS,CAAC,EAC9De,EAAKD,GAAKF,EAAK,EAAI,GAAK,GACxBI,EAEJ,IAAKb,EAAI,EAAGA,EAAIC,EAAG,EAAED,GACda,EAAIN,EAAKD,EAAMN,CAAC,EAAIA,CAAC,EAAI,CAACV,EAAMS,EAAKC,CAAC,EAAGA,EAAGD,CAAI,GAAK,IACxDM,GAAOQ,GASX,IAJItB,GAAc,KAAMe,EAAM,KAAK,SAASN,EAAGG,EAAG,CAAE,OAAOZ,EAAWgB,EAAKP,CAAC,EAAGO,EAAKJ,CAAC,CAAC,CAAE,CAAE,EACjFX,GAAQ,MAAMc,EAAM,KAAK,SAASN,EAAGG,EAAG,CAAE,OAAOX,EAAKO,EAAKC,CAAC,EAAGD,EAAKI,CAAC,CAAC,CAAE,CAAE,EAG9EH,EAAI,EAAGI,EAAIC,GAAOI,EAAKR,EAAIW,GAAMP,EAAM,EAAGL,EAAIC,EAAG,EAAED,EAAGQ,EAAKE,EAC9DP,EAAIG,EAAMN,CAAC,EAAGa,EAAIN,EAAKJ,CAAC,EAAGO,EAAKF,GAAMK,EAAI,EAAIA,EAAIT,EAAI,GAAKQ,EAAIL,EAAKJ,CAAC,EAAI,CACvE,KAAMJ,EAAKI,CAAC,EACZ,MAAOH,EACP,MAAOa,EACP,WAAYL,EACZ,SAAUE,EACV,SAAUC,CAClB,EAGI,OAAOJ,CACR,CAED,OAAAT,EAAI,MAAQ,SAASgB,EAAG,CACtB,OAAO,UAAU,QAAUxB,EAAQ,OAAOwB,GAAM,WAAaA,EAAIpB,EAAS,CAACoB,CAAC,EAAGhB,GAAOR,CAC1F,EAEEQ,EAAI,WAAa,SAASgB,EAAG,CAC3B,OAAO,UAAU,QAAUvB,EAAauB,EAAGtB,EAAO,KAAMM,GAAOP,CACnE,EAEEO,EAAI,KAAO,SAASgB,EAAG,CACrB,OAAO,UAAU,QAAUtB,EAAOsB,EAAGvB,EAAa,KAAMO,GAAON,CACnE,EAEEM,EAAI,WAAa,SAASgB,EAAG,CAC3B,OAAO,UAAU,QAAUrB,EAAa,OAAOqB,GAAM,WAAaA,EAAIpB,EAAS,CAACoB,CAAC,EAAGhB,GAAOL,CAC/F,EAEEK,EAAI,SAAW,SAASgB,EAAG,CACzB,OAAO,UAAU,QAAUnB,EAAW,OAAOmB,GAAM,WAAaA,EAAIpB,EAAS,CAACoB,CAAC,EAAGhB,GAAOH,CAC7F,EAEEG,EAAI,SAAW,SAASgB,EAAG,CACzB,OAAO,UAAU,QAAUjB,EAAW,OAAOiB,GAAM,WAAaA,EAAIpB,EAAS,CAACoB,CAAC,EAAGhB,GAAOD,CAC7F,EAESC,CACT,CClDA,IAAIiB,EAAqBC,EAAsB,IAC3CC,EAAiB,CACnB,SAA0B,IAAI,IAC9B,SAAU,GACV,OAAQF,CACV,EACIG,EAAWD,EAAe,SAC1BE,EAAWF,EAAe,SAC1BG,GAAS,gBAAgBL,CAAkB,EAC3CM,GAA6BC,EAAO,IAAM,gBAAgBF,EAAM,EAAG,WAAW,EAC9EG,GAAyBD,EAAO,IAAM,CACxCJ,EAA2B,IAAI,IAC/BC,EAAWF,EAAe,SAC1BO,GACF,EAAG,OAAO,EACNC,GAA6BH,EAAO,CAAC,CAAE,MAAAI,EAAO,MAAApC,CAAK,IAAO,CACvD4B,EAAS,IAAIQ,CAAK,IACrBR,EAAS,IAAIQ,EAAOpC,CAAK,EACzBqC,EAAI,MAAM,sBAAsBD,CAAK,iBAAiBpC,CAAK,EAAE,EAEjE,EAAG,YAAY,EACXsC,GAA8BN,EAAO,IAAMJ,EAAU,aAAa,EAClEW,GAA8BP,EAAQQ,GAAW,CACnDX,EAAWW,CACb,EAAG,aAAa,EACZC,GAA8BT,EAAO,IAAMH,EAAU,aAAa,EAClEa,EAAK,CACP,UAAWX,GACX,MAAOE,GACP,gBAAAU,EACA,gBAAAC,EACA,YAAAC,EACA,YAAAC,EACA,kBAAAC,EACA,kBAAAC,EACA,WAAAb,GACA,YAAAG,GACA,YAAAC,GACA,YAAAE,EACF,EAGIQ,GAA6BjB,EAAO,CAACkB,EAAKC,IAAQ,CACpDC,EAAiBF,EAAKC,CAAG,EACzBA,EAAI,YAAYD,EAAI,QAAQ,EAC5BA,EAAI,SAAS,IAAIC,EAAI,UAAU,CACjC,EAAG,YAAY,EACXE,GAAS,CACX,MAAuBrB,EAAO,MAAOsB,GAAU,CAC7C,MAAMJ,EAAM,MAAMK,GAAM,MAAOD,CAAK,EACpCjB,EAAI,MAAMa,CAAG,EACbD,GAAWC,EAAKR,CAAE,CACnB,EAAE,OAAO,CACZ,EAGIc,GAA4BxB,EAAQyB,GAAY;AAAA;AAAA,cAEtCA,EAAQ,cAAc;AAAA,qBACfA,EAAQ,cAAc;AAAA,gBAC3BA,EAAQ,UAAU;AAAA;AAAA;AAAA,cAGpBA,EAAQ,mBAAmB;AAAA,oBACrBA,EAAQ,mBAAmB;AAAA;AAAA;AAAA;AAAA;AAAA,iBAK9BA,EAAQ,gBAAgB;AAAA,YAC7BA,EAAQ,iBAAiB;AAAA,mBAClBA,EAAQ,UAAU;AAAA;AAAA;AAAA,mBAGlBA,EAAQ,UAAU;AAAA,YACzBA,EAAQ,mBAAmB;AAAA,gBACvBA,EAAQ,kBAAkB;AAAA;AAAA;AAAA;AAAA,YAI9BA,EAAQ,kBAAkB;AAAA,mBACnBA,EAAQ,UAAU;AAAA,iBACpBA,EAAQ,iBAAiB;AAAA;AAAA,EAEvC,WAAW,EACVC,GAAoBF,GAIpBG,GAAgC3B,EAAQ4B,GAAc,CACxD,MAAMC,EAAU,CAAC,GAAGD,EAAU,QAAO,CAAE,EAAE,IAAKE,IACrC,CACL,MAAOA,EAAQ,CAAC,EAChB,MAAOA,EAAQ,CAAC,CACtB,EACG,EAAE,KAAK,CAACnE,EAAGC,IACHA,EAAE,MAAQD,EAAE,KACpB,EAID,OAHYI,GAAK,EAAG,MACjBgE,GAAcA,EAAU,KAC7B,EACaF,CAAO,CACpB,EAAG,eAAe,EACdG,GAAuBhC,EAAO,CAACiC,EAAMC,EAAIC,EAAUC,IAAY,CACjE/B,EAAI,MAAM;AAAA,EAA0B4B,CAAI,EACxC,MAAMd,EAAMiB,EAAQ,GACdC,EAAeC,IACfC,EAAYC,GAAcrB,EAAI,UAAS,EAAIkB,EAAa,GAAG,EAC3DI,EAAS,GACTC,EAAmB,GACnBC,EAAiB,EACjBC,EAAS,IACTC,EAAWD,EACXE,EAAMC,GAAiBb,CAAE,EACzBc,EAAQF,EAAI,OAAO,GAAG,EAC5BE,EAAM,KAAK,YAAa,aAAeH,EAAW,EAAI,IAAMD,EAAS,EAAI,GAAG,EAC5E,KAAM,CAAE,eAAAK,CAAgB,EAAGZ,EAC3B,GAAI,CAACa,CAAgB,EAAIC,GAAcF,EAAe,mBAAmB,EACzEC,IAAqB,EACrB,MAAME,EAAeb,EAAU,aACzBc,EAAS,KAAK,IAAIR,EAAUD,CAAM,EAAI,EAAIH,EAC1Ca,EAAeC,IAAM,YAAY,CAAC,EAAE,YAAYF,CAAM,EACtDG,EAAoBD,EAAK,EAAC,YAAYF,EAASD,CAAY,EAAE,YAAYC,EAASD,CAAY,EACpGJ,EAAM,OAAO,QAAQ,EAAE,KAAK,KAAM,CAAC,EAAE,KAAK,KAAM,CAAC,EAAE,KAAK,IAAKK,EAASH,EAAmB,CAAC,EAAE,KAAK,QAAS,gBAAgB,EAC1H,MAAMtB,EAAYT,EAAI,cAChBlC,EAAO0C,GAAcC,CAAS,EAC9B6B,EAAoB,CACxBR,EAAe,KACfA,EAAe,KACfA,EAAe,KACfA,EAAe,KACfA,EAAe,KACfA,EAAe,KACfA,EAAe,KACfA,EAAe,KACfA,EAAe,KACfA,EAAe,MACfA,EAAe,MACfA,EAAe,KACnB,EACQS,EAAQC,GAAaF,CAAiB,EAC5CT,EAAM,UAAU,UAAU,EAAE,KAAK/D,CAAI,EAAE,QAAQ,OAAO,MAAM,EAAE,KAAK,IAAKqE,CAAY,EAAE,KAAK,OAASM,GAC3FF,EAAME,EAAM,KAAK,KAAK,CAC9B,EAAE,KAAK,QAAS,WAAW,EAC5B,IAAI7E,EAAM,EACV6C,EAAU,QAASiC,GAAY,CAC7B9E,GAAO8E,CACX,CAAG,EACDb,EAAM,UAAU,UAAU,EAAE,KAAK/D,CAAI,EAAE,MAAO,EAAC,OAAO,MAAM,EAAE,KAAM2E,IAC1DA,EAAM,KAAK,MAAQ7E,EAAM,KAAK,QAAQ,CAAC,EAAI,GACpD,EAAE,KAAK,YAAc6E,GACb,aAAeJ,EAAkB,SAASI,CAAK,EAAI,GAC3D,EAAE,MAAM,cAAe,QAAQ,EAAE,KAAK,QAAS,OAAO,EACvDZ,EAAM,OAAO,MAAM,EAAE,KAAK7B,EAAI,gBAAiB,CAAA,EAAE,KAAK,IAAK,CAAC,EAAE,KAAK,IAAK,EAAEyB,EAAS,IAAM,CAAC,EAAE,KAAK,QAAS,cAAc,EACxH,MAAMkB,EAASd,EAAM,UAAU,SAAS,EAAE,KAAKU,EAAM,OAAQ,CAAA,EAAE,MAAK,EAAG,OAAO,GAAG,EAAE,KAAK,QAAS,QAAQ,EAAE,KAAK,YAAa,CAACK,EAAQ/E,IAAU,CAC9I,MAAMgF,EAAUtB,EAAmBC,EAC7BsB,EAASD,EAAUN,EAAM,OAAQ,EAAC,OAAS,EAC3CQ,EAAa,GAAKxB,EAClByB,EAAWnF,EAAQgF,EAAUC,EACnC,MAAO,aAAeC,EAAa,IAAMC,EAAW,GACxD,CAAG,EACDL,EAAO,OAAO,MAAM,EAAE,KAAK,QAASpB,CAAgB,EAAE,KAAK,SAAUA,CAAgB,EAAE,MAAM,OAAQgB,CAAK,EAAE,MAAM,SAAUA,CAAK,EACjII,EAAO,KAAK7E,CAAI,EAAE,OAAO,MAAM,EAAE,KAAK,IAAKyD,EAAmBC,CAAc,EAAE,KAAK,IAAKD,EAAmBC,CAAc,EAAE,KAAMiB,GAAU,CACzI,KAAM,CAAE,MAAAxD,EAAO,MAAApC,GAAU4F,EAAM,KAC/B,OAAIzC,EAAI,cACC,GAAGf,CAAK,KAAKpC,CAAK,IAEpBoC,CACX,CAAG,EACD,MAAMgE,EAAmB,KAAK,IAC5B,GAAGN,EAAO,UAAU,MAAM,EAAE,MAAK,EAAG,IAAKO,GAASA,GAAM,wBAAwB,OAAS,CAAC,CAC9F,EACQC,EAAazB,EAAWJ,EAASC,EAAmBC,EAAiByB,EAC3EtB,EAAI,KAAK,UAAW,OAAOwB,CAAU,IAAI1B,CAAM,EAAE,EACjD2B,GAAiBzB,EAAKF,EAAQ0B,EAAY/B,EAAU,WAAW,CACjE,EAAG,MAAM,EACLiC,GAAW,CAAE,KAAAxC,IAGbyC,GAAU,CACZ,OAAApD,GACA,GAAAX,EACA,SAAA8D,GACA,OAAQ9C,EACV", "x_google_ignoreList": [0, 1, 2, 3]}