import{aH as N,V as m,aI as H,a as R,M,ak as A,k as U,aJ as w,aK as j,aL as G,p as I}from"./index-DIZHUVg4.js";import"./index-DJ2rNx9E.js";import"./svelte/svelte.js";const W={internalPickerForMesh:void 0};class h{constructor(i,e,t=Number.MAX_VALUE,r=N){this.origin=i,this.direction=e,this.length=t,this.epsilon=r}clone(){return new h(this.origin.clone(),this.direction.clone(),this.length)}intersectsBoxMinMax(i,e,t=0){const r=h._TmpVector3[0].copyFromFloats(i.x-t,i.y-t,i.z-t),o=h._TmpVector3[1].copyFromFloats(e.x+t,e.y+t,e.z+t);let s=0,l=Number.MAX_VALUE,f,c,a,u;if(Math.abs(this.direction.x)<1e-7){if(this.origin.x<r.x||this.origin.x>o.x)return!1}else if(f=1/this.direction.x,c=(r.x-this.origin.x)*f,a=(o.x-this.origin.x)*f,a===-1/0&&(a=1/0),c>a&&(u=c,c=a,a=u),s=Math.max(c,s),l=Math.min(a,l),s>l)return!1;if(Math.abs(this.direction.y)<1e-7){if(this.origin.y<r.y||this.origin.y>o.y)return!1}else if(f=1/this.direction.y,c=(r.y-this.origin.y)*f,a=(o.y-this.origin.y)*f,a===-1/0&&(a=1/0),c>a&&(u=c,c=a,a=u),s=Math.max(c,s),l=Math.min(a,l),s>l)return!1;if(Math.abs(this.direction.z)<1e-7){if(this.origin.z<r.z||this.origin.z>o.z)return!1}else if(f=1/this.direction.z,c=(r.z-this.origin.z)*f,a=(o.z-this.origin.z)*f,a===-1/0&&(a=1/0),c>a&&(u=c,c=a,a=u),s=Math.max(c,s),l=Math.min(a,l),s>l)return!1;return!0}intersectsBox(i,e=0){return this.intersectsBoxMinMax(i.minimum,i.maximum,e)}intersectsSphere(i,e=0){const t=i.center.x-this.origin.x,r=i.center.y-this.origin.y,o=i.center.z-this.origin.z,s=t*t+r*r+o*o,l=i.radius+e,f=l*l;if(s<=f)return!0;const c=t*this.direction.x+r*this.direction.y+o*this.direction.z;return c<0?!1:s-c*c<=f}intersectsTriangle(i,e,t){const r=h._TmpVector3[0],o=h._TmpVector3[1],s=h._TmpVector3[2],l=h._TmpVector3[3],f=h._TmpVector3[4];e.subtractToRef(i,r),t.subtractToRef(i,o),m.CrossToRef(this.direction,o,s);const c=m.Dot(r,s);if(c===0)return null;const a=1/c;this.origin.subtractToRef(i,l);const u=m.Dot(l,s)*a;if(u<-this.epsilon||u>1+this.epsilon)return null;m.CrossToRef(l,r,f);const d=m.Dot(this.direction,f)*a;if(d<-this.epsilon||u+d>1+this.epsilon)return null;const g=m.Dot(o,f)*a;return g>this.length?null:new H(1-u-d,u,g)}intersectsPlane(i){let e;const t=m.Dot(i.normal,this.direction);if(Math.abs(t)<999999997475243e-21)return null;{const r=m.Dot(i.normal,this.origin);return e=(-i.d-r)/t,e<0?e<-999999997475243e-21?null:0:e}}intersectsAxis(i,e=0){switch(i){case"y":{const t=(this.origin.y-e)/this.direction.y;return t>0?null:new m(this.origin.x+this.direction.x*-t,e,this.origin.z+this.direction.z*-t)}case"x":{const t=(this.origin.x-e)/this.direction.x;return t>0?null:new m(e,this.origin.y+this.direction.y*-t,this.origin.z+this.direction.z*-t)}case"z":{const t=(this.origin.z-e)/this.direction.z;return t>0?null:new m(this.origin.x+this.direction.x*-t,this.origin.y+this.direction.y*-t,e)}default:return null}}intersectsMesh(i,e,t,r=!1,o,s=!1){const l=R.Matrix[0];return i.getWorldMatrix().invertToRef(l),this._tmpRay?h.TransformToRef(this,l,this._tmpRay):this._tmpRay=h.Transform(this,l),i.intersects(this._tmpRay,e,t,r,o,s)}intersectsMeshes(i,e,t){t?t.length=0:t=[];for(let r=0;r<i.length;r++){const o=this.intersectsMesh(i[r],e);o.hit&&t.push(o)}return t.sort(this._comparePickingInfo),t}_comparePickingInfo(i,e){return i.distance<e.distance?-1:i.distance>e.distance?1:0}intersectionSegment(i,e,t){const r=this.origin,o=R.Vector3[0],s=R.Vector3[1],l=R.Vector3[2],f=R.Vector3[3];e.subtractToRef(i,o),this.direction.scaleToRef(h._Rayl,l),r.addToRef(l,s),i.subtractToRef(r,f);const c=m.Dot(o,o),a=m.Dot(o,l),u=m.Dot(l,l),d=m.Dot(o,f),g=m.Dot(l,f),T=c*u-a*a;let p,x=T,y,k=T;T<h._Smallnum?(p=0,x=1,y=g,k=u):(p=a*g-u*d,y=c*g-a*d,p<0?(p=0,y=g,k=u):p>x&&(p=x,y=g+a,k=u)),y<0?(y=0,-d<0?p=0:-d>c?p=x:(p=-d,x=c)):y>k&&(y=k,-d+a<0?p=0:-d+a>c?p=x:(p=-d+a,x=c));const P=Math.abs(p)<h._Smallnum?0:p/x,b=Math.abs(y)<h._Smallnum?0:y/k,_=R.Vector3[4];l.scaleToRef(b,_);const v=R.Vector3[5];o.scaleToRef(P,v),v.addInPlace(f);const D=R.Vector3[6];return v.subtractToRef(_,D),b>0&&b<=this.length&&D.lengthSquared()<t*t?v.length():-1}update(i,e,t,r,o,s,l,f=!1){if(f){h._RayDistant||(h._RayDistant=h.Zero()),h._RayDistant.unprojectRayToRef(i,e,t,r,M.IdentityReadOnly,s,l);const c=R.Matrix[0];o.invertToRef(c),h.TransformToRef(h._RayDistant,c,this)}else this.unprojectRayToRef(i,e,t,r,o,s,l);return this}static Zero(){return new h(m.Zero(),m.Zero())}static CreateNew(i,e,t,r,o,s,l){return h.Zero().update(i,e,t,r,o,s,l)}static CreateNewFromTo(i,e,t=M.IdentityReadOnly){const r=new h(new m(0,0,0),new m(0,0,0));return h.CreateFromToToRef(i,e,r,t)}static CreateFromToToRef(i,e,t,r=M.IdentityReadOnly){t.origin.copyFrom(i);const o=e.subtractToRef(i,t.direction),s=Math.sqrt(o.x*o.x+o.y*o.y+o.z*o.z);return t.length=s,t.direction.normalize(),h.TransformToRef(t,r,t)}static Transform(i,e){const t=new h(new m(0,0,0),new m(0,0,0));return h.TransformToRef(i,e,t),t}static TransformToRef(i,e,t){m.TransformCoordinatesToRef(i.origin,e,t.origin),m.TransformNormalToRef(i.direction,e,t.direction),t.length=i.length,t.epsilon=i.epsilon;const r=t.direction,o=r.length();if(!(o===0||o===1)){const s=1/o;r.x*=s,r.y*=s,r.z*=s,t.length*=o}return t}unprojectRayToRef(i,e,t,r,o,s,l){const f=R.Matrix[0];o.multiplyToRef(s,f),f.multiplyToRef(l,f),f.invert();const c=A.LastCreatedEngine,a=R.Vector3[0];a.x=i/t*2-1,a.y=-(e/r*2-1),a.z=c?.useReverseDepthBuffer?1:c?.isNDCHalfZRange?0:-1;const u=R.Vector3[1].copyFromFloats(a.x,a.y,1-1e-8),d=R.Vector3[2],g=R.Vector3[3];m._UnprojectFromInvertedMatrixToRef(a,f,d),m._UnprojectFromInvertedMatrixToRef(u,f,g),this.origin.copyFrom(d),g.subtractToRef(d,this.direction),this.direction.normalize()}}h._TmpVector3=U(6,m.Zero);h._RayDistant=h.Zero();h._Smallnum=1e-8;h._Rayl=1e9;function F(n,i,e,t,r,o=!1){const s=h.Zero();return V(n,i,e,t,s,r,o),s}function V(n,i,e,t,r,o,s=!1,l=!1){const f=n.getEngine();if(!o&&!(o=n.activeCamera))return n;const c=o.viewport,a=f.getRenderHeight(),{x:u,y:d,width:g,height:T}=c.toGlobal(f.getRenderWidth(),a),p=1/f.getHardwareScalingLevel();return i=i*p-u,e=e*p-(a-d-T),r.update(i,e,g,T,t||M.IdentityReadOnly,s?M.IdentityReadOnly:o.getViewMatrix(),o.getProjectionMatrix(),l),n}function L(n,i,e,t){const r=h.Zero();return S(n,i,e,r,t),r}function S(n,i,e,t,r){if(!w)return n;const o=n.getEngine();if(!r&&!(r=n.activeCamera))throw new Error("Active camera not set");const s=r.viewport,l=o.getRenderHeight(),{x:f,y:c,width:a,height:u}=s.toGlobal(o.getRenderWidth(),l),d=M.Identity(),g=1/o.getHardwareScalingLevel();return i=i*g-f,e=e*g-(l-c-u),t.update(i,e,a,u,d,d,r.getProjectionMatrix()),n}function Z(n,i,e,t,r,o,s,l){const f=i(t,e.enableDistantPicking),c=e.intersects(f,r,s,o,t,l);return!c||!c.hit||!r&&n!=null&&c.distance>=n.distance?null:c}function C(n,i,e,t,r,o){let s=null;const l=!!(n.activeCameras&&n.activeCameras.length>1&&n.cameraToUseForPointers!==n.activeCamera),f=n.cameraToUseForPointers||n.activeCamera,c=W.internalPickerForMesh||Z;for(let a=0;a<n.meshes.length;a++){const u=n.meshes[a];if(e){if(!e(u,-1))continue}else if(!u.isEnabled()||!u.isVisible||!u.isPickable)continue;const d=l&&u.isWorldMatrixCameraDependent(),g=u.computeWorldMatrix(d,f);if(u.hasThinInstances&&u.thinInstanceEnablePicking){const T=c(s,i,u,g,!0,!0,o);if(T){if(r)return T;const p=R.Matrix[1],x=u.thinInstanceGetWorldMatrices();for(let y=0;y<x.length;y++){if(e&&!e(u,y))continue;x[y].multiplyToRef(g,p);const P=c(s,i,u,p,t,r,o,!0);if(P&&(s=P,s.thinInstanceIndex=y,t))return s}}}else{const T=c(s,i,u,g,t,r,o);if(T&&(s=T,t))return s}}return s||new w}function E(n,i,e,t){if(!w)return null;const r=[],o=!!(n.activeCameras&&n.activeCameras.length>1&&n.cameraToUseForPointers!==n.activeCamera),s=n.cameraToUseForPointers||n.activeCamera,l=W.internalPickerForMesh||Z;for(let f=0;f<n.meshes.length;f++){const c=n.meshes[f];if(e){if(!e(c,-1))continue}else if(!c.isEnabled()||!c.isVisible||!c.isPickable)continue;const a=o&&c.isWorldMatrixCameraDependent(),u=c.computeWorldMatrix(a,s);if(c.hasThinInstances&&c.thinInstanceEnablePicking){if(l(null,i,c,u,!0,!0,t)){const g=R.Matrix[1],T=c.thinInstanceGetWorldMatrices();for(let p=0;p<T.length;p++){if(e&&!e(c,p))continue;T[p].multiplyToRef(u,g);const y=l(null,i,c,g,!1,!1,t,!0);y&&(y.thinInstanceIndex=p,r.push(y))}}}else{const d=l(null,i,c,u,!1,!1,t);d&&r.push(d)}}return r}function q(n,i,e,t,r,o){if(!w)return null;const s=C(n,l=>(n._tempPickingRay||(n._tempPickingRay=h.Zero()),V(n,i,e,l,n._tempPickingRay,o||null),n._tempPickingRay),t,r,!0);return s&&(s.ray=F(n,i,e,M.Identity(),o||null)),s}function O(n,i,e,t,r,o,s,l=!1){const f=C(n,(c,a)=>(n._tempPickingRay||(n._tempPickingRay=h.Zero()),V(n,i,e,c,n._tempPickingRay,o||null,!1,a),n._tempPickingRay),t,r,!1,s);return f&&(f.ray=F(n,i,e,M.Identity(),o||null)),f}function B(n,i,e,t,r){const o=C(n,s=>(n._pickWithRayInverseMatrix||(n._pickWithRayInverseMatrix=M.Identity()),s.invertToRef(n._pickWithRayInverseMatrix),n._cachedRayForTransform||(n._cachedRayForTransform=h.Zero()),h.TransformToRef(i,n._pickWithRayInverseMatrix,n._cachedRayForTransform),n._cachedRayForTransform),e,t,!1,r);return o&&(o.ray=i),o}function X(n,i,e,t,r,o){return E(n,s=>F(n,i,e,s,r||null),t,o)}function J(n,i,e,t){return E(n,r=>(n._pickWithRayInverseMatrix||(n._pickWithRayInverseMatrix=M.Identity()),r.invertToRef(n._pickWithRayInverseMatrix),n._cachedRayForTransform||(n._cachedRayForTransform=h.Zero()),h.TransformToRef(i,n._pickWithRayInverseMatrix,n._cachedRayForTransform),n._cachedRayForTransform),e,t)}function it(n,i=100,e,t){return z(n,new h(m.Zero(),m.Zero(),i),i,e,t)}function z(n,i,e=100,t,r){t||(t=n.getWorldMatrix()),i.length=e,r?i.origin.copyFrom(r):i.origin.copyFrom(n.position);const o=R.Vector3[2];o.set(0,0,n._scene.useRightHandedSystem?-1:1);const s=R.Vector3[3];return m.TransformNormalToRef(o,t,s),m.NormalizeToRef(s,i.direction),i}function K(n,i){i&&(i.prototype.getForwardRay=function(e=100,t,r){return z(this,new h(m.Zero(),m.Zero(),e),e,t,r)},i.prototype.getForwardRayToRef=function(e,t=100,r,o){return z(this,e,t,r,o)}),n&&(j._IsPickingAvailable=!0,n.prototype.createPickingRay=function(e,t,r,o,s=!1){return F(this,e,t,r,o,s)})}K(I,G);I.prototype.createPickingRayToRef=function(n,i,e,t,r,o=!1,s=!1){return V(this,n,i,e,t,r,o,s)};I.prototype.createPickingRayInCameraSpace=function(n,i,e){return L(this,n,i,e)};I.prototype.createPickingRayInCameraSpaceToRef=function(n,i,e,t){return S(this,n,i,e,t)};I.prototype.pickWithBoundingInfo=function(n,i,e,t,r){return q(this,n,i,e,t,r)};I.prototype.pick=function(n,i,e,t,r,o,s=!1){return O(this,n,i,e,t,r,o,s)};I.prototype.pickWithRay=function(n,i,e,t){return B(this,n,i,e,t)};I.prototype.multiPick=function(n,i,e,t,r){return X(this,n,i,e,t,r)};I.prototype.multiPickWithRay=function(n,i,e){return J(this,n,i,e)};export{K as AddRayExtensions,F as CreatePickingRay,L as CreatePickingRayInCameraSpace,S as CreatePickingRayInCameraSpaceToRef,V as CreatePickingRayToRef,it as GetForwardRay,z as GetForwardRayToRef,X as MultiPick,J as MultiPickWithRay,O as Pick,q as PickWithBoundingInfo,B as PickWithRay,W as PickingCustomization,h as Ray};
//# sourceMappingURL=ray-BZ14Vjla.js.map
